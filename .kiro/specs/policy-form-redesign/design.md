# Design Document

## Overview

The PolicyForm redesign implements a sophisticated visual condition builder that replaces the current simple text area approach. The new design provides a user-friendly interface for creating complex verification policy conditions through cascading selects, nested logic groups, and real-time validation. The component maintains backward compatibility with existing policy data while introducing advanced condition building capabilities.

## Architecture

### Component Hierarchy

```
PolicyForm (Main Container)
├── PolicyBasicInfo (Scene, Dimension, Priority, Description)
├── ConditionBuilder (Visual Expression Builder)
│   ├── ConditionGroup (Logic Group Container)
│   │   ├── LogicSelector (AND/OR/NOT)
│   │   ├── ConditionRow[] (Individual Conditions)
│   │   │   ├── DimensionSelect
│   │   │   ├── KeySelect
│   │   │   ├── OperatorSelect
│   │   │   └── ValueInput
│   │   └── NestedConditionGroup[] (Recursive)
│   ├── TemplateSelector (Predefined Templates)
│   ├── ExpressionPreview (JSON/Human Readable)
│   └── ExpressionTester (Test Interface)
├── VerificationConfig (Target Type, Token Config, etc.)
├── RateLimitConfig (Frequency Limits)
└── TemplateConfig (Message Template)
```

### Data Flow

1. **Initialization**: Load existing policy data and populate form fields
2. **Condition Building**: User interactions update condition state through cascading selects
3. **Real-time Validation**: Condition changes trigger validation and preview updates
4. **Expression Generation**: Visual conditions convert to JSON expression format
5. **Form Submission**: Complete policy data (including generated expression) sent to backend

## Components and Interfaces

### ConditionBuilder Component

```typescript
interface ConditionBuilderProps {
  value?: ConditionExpression;
  onChange: (expression: ConditionExpression) => void;
  onValidationChange: (isValid: boolean, errors: string[]) => void;
}

interface ConditionExpression {
  logic: 'AND' | 'OR' | 'NOT';
  conditions: Array<Condition | ConditionExpression>;
}

interface Condition {
  id: string;
  dimension: string;
  key: string;
  operator: string;
  value: any;
}
```

### DimensionSchema Configuration

```typescript
interface DimensionSchema {
  value: string;
  label: string;
  keys: DimensionKey[];
}

interface DimensionKey {
  value: string;
  label: string;
  type: 'number' | 'boolean' | 'string' | 'date' | 'array';
  operators: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
  };
}

const DIMENSION_SCHEMAS: DimensionSchema[] = [
  {
    value: 'ip',
    label: 'IP地址',
    keys: [
      {
        value: 'fail_count',
        label: '失败次数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 1000 }
      },
      {
        value: 'is_black_ip',
        label: '黑名单IP',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'address',
        label: 'IP地址',
        type: 'string',
        operators: ['==', '!=', 'contains', 'startsWith', 'endsWith'],
        validation: { pattern: '^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$' }
      }
    ]
  },
  {
    value: 'user',
    label: '用户',
    keys: [
      {
        value: 'fail_count',
        label: '失败次数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between']
      },
      {
        value: 'is_new_user',
        label: '新用户',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'registration_days',
        label: '注册天数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between']
      }
    ]
  },
  {
    value: 'device',
    label: '设备',
    keys: [
      {
        value: 'is_new_device',
        label: '新设备',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'is_trusted',
        label: '可信设备',
        type: 'boolean',
        operators: ['==', '!=']
      }
    ]
  },
  {
    value: 'time',
    label: '时间',
    keys: [
      {
        value: 'within_minutes',
        label: '时间范围(分钟)',
        type: 'number',
        operators: ['<=', '<'],
        validation: { min: 1, max: 1440 }
      },
      {
        value: 'hour_of_day',
        label: '小时',
        type: 'number',
        operators: ['>=', '<=', 'between'],
        validation: { min: 0, max: 23 }
      }
    ]
  }
];
```

### ValueInput Component

```typescript
interface ValueInputProps {
  type: 'number' | 'boolean' | 'string' | 'date' | 'array';
  operator: string;
  value: any;
  onChange: (value: any) => void;
  validation?: ValidationRule;
}

// Renders different input types based on data type and operator
// - number: InputNumber or dual InputNumber for 'between'
// - boolean: Switch or Select
// - string: Input with pattern validation
// - date: DatePicker or RangePicker
// - array: Select with multiple mode
```

### ExpressionPreview Component

```typescript
interface ExpressionPreviewProps {
  expression: ConditionExpression;
  mode: 'json' | 'readable' | 'both';
}

// Displays real-time preview of the condition expression
// JSON mode: Formatted JSON structure
// Readable mode: Human-readable text like "IP失败次数 > 5 AND 用户是新用户"
// Both mode: Side-by-side display
```

### ExpressionTester Component

```typescript
interface ExpressionTesterProps {
  expression: ConditionExpression;
  onTest: (sampleData: Record<string, any>) => Promise<boolean>;
}

// Provides interface for testing expressions with sample data
// Includes sample data input form and result display
```

### TemplateSelector Component

```typescript
interface Template {
  id: string;
  name: string;
  description: string;
  expression: ConditionExpression;
  category: string;
}

const PREDEFINED_TEMPLATES: Template[] = [
  {
    id: 'brute_force_protection',
    name: '暴力破解防护',
    description: 'IP失败次数超过5次或在黑名单中',
    category: 'security',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'ip',
          key: 'fail_count',
          operator: '>',
          value: 5
        },
        {
          id: '2',
          dimension: 'ip',
          key: 'is_black_ip',
          operator: '==',
          value: true
        }
      ]
    }
  },
  {
    id: 'new_device_login',
    name: '新设备登录',
    description: '新设备或不可信设备登录',
    category: 'device',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'device',
          key: 'is_new_device',
          operator: '==',
          value: true
        },
        {
          id: '2',
          dimension: 'device',
          key: 'is_trusted',
          operator: '==',
          value: false
        }
      ]
    }
  }
];
```

## Data Models

### Enhanced Verification Types

```typescript
// Extend existing types with condition builder support
export interface ConditionExpression {
  logic: 'AND' | 'OR' | 'NOT';
  conditions: Array<Condition | ConditionExpression>;
}

export interface Condition {
  id: string;
  dimension: string;
  key: string;
  operator: string;
  value: any;
  validation?: {
    isValid: boolean;
    errors: string[];
  };
}

export interface PolicyFormData extends Omit<CreatePolicyRequest, 'condition_expr'> {
  condition_expression?: ConditionExpression;
  condition_expr?: string; // Generated from condition_expression
}

// Validation state for the entire form
export interface FormValidationState {
  isValid: boolean;
  errors: Record<string, string[]>;
  conditionErrors: string[];
}
```

### Expression Conversion

```typescript
// Convert visual conditions to backend expression format
export class ExpressionConverter {
  static toBackendFormat(expression: ConditionExpression): string {
    // Convert structured expression to backend-compatible string format
    // Example: { logic: 'AND', conditions: [...] } -> "ip.fail_count > 5 AND ip.is_black_ip == true"
  }

  static fromBackendFormat(expr: string): ConditionExpression {
    // Parse backend expression string to structured format
    // For backward compatibility with existing policies
  }

  static toHumanReadable(expression: ConditionExpression): string {
    // Convert to human-readable format for preview
    // Example: "IP失败次数 > 5 并且 IP在黑名单中"
  }
}
```

## Error Handling

### Validation Strategy

1. **Field-level Validation**: Each condition field validates independently
2. **Expression-level Validation**: Complete expression structure validation
3. **Backend Validation**: Server-side expression syntax validation
4. **Real-time Feedback**: Immediate visual feedback for validation errors

### Error Types and Handling

```typescript
enum ValidationErrorType {
  REQUIRED_FIELD = 'required_field',
  INVALID_VALUE = 'invalid_value',
  INVALID_EXPRESSION = 'invalid_expression',
  CIRCULAR_REFERENCE = 'circular_reference',
  TOO_COMPLEX = 'too_complex'
}

interface ValidationError {
  type: ValidationErrorType;
  field?: string;
  message: string;
  path?: string; // For nested condition errors
}

// Error display strategy
const ERROR_MESSAGES = {
  [ValidationErrorType.REQUIRED_FIELD]: '此字段为必填项',
  [ValidationErrorType.INVALID_VALUE]: '输入值不符合要求',
  [ValidationErrorType.INVALID_EXPRESSION]: '表达式格式错误',
  [ValidationErrorType.CIRCULAR_REFERENCE]: '检测到循环引用',
  [ValidationErrorType.TOO_COMPLEX]: '表达式过于复杂'
};
```

## Testing Strategy

### Unit Testing

1. **Component Testing**: Test individual components (ConditionBuilder, ValueInput, etc.)
2. **Expression Conversion**: Test conversion between visual and backend formats
3. **Validation Logic**: Test all validation rules and error handling
4. **Template System**: Test template loading and application

### Integration Testing

1. **Form Submission**: Test complete form submission flow
2. **Backend Integration**: Test API calls and response handling
3. **Data Persistence**: Test saving and loading of complex expressions
4. **Cross-browser Compatibility**: Test on different browsers and devices

### User Acceptance Testing

1. **Usability Testing**: Test with actual administrators
2. **Performance Testing**: Test with complex expressions and large datasets
3. **Accessibility Testing**: Ensure compliance with accessibility standards
4. **Mobile Responsiveness**: Test on various screen sizes

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load dimension schemas and templates on demand
2. **Debounced Validation**: Debounce real-time validation to avoid excessive API calls
3. **Memoization**: Cache expression conversion results
4. **Virtual Scrolling**: For large lists of conditions or templates
5. **Code Splitting**: Split condition builder into separate chunks

### Memory Management

1. **Component Cleanup**: Proper cleanup of event listeners and timers
2. **State Management**: Efficient state updates to prevent unnecessary re-renders
3. **Expression Caching**: Cache parsed expressions to avoid repeated parsing

## Migration Strategy

### Backward Compatibility

1. **Expression Parsing**: Parse existing text-based expressions into visual format
2. **Fallback Mode**: Provide text area fallback for complex expressions that can't be visualized
3. **Data Migration**: Gradual migration of existing policies to new format
4. **API Compatibility**: Maintain compatibility with existing backend APIs

### Deployment Plan

1. **Feature Flag**: Deploy behind feature flag for gradual rollout
2. **A/B Testing**: Test new interface with subset of users
3. **Monitoring**: Monitor performance and error rates
4. **Rollback Plan**: Quick rollback to old interface if issues arise

## Security Considerations

### Input Validation

1. **Expression Sanitization**: Sanitize all user inputs in expressions
2. **Injection Prevention**: Prevent code injection through expression values
3. **Access Control**: Ensure only authorized users can create/modify policies
4. **Audit Logging**: Log all policy changes for security auditing

### Data Protection

1. **Sensitive Data**: Avoid exposing sensitive system information in expressions
2. **Expression Limits**: Limit expression complexity to prevent DoS attacks
3. **Rate Limiting**: Rate limit expression validation API calls
4. **Error Information**: Limit error information to prevent information disclosure