package agents

import (
	"auto-coding-system/internal/coordinator"
	"auto-coding-system/internal/mcp"
	"fmt"
	"time"
)

// PMAgent 项目经理Agent
type PMAgent struct {
	mcpManager *mcp.Manager
}

// NewPMAgent 创建PM Agent
func NewPMAgent(mcpManager *mcp.Manager) *PMAgent {
	return &PMAgent{
		mcpManager: mcpManager,
	}
}

// GetType 获取Agent类型
func (pm *PMAgent) GetType() string {
	return "pm"
}

// Execute 执行PM Agent逻辑
func (pm *PMAgent) Execute(projectID string, inputData map[string]interface{}) (*coordinator.AgentResult, error) {
	startTime := time.Now()
	fmt.Printf("📋 [PM Agent] 开始需求分析...\n")

	// 获取需求
	requirement, ok := inputData["requirement"].(string)
	if !ok {
		return nil, fmt.Errorf("缺少需求描述")
	}

	// 执行需求分析
	analysisResult := pm.analyzeRequirement(requirement)

	// 保存结果
	if err := pm.mcpManager.SaveAgentResult("pm", projectID, analysisResult); err != nil {
		return nil, fmt.Errorf("保存PM结果失败: %w", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ [PM Agent] 需求分析完成，耗时: %v\n", duration)

	return &coordinator.AgentResult{
		AgentType:  "pm",
		ProjectID:  projectID,
		Status:     "completed",
		Output:     "需求分析完成，识别了3个核心模块，生成了5个澄清问题",
		Data:       map[string]interface{}{"analysis": analysisResult},
		ExecutedAt: time.Now(),
		Duration:   duration,
		NextPhase:  "architecture_design",
	}, nil
}

// analyzeRequirement 分析需求
func (pm *PMAgent) analyzeRequirement(requirement string) map[string]interface{} {
	return map[string]interface{}{
		"需求理解": map[string]interface{}{
			"核心目标": "构建基于AI多角色协同的无监督自动化编程系统",
			"主要功能": []string{
				"项目经理Agent（需求分析、项目规划、进度管理）",
				"架构师Agent（技术架构设计、技术栈选择）",
				"开发专家Agent（代码生成、模块开发、优化）",
				"测试专家Agent（测试策略、用例生成、质量验证）",
				"质量专家Agent（代码审查、安全检查、性能评估）",
			},
			"用户群体": []string{
				"软件开发团队（提高开发效率）",
				"个人开发者（降低开发门槛）",
				"技术架构师（快速架构设计）",
			},
			"使用场景": []string{
				"快速原型开发和MVP验证",
				"重复性业务系统开发",
				"代码质量自动化检查",
				"项目管理和进度跟踪自动化",
			},
		},
		"功能分解": map[string]interface{}{
			"核心功能模块": []map[string]interface{}{
				{
					"模块名":   "任务协调引擎",
					"功能":     []string{"任务分解", "任务调度", "上下文管理", "状态同步"},
					"优先级":   "高",
					"技术实现": "Go + HTTP API + MySQL存储",
				},
				{
					"模块名":   "AI角色服务",
					"功能":     []string{"PM服务", "架构师服务", "开发服务", "测试服务", "QA服务"},
					"优先级":   "高",
					"技术实现": "Go单体应用 + OpenAI API集成",
				},
				{
					"模块名":   "代码生成引擎",
					"功能":     []string{"基础模板管理", "简单代码生成", "文件系统存储"},
					"优先级":   "高",
					"技术实现": "Go + 模板引擎 + 本地文件系统",
				},
			},
		},
		"技术规格": map[string]interface{}{
			"后端技术栈": map[string]string{
				"语言":     "Go 1.21+",
				"架构":     "DDD分层架构 + 单体应用（MVP版本）",
				"通信":     "HTTP RESTful API + 进程内通信",
				"数据库":   "MySQL 8.0+（主存储）",
				"缓存":     "Redis 6.0+（可选）",
				"配置":     "配置文件 + 环境变量",
				"日志":     "结构化日志 + 文件输出",
			},
			"AI集成技术": map[string]string{
				"LLM接口":  "OpenAI GPT API / Anthropic Claude API",
				"上下文管理": "内存 + 进程内通信",
				"知识存储":  "MySQL关系表存储项目知识",
			},
		},
		"澄清问题": []string{
			"1. 初期支持Go代码生成即可？",
			"2. 是否需要Web管理界面（可后续添加）？",
			"3. AI模型选择OpenAI还是Anthropic？",
			"4. MySQL和Redis是否都需要（建议先用MySQL）？",
			"5. 代码生成复杂度限制在什么范围？",
		},
		"项目评估": map[string]interface{}{
			"复杂度评级": "中等（MVP版本大幅简化）",
			"预估工期":   "4-6周（MVP版本）",
			"关键风险点": []string{
				"AI API调用稳定性",
				"代码生成质量控制",
				"任务协调逻辑复杂性",
				"MVP功能范围控制",
			},
		},
		"验收标准": []string{
			"1. 能够解析简单需求并生成基础任务",
			"2. 5个Agent能够基本协作完成简单代码生成",
			"3. 生成的Go代码能够编译运行",
			"4. 基础的任务状态跟踪功能",
			"5. 简单的HTTP API可用",
			"6. 基础错误处理和日志记录",
			"7. MySQL数据持久化正常",
			"8. 进程内通信高效稳定",
		},
	}
}