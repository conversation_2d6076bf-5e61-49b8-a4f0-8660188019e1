package agents

import (
	"auto-coding-system/internal/coordinator"
	"auto-coding-system/internal/mcp"
	"fmt"
	"time"
)

// DeveloperAgent 开发者Agent
type DeveloperAgent struct {
	mcpManager *mcp.Manager
}

// NewDeveloperAgent 创建开发者Agent
func NewDeveloperAgent(mcpManager *mcp.Manager) *DeveloperAgent {
	return &DeveloperAgent{
		mcpManager: mcpManager,
	}
}

// GetType 获取Agent类型
func (dev *DeveloperAgent) GetType() string {
	return "developer"
}

// Execute 执行开发者Agent逻辑
func (dev *DeveloperAgent) Execute(projectID string, inputData map[string]interface{}) (*coordinator.AgentResult, error) {
	startTime := time.Now()
	fmt.Printf("💻 [Developer Agent] 开始代码开发...\n")

	// 获取架构设计结果
	var architectResult map[string]interface{}
	if err := dev.mcpManager.LoadAgentResult("architect", projectID, &architectResult); err != nil {
		fmt.Printf("⚠️ 无法加载架构设计结果，使用默认配置\n")
	}

	// 获取PM分析结果
	var pmResult map[string]interface{}
	if err := dev.mcpManager.LoadAgentResult("pm", projectID, &pmResult); err != nil {
		fmt.Printf("⚠️ 无法加载PM结果，使用默认配置\n")
	}

	// 执行代码开发
	developmentResult := dev.generateCode(pmResult, architectResult)

	// 保存结果
	if err := dev.mcpManager.SaveAgentResult("developer", projectID, developmentResult); err != nil {
		return nil, fmt.Errorf("保存开发结果失败: %w", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ [Developer Agent] 代码开发完成，耗时: %v\n", duration)

	return &coordinator.AgentResult{
		AgentType:  "developer",
		ProjectID:  projectID,
		Status:     "completed",
		Output:     "代码开发完成，生成了8个核心模块，包含领域层、应用层、基础设施层和接口层",
		Data:       map[string]interface{}{"development": developmentResult},
		ExecutedAt: time.Now(),
		Duration:   duration,
		NextPhase:  "testing",
	}, nil
}

// generateCode 生成代码
func (dev *DeveloperAgent) generateCode(pmResult, architectResult map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"代码架构": map[string]interface{}{
			"项目结构": map[string]interface{}{
				"领域层": map[string]interface{}{
					"目录": "internal/domain/",
					"生成文件": []string{
						"project.go - 项目实体和业务规则",
						"task.go - 任务实体和状态管理",
						"agent.go - AI代理实体定义", 
						"code_template.go - 代码模板实体",
						"generated_code.go - 生成代码实体",
						"repository.go - 仓储接口定义",
					},
					"业务规则实现": []string{
						"项目生命周期管理",
						"任务依赖关系验证",
						"Agent角色和权限控制",
						"代码生成规则验证",
						"质量标准检查",
					},
				},
				"应用层": map[string]interface{}{
					"目录": "internal/application/",
					"生成文件": []string{
						"services/project_service.go - 项目管理服务",
						"services/task_coordinator_service.go - 任务协调服务",
						"services/agent_communication_service.go - Agent通信服务",
						"services/code_generation_service.go - 代码生成服务",
						"services/quality_check_service.go - 质量检查服务",
						"usecases/create_project_usecase.go - 创建项目用例",
						"usecases/execute_workflow_usecase.go - 执行工作流用例",
					},
					"核心逻辑": []string{
						"项目创建和初始化流程",
						"任务分解和调度算法",
						"Agent间协作通信机制",
						"代码生成和模板管理",
						"质量检查和评估流程",
					},
				},
				"基础设施层": map[string]interface{}{
					"目录": "internal/infrastructure/",
					"生成文件": []string{
						"database/mysql_repository.go - MySQL数据访问",
						"database/migrations/ - 数据库迁移脚本",
						"messaging/in_process_bus.go - 进程内消息总线",
						"external/openai_client.go - OpenAI API客户端",
						"external/file_storage.go - 文件系统存储",
						"logging/structured_logger.go - 结构化日志",
						"config/app_config.go - 应用配置管理",
					},
					"技术实现": []string{
						"MySQL连接池和事务管理",
						"基于channel的消息传递",
						"HTTP客户端和重试机制",
						"文件系统操作和权限管理",
						"日志格式化和输出配置",
					},
				},
				"接口层": map[string]interface{}{
					"目录": "internal/interfaces/",
					"生成文件": []string{
						"http/handlers/project_handler.go - 项目API处理器",
						"http/handlers/agent_handler.go - Agent API处理器",
						"http/middleware/auth_middleware.go - 认证中间件",
						"http/middleware/logging_middleware.go - 日志中间件",
						"http/router.go - 路由配置",
						"grpc/ - gRPC接口定义(预留)",
					},
					"API设计": []string{
						"RESTful API设计规范",
						"统一响应格式处理",
						"请求参数验证和错误处理",
						"HTTP状态码标准化",
						"API文档和测试接口",
					},
				},
			},
		},
		"数据库设计": map[string]interface{}{
			"表结构设计": []map[string]interface{}{
				{
					"表名": "projects",
					"DDL": `CREATE TABLE projects (
						id VARCHAR(64) PRIMARY KEY,
						name VARCHAR(255) NOT NULL,
						description TEXT,
						requirement TEXT NOT NULL,
						status ENUM('INITIALIZED', 'IN_PROGRESS', 'COMPLETED', 'FAILED') DEFAULT 'INITIALIZED',
						current_phase VARCHAR(64),
						progress INT DEFAULT 0,
						metadata JSON,
						created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
						updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
						INDEX idx_status (status),
						INDEX idx_created_at (created_at)
					)`,
				},
				{
					"表名": "tasks",
					"DDL": `CREATE TABLE tasks (
						id VARCHAR(64) PRIMARY KEY,
						project_id VARCHAR(64) NOT NULL,
						agent_type VARCHAR(32) NOT NULL,
						type VARCHAR(64) NOT NULL,
						description TEXT,
						status ENUM('assigned', 'in_progress', 'completed', 'failed') DEFAULT 'assigned',
						input_data JSON,
						result JSON,
						created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
						completed_at TIMESTAMP NULL,
						FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
						INDEX idx_project_id (project_id),
						INDEX idx_agent_type (agent_type),
						INDEX idx_status (status)
					)`,
				},
				{
					"表名": "agent_results",
					"DDL": `CREATE TABLE agent_results (
						id INT AUTO_INCREMENT PRIMARY KEY,
						project_id VARCHAR(64) NOT NULL,
						agent_type VARCHAR(32) NOT NULL,
						result_data JSON NOT NULL,
						status VARCHAR(32) NOT NULL,
						duration BIGINT COMMENT 'Duration in nanoseconds',
						created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
						FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
						INDEX idx_project_id (project_id),
						INDEX idx_agent_type (agent_type),
						UNIQUE KEY uk_project_agent (project_id, agent_type)
					)`,
				},
				{
					"表名": "generated_codes",
					"DDL": `CREATE TABLE generated_codes (
						id INT AUTO_INCREMENT PRIMARY KEY,
						project_id VARCHAR(64) NOT NULL,
						file_path VARCHAR(512) NOT NULL,
						content LONGTEXT NOT NULL,
						language VARCHAR(32) NOT NULL,
						file_type ENUM('entity', 'service', 'repository', 'handler', 'config', 'test') NOT NULL,
						created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
						FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
						INDEX idx_project_id (project_id),
						INDEX idx_language (language),
						INDEX idx_file_type (file_type)
					)`,
				},
				{
					"表名": "code_templates",
					"DDL": `CREATE TABLE code_templates (
						id INT AUTO_INCREMENT PRIMARY KEY,
						name VARCHAR(255) NOT NULL,
						type VARCHAR(64) NOT NULL COMMENT 'Template type: entity, service, handler, etc',
						content LONGTEXT NOT NULL,
						language VARCHAR(32) NOT NULL,
						description TEXT,
						variables JSON COMMENT 'Template variables definition',
						created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
						updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
						INDEX idx_type (type),
						INDEX idx_language (language),
						UNIQUE KEY uk_name_lang (name, language)
					)`,
				},
			},
		},
		"代码实现": map[string]interface{}{
			"核心业务逻辑": []map[string]interface{}{
				{
					"模块": "项目管理",
					"文件": "internal/domain/project.go",
					"代码示例": `package domain

import (
	"time"
	"errors"
)

type ProjectStatus string

const (
	StatusInitialized ProjectStatus = "INITIALIZED"
	StatusInProgress  ProjectStatus = "IN_PROGRESS"
	StatusCompleted   ProjectStatus = "COMPLETED"
	StatusFailed      ProjectStatus = "FAILED"
)

type Project struct {
	ID           string                 
	Name         string                 
	Description  string                 
	Requirement  string                 
	Status       ProjectStatus          
	CurrentPhase string                 
	Progress     int                    
	Phases       map[string]PhaseInfo   
	Metadata     map[string]interface{} 
	CreatedAt    time.Time              
	UpdatedAt    time.Time              
}

type PhaseInfo struct {
	Status   string 
	Progress int    
}

func NewProject(name, requirement string) *Project {
	return &Project{
		ID:           generateProjectID(),
		Name:         name,
		Requirement:  requirement,
		Status:       StatusInitialized,
		CurrentPhase: "requirement_analysis",
		Progress:     0,
		Phases:       initializePhases(),
		Metadata:     make(map[string]interface{}),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

func (p *Project) AdvanceToPhase(phase string) error {
	if !isValidPhase(phase) {
		return errors.New("invalid phase")
	}
	p.CurrentPhase = phase
	p.UpdatedAt = time.Now()
	return nil
}`,
				},
				{
					"模块": "任务协调",
					"文件": "internal/application/services/task_coordinator_service.go",
					"代码示例": `package services

import (
	"context"
	"auto-coding-system/internal/domain"
)

type TaskCoordinatorService struct {
	projectRepo domain.ProjectRepository
	taskRepo    domain.TaskRepository
	messageBus  MessageBus
}

func NewTaskCoordinatorService(
	projectRepo domain.ProjectRepository,
	taskRepo domain.TaskRepository,
	messageBus MessageBus,
) *TaskCoordinatorService {
	return &TaskCoordinatorService{
		projectRepo: projectRepo,
		taskRepo:    taskRepo,
		messageBus:  messageBus,
	}
}

func (s *TaskCoordinatorService) ExecuteWorkflow(ctx context.Context, projectID string) error {
	project, err := s.projectRepo.GetByID(ctx, projectID)
	if err != nil {
		return err
	}
	
	workflow := []string{"pm", "architect", "developer", "test", "qa"}
	
	for _, agentType := range workflow {
		task := domain.NewTask(projectID, agentType, "execute_phase")
		if err := s.taskRepo.Save(ctx, task); err != nil {
			return err
		}
		
		// 发送任务到Agent
		if err := s.messageBus.SendTask(agentType, task); err != nil {
			return err
		}
	}
	
	return nil
}`,
				},
			},
			"进程内通信": map[string]interface{}{
				"消息总线": `package messaging

import (
	"sync"
	"auto-coding-system/internal/domain"
)

type InProcessBus struct {
	channels map[string]chan *domain.Task
	mu       sync.RWMutex
}

func NewInProcessBus() *InProcessBus {
	return &InProcessBus{
		channels: make(map[string]chan *domain.Task),
	}
}

func (bus *InProcessBus) RegisterAgent(agentType string, handler func(*domain.Task)) {
	bus.mu.Lock()
	defer bus.mu.Unlock()
	
	ch := make(chan *domain.Task, 100)
	bus.channels[agentType] = ch
	
	go func() {
		for task := range ch {
			handler(task)
		}
	}()
}

func (bus *InProcessBus) SendTask(agentType string, task *domain.Task) error {
	bus.mu.RLock()
	defer bus.mu.RUnlock()
	
	ch, exists := bus.channels[agentType]
	if !exists {
		return errors.New("agent not registered")
	}
	
	select {
	case ch <- task:
		return nil
	default:
		return errors.New("agent channel full")
	}
}`,
			},
		},
		"API接口实现": map[string]interface{}{
			"项目管理API": []map[string]interface{}{
				{
					"端点": "POST /api/projects",
					"功能": "创建新项目并启动工作流程",
					"实现": `func (h *ProjectHandler) CreateProject(w http.ResponseWriter, r *http.Request) {
	var req CreateProjectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}
	
	project := domain.NewProject(req.Name, req.Requirement)
	if err := h.projectService.Create(r.Context(), project); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 启动工作流程
	go h.coordinatorService.ExecuteWorkflow(context.Background(), project.ID)
	
	response := map[string]interface{}{
		"code": 200,
		"data": map[string]string{
			"project_id": project.ID,
			"status": "running",
		},
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}`,
				},
				{
					"端点": "GET /api/projects/{id}",
					"功能": "获取项目详情和执行状态", 
					"实现": `func (h *ProjectHandler) GetProject(w http.ResponseWriter, r *http.Request) {
	projectID := mux.Vars(r)["id"]
	
	project, err := h.projectService.GetByID(r.Context(), projectID)
	if err != nil {
		http.Error(w, "Project not found", http.StatusNotFound)
		return
	}
	
	response := map[string]interface{}{
		"code": 200,
		"data": project,
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}`,
				},
			},
		},
		"质量指标": map[string]interface{}{
			"代码质量": []string{
				"遵循Go代码规范和最佳实践",
				"95%以上的测试覆盖率目标",
				"清晰的包结构和依赖关系",
				"完整的错误处理和日志记录",
				"性能优化的数据库查询",
			},
			"架构质量": []string{
				"清晰的分层架构和职责分离",
				"低耦合高内聚的模块设计", 
				"可扩展的接口和抽象设计",
				"高效的进程内通信机制",
				"完善的配置和环境管理",
			},
			"可维护性": []string{
				"详细的代码注释和文档",
				"标准化的命名规范",
				"易于理解的业务逻辑实现",
				"模块化的组件设计",
				"版本控制和变更管理",
			},
		},
		"技术亮点": []string{
			"基于DDD的清晰架构分层",
			"高效的进程内Agent通信机制",
			"完整的项目生命周期管理",
			"可扩展的代码生成模板系统",
			"实时的任务状态跟踪和监控",
			"标准化的API设计和错误处理",
			"优化的数据库设计和索引策略",
			"灵活的配置管理和环境适配",
		},
	}
}