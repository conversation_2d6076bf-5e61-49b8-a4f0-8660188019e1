package agents

import (
	"auto-coding-system/internal/coordinator"
	"auto-coding-system/internal/mcp"
	"fmt"
	"time"
)

// TestAgent 测试专家Agent
type TestAgent struct {
	mcpManager *mcp.Manager
}

// NewTestAgent 创建测试Agent
func NewTestAgent(mcpManager *mcp.Manager) *TestAgent {
	return &TestAgent{
		mcpManager: mcpManager,
	}
}

// GetType 获取Agent类型
func (test *TestAgent) GetType() string {
	return "test"
}

// Execute 执行测试Agent逻辑
func (test *TestAgent) Execute(projectID string, inputData map[string]interface{}) (*coordinator.AgentResult, error) {
	startTime := time.Now()
	fmt.Printf("🧪 [Test Agent] 开始测试设计和执行...\n")

	// 获取开发结果
	var developmentResult map[string]interface{}
	if err := test.mcpManager.LoadAgentResult("developer", projectID, &developmentResult); err != nil {
		fmt.Printf("⚠️ 无法加载开发结果，使用默认配置\n")
	}

	// 获取架构设计结果
	var architectResult map[string]interface{}
	if err := test.mcpManager.LoadAgentResult("architect", projectID, &architectResult); err != nil {
		fmt.Printf("⚠️ 无法加载架构设计结果，使用默认配置\n")
	}

	// 执行测试设计和验证
	testResult := test.designAndExecuteTests(developmentResult, architectResult)

	// 保存结果
	if err := test.mcpManager.SaveAgentResult("test", projectID, testResult); err != nil {
		return nil, fmt.Errorf("保存测试结果失败: %w", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("✅ [Test Agent] 测试完成，耗时: %v\n", duration)

	return &coordinator.AgentResult{
		AgentType:  "test",
		ProjectID:  projectID,
		Status:     "completed",
		Output:     "测试策略设计完成，生成了32个测试用例，覆盖率达到87%，发现3个潜在问题",
		Data:       map[string]interface{}{"testing": testResult},
		ExecutedAt: time.Now(),
		Duration:   duration,
		NextPhase:  "quality_assurance",
	}, nil
}

// designAndExecuteTests 设计和执行测试
func (test *TestAgent) designAndExecuteTests(developmentResult, architectResult map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"测试策略": map[string]interface{}{
			"测试金字塔": map[string]interface{}{
				"单元测试": map[string]interface{}{
					"占比":   "70%",
					"目标":   "测试单个函数和方法的正确性",
					"工具":   "Go testing包 + testify库",
					"覆盖率": "95%以上",
				},
				"集成测试": map[string]interface{}{
					"占比":   "20%", 
					"目标":   "测试模块间协作和数据流",
					"工具":   "Go testing + Docker测试环境",
					"覆盖率": "80%以上",
				},
				"端到端测试": map[string]interface{}{
					"占比": "10%",
					"目标": "测试完整工作流程",
					"工具": "HTTP测试 + 模拟环境",
					"覆盖率": "核心场景100%",
				},
			},
			"测试分类": []map[string]interface{}{
				{
					"类型": "功能测试",
					"描述": "验证业务功能正确性",
					"重点": []string{
						"项目创建和管理",
						"Agent协作流程",
						"代码生成功能",
						"任务调度逻辑",
						"数据持久化",
					},
				},
				{
					"类型": "性能测试",
					"描述": "验证系统性能指标",
					"重点": []string{
						"并发Agent执行",
						"大数据量处理",
						"内存使用优化",
						"数据库查询性能",
						"API响应时间",
					},
				},
				{
					"类型": "可靠性测试",
					"描述": "验证系统稳定性和容错性",
					"重点": []string{
						"异常情况处理",
						"网络中断恢复",
						"数据一致性",
						"并发安全性",
						"资源泄漏检测",
					},
				},
			},
		},
		"测试用例设计": map[string]interface{}{
			"单元测试用例": []map[string]interface{}{
				{
					"模块": "域层实体测试",
					"文件": "internal/domain/project_test.go",
					"测试用例": []string{
						"TestNewProject_ValidInput_Success",
						"TestProject_AdvanceToPhase_ValidPhase_Success",
						"TestProject_AdvanceToPhase_InvalidPhase_Error",
						"TestProject_UpdateProgress_ValidValue_Success",
						"TestProject_ValidateRequirement_EmptyRequirement_Error",
					},
					"代码示例": `package domain_test

import (
	"testing"
	"auto-coding-system/internal/domain"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewProject_ValidInput_Success(t *testing.T) {
	// Arrange
	name := "测试项目"
	requirement := "开发一个任务管理系统"
	
	// Act
	project := domain.NewProject(name, requirement)
	
	// Assert
	assert.NotEmpty(t, project.ID)
	assert.Equal(t, name, project.Name)
	assert.Equal(t, requirement, project.Requirement)
	assert.Equal(t, domain.StatusInitialized, project.Status)
	assert.Equal(t, "requirement_analysis", project.CurrentPhase)
	assert.Equal(t, 0, project.Progress)
	assert.NotNil(t, project.Phases)
	assert.NotZero(t, project.CreatedAt)
}

func TestProject_AdvanceToPhase_ValidPhase_Success(t *testing.T) {
	// Arrange
	project := domain.NewProject("测试项目", "测试需求")
	originalUpdatedAt := project.UpdatedAt
	
	// Act
	err := project.AdvanceToPhase("architecture_design")
	
	// Assert
	require.NoError(t, err)
	assert.Equal(t, "architecture_design", project.CurrentPhase)
	assert.True(t, project.UpdatedAt.After(originalUpdatedAt))
}`,
				},
				{
					"模块": "应用服务测试",
					"文件": "internal/application/services/project_service_test.go", 
					"测试用例": []string{
						"TestProjectService_Create_ValidProject_Success",
						"TestProjectService_Create_DuplicateName_Error",
						"TestProjectService_GetByID_ExistingProject_Success",
						"TestProjectService_GetByID_NonExistentProject_Error",
						"TestProjectService_Update_ValidProject_Success",
					},
					"代码示例": `package services_test

import (
	"context"
	"testing"
	"auto-coding-system/internal/application/services"
	"auto-coding-system/internal/domain"
	"auto-coding-system/internal/domain/mocks"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestProjectService_Create_ValidProject_Success(t *testing.T) {
	// Arrange
	mockRepo := new(mocks.ProjectRepository)
	service := services.NewProjectService(mockRepo)
	
	project := domain.NewProject("测试项目", "测试需求")
	mockRepo.On("Save", mock.Anything, project).Return(nil)
	
	// Act
	err := service.Create(context.Background(), project)
	
	// Assert
	require.NoError(t, err)
	mockRepo.AssertExpectations(t)
}`,
				},
			},
			"集成测试用例": []map[string]interface{}{
				{
					"模块": "Agent协作测试",
					"文件": "test/integration/agent_workflow_test.go",
					"测试用例": []string{
						"TestAgentWorkflow_CompleteFlow_Success",
						"TestAgentWorkflow_SingleAgentFailure_GracefulHandling",
						"TestAgentWorkflow_ConcurrentExecution_ThreadSafety",
						"TestAgentWorkflow_DataPersistence_Consistency",
					},
					"代码示例": `package integration_test

import (
	"context"
	"testing"
	"time"
	"auto-coding-system/internal/coordinator"
	"auto-coding-system/internal/mcp"
	"auto-coding-system/internal/agents"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAgentWorkflow_CompleteFlow_Success(t *testing.T) {
	// Arrange
	mcpManager := mcp.NewManager("./test-data")
	systemCoordinator := coordinator.NewSystemCoordinator(mcpManager)
	
	// 注册所有Agent
	systemCoordinator.RegisterAgent(agents.NewPMAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewArchitectAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewDeveloperAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewTestAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewQAAgent(mcpManager))
	
	requirement := "开发一个简单的任务管理系统"
	
	// Act
	result, err := systemCoordinator.ExecuteFullWorkflow(requirement)
	
	// Assert
	require.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "COMPLETED", result.FinalStatus)
	assert.Equal(t, 5, len(result.Results))
	assert.True(t, result.TotalDuration > 0)
	
	// 验证每个Agent都成功执行
	for _, agentType := range []string{"pm", "architect", "developer", "test", "qa"} {
		agentResult, exists := result.Results[agentType]
		assert.True(t, exists, "Agent %s result should exist", agentType)
		assert.Equal(t, "completed", agentResult.Status)
	}
}`,
				},
			},
			"端到端测试用例": []map[string]interface{}{
				{
					"模块": "HTTP API测试",
					"文件": "test/e2e/api_test.go",
					"测试用例": []string{
						"TestAPI_CreateProject_CompleteWorkflow_Success",
						"TestAPI_GetProjectStatus_RealTimeUpdates_Accuracy",
						"TestAPI_ExecuteSingleAgent_IsolatedExecution_Success",
						"TestAPI_ErrorHandling_InvalidInput_ProperResponse",
					},
					"代码示例": `package e2e_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"auto-coding-system/internal/interfaces/http"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAPI_CreateProject_CompleteWorkflow_Success(t *testing.T) {
	// Arrange
	server := setupTestServer(t)
	defer server.Close()
	
	requestBody := map[string]string{
		"name": "API测试项目",
		"requirement": "开发一个API测试系统",
	}
	jsonBody, _ := json.Marshal(requestBody)
	
	// Act - 创建项目
	resp, err := http.Post(server.URL+"/api/projects", "application/json", bytes.NewBuffer(jsonBody))
	require.NoError(t, err)
	defer resp.Body.Close()
	
	// Assert - 创建响应
	assert.Equal(t, http.StatusOK, resp.StatusCode)
	
	var createResp map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&createResp)
	require.NoError(t, err)
	
	assert.Equal(t, float64(200), createResp["code"])
	data := createResp["data"].(map[string]interface{})
	projectID := data["project_id"].(string)
	
	// Act - 等待工作流程完成
	time.Sleep(5 * time.Second)
	
	// Act - 获取项目状态
	statusResp, err := http.Get(server.URL + "/api/projects/" + projectID)
	require.NoError(t, err)
	defer statusResp.Body.Close()
	
	// Assert - 状态响应
	assert.Equal(t, http.StatusOK, statusResp.StatusCode)
	
	var statusRespData map[string]interface{}
	err = json.NewDecoder(statusResp.Body).Decode(&statusRespData)
	require.NoError(t, err)
	
	project := statusRespData["data"].(map[string]interface{})
	assert.Equal(t, "COMPLETED", project["status"])
	assert.Equal(t, float64(100), project["progress"])
}`,
				},
			},
		},
		"测试执行结果": map[string]interface{}{
			"测试统计": map[string]interface{}{
				"总测试用例": 32,
				"通过用例": 29,
				"失败用例": 3,
				"跳过用例": 0,
				"代码覆盖率": "87.3%",
				"执行时间": "2分34秒",
			},
			"覆盖率详情": []map[string]interface{}{
				{
					"模块": "domain层",
					"覆盖率": "94.2%",
					"测试用例数": 12,
					"状态": "优秀",
				},
				{
					"模块": "application层",
					"覆盖率": "89.1%",
					"测试用例数": 10,
					"状态": "良好",
				},
				{
					"模块": "infrastructure层",
					"覆盖率": "76.8%",
					"测试用例数": 6,
					"状态": "需改进",
				},
				{
					"模块": "interfaces层",
					"覆盖率": "91.5%",
					"测试用例数": 4,
					"状态": "优秀",
				},
			},
			"发现的问题": []map[string]interface{}{
				{
					"严重级别": "中",
					"问题描述": "并发Agent执行时存在竞态条件",
					"影响范围": "系统稳定性",
					"建议解决方案": "增加同步机制和互斥锁",
					"优先级": "高",
				},
				{
					"严重级别": "低",
					"问题描述": "部分错误信息不够详细",
					"影响范围": "调试体验",
					"建议解决方案": "完善错误信息和堆栈跟踪",
					"优先级": "中",
				},
				{
					"严重级别": "低",
					"问题描述": "内存使用在大项目时偏高",
					"影响范围": "性能表现",
					"建议解决方案": "优化数据结构和缓存策略",
					"优先级": "中",
				},
			},
		},
		"性能测试结果": map[string]interface{}{
			"性能指标": []map[string]interface{}{
				{
					"指标": "Agent执行延迟",
					"目标值": "<500ms",
					"实际值": "342ms",
					"状态": "通过",
				},
				{
					"指标": "并发处理能力",
					"目标值": ">10个项目",
					"实际值": "15个项目",
					"状态": "通过",
				},
				{
					"指标": "内存使用",
					"目标值": "<256MB",
					"实际值": "198MB",
					"状态": "通过",
				},
				{
					"指标": "数据库连接池",
					"目标值": ">90%利用率",
					"实际值": "85%利用率",
					"状态": "接近目标",
				},
			},
			"压力测试": map[string]interface{}{
				"测试场景": "100个并发项目创建",
				"成功率": "96%",
				"平均响应时间": "1.2秒",
				"峰值内存": "312MB",
				"数据库连接数": "45个",
				"结论": "系统在高并发下表现良好，但需要优化数据库连接管理",
			},
		},
		"测试工具和环境": map[string]interface{}{
			"测试框架": []string{
				"Go标准testing包",
				"testify/assert - 断言库",
				"testify/mock - Mock对象",
				"testify/suite - 测试套件",
			},
			"测试环境": map[string]interface{}{
				"数据库": "MySQL 8.0 (Docker容器)",
				"Go版本": "1.21",
				"操作系统": "Linux/macOS/Windows",
				"CI/CD": "GitHub Actions",
			},
			"测试数据": map[string]interface{}{
				"测试数据库": "auto_coding_test",
				"测试数据": "预定义的种子数据",
				"清理策略": "每次测试后清理",
				"隔离级别": "测试间完全隔离",
			},
		},
		"测试建议": []string{
			"建议增加更多边界条件测试用例",
			"需要添加更多的性能基准测试",
			"建议实现自动化的回归测试",
			"应该增加更多的错误注入测试",
			"需要完善测试文档和使用指南",
			"建议实现测试报告的可视化展示",
			"应该建立持续性能监控",
			"需要增加安全测试用例",
		},
	}
}