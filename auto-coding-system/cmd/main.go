package main

import (
	"auto-coding-system/internal/agents"
	"auto-coding-system/internal/coordinator"
	"auto-coding-system/internal/mcp"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found")
	}

	fmt.Println("🚀 启动自动化编程系统（集成版本）")
	fmt.Println(strings.Repeat("=", 50))

	// 初始化MCP数据管理器
	mcpManager := mcp.NewManager("./mcp-data")

	// 初始化系统协调器
	systemCoordinator := coordinator.NewSystemCoordinator(mcpManager)

	// 注册所有Agent
	systemCoordinator.RegisterAgent(agents.NewPMAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewArchitectAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewDeveloperAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewTestAgent(mcpManager))
	systemCoordinator.RegisterAgent(agents.NewQAAgent(mcpManager))

	// 检查命令行参数
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "run":
		// 运行完整的项目开发流程
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供项目需求描述")
			fmt.Println("使用方式: go run main.go run \"项目需求描述\"")
			return
		}
		requirement := os.Args[2]
		runFullWorkflow(systemCoordinator, requirement)

	case "agent":
		// 运行单个Agent
		if len(os.Args) < 4 {
			fmt.Println("❌ 请指定Agent类型和项目ID")
			fmt.Println("使用方式: go run main.go agent <pm|architect|developer|test|qa> <project_id>")
			return
		}
		agentType := os.Args[2]
		projectID := os.Args[3]
		runSingleAgent(systemCoordinator, agentType, projectID)

	case "status":
		// 查看项目状态
		if len(os.Args) < 3 {
			fmt.Println("❌ 请提供项目ID")
			fmt.Println("使用方式: go run main.go status <project_id>")
			return
		}
		projectID := os.Args[2]
		showProjectStatus(systemCoordinator, projectID)

	case "list":
		// 列出所有项目
		listProjects(systemCoordinator)

	default:
		printUsage()
	}
}

func runFullWorkflow(coordinator *coordinator.SystemCoordinator, requirement string) {
	fmt.Printf("🎯 开始执行完整的项目开发流程\n")
	fmt.Printf("📋 需求: %s\n\n", requirement)

	// 执行完整工作流程
	result, err := coordinator.ExecuteFullWorkflow(requirement)
	if err != nil {
		fmt.Printf("❌ 工作流程执行失败: %v\n", err)
		return
	}

	fmt.Println("\n🎉 项目开发完成！")
	fmt.Printf("📊 项目ID: %s\n", result.ProjectID)
	fmt.Printf("🎯 最终状态: %s\n", result.FinalStatus)
	fmt.Printf("📈 完成度: %s\n", result.CompletionRate)
	fmt.Printf("⭐ 质量评分: %s\n", result.QualityScore)
}

func runSingleAgent(coordinator *coordinator.SystemCoordinator, agentType, projectID string) {
	fmt.Printf("🤖 运行单个Agent: %s (项目ID: %s)\n", agentType, projectID)

	result, err := coordinator.ExecuteSingleAgent(agentType, projectID)
	if err != nil {
		fmt.Printf("❌ Agent执行失败: %v\n", err)
		return
	}

	fmt.Printf("✅ Agent %s 执行完成\n", agentType)
	fmt.Printf("📊 执行结果: %s\n", result.Status)
	fmt.Printf("📋 输出: %s\n", result.Output)
}

func showProjectStatus(coordinator *coordinator.SystemCoordinator, projectID string) {
	fmt.Printf("📊 查看项目状态 (项目ID: %s)\n", projectID)

	status, err := coordinator.GetProjectStatus(projectID)
	if err != nil {
		fmt.Printf("❌ 获取项目状态失败: %v\n", err)
		return
	}

	fmt.Printf("📋 项目名称: %s\n", status.Name)
	fmt.Printf("🎯 当前阶段: %s\n", status.CurrentPhase)
	fmt.Printf("📈 整体进度: %d%%\n", status.Progress)
	fmt.Printf("📊 状态: %s\n", status.Status)

	fmt.Println("\n📋 各阶段详情:")
	for phase, info := range status.Phases {
		fmt.Printf("  %s: %s (%d%%)\n", phase, info.Status, info.Progress)
	}
}

func listProjects(coordinator *coordinator.SystemCoordinator) {
	fmt.Println("📋 所有项目列表:")

	projects, err := coordinator.ListProjects()
	if err != nil {
		fmt.Printf("❌ 获取项目列表失败: %v\n", err)
		return
	}

	if len(projects) == 0 {
		fmt.Println("暂无项目")
		return
	}

	for _, project := range projects {
		fmt.Printf("  ID: %s | 名称: %s | 状态: %s | 进度: %d%%\n",
			project.ID, project.Name, project.Status, project.Progress)
	}
}

func printUsage() {
	fmt.Println("自动化编程系统 - 使用说明")
	fmt.Println(strings.Repeat("=", 30))
	fmt.Println("可用命令:")
	fmt.Println("  run \"需求描述\"                    - 运行完整的项目开发流程")
	fmt.Println("  agent <type> <project_id>         - 运行单个Agent")
	fmt.Println("    类型: pm, architect, developer, test, qa")
	fmt.Println("  status <project_id>               - 查看项目状态")
	fmt.Println("  list                              - 列出所有项目")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  go run main.go run \"开发一个任务管理系统\"")
	fmt.Println("  go run main.go agent pm project_001")
	fmt.Println("  go run main.go status project_001")
	fmt.Println("  go run main.go list")
}