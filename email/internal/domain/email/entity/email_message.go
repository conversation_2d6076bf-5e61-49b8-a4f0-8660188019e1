package entity

import (
	"time"
)

// EmailMessage 邮件消息实体
type EmailMessage struct {
	ID           int64                  `json:"id"`
	EmailID      int64                  `json:"email_id"`  // 分布式ID，业务主键
	TenantID     int64                  `json:"tenant_id"` // 租户ID - 修改为int64类型
	TemplateID   string                 `json:"template_id,omitempty"`
	FromAddress  string                 `json:"from_address"`
	ToAddress    string                 `json:"to_address"`
	CcAddresses  []string               `json:"cc_addresses,omitempty"`
	BccAddresses []string               `json:"bcc_addresses,omitempty"`
	Subject      string                 `json:"subject"`
	HTMLContent  string                 `json:"html_content"`
	TextContent  string                 `json:"text_content,omitempty"`
	Variables    map[string]interface{} `json:"variables,omitempty"`
	Status       string                 `json:"status"`
	ErrorMsg     string                 `json:"error_msg,omitempty"`
	RetryCount   int64                  `json:"retry_count"`
	MaxRetries   int64                  `json:"max_retries"`
	SentAt       *time.Time             `json:"sent_at,omitempty"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	DeletedAt    *time.Time             `json:"deleted_at,omitempty"`
}

// NewEmailMessage 创建新的邮件消息
// 注意：此函数已被废弃，请使用 EntityFactory.NewEmailMessage
// func NewEmailMessage(toAddress string, subject string) (*EmailMessage, error) {
// 	now := time.Now()
// 	return &EmailMessage{
// 		ToAddress:  toAddress,
// 		Subject:    subject,
// 		Status:     "pending",
// 		MaxRetries: 3,
// 		RetryCount: 0,
// 		CreatedAt:  now,
// 		UpdatedAt:  now,
// 	}, nil
// }

// NewTemplateEmailMessage 创建基于模板的邮件消息
// 注意：此函数已被废弃，请使用 EntityFactory.NewTemplateEmailMessage
// func NewTemplateEmailMessage(tenantID string, templateID string, toAddress string, variables map[string]interface{}) (*EmailMessage, error) {
// 	message, err := NewEmailMessage(toAddress, "")
// 	if err != nil {
// 		return nil, err
// 	}

// 	message.TenantID = tenantID
// 	message.TemplateID = templateID
// 	message.Variables = variables

// 	return message, nil
// }

// SetStatus 设置邮件状态
func (m *EmailMessage) SetStatus(status string) {
	m.Status = status
	m.UpdatedAt = time.Now()
}

// SetError 设置错误信息
func (m *EmailMessage) SetError(err error) {
	m.Status = "failed"
	m.ErrorMsg = err.Error()
	m.UpdatedAt = time.Now()
}

// IncrementRetry 增加重试次数
func (m *EmailMessage) IncrementRetry() {
	now := time.Now()
	m.RetryCount++
	m.UpdatedAt = now
}

// CanRetry 检查是否可以重试
func (m *EmailMessage) CanRetry() bool {
	return m.Status == "failed" && m.RetryCount < m.MaxRetries
}

// MarkAsSent 标记为已发送
func (m *EmailMessage) MarkAsSent() {
	now := time.Now()
	m.Status = "sent"
	m.SentAt = &now
	m.UpdatedAt = now
}

// Cancel 取消发送
func (m *EmailMessage) Cancel() {
	m.Status = "canceled"
	m.UpdatedAt = time.Now()
}
