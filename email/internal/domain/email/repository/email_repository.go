package repository

import (
	"context"

	"platforms-email/internal/domain/email/entity"
	"platforms-email/internal/domain/email/value_object"
)

// Repository 邮件仓储接口
type Repository interface {
	// Save 保存邮件消息
	Save(ctx context.Context, message *entity.EmailMessage) error

	// FindByEmailID 根据邮件ID查找邮件
	FindByEmailID(ctx context.Context, emailID int64) (*entity.EmailMessage, error)

	// GetStatistics 获取邮件统计信息
	GetStatistics(ctx context.Context, params *value_object.StatisticsQueryParams) (*value_object.EmailStatistics, error)

	// UpdateStatus 更新邮件状态
	UpdateStatus(ctx context.Context, emailID int64, status string) error

	// List 获取邮件列表
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailMessage, int64, error)
}
