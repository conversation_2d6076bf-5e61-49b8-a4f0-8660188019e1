package repository

import (
	"context"
	"platforms-email/internal/domain/email/entity"
)

// QueryOptions 查询参数对象，声明式承载分页/游标/排序等
// 可复用到所有列表查询
type QueryOptions struct {
	TenantID string
	Cursor   int64
	Offset   int
	Limit    int
	OrderBy  string
}

// EmailAccountRepository 邮件账号仓储接口
type EmailAccountRepository interface {
	// Save 保存邮件账号
	Save(ctx context.Context, account *entity.EmailAccount) error

	// FindByID 根据ID查找邮件账号（数据库自增ID，仅用于编辑配置）
	FindByID(ctx context.Context, id int64) (*entity.EmailAccount, error)

	// FindByAccountID 根据账号ID查找邮件账号（分布式ID，用于业务逻辑）
	FindByAccountID(ctx context.Context, accountID int64) (*entity.EmailAccount, error)

	// FindByName 根据名称查找邮件账号
	FindByName(ctx context.Context, tenantID int64, name string) (*entity.EmailAccount, error)

	// FindByTenantID 根据租户ID查找邮件账号列表
	FindByTenantID(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailAccount, int64, error)

	// FindActiveByTenantID 根据租户ID查找激活的邮件账号
	FindActiveByTenantID(ctx context.Context, tenantID int64) ([]*entity.EmailAccount, error)

	// Update 更新邮件账号
	Update(ctx context.Context, account *entity.EmailAccount) error

	// Delete 删除邮件账号
	Delete(ctx context.Context, id int64) error

	// UpdateSentCount 更新发送计数
	UpdateSentCount(ctx context.Context, id int64, sentToday, sentThisMonth int) error

	// UpdateTestResult 更新测试结果
	UpdateTestResult(ctx context.Context, id int64, testStatus entity.TestStatus, testMessage string) error

	// ResetDailyCount 重置日发送计数
	ResetDailyCount(ctx context.Context, tenantID int64) error

	// ResetMonthlyCount 重置月发送计数
	ResetMonthlyCount(ctx context.Context, tenantID int64) error

	// List 获取邮件账号列表
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailAccount, int64, error)
}
