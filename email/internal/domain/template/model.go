package template

import (
	"encoding/json"
	"platforms-pkg/common"
	"time"
)

// EmailTemplate 邮件模板聚合根（简化设计）
type EmailTemplate struct {
	// 基本信息
	ID           int64  `json:"id" db:"id"`
	TenantID     int64  `json:"tenant_id" db:"tenant_id"` // 0=系统模板, >0=租户模板
	ScenarioCode string `json:"scenario_code" db:"scenario_code"`
	TemplateName string `json:"template_name" db:"template_name"`

	// 邮件内容
	Subject string `json:"subject" db:"subject"`
	Content string `json:"content" db:"content"`

	// 模板变量（JSON存储）
	Variables []TemplateVariable `json:"variables" db:"variables"`

	// 基础字段
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Name         string `json:"name"`
	Type         string `json:"type"`
	Description  string `json:"description"`
	Required     bool   `json:"required"`
	DefaultValue string `json:"default_value,omitempty"`
}

// 领域方法

// IsSystemTemplate 是否为系统模板
func (t *EmailTemplate) IsSystemTemplate() bool {
	return t.TenantID == common.SystemTenantID
}

// IsTenantTemplate 是否为租户模板
func (t *EmailTemplate) IsTenantTemplate() bool {
	return t.TenantID != common.SystemTenantID
}

// ValidateVariables 验证模板变量
func (t *EmailTemplate) ValidateVariables(variables map[string]interface{}) error {
	for _, templateVar := range t.Variables {
		if templateVar.Required {
			if _, exists := variables[templateVar.Name]; !exists {
				return NewMissingVariableError(templateVar.Name)
			}
		}
	}
	return nil
}

// GetVariableNames 获取所有变量名
func (t *EmailTemplate) GetVariableNames() []string {
	var names []string
	for _, variable := range t.Variables {
		names = append(names, variable.Name)
	}
	return names
}

// GetRequiredVariables 获取必需的变量
func (t *EmailTemplate) GetRequiredVariables() []TemplateVariable {
	var required []TemplateVariable
	for _, variable := range t.Variables {
		if variable.Required {
			required = append(required, variable)
		}
	}
	return required
}

// Clone 克隆模板（用于创建租户模板）
func (t *EmailTemplate) Clone(newTenantID int64, newTemplateName string) *EmailTemplate {
	clone := &EmailTemplate{
		TenantID:     newTenantID,
		ScenarioCode: t.ScenarioCode,
		TemplateName: newTemplateName,
		Subject:      t.Subject,
		Content:      t.Content,
		Variables:    make([]TemplateVariable, len(t.Variables)),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 深拷贝变量
	copy(clone.Variables, t.Variables)

	return clone
}

// UpdateContent 更新模板内容
func (t *EmailTemplate) UpdateContent(subject, content string) {
	if subject != "" {
		t.Subject = subject
	}
	if content != "" {
		t.Content = content
	}
	t.UpdatedAt = time.Now()
}

// AddVariable 添加模板变量
func (t *EmailTemplate) AddVariable(variable TemplateVariable) {
	// 检查是否已存在
	for i, existing := range t.Variables {
		if existing.Name == variable.Name {
			t.Variables[i] = variable // 更新现有变量
			t.UpdatedAt = time.Now()
			return
		}
	}

	// 添加新变量
	t.Variables = append(t.Variables, variable)
	t.UpdatedAt = time.Now()
}

// RemoveVariable 移除模板变量
func (t *EmailTemplate) RemoveVariable(variableName string) {
	for i, variable := range t.Variables {
		if variable.Name == variableName {
			t.Variables = append(t.Variables[:i], t.Variables[i+1:]...)
			t.UpdatedAt = time.Now()
			break
		}
	}
}

// JSON序列化方法

// MarshalVariablesJSON 将变量序列化为JSON
func (t *EmailTemplate) MarshalVariablesJSON() ([]byte, error) {
	return json.Marshal(t.Variables)
}

// UnmarshalVariablesJSON 从JSON反序列化变量
func (t *EmailTemplate) UnmarshalVariablesJSON(data []byte) error {
	return json.Unmarshal(data, &t.Variables)
}

// 错误定义

// TemplateError 模板相关错误
type TemplateError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *TemplateError) Error() string {
	return e.Message
}

// NewMissingVariableError 创建缺少变量错误
func NewMissingVariableError(variableName string) *TemplateError {
	return &TemplateError{
		Code:    "MISSING_VARIABLE",
		Message: "缺少必需的模板变量: " + variableName,
	}
}
