package entity

// TemplateFilter 模板筛选条件
type TemplateFilter struct {
	TenantIDs    []int64 `json:"tenant_ids,omitempty"`    // 租户ID列表，支持多个租户查询
	TemplateCode string  `json:"template_code,omitempty"` // 模板代码，支持精确查询
	Type         *uint8  `json:"type,omitempty"`          // 模板类型
	Status       *uint8  `json:"status,omitempty"`        // 模板状态
	Search       string  `json:"search,omitempty"`        // 搜索关键字
	SortBy       string  `json:"sort_by,omitempty"`       // 排序字段
	SortOrder    string  `json:"sort_order,omitempty"`    // 排序方向
	Page         int     `json:"page"`                    // 页码
	PageSize     int     `json:"page_size"`               // 每页数量
}
