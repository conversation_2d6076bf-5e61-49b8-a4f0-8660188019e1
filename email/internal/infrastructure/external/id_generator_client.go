package external

import (
	"context"
	"fmt"

	"platforms-pkg/grpcregistry"
	"platforms-pkg/logiface"
	"platforms-user/api/idgeneratorpb"

	"google.golang.org/grpc"
)

// IDGeneratorClient ID生成器gRPC客户端
// 提供生成单个和批量ID的方法
type IDGeneratorClient struct {
	serviceName string
	logger      logiface.Logger
}

// NewIDGeneratorClient 创建ID生成器gRPC客户端
func NewIDGeneratorClient(logger logiface.Logger) *IDGeneratorClient {
	return &IDGeneratorClient{
		serviceName: "platforms-user", // idgenerator服务与user服务同一进程
		logger:      logger,
	}
}

// getClient 获取ID生成器gRPC客户端
func (c *IDGeneratorClient) getClient(ctx context.Context) (idgeneratorpb.IdGeneratorServiceClient, error) {
	client, err := grpcregistry.GetClientGlobal(ctx, c.serviceName, func(conn *grpc.ClientConn) interface{} {
		return idgeneratorpb.NewIdGeneratorServiceClient(conn)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get idgenerator service client: %w", err)
	}
	idClient, ok := client.(idgeneratorpb.IdGeneratorServiceClient)
	if !ok {
		return nil, fmt.Errorf("failed to cast to IdGeneratorServiceClient")
	}
	return idClient, nil
}

// GenerateID 生成单个ID
func (c *IDGeneratorClient) GenerateID(ctx context.Context, businessType string, tenantId int64) (int64, error) {
	client, err := c.getClient(ctx)
	if err != nil {
		return 0, err
	}
	resp, err := client.GenerateId(ctx, &idgeneratorpb.GenerateIdRequest{
		BusinessType: businessType,
		TenantId:     tenantId,
	})
	if err != nil {
		c.logger.Error(ctx, "Failed to generate ID", logiface.Error(err), logiface.String("business_type", businessType))
		return 0, fmt.Errorf("grpc call failed: %w", err)
	}
	c.logger.Debug(ctx, "Generated ID", logiface.String("business_type", businessType), logiface.Int64("id", resp.GetId()))
	return resp.GetId(), nil
}

// GenerateBatchIDs 批量生成ID
func (c *IDGeneratorClient) GenerateBatchIDs(ctx context.Context, businessType string, tenantId int64, count int) ([]int64, error) {
	client, err := c.getClient(ctx)
	if err != nil {
		return nil, err
	}
	resp, err := client.GenerateBatchIds(ctx, &idgeneratorpb.GenerateBatchIdsRequest{
		BusinessType: businessType,
		TenantId:     tenantId,
		Count:        int32(count),
	})
	if err != nil {
		c.logger.Error(ctx, "Failed to generate batch IDs", logiface.Error(err), logiface.String("business_type", businessType), logiface.Int("count", count))
		return nil, fmt.Errorf("grpc call failed: %w", err)
	}
	c.logger.Debug(ctx, "Generated batch IDs", logiface.String("business_type", businessType), logiface.Int("count", count))
	return resp.GetIds(), nil
}
