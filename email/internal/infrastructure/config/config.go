package config

import (
	"encoding/json"
	"fmt"
	"os"
	common_database "platforms-pkg/db"
	"strings"
	"sync"
	"time"

	"github.com/pelletier/go-toml/v2"

	"platforms-pkg/grpcregistry"
	"platforms-pkg/logiface"
	"platforms-pkg/nacosconfig"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server            ServerConfig                   `yaml:"server"`
	Database          common_database.DatabaseConfig `yaml:"database"`
	Log               map[string]logiface.LogConfig  `yaml:"log"`
	JWT               JWTConfig                      `yaml:"jwt"`
	Redis             RedisConfig                    `yaml:"redis"`
	GRPC              GRPCConfig                     `yaml:"grpc"`
	Otel              OtelConfig                     `yaml:"otel"`
	AliyunDM          AliyunDMConfig                 `yaml:"aliyun_dm"`
	Nacos             NacosConfig                    `yaml:"nacos"`
	UseNacos          bool                           `yaml:"use_nacos"`
	ServiceName       string
	GRPCSubscriptions []*grpcregistry.ClientManagerConfig `yaml:"grpcSubscriptions"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int    `yaml:"port"`
	ReadTimeout  string `yaml:"read_timeout"`
	WriteTimeout string `yaml:"write_timeout"`
	IdleTimeout  string `yaml:"idle_timeout"`
	Env          string `yaml:"env"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret          string `yaml:"secret"`
	AccessTokenTTL  string `yaml:"access_token_ttl"`
	RefreshTokenTTL string `yaml:"refresh_token_ttl"`
	Issuer          string `yaml:"issuer"`
	Audience        string `yaml:"audience"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Password string `yaml:"password"`
	Database int    `yaml:"database"`
	PoolSize int    `yaml:"pool_size"`
}

// GRPCConfig gRPC配置
type GRPCConfig struct {
	Port        int               `yaml:"port"`
	ServiceName string            `yaml:"service_name"`
	Group       string            `yaml:"group"`
	Namespace   string            `yaml:"namespace"`
	Weight      float64           `yaml:"weight"`
	Metadata    map[string]string `yaml:"metadata"`
	LocalIP     string            `yaml:"local_ip,omitempty"` // 本地IP地址，优先使用此配置
}

// OtelConfig OpenTelemetry配置
type OtelConfig struct {
	Endpoint string `yaml:"endpoint"`
}

// AliyunDMConfig 阿里云邮件推送配置
type AliyunDMConfig struct {
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	RegionID        string `yaml:"region_id"`
	Endpoint        string `yaml:"endpoint"`
	AccountName     string `yaml:"account_name"`
	ReplyToAddress  bool   `yaml:"reply_to_address"`
	AddressType     int    `yaml:"address_type"`
}

// NacosConfig Nacos配置
type NacosConfig struct {
	Host      string `yaml:"host"`
	Port      int64  `yaml:"port"`
	Namespace string `yaml:"namespace"`
	Group     string `yaml:"group"`
	Username  string `yaml:"username"`
	Password  string `yaml:"password"`
}

// 全局配置实例
var globalConfig *Config
var configMutex sync.RWMutex

// GetConfig 获取全局配置
func GetConfig() *Config {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return globalConfig
}

// SetConfig 设置全局配置
func SetConfig(config *Config) {
	configMutex.Lock()
	defer configMutex.Unlock()
	globalConfig = config
}

// LoadConfig 一次性从 Nacos 加载完整配置
func LoadConfig() (*Config, error) {
	// 首先检查是否使用 Nacos（从环境变量）
	useNacos := getEnvOrDefault("USE_NACOS", "true") == "true"

	if !useNacos {
		// 从本地文件加载配置（如果存在）
		if config, err := loadConfigFromFile(""); err == nil {
			setDefaultValues(config)
			SetConfig(config)
			fmt.Println("Successfully loaded config from local file")
			return config, nil
		}

		// 否则使用默认配置
		config := &Config{}
		setDefaultValues(config)
		SetConfig(config)
		fmt.Println("Using default configuration")
		return config, nil
	}

	// 使用 Nacos 加载配置
	config, err := loadConfigFromNacos()
	if err != nil {
		fmt.Printf("Failed to load from Nacos, falling back to default: %v\n", err)
		config = &Config{}
		setDefaultValues(config)
		SetConfig(config)
		return config, nil
	}

	// 成功从 Nacos 加载，设置为全局配置
	SetConfig(config)
	fmt.Printf("Successfully loaded config from Nacos, server_port=%d, mysql_host=%s\n",
		config.Server.Port, config.Database.MySQL.Host)

	return config, nil
}

// loadConfigFromNacos 从 Nacos 加载配置（内部函数）
func loadConfigFromNacos() (*Config, error) {
	// 使用公共的 NewNacosConfigFromEnv 函数创建 Nacos 配置
	dataId := getEnvOrDefault("NACOS_DATA_ID", "platforms-email")
	nacosCfg := nacosconfig.NewNacosConfigFromEnv(dataId)

	// 创建 Nacos 客户端
	nacosClient, err := nacosconfig.NewNacosClient(nacosCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos client: %w", err)
	}
	defer nacosClient.Close()

	// 获取配置内容（使用公共函数的重试机制）
	content, err := nacosClient.GetConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get config from nacos: %w", err)
	}

	// 解析配置
	config := &Config{}
	if err := toml.Unmarshal([]byte(content), config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 设置 Nacos 连接信息（从公共配置转换）
	config.Nacos = NacosConfig{
		Host: strings.Split(nacosCfg.Address, ":")[0],
		Port: func() int64 {
			if parts := strings.Split(nacosCfg.Address, ":"); len(parts) > 1 {
				if port := parseUint64(parts[1]); port > 0 {
					return port
				}
			}
			return 8848
		}(),
		Namespace: nacosCfg.Namespace,
		Group:     nacosCfg.Group,
		Username:  nacosCfg.User,
		Password:  nacosCfg.Password,
	}
	config.UseNacos = true

	// 设置默认值
	setDefaultValues(config)

	// 验证配置有效性
	if err := validateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	return config, nil
}

// loadConfigFromFile 从文件加载配置（仅用于开发调试）
func loadConfigFromFile(configPath string) (*Config, error) {
	if configPath == "" {
		configPath = "configs/app.nacos.toml"
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}
	var config Config
	content := strings.TrimSpace(string(data))
	if strings.HasPrefix(content, "#") || strings.Contains(content, "[") {
		if err := toml.Unmarshal(data, &config); err != nil {
			return nil, err
		}
		fmt.Println("Successfully parsed TOML config from local file")
	} else {
		if err := json.Unmarshal(data, &config); err != nil {
			return nil, err
		}
		fmt.Println("Successfully parsed JSON config from local file")
	}
	setDefaultValues(&config)
	SetConfig(&config)
	return &config, nil
}

// setDefaultValues 设置默认值
func setDefaultValues(config *Config) {
	// 服务器默认值
	if config.Server.Port == 0 {
		config.Server.Port = 8082
	}
	if config.Server.ReadTimeout == "" {
		config.Server.ReadTimeout = "60s"
	}
	if config.Server.WriteTimeout == "" {
		config.Server.WriteTimeout = "60s"
	}
	if config.Server.IdleTimeout == "" {
		config.Server.IdleTimeout = "60s"
	}

	// 数据库默认值
	if config.Database.MySQL.Host == "" {
		config.Database.MySQL.Host = "**************"
	}
	if config.Database.MySQL.Port == 0 {
		config.Database.MySQL.Port = 3308
	}
	if config.Database.MySQL.Database == "" {
		config.Database.MySQL.Database = "platforms-email"
	}
	if config.Database.MySQL.Username == "" {
		config.Database.MySQL.Username = "root"
	}
	if config.Database.MySQL.Password == "" {
		config.Database.MySQL.Password = "Pu0cF6KVs]7AockCKVC"
	}
	if config.Database.MySQL.Charset == "" {
		config.Database.MySQL.Charset = "utf8mb4"
	}
	if !config.Database.MySQL.ParseTime {
		config.Database.MySQL.ParseTime = true
	}
	if config.Database.MySQL.Loc == "" {
		config.Database.MySQL.Loc = "Local"
	}
	if config.Database.MySQL.MaxOpenConns == 0 {
		config.Database.MySQL.MaxOpenConns = 50
	}
	if config.Database.MySQL.MaxIdleConns == 0 {
		config.Database.MySQL.MaxIdleConns = 10
	}
	if config.Database.MySQL.ConnMaxLifetime == "" {
		config.Database.MySQL.ConnMaxLifetime = "300s"
	}
	if config.Database.MySQL.Params == nil {
		config.Database.MySQL.Params = map[string]string{
			//"useSSL":         "false",
			//"serverTimezone": "UTC",
			//"allowPublicKeyRetrieval": "true",
			"parseTime": "true",
			"loc":       "Local",
		}
	}

	// 日志默认值 - App
	if config.Log == nil {
		config.Log = make(map[string]logiface.LogConfig)
	}

	appLog := config.Log["app"]
	if appLog.Level == "" {
		appLog.Level = "info"
	}
	if appLog.Format == "" {
		appLog.Format = "console"
	}
	if appLog.Output == "" {
		appLog.Output = "stdout"
	}
	if appLog.File.MaxSize == 0 {
		appLog.File.MaxSize = 512
	}
	if appLog.File.MaxBackups == 0 {
		appLog.File.MaxBackups = 7
	}
	if appLog.File.MaxAge == 0 {
		appLog.File.MaxAge = 30
	}
	config.Log["app"] = appLog

	// 日志默认值 - Access
	accessLog := config.Log["access"]
	if accessLog.Level == "" {
		accessLog.Level = "info"
	}
	if accessLog.Format == "" {
		accessLog.Format = "console"
	}
	if accessLog.Output == "" {
		accessLog.Output = "stdout"
	}
	if accessLog.File.MaxSize == 0 {
		accessLog.File.MaxSize = 1024
	}
	if accessLog.File.MaxBackups == 0 {
		accessLog.File.MaxBackups = 3
	}
	if accessLog.File.MaxAge == 0 {
		accessLog.File.MaxAge = 7
	}
	config.Log["access"] = accessLog

	// 日志默认值 - Error
	errorLog := config.Log["error"]
	if errorLog.Level == "" {
		errorLog.Level = "error"
	}
	if errorLog.Format == "" {
		errorLog.Format = "console"
	}
	if errorLog.Output == "" {
		errorLog.Output = "stdout"
	}
	if errorLog.File.MaxSize == 0 {
		errorLog.File.MaxSize = 256
	}
	if errorLog.File.MaxBackups == 0 {
		errorLog.File.MaxBackups = 10
	}
	if errorLog.File.MaxAge == 0 {
		errorLog.File.MaxAge = 60
	}
	config.Log["error"] = errorLog

	// Redis默认值
	if config.Redis.Host == "" {
		config.Redis.Host = "127.0.0.1"
	}
	if config.Redis.Port == 0 {
		config.Redis.Port = 6379
	}
	if config.Redis.PoolSize == 0 {
		config.Redis.PoolSize = 10
	}

	// gRPC默认值
	if config.GRPC.Port == 0 {
		config.GRPC.Port = 50052
	}
	if config.GRPC.ServiceName == "" {
		config.GRPC.ServiceName = "platforms-email"
	}
	if config.GRPC.Group == "" {
		config.GRPC.Group = "DEFAULT_GROUP"
	}
	if config.GRPC.Weight == 0 {
		config.GRPC.Weight = 1.0
	}

	// OpenTelemetry默认值
	if config.Otel.Endpoint == "" {
		config.Otel.Endpoint = "http://localhost:14268/api/traces"
	}

}

// getEnvOrDefault 获取环境变量，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// parseUint64 解析字符串为int64
func parseUint64(s string) int64 {
	var result int64
	fmt.Sscanf(s, "%d", &result)
	return result
}

// GetServerReadTimeout 获取服务器读取超时时间
func (c *Config) GetServerReadTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.ReadTimeout)
	if err != nil {
		fmt.Printf("Failed to parse read_timeout, using default: %v\n", err)
		return 60 * time.Second
	}
	return duration
}

// GetServerWriteTimeout 获取服务器写入超时时间
func (c *Config) GetServerWriteTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.WriteTimeout)
	if err != nil {
		fmt.Printf("Failed to parse write_timeout, using default: %v\n", err)
		return 60 * time.Second
	}
	return duration
}

// GetServerIdleTimeout 获取服务器空闲超时时间
func (c *Config) GetServerIdleTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.IdleTimeout)
	if err != nil {
		fmt.Printf("Failed to parse idle_timeout, using default: %v\n", err)
		return 60 * time.Second
	}
	return duration
}

// GetMySQLConnMaxLifetime 获取MySQL连接最大生命周期
func (c *Config) GetMySQLConnMaxLifetime() time.Duration {
	duration, err := time.ParseDuration(c.Database.MySQL.ConnMaxLifetime)
	if err != nil {
		fmt.Printf("Failed to parse conn_max_lifetime, using default: %v\n", err)
		return 300 * time.Second
	}
	return duration
}

// GetJWTAccessTokenTTL 获取JWT访问令牌TTL
func (c *Config) GetJWTAccessTokenTTL() time.Duration {
	duration, err := time.ParseDuration(c.JWT.AccessTokenTTL)
	if err != nil {
		fmt.Printf("Failed to parse access_token_ttl, using default: %v\n", err)
		return 1 * time.Hour
	}
	return duration
}

// GetJWTRefreshTokenTTL 获取JWT刷新令牌TTL
func (c *Config) GetJWTRefreshTokenTTL() time.Duration {
	duration, err := time.ParseDuration(c.JWT.RefreshTokenTTL)
	if err != nil {
		fmt.Printf("Failed to parse refresh_token_ttl, using default: %v\n", err)
		return 24 * time.Hour
	}
	return duration
}

// ListenNacosConfigChange 启动 Nacos 配置变更监听，变更时自动刷新全局配置
func ListenNacosConfigChange(dataId string) error {
	// 使用公共的 NewNacosConfigFromEnv 函数创建 Nacos 配置
	nacosCfg := nacosconfig.NewNacosConfigFromEnv(dataId)

	// 创建 Nacos 客户端
	nacosClient, err := nacosconfig.NewNacosClient(nacosCfg)
	if err != nil {
		return fmt.Errorf("failed to create nacos client: %w", err)
	}

	// 启动监听，并定义配置变更回调函数
	err = nacosClient.ListenConfig(func(data string) {
		fmt.Printf("Nacos config changed for %s, reloading...\n", dataId)

		// 解析新配置
		var newConfig Config
		if err := yaml.Unmarshal([]byte(data), &newConfig); err != nil {
			fmt.Printf("Failed to unmarshal config on change: %v\n", err)
			return
		}

		// 设置默认值
		setDefaultValues(&newConfig)

		// 验证配置有效性
		if err := validateConfig(&newConfig); err != nil {
			fmt.Printf("Invalid config on change, skipping: %v\n", err)
			return
		}

		// 原子性更新全局配置
		configMutex.Lock()
		globalConfig = &newConfig
		configMutex.Unlock()

		// 触发配置变更回调
		triggerConfigChangeCallbacks(&newConfig)

		fmt.Printf("Successfully reloaded config from Nacos (on change)\n")
	})

	if err != nil {
		return fmt.Errorf("failed to listen config change: %w", err)
	}

	fmt.Printf("Started listening for config changes for dataId: %s\n", dataId)
	return nil
}

// 配置变更回调列表
var configChangeCallbacks []func(*Config)
var callbackMutex sync.RWMutex

// RegisterConfigChangeCallback 注册配置变更回调
func RegisterConfigChangeCallback(callback func(*Config)) {
	callbackMutex.Lock()
	defer callbackMutex.Unlock()
	configChangeCallbacks = append(configChangeCallbacks, callback)
}

// triggerConfigChangeCallbacks 触发所有配置变更回调
func triggerConfigChangeCallbacks(config *Config) {
	callbackMutex.RLock()
	callbacks := make([]func(*Config), len(configChangeCallbacks))
	copy(callbacks, configChangeCallbacks)
	callbackMutex.RUnlock()

	for _, callback := range callbacks {
		go func(cb func(*Config)) {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("Config change callback panic: %v\n", r)
				}
			}()
			cb(config)
		}(callback)
	}
}

// validateConfig 验证配置有效性
func validateConfig(config *Config) error {
	// 基本验证
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	if config.Database.MySQL.Host == "" {
		return fmt.Errorf("mysql host cannot be empty")
	}

	if config.Database.MySQL.Port <= 0 || config.Database.MySQL.Port > 65535 {
		return fmt.Errorf("invalid mysql port: %d", config.Database.MySQL.Port)
	}

	// 日志配置验证
	logLevels := map[string]bool{"debug": true, "info": true, "warn": true, "error": true}
	if !logLevels[config.Log["app"].Level] {
		return fmt.Errorf("invalid app log level: %s", config.Log["app"].Level)
	}
	if !logLevels[config.Log["access"].Level] {
		return fmt.Errorf("invalid access log level: %s", config.Log["access"].Level)
	}
	if !logLevels[config.Log["error"].Level] {
		return fmt.Errorf("invalid error log level: %s", config.Log["error"].Level)
	}

	return nil
}
