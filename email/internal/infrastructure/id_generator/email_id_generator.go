package id_generator

import (
	"context"
	"fmt"

	"platforms-email/internal/infrastructure/external"
	"platforms-pkg/id"
	"platforms-pkg/logiface"
)

// EmailIDGenerator 邮件模块ID生成器
type EmailIDGenerator struct {
	idGeneratorClient *external.IDGeneratorClient
	logger            logiface.Logger
}

// NewEmailIDGenerator 创建邮件ID生成器
func NewEmailIDGenerator(idGeneratorClient *external.IDGeneratorClient, logger logiface.Logger) *EmailIDGenerator {
	return &EmailIDGenerator{
		idGeneratorClient: idGeneratorClient,
		logger:            logger,
	}
}

// GenerateEmailAccountID 生成邮件账号ID
func (g *EmailIDGenerator) GenerateEmailAccountID(ctx context.Context, tenantID int64) (int64, error) {
	return g.idGeneratorClient.GenerateID(ctx, "email_account", tenantID)
}

// GenerateEmailMessageID 生成邮件消息ID
func (g *EmailIDGenerator) GenerateEmailMessageID(ctx context.Context, tenantID int64) (int64, error) {
	return g.idGeneratorClient.GenerateID(ctx, "email_message", tenantID)
}

// GenerateEmailTemplateID 生成邮件模板ID
func (g *EmailIDGenerator) GenerateEmailTemplateID(ctx context.Context, tenantID int64) (int64, error) {
	return g.idGeneratorClient.GenerateID(ctx, "email_template", tenantID)
}

// GenerateID 通用ID生成方法
func (g *EmailIDGenerator) GenerateID(ctx context.Context, businessType string, tenantID int64) (int64, error) {
	return g.idGeneratorClient.GenerateID(ctx, businessType, tenantID)
}

// GenerateSessionID 生成会话ID（使用雪花算法）
func (g *EmailIDGenerator) GenerateSessionID() string {
	return fmt.Sprintf("sess_%d", id.GenerateID())
}

// GenerateRequestID 生成请求ID（使用雪花算法）
func (g *EmailIDGenerator) GenerateRequestID() string {
	return fmt.Sprintf("req_%d", id.GenerateID())
}
