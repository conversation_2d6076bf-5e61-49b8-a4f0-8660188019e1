package model

import (
	"encoding/json"
	"platforms-email/internal/domain/template/entity"
	"time"

	"gorm.io/gorm"
)

// EmailTemplateModel 邮件模板数据模型
type EmailTemplateModel struct {
	ID                 int64   `gorm:"primaryKey;autoIncrement;column:id"`
	TenantID           int64   `gorm:"not null;index:idx_email_templates_tenant_id;column:tenant_id"`
	TemplateCode       string  `gorm:"not null;size:50;uniqueIndex:uk_tenant_template_code,priority:2;column:template_code;comment:模板代码"`
	AccountID          int64   `gorm:"not null;index:idx_account;column:account_id;comment:关联的发送账户ID"`
	Name               string  `gorm:"not null;size:100;column:name"`
	Type               uint8   `gorm:"not null;column:type;comment:模板类型"`
	Subject            string  `gorm:"not null;size:200;column:subject"`
	HTMLContent        string  `gorm:"type:text;column:html_content"`
	PlainTextContent   string  `gorm:"type:text;column:plain_text_content"`
	Variables          JSONMap `gorm:"type:json;column:variables;comment:模板变量定义"`
	RateLimitPerMinute uint    `gorm:"default:60;column:rate_limit_per_minute;comment:每分钟发送限制"`
	RateLimitPerHour   uint    `gorm:"default:1000;column:rate_limit_per_hour;comment:每小时发送限制"`
	RateLimitPerDay    uint    `gorm:"default:10000;column:rate_limit_per_day;comment:每日发送限制"`
	ThumbnailURL       string  `gorm:"size:500;column:thumbnail_url"`
	IsResponsive       bool    `gorm:"default:false;column:is_responsive"`
	Description        string  `gorm:"type:text;column:description;comment:模板描述"`
	Status             uint8   `gorm:"not null;default:1;column:status;comment:模板状态：1=草稿，2=已发布，3=已停用，4=已删除"`

	// 时间字段
	CreatedAt time.Time      `gorm:"column:created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_email_templates_deleted_at;column:deleted_at"`

	// 用户字段
	CreatedBy int64 `gorm:"column:created_by"`
	UpdatedBy int64 `gorm:"column:updated_by"`

	// 版本字段 - 固定为0，暂不实现版本管理
	Version int64 `gorm:"not null;default:0;column:version;comment:版本号，固定为0"`

	// 其他字段
	Statistics json.RawMessage `gorm:"type:json;column:statistics"`
	IsSystem   bool            `gorm:"default:false;column:is_system;comment:是否为系统内置模板"`
}

// TableName 指定表名
func (EmailTemplateModel) TableName() string {
	return "email_templates"
}

// ToEntity 转换为领域实体
func (m *EmailTemplateModel) ToEntity() *entity.EmailTemplate {
	template := &entity.EmailTemplate{
		ID:                 m.ID,
		TenantID:           m.TenantID,
		TemplateCode:       m.TemplateCode,
		AccountID:          m.AccountID,
		Name:               m.Name,
		Type:               entity.TemplateType(m.Type),
		Status:             entity.TemplateStatus(m.Status),
		Subject:            m.Subject,
		HTMLContent:        m.HTMLContent,
		PlainTextContent:   m.PlainTextContent,
		Variables:          make(map[string]entity.TemplateVariable),
		RateLimitPerMinute: m.RateLimitPerMinute,
		RateLimitPerHour:   m.RateLimitPerHour,
		RateLimitPerDay:    m.RateLimitPerDay,
		ThumbnailURL:       m.ThumbnailURL,
		IsResponsive:       m.IsResponsive,
		Description:        m.Description,
		Version:            m.Version,
		CreatedAt:          m.CreatedAt,
		UpdatedAt:          m.UpdatedAt,
		CreatedBy:          m.CreatedBy,
		UpdatedBy:          m.UpdatedBy,
		Statistics:         make(map[string]interface{}),
		IsSystem:           m.IsSystem,
	}

	// 转换变量
	if m.Variables != nil {
		for key, value := range m.Variables {
			if varMap, ok := value.(map[string]interface{}); ok {
				template.Variables[key] = entity.TemplateVariable{
					Label:       getString(varMap, "label"),
					Type:        getString(varMap, "type"),
					Required:    getBool(varMap, "required"),
					Description: getString(varMap, "description"),
				}
			}
		}
	}

	// 设置删除时间
	if m.DeletedAt.Valid {
		template.DeletedAt = &m.DeletedAt.Time
	}

	// 设置统计信息
	if len(m.Statistics) > 0 {
		var stats map[string]interface{}
		if err := json.Unmarshal(m.Statistics, &stats); err == nil {
			template.Statistics = stats
		}
	}

	return template
}

// FromEntity 从领域实体转换
func (m *EmailTemplateModel) FromEntity(e *entity.EmailTemplate) {
	m.ID = e.ID
	m.TenantID = e.TenantID
	m.TemplateCode = e.TemplateCode
	m.AccountID = e.AccountID
	m.Name = e.Name
	m.Type = uint8(e.Type)
	m.Subject = e.Subject
	m.HTMLContent = e.HTMLContent
	m.PlainTextContent = e.PlainTextContent
	m.RateLimitPerMinute = e.RateLimitPerMinute
	m.RateLimitPerHour = e.RateLimitPerHour
	m.RateLimitPerDay = e.RateLimitPerDay
	m.ThumbnailURL = e.ThumbnailURL
	m.IsResponsive = e.IsResponsive
	m.Description = e.Description
	m.Status = uint8(e.Status)
	m.Version = 0 // 固定为0，暂不实现版本管理
	m.CreatedAt = e.CreatedAt
	m.UpdatedAt = e.UpdatedAt
	m.CreatedBy = e.CreatedBy
	m.UpdatedBy = e.UpdatedBy
	m.IsSystem = e.IsSystem

	// 转换变量
	if e.Variables != nil {
		m.Variables = make(JSONMap)
		for key, variable := range e.Variables {
			m.Variables[key] = map[string]interface{}{
				"label":       variable.Label,
				"type":        variable.Type,
				"required":    variable.Required,
				"description": variable.Description,
			}
		}
	}

	// 转换统计信息
	if e.Statistics != nil {
		if stats, err := json.Marshal(e.Statistics); err == nil {
			m.Statistics = stats
		}
	}
}

// 辅助函数
func getString(m map[string]interface{}, key string) string {
	if v, ok := m[key]; ok {
		if s, ok := v.(string); ok {
			return s
		}
	}
	return ""
}

func getBool(m map[string]interface{}, key string) bool {
	if v, ok := m[key]; ok {
		if b, ok := v.(bool); ok {
			return b
		}
	}
	return false
}
