package model

import (
	"time"

	"gorm.io/gorm"
)

// EmailAccountModel 邮件账号数据模型
type EmailAccountModel struct {
	ID             uint   `gorm:"primaryKey;autoIncrement"`
	AccountID      int64  `gorm:"uniqueIndex;not null"` // 分布式ID，业务主键
	TenantID       int64  `gorm:"index;not null"`       // 租户ID - 修改为int64类型
	Name           string `gorm:"not null;size:255"`
	Type           int    `gorm:"not null"`
	Provider       string `gorm:"not null;size:100"`
	Host           string `gorm:"size:255"`
	Port           int    `gorm:"default:0"`
	Username       string `gorm:"size:255"`
	Password       string `gorm:"size:500"`
	FromAddress    string `gorm:"not null;size:255"`
	FromName       string `gorm:"size:255"`
	ReplyToAddress string `gorm:"size:255"`
	IsSSL          bool   `gorm:"default:true"`
	IsActive       bool   `gorm:"default:true"`
	DailyLimit     int    `gorm:"default:0"`
	MonthlyLimit   int    `gorm:"default:0"`
	SentToday      int    `gorm:"default:0"`
	SentThisMonth  int    `gorm:"default:0"`
	LastSentAt     *time.Time
	TestStatus     int     `gorm:"default:0"`
	TestMessage    string  `gorm:"type:text"`
	Config         JSONMap `gorm:"type:json"`
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      gorm.DeletedAt `gorm:"index"`
	CreatedBy      string         `gorm:"size:50"`
	UpdatedBy      string         `gorm:"size:50"`
	Version        int            `gorm:"default:1"`
	IsSystem       bool           `gorm:"default:false;comment:是否为系统内置账号"`
}

// TableName 指定表名
func (EmailAccountModel) TableName() string {
	return "email_accounts"
}
