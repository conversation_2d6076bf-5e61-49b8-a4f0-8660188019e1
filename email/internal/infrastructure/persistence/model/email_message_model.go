package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// EmailMessageModel 邮件消息数据模型
type EmailMessageModel struct {
	ID           int64          `gorm:"primaryKey;autoIncrement"`               // 自增主键
	EmailID      int64          `gorm:"uniqueIndex;not null"`                   // 分布式ID，业务主键
	TenantID     int64          `gorm:"index;not null"`                         // 租户ID - 修改为int64类型
	TemplateID   string         `gorm:"size:100"`                               // 模板ID
	FromAddress  string         `gorm:"not null;size:200"`                      // 发件人地址
	ToAddresses  JSONString     `gorm:"type:json;not null"`                     // 收件人列表
	CcAddresses  JSONString     `gorm:"type:json"`                              // 抄送列表
	BccAddresses JSONString     `gorm:"type:json"`                              // 密送列表
	Subject      string         `gorm:"not null;size:500"`                      // 邮件主题
	HTMLContent  string         `gorm:"type:text"`                              // HTML内容
	TextContent  string         `gorm:"type:text"`                              // 纯文本内容
	Variables    JSONMap        `gorm:"type:json"`                              // 模板变量
	Status       string         `gorm:"index;not null;size:20;default:pending"` // 发送状态
	Priority     int64          `gorm:"default:2"`                              // 优先级
	RetryCount   int64          `gorm:"default:0"`                              // 重试次数
	MaxRetries   int64          `gorm:"default:3"`                              // 最大重试次数
	ErrorMsg     string         `gorm:"type:text"`                              // 错误信息
	SentAt       *time.Time     `gorm:"type:datetime(3)"`                       // 发送时间
	CreatedAt    time.Time      `gorm:"type:datetime(3)"`                       // 创建时间
	UpdatedAt    time.Time      `gorm:"type:datetime(3)"`                       // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"index;type:datetime(3)"`                 // 删除时间
}

// TableName 指定表名
func (EmailMessageModel) TableName() string {
	return "email_messages"
}

// JSONString JSON字符串类型
type JSONString []string

// Value 实现driver.Valuer接口
func (j JSONString) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONString) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// JSONMap JSON映射类型
type JSONMap map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	return json.Unmarshal(bytes, j)
}
