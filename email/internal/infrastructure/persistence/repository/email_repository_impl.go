package repository

import (
	"context"
	"fmt"
	"time"

	"platforms-email/internal/domain/email/entity"
	"platforms-email/internal/domain/email/value_object"
	"platforms-email/internal/infrastructure/persistence/model"

	"gorm.io/gorm"
)

// EmailRepositoryImpl 邮件仓储实现
type EmailRepositoryImpl struct {
	db *gorm.DB
}

// NewEmailRepositoryImpl 创建邮件仓储实现
func NewEmailRepositoryImpl(db *gorm.DB) *EmailRepositoryImpl {
	return &EmailRepositoryImpl{
		db: db,
	}
}

// Save 保存邮件
func (r *EmailRepositoryImpl) Save(ctx context.Context, email *entity.EmailMessage) error {
	model := r.toModel(email)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create email message: %w", err)
	}

	return nil
}

// FindByID 根据ID查找邮件
func (r *EmailRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.EmailMessage, error) {
	var model model.EmailMessageModel

	if err := r.db.WithContext(ctx).Where("email_id = ? AND deleted_at IS NULL", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.ErrEmailNotFound
		}
		return nil, fmt.Errorf("failed to find email by id: %w", err)
	}

	return r.toEntity(&model), nil
}

// FindByEmailID 根据邮件ID查找邮件
func (r *EmailRepositoryImpl) FindByEmailID(ctx context.Context, emailID int64) (*entity.EmailMessage, error) {
	return r.FindByID(ctx, emailID)
}

// FindByTenantID 根据租户ID查找邮件列表
func (r *EmailRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailMessage, int64, error) {
	var models []model.EmailMessageModel
	var total int64

	// 构建基础查询 - 使用int64类型的tenantID
	db := r.db.WithContext(ctx).Where("tenant_id = ? AND deleted_at IS NULL", tenantID)

	// 获取总数
	if err := db.Model(&model.EmailMessageModel{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email messages: %w", err)
	}

	// 获取分页数据
	if err := db.Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to find email messages: %w", err)
	}

	// 转换为实体
	messages := make([]*entity.EmailMessage, len(models))
	for i, model := range models {
		messages[i] = r.toEntity(&model)
	}

	return messages, total, nil
}

// FindByStatus 根据状态查找邮件列表
func (r *EmailRepositoryImpl) FindByStatus(ctx context.Context, status string, limit int) ([]*entity.EmailMessage, error) {
	var models []*model.EmailMessageModel

	if err := r.db.WithContext(ctx).
		Where("status = ? AND deleted_at IS NULL", status).
		Limit(limit).
		Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to find emails by status: %w", err)
	}

	var emails []*entity.EmailMessage
	for _, m := range models {
		emails = append(emails, r.toEntity(m))
	}

	return emails, nil
}

// FindPendingEmails 查找待发送的邮件
func (r *EmailRepositoryImpl) FindPendingEmails(ctx context.Context, limit int) ([]*entity.EmailMessage, error) {
	return r.FindByStatus(ctx, string(entity.EmailStatusPending), limit)
}

// FindFailedEmails 查找发送失败的邮件
func (r *EmailRepositoryImpl) FindFailedEmails(ctx context.Context, limit int) ([]*entity.EmailMessage, error) {
	return r.FindByStatus(ctx, string(entity.EmailStatusFailed), limit)
}

// UpdateStatus 更新邮件状态
func (r *EmailRepositoryImpl) UpdateStatus(ctx context.Context, emailID int64, status string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": gorm.Expr("NOW()"),
	}

	// 如果状态是已发送，设置发送时间
	if status == string(entity.EmailStatusSent) {
		updates["sent_at"] = gorm.Expr("NOW()")
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailMessageModel{}).
		Where("email_id = ?", emailID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update email status: %w", err)
	}

	return nil
}

// UpdateRetryInfo 更新重试信息
func (r *EmailRepositoryImpl) UpdateRetryInfo(ctx context.Context, emailID int64, retryCount int, lastRetryAt time.Time) error {
	updates := map[string]interface{}{
		"retry_count":   retryCount,
		"last_retry_at": lastRetryAt,
		"updated_at":    gorm.Expr("NOW()"),
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailMessageModel{}).
		Where("email_id = ?", emailID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update retry info: %w", err)
	}

	return nil
}

// Delete 删除邮件
func (r *EmailRepositoryImpl) Delete(ctx context.Context, id int64) error {
	if err := r.db.WithContext(ctx).Where("email_id = ?", id).Delete(&model.EmailMessageModel{}).Error; err != nil {
		return fmt.Errorf("failed to delete email: %w", err)
	}

	return nil
}

// GetStatistics 获取邮件统计信息
func (r *EmailRepositoryImpl) GetStatistics(ctx context.Context, params *value_object.StatisticsQueryParams) (*value_object.EmailStatistics, error) {
	// 验证并调整查询参数
	if err := params.ValidateAndAdjust(); err != nil {
		return nil, fmt.Errorf("invalid query parameters: %w", err)
	}

	stats := value_object.NewEmailStatistics(params.TenantID)

	// 构建基础查询条件
	whereConditions := []string{"tenant_id = ?", "deleted_at IS NULL"}
	args := []interface{}{params.TenantID}

	// 添加时间范围条件
	whereConditions = append(whereConditions, "created_at >= ?", "created_at <= ?")
	args = append(args, *params.StartDate, *params.EndDate)

	// 添加状态筛选条件
	if params.Status != "" {
		whereConditions = append(whereConditions, "status = ?")
		args = append(args, params.Status)
	}

	// 添加账号筛选条件
	if params.AccountID > 0 {
		whereConditions = append(whereConditions, "account_id = ?")
		args = append(args, params.AccountID)
	}

	// 添加模板筛选条件
	if params.TemplateID > 0 {
		whereConditions = append(whereConditions, "template_id = ?")
		args = append(args, params.TemplateID)
	}

	// 构建WHERE子句
	whereClause := ""
	for i, condition := range whereConditions {
		if i > 0 {
			whereClause += " AND "
		}
		whereClause += condition
	}

	// 1. 查询状态统计（修复：只按状态分组，避免重复计算）
	var statusResults []struct {
		Status       string     `gorm:"column:status"`
		Count        int64      `gorm:"column:count"`
		LastSentAt   *time.Time `gorm:"column:last_sent"`
		LastFailedAt *time.Time `gorm:"column:last_failed"`
	}

	statusQuery := `
		SELECT 
			status,
			COUNT(*) as count,
			MAX(CASE WHEN status = ? THEN sent_at END) as last_sent,
			MAX(CASE WHEN status = ? THEN updated_at END) as last_failed
		FROM email_messages 
		WHERE ` + whereClause + `
		GROUP BY status
	`

	statusArgs := append([]interface{}{
		string(entity.EmailStatusSent),
		string(entity.EmailStatusFailed),
	}, args...)

	if err := r.db.WithContext(ctx).Raw(statusQuery, statusArgs...).Scan(&statusResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get status statistics: %w", err)
	}

	// 处理状态统计结果
	statusCounts := make(map[string]int64)
	var lastSentAt, lastFailedAt *time.Time

	for _, result := range statusResults {
		statusCounts[result.Status] = result.Count // 修复：直接赋值，不累加

		// 更新最后发送和失败时间
		if result.Status == string(entity.EmailStatusSent) && result.LastSentAt != nil {
			if lastSentAt == nil || result.LastSentAt.After(*lastSentAt) {
				lastSentAt = result.LastSentAt
			}
		}
		if result.Status == string(entity.EmailStatusFailed) && result.LastFailedAt != nil {
			if lastFailedAt == nil || result.LastFailedAt.After(*lastFailedAt) {
				lastFailedAt = result.LastFailedAt
			}
		}
	}

	// 2. 查询每日统计（修复：统计发送成功的邮件，按日期分组）
	var dailyResults []struct {
		Date  string `gorm:"column:date"`
		Count int64  `gorm:"column:count"`
	}

	dailyQuery := `
		SELECT 
			DATE(sent_at) as date,
			COUNT(*) as count
		FROM email_messages 
		WHERE ` + whereClause + ` AND status = ? AND sent_at IS NOT NULL
		GROUP BY DATE(sent_at)
		ORDER BY date DESC
	`

	dailyArgs := append(args, string(entity.EmailStatusSent))
	if err := r.db.WithContext(ctx).Raw(dailyQuery, dailyArgs...).Scan(&dailyResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily statistics: %w", err)
	}

	// 处理每日统计结果
	dailyStats := make(map[string]int64)
	for _, result := range dailyResults {
		dailyStats[result.Date] = result.Count // 修复：直接赋值，不累加
	}

	// 3. 查询每月统计（修复：统计发送成功的邮件，按月份分组）
	var monthlyResults []struct {
		Month string `gorm:"column:month"`
		Count int64  `gorm:"column:count"`
	}

	monthlyQuery := `
		SELECT 
			DATE_FORMAT(sent_at, '%Y-%m') as month,
			COUNT(*) as count
		FROM email_messages 
		WHERE ` + whereClause + ` AND status = ? AND sent_at IS NOT NULL
		GROUP BY DATE_FORMAT(sent_at, '%Y-%m')
		ORDER BY month DESC
	`

	monthlyArgs := append(args, string(entity.EmailStatusSent))
	if err := r.db.WithContext(ctx).Raw(monthlyQuery, monthlyArgs...).Scan(&monthlyResults).Error; err != nil {
		return nil, fmt.Errorf("failed to get monthly statistics: %w", err)
	}

	// 处理每月统计结果
	monthlyStats := make(map[string]int64)
	for _, result := range monthlyResults {
		monthlyStats[result.Month] = result.Count // 修复：直接赋值，不累加
	}

	// 设置统计结果
	stats.StatusCounts = statusCounts
	stats.LastSentAt = lastSentAt
	stats.LastFailedAt = lastFailedAt
	stats.DailyStats = dailyStats
	stats.MonthlyStats = monthlyStats

	// 设置总计数
	stats.TotalSent = statusCounts[string(entity.EmailStatusSent)]
	stats.TotalFailed = statusCounts[string(entity.EmailStatusFailed)]
	stats.TotalPending = statusCounts[string(entity.EmailStatusPending)]

	// 计算今日和本月统计（修复：使用发送成功的邮件统计）
	today := time.Now().Format("2006-01-02")
	if todayCount, exists := dailyStats[today]; exists {
		stats.DailySentCount = todayCount
	}

	currentMonth := time.Now().Format("2006-01")
	if monthCount, exists := monthlyStats[currentMonth]; exists {
		stats.MonthlySentCount = monthCount
	}

	// 计算失败率（修复：使用正确的计算公式）
	if stats.TotalSent > 0 {
		stats.MonthlyFailedRate = float64(stats.TotalFailed) / float64(stats.TotalSent)
	}

	return stats, nil
}

// List 获取邮件列表
func (r *EmailRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailMessage, int64, error) {
	return r.FindByTenantID(ctx, tenantID, offset, limit)
}

// toModel 将实体转换为模型
func (r *EmailRepositoryImpl) toModel(email *entity.EmailMessage) *model.EmailMessageModel {
	return &model.EmailMessageModel{
		ID:           email.ID,
		EmailID:      email.EmailID,
		TenantID:     email.TenantID, // 直接使用int64类型
		TemplateID:   email.TemplateID,
		FromAddress:  email.FromAddress,
		ToAddresses:  model.JSONString{email.ToAddress},
		CcAddresses:  model.JSONString(email.CcAddresses),
		BccAddresses: model.JSONString(email.BccAddresses),
		Subject:      email.Subject,
		HTMLContent:  email.HTMLContent,
		TextContent:  email.TextContent,
		Variables:    model.JSONMap(email.Variables),
		Status:       email.Status,
		Priority:     2, // 默认优先级
		RetryCount:   email.RetryCount,
		MaxRetries:   email.MaxRetries,
		ErrorMsg:     email.ErrorMsg,
		SentAt:       email.SentAt,
		CreatedAt:    email.CreatedAt,
		UpdatedAt:    email.UpdatedAt,
		DeletedAt:    gorm.DeletedAt{},
	}
}

// toEntity 将模型转换为实体
func (r *EmailRepositoryImpl) toEntity(model *model.EmailMessageModel) *entity.EmailMessage {
	var toAddress string
	if len(model.ToAddresses) > 0 {
		toAddress = model.ToAddresses[0]
	}

	var deletedAt *time.Time
	if model.DeletedAt.Valid {
		deletedAt = &model.DeletedAt.Time
	}

	return &entity.EmailMessage{
		ID:           model.ID,
		EmailID:      model.EmailID,
		TenantID:     model.TenantID, // 直接使用int64类型
		TemplateID:   model.TemplateID,
		FromAddress:  model.FromAddress,
		ToAddress:    toAddress,
		CcAddresses:  []string(model.CcAddresses),
		BccAddresses: []string(model.BccAddresses),
		Subject:      model.Subject,
		HTMLContent:  model.HTMLContent,
		TextContent:  model.TextContent,
		Variables:    map[string]interface{}(model.Variables),
		Status:       model.Status,
		RetryCount:   model.RetryCount,
		MaxRetries:   model.MaxRetries,
		ErrorMsg:     model.ErrorMsg,
		SentAt:       model.SentAt,
		CreatedAt:    model.CreatedAt,
		UpdatedAt:    model.UpdatedAt,
		DeletedAt:    deletedAt,
	}
}
