package repository

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"platforms-email/internal/domain/template/entity"
	"platforms-email/internal/infrastructure/permission"
	"platforms-email/internal/infrastructure/persistence/model"

	"gorm.io/gorm"
)

// TemplateRepositoryImpl 模板仓储实现
type TemplateRepositoryImpl struct {
	db                *gorm.DB
	permissionChecker *permission.PermissionChecker
}

// NewTemplateRepository 创建模板仓储实现
func NewTemplateRepository(db *gorm.DB) *TemplateRepositoryImpl {
	return &TemplateRepositoryImpl{
		db:                db,
		permissionChecker: permission.NewPermissionChecker(), // 使用延迟初始化
	}
}

// NewTemplateRepositoryWithPermissionChecker 创建模板仓储实现（带权限检查器）
func NewTemplateRepositoryWithPermissionChecker(db *gorm.DB, permissionChecker *permission.PermissionChecker) *TemplateRepositoryImpl {
	return &TemplateRepositoryImpl{
		db:                db,
		permissionChecker: permissionChecker,
	}
}

// Get 根据ID获取模板
func (r *TemplateRepositoryImpl) Get(ctx context.Context, tenantID int64, id int64) (*entity.EmailTemplate, error) {
	var model model.EmailTemplateModel
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ? AND deleted_at IS NULL", tenantID, id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("template not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}
	return model.ToEntity(), nil
}

// GetByCode 根据代码获取模板
func (r *TemplateRepositoryImpl) GetByCode(ctx context.Context, tenantID int64, code string) (*entity.EmailTemplate, error) {
	var model model.EmailTemplateModel
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND template_code = ? AND deleted_at IS NULL", tenantID, code).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("template not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}
	return model.ToEntity(), nil
}

// Create 创建模板
func (r *TemplateRepositoryImpl) Create(ctx context.Context, template *entity.EmailTemplate) error {
	model := &model.EmailTemplateModel{
		TenantID:           template.TenantID,
		TemplateCode:       template.TemplateCode,
		AccountID:          template.AccountID,
		Name:               template.Name,
		Type:               uint8(template.Type),
		Subject:            template.Subject,
		HTMLContent:        template.HTMLContent,
		PlainTextContent:   template.PlainTextContent,
		ThumbnailURL:       template.ThumbnailURL,
		IsResponsive:       template.IsResponsive,
		RateLimitPerMinute: template.RateLimitPerMinute,
		RateLimitPerHour:   template.RateLimitPerHour,
		RateLimitPerDay:    template.RateLimitPerDay,
		Version:            template.Version,
	}

	// 转换变量
	if template.Variables != nil {
		model.Variables = make(map[string]interface{})
		for key, variable := range template.Variables {
			model.Variables[key] = map[string]interface{}{
				"label":       variable.Label,
				"type":        variable.Type,
				"required":    variable.Required,
				"description": variable.Description,
			}
		}
	}

	// 转换统计信息
	if template.Statistics != nil {
		if statsJSON, err := json.Marshal(template.Statistics); err == nil {
			model.Statistics = statsJSON
		}
	}

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create template: %w", err)
	}

	template.ID = model.ID
	template.CreatedAt = model.CreatedAt
	template.UpdatedAt = model.UpdatedAt

	return nil
}

// Update 更新模板
func (r *TemplateRepositoryImpl) Update(ctx context.Context, template *entity.EmailTemplate) error {
	model := &model.EmailTemplateModel{
		ID:                 template.ID,
		TenantID:           template.TenantID,
		TemplateCode:       template.TemplateCode,
		AccountID:          template.AccountID,
		Name:               template.Name,
		Type:               uint8(template.Type),
		Subject:            template.Subject,
		HTMLContent:        template.HTMLContent,
		PlainTextContent:   template.PlainTextContent,
		ThumbnailURL:       template.ThumbnailURL,
		IsResponsive:       template.IsResponsive,
		RateLimitPerMinute: template.RateLimitPerMinute,
		RateLimitPerHour:   template.RateLimitPerHour,
		RateLimitPerDay:    template.RateLimitPerDay,
		Status:             uint8(template.Status),
		Version:            template.Version,
	}

	// 转换变量
	if template.Variables != nil {
		model.Variables = make(map[string]interface{})
		for key, variable := range template.Variables {
			model.Variables[key] = map[string]interface{}{
				"label":       variable.Label,
				"type":        variable.Type,
				"required":    variable.Required,
				"description": variable.Description,
			}
		}
	}

	// 转换统计信息
	if template.Statistics != nil {
		if statsJSON, err := json.Marshal(template.Statistics); err == nil {
			model.Statistics = statsJSON
		}
	}

	if err := r.db.WithContext(ctx).Where("id = ? AND tenant_id = ?", template.ID, template.TenantID).Updates(model).Error; err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}

	return nil
}

// Delete 删除模板
func (r *TemplateRepositoryImpl) Delete(ctx context.Context, tenantID int64, id int64) error {
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, id).Delete(&model.EmailTemplateModel{}).Error; err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}
	return nil
}

// List 列出模板
func (r *TemplateRepositoryImpl) List(ctx context.Context, tenantID int64, filter *entity.TemplateFilter) ([]*entity.EmailTemplate, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.EmailTemplateModel{}).Where("deleted_at IS NULL")

	// 处理租户ID过滤
	if filter != nil && len(filter.TenantIDs) > 0 {
		// 如果指定了租户ID列表，使用列表中的租户ID
		query = query.Where("tenant_id IN ?", filter.TenantIDs)
	} else {
		// 否则使用传入的单个租户ID
		query = query.Where("tenant_id = ?", tenantID)
	}

	// 应用过滤条件
	if filter != nil {
		if filter.TemplateCode != "" {
			// 优先使用精确的模板代码查询
			query = query.Where("template_code = ?", filter.TemplateCode)
		} else if filter.Search != "" {
			// 如果没有模板代码，则使用模糊搜索
			query = query.Where("name LIKE ? OR template_code LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
		}
		if filter.Type != nil {
			query = query.Where("type = ?", *filter.Type)
		}
		if filter.Status != nil {
			query = query.Where("status = ?", *filter.Status)
		}
		if filter.SortBy != "" {
			if filter.SortOrder != "" {
				query = query.Order(fmt.Sprintf("%s %s", filter.SortBy, filter.SortOrder))
			} else {
				query = query.Order(filter.SortBy)
			}
		}
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// 分页
	if filter != nil && filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 查询数据
	var models []model.EmailTemplateModel
	if err := query.Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list templates: %w", err)
	}

	// 转换为实体
	templates := make([]*entity.EmailTemplate, len(models))
	for i, m := range models {
		templates[i] = m.ToEntity()
	}

	return templates, total, nil
}

// CheckNameExists 检查名称是否存在
func (r *TemplateRepositoryImpl) CheckNameExists(ctx context.Context, tenantID int64, name string, excludeID int64) (bool, error) {
	query := r.db.WithContext(ctx).Model(&model.EmailTemplateModel{}).
		Where("tenant_id = ? AND name = ? AND deleted_at IS NULL", tenantID, name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check name existence: %w", err)
	}

	return count > 0, nil
}

// GetByAccountID 根据账户ID获取模板列表
func (r *TemplateRepositoryImpl) GetByAccountID(ctx context.Context, tenantID int64, accountID int64) ([]*entity.EmailTemplate, error) {
	var models []model.EmailTemplateModel
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND account_id = ? AND deleted_at IS NULL", tenantID, accountID).Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to get templates by account ID: %w", err)
	}

	templates := make([]*entity.EmailTemplate, len(models))
	for i, m := range models {
		templates[i] = m.ToEntity()
	}

	return templates, nil
}

// UpdateStatistics 更新模板统计信息
func (r *TemplateRepositoryImpl) UpdateStatistics(ctx context.Context, tenantID int64, id int64, statistics map[string]interface{}) error {
	statsJSON, err := json.Marshal(statistics)
	if err != nil {
		return fmt.Errorf("failed to marshal statistics: %w", err)
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailTemplateModel{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("statistics", statsJSON).Error; err != nil {
		return fmt.Errorf("failed to update statistics: %w", err)
	}

	return nil
}

// ========== 带权限检查的方法 ==========

// GetWithPermission 根据ID获取模板（带权限检查）
func (r *TemplateRepositoryImpl) GetWithPermission(ctx context.Context, tenantID int64, id int64, userID int64) (*entity.EmailTemplate, error) {
	var model model.EmailTemplateModel
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ? AND deleted_at IS NULL", tenantID, id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("template not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	template := model.ToEntity()

	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermission(ctx, userID, template.IsSystem); err != nil {
		return nil, fmt.Errorf("权限检查失败: %w", err)
	}

	return template, nil
}

// GetByCodeWithPermission 根据代码获取模板（带权限检查）
func (r *TemplateRepositoryImpl) GetByCodeWithPermission(ctx context.Context, tenantID int64, code string, userID int64) (*entity.EmailTemplate, error) {
	var model model.EmailTemplateModel
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND template_code = ? AND deleted_at IS NULL", tenantID, code).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("template not found: %w", err)
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	template := model.ToEntity()

	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermission(ctx, userID, template.IsSystem); err != nil {
		return nil, fmt.Errorf("权限检查失败: %w", err)
	}

	return template, nil
}

// CreateWithPermission 创建模板（带权限检查）
func (r *TemplateRepositoryImpl) CreateWithPermission(ctx context.Context, template *entity.EmailTemplate, userID int64) error {
	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermission(ctx, userID, template.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	return r.Create(ctx, template)
}

// UpdateWithPermission 更新模板（带权限检查）
func (r *TemplateRepositoryImpl) UpdateWithPermission(ctx context.Context, template *entity.EmailTemplate, userID int64) error {
	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermission(ctx, userID, template.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	return r.Update(ctx, template)
}

// DeleteWithPermission 删除模板（带权限检查）
func (r *TemplateRepositoryImpl) DeleteWithPermission(ctx context.Context, tenantID int64, id int64, userID int64) error {
	// 先获取模板以检查是否为系统资源
	template, err := r.Get(ctx, tenantID, id)
	if err != nil {
		return err
	}

	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermission(ctx, userID, template.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	return r.Delete(ctx, tenantID, id)
}

// ListWithPermission 获取模板列表（带权限检查）
func (r *TemplateRepositoryImpl) ListWithPermission(ctx context.Context, tenantID int64, filter *entity.TemplateFilter, userID int64) ([]*entity.EmailTemplate, int64, error) {
	// 构建基础查询
	query := r.db.WithContext(ctx).Model(&model.EmailTemplateModel{}).Where("tenant_id = ? AND deleted_at IS NULL", tenantID)

	// 添加权限过滤条件
	if userID > 0 {
		filterCondition, filterArgs, err := r.permissionChecker.BuildSystemResourceFilter(ctx, userID)
		if err != nil {
			return nil, 0, fmt.Errorf("构建权限过滤条件失败: %w", err)
		}
		if filterCondition != "" {
			query = query.Where(filterCondition, filterArgs...)
		}
	}

	// 应用其他过滤条件
	if filter != nil {
		if filter.Type != nil {
			query = query.Where("type = ?", *filter.Type)
		}
		if filter.Status != nil {
			query = query.Where("status = ?", *filter.Status)
		}
		if filter.Search != "" {
			query = query.Where("name LIKE ? OR template_code LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
		}
		if filter.SortBy != "" {
			if filter.SortOrder != "" {
				query = query.Order(fmt.Sprintf("%s %s", filter.SortBy, filter.SortOrder))
			} else {
				query = query.Order(filter.SortBy)
			}
		}
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// 分页
	if filter != nil && filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 查询数据
	var models []model.EmailTemplateModel
	if err := query.Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list templates: %w", err)
	}

	// 转换为实体
	templates := make([]*entity.EmailTemplate, len(models))
	for i, m := range models {
		templates[i] = m.ToEntity()
	}

	return templates, total, nil
}
