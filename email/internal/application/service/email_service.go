package service

import (
	"context"
	"strconv"

	emailAppService "platforms-email/internal/application/email/service"
	"platforms-email/internal/application/email/dto"
)

// EmailService is a wrapper service for gRPC interface that adapts the existing EmailApplicationService
type EmailService struct {
	emailAppService *emailAppService.EmailApplicationService
}

// NewEmailService creates a new EmailService wrapper
func NewEmailService(emailAppService *emailAppService.EmailApplicationService) *EmailService {
	return &EmailService{
		emailAppService: emailAppService,
	}
}

// SendTemplateEmail sends template email using the existing application service
// Adapts the gRPC interface to the existing application service interface
func (s *EmailService) SendTemplateEmail(ctx context.Context, tenantID string, templateCode string, toAddresses []string, variables map[string]string) (string, error) {
	// Convert tenant ID from string to int64
	tenantIDInt, err := strconv.ParseInt(tenantID, 10, 64)
	if err != nil {
		return "", err
	}

	// Convert variables from map[string]string to map[string]interface{}
	variablesInterface := make(map[string]interface{})
	for k, v := range variables {
		variablesInterface[k] = v
	}

	// For now, we'll use the first email address as the primary recipient
	// In the future, this could be enhanced to support multiple recipients
	toAddress := toAddresses[0]

	// Create the request DTO
	request := &dto.SendTemplateEmailRequest{
		TemplateCode: templateCode,
		ToAddress:    toAddress,
		Variables:    variablesInterface,
	}

	// Call the existing application service
	response, err := s.emailAppService.SendTemplateEmail(ctx, tenantIDInt, request)
	if err != nil {
		return "", err
	}

	return response.EmailID, nil
}