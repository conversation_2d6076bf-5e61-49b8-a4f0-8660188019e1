package dto

import (
	"time"

	"platforms-email/internal/domain/template/entity"
)

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	TenantID           int64                       `json:"-"` // 由服务端从JWT token设置，不允许客户端传递
	TemplateCode       string                      `json:"template_code" binding:"required,min=1,max=50"`
	AccountID          int64                       `json:"account_id" binding:"required"`
	Name               string                      `json:"name" binding:"required,min=1,max=100"`
	Type               uint8                       `json:"type" binding:"required,min=1,max=2"` // 1=HTML, 2=Text
	Subject            string                      `json:"subject" binding:"required,min=1,max=200"`
	HTMLContent        string                      `json:"html_content,omitempty" binding:"max=50000"`
	PlainTextContent   string                      `json:"plain_text_content,omitempty" binding:"max=10000"`
	Variables          map[string]TemplateVariable `json:"variables,omitempty"`
	RateLimitPerMinute uint                        `json:"rate_limit_per_minute,omitempty" binding:"min=0,max=1000"`
	RateLimitPerHour   uint                        `json:"rate_limit_per_hour,omitempty" binding:"min=0,max=10000"`
	RateLimitPerDay    uint                        `json:"rate_limit_per_day,omitempty" binding:"min=0,max=100000"`
	ThumbnailURL       string                      `json:"thumbnail_url,omitempty" binding:"max=500"`
	IsResponsive       bool                        `json:"is_responsive,omitempty"`
	Description        string                      `json:"description,omitempty" binding:"max=1000"`
	Status             uint8                       `json:"status,omitempty" binding:"omitempty,min=1,max=4"`
	IsSystem           bool                        `json:"is_system,omitempty"`
	CreatedBy          int64                       `json:"-"` // 由服务端设置
}

// CreateTemplateResponse 创建模板响应
type CreateTemplateResponse struct {
	TemplateResponse
}

// GetTemplateRequest 获取模板请求
type GetTemplateRequest struct {
	TemplateID int64 `json:"-"` // 由路径参数传递
}

// GetTemplateResponse 获取模板响应
type GetTemplateResponse struct {
	TemplateResponse
}

// ListTemplateRequest 获取模板列表请求
type ListTemplateRequest struct {
	Type      *uint8 `json:"type,omitempty"`       // 模板类型
	Status    *uint8 `json:"status,omitempty"`     // 模板状态
	Search    string `json:"search,omitempty"`     // 搜索关键字
	SortBy    string `json:"sort_by,omitempty"`    // 排序字段
	SortOrder string `json:"sort_order,omitempty"` // 排序方向
	Page      int    `json:"page"`                 // 页码
	PageSize  int    `json:"page_size"`            // 每页数量
}

// ListTemplateResponse 获取模板列表响应
type ListTemplateResponse struct {
	TemplateResponse
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	ID                 int64                       `json:"id"` // 由路径参数传递
	TenantID           int64                       `json:"-"`  // 由服务端从JWT token设置
	Name               string                      `json:"name,omitempty" binding:"omitempty,min=1,max=100"`
	Type               uint8                       `json:"type,omitempty" binding:"omitempty,min=1,max=6"`
	Subject            string                      `json:"subject,omitempty" binding:"omitempty,min=1,max=200"`
	HTMLContent        string                      `json:"html_content,omitempty" binding:"max=50000"`
	PlainTextContent   string                      `json:"plain_text_content,omitempty" binding:"max=10000"`
	Variables          map[string]TemplateVariable `json:"variables,omitempty"`
	RateLimitPerMinute uint                        `json:"rate_limit_per_minute,omitempty" binding:"min=0,max=1000"`
	RateLimitPerHour   uint                        `json:"rate_limit_per_hour,omitempty" binding:"min=0,max=10000"`
	RateLimitPerDay    uint                        `json:"rate_limit_per_day,omitempty" binding:"min=0,max=100000"`
	ThumbnailURL       string                      `json:"thumbnail_url,omitempty" binding:"max=500"`
	IsResponsive       bool                        `json:"is_responsive,omitempty"`
	Description        string                      `json:"description,omitempty" binding:"max=255"`
	Status             uint8                       `json:"status,omitempty" binding:"omitempty,min=1,max=4"`
	AccountID          int64                       `json:"account_id,omitempty"` // 发件账户ID
	IsSystem           bool                        `json:"is_system,omitempty"`
	UpdateMode         string                      `json:"update_mode,omitempty"` // 更新模式：full(完整更新), variables_only(仅更新变量)
}

// UpdateTemplateResponse 更新模板响应
type UpdateTemplateResponse struct {
	TemplateResponse
}

// DeleteTemplateRequest 删除模板请求
type DeleteTemplateRequest struct {
	TenantID   int64 `json:"-"` // 由服务端从JWT token设置
	TemplateID int64 `json:"-"` // 由路径参数传递
}

// DeleteTemplateResponse 删除模板响应
type DeleteTemplateResponse struct {
	TemplateID int64      `json:"template_id"`
	TenantID   int64      `json:"tenant_id"`
	DeletedAt  *time.Time `json:"deleted_at"`
}

// CloneTemplateRequest 克隆模板请求
type CloneTemplateRequest struct {
	TenantID   int64  `json:"-"`                                     // 由服务端从JWT token设置
	TemplateID int64  `json:"-"`                                     // 由路径参数传递
	Name       string `json:"name" binding:"required,min=1,max=100"` // 新模板名称
}

// CloneTemplateResponse 克隆模板响应
type CloneTemplateResponse struct {
	TemplateResponse
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Label       string `json:"label" binding:"required"`               // 显示名称
	Type        string `json:"type" binding:"required"`                // 变量类型
	Required    bool   `json:"required"`                               // 是否必填
	Description string `json:"description" binding:"required,max=200"` // 描述
}

// TemplateResponse 模板响应
type TemplateResponse struct {
	ID                 int64                       `json:"id"`
	TenantID           int64                       `json:"tenant_id"`
	TemplateCode       string                      `json:"template_code"`
	AccountID          int64                       `json:"account_id"`
	Name               string                      `json:"name"`
	Type               uint8                       `json:"type"`
	Subject            string                      `json:"subject"`
	HTMLContent        string                      `json:"html_content,omitempty"`
	PlainTextContent   string                      `json:"plain_text_content,omitempty"`
	Variables          map[string]TemplateVariable `json:"variables,omitempty"`
	RateLimitPerMinute uint                        `json:"rate_limit_per_minute"`
	RateLimitPerHour   uint                        `json:"rate_limit_per_hour"`
	RateLimitPerDay    uint                        `json:"rate_limit_per_day"`
	ThumbnailURL       string                      `json:"thumbnail_url,omitempty"`
	IsResponsive       bool                        `json:"is_responsive"`
	Description        string                      `json:"description,omitempty"`
	Status             uint8                       `json:"status"`
	CreatedAt          time.Time                   `json:"created_at"`
	UpdatedAt          time.Time                   `json:"updated_at"`
	CreatedBy          int64                       `json:"created_by"`
	UpdatedBy          int64                       `json:"updated_by"`
	Version            int64                       `json:"version"`
	Statistics         map[string]interface{}      `json:"statistics,omitempty"`
	IsSystem           bool                        `json:"is_system"`
}

// ToEntity 转换为实体
func (r *CreateTemplateRequest) ToEntity() (*entity.EmailTemplate, error) {
	template, err := entity.NewEmailTemplate(
		r.TenantID,
		r.TemplateCode,
		r.AccountID,
		r.Name,
		entity.TemplateType(r.Type),
		r.CreatedBy,
	)
	if err != nil {
		return nil, err
	}

	// 设置其他字段
	template.Subject = r.Subject
	template.HTMLContent = r.HTMLContent
	template.PlainTextContent = r.PlainTextContent
	template.Variables = toEntityVariables(r.Variables)
	template.RateLimitPerMinute = r.RateLimitPerMinute
	template.RateLimitPerHour = r.RateLimitPerHour
	template.RateLimitPerDay = r.RateLimitPerDay
	template.ThumbnailURL = r.ThumbnailURL
	template.IsResponsive = r.IsResponsive
	template.Description = r.Description
	if r.Status != 0 {
		template.Status = entity.TemplateStatus(r.Status)
	}
	template.IsSystem = r.IsSystem

	return template, nil
}

// FromEntity 从实体转换
func (r *TemplateResponse) FromEntity(e *entity.EmailTemplate) {
	r.ID = e.ID
	r.TenantID = int64(e.TenantID)
	r.TemplateCode = e.TemplateCode
	r.AccountID = e.AccountID
	r.Name = e.Name
	r.Type = uint8(e.Type)
	r.Subject = e.Subject
	r.HTMLContent = e.HTMLContent
	r.PlainTextContent = e.PlainTextContent
	r.Variables = fromEntityVariables(e.Variables)
	r.RateLimitPerMinute = e.RateLimitPerMinute
	r.RateLimitPerHour = e.RateLimitPerHour
	r.RateLimitPerDay = e.RateLimitPerDay
	r.ThumbnailURL = e.ThumbnailURL
	r.IsResponsive = e.IsResponsive
	r.Description = e.Description
	r.Status = uint8(e.Status)
	r.CreatedAt = e.CreatedAt
	r.UpdatedAt = e.UpdatedAt
	r.CreatedBy = e.CreatedBy
	r.UpdatedBy = e.UpdatedBy
	r.Version = e.Version
	r.Statistics = e.Statistics
	r.IsSystem = e.IsSystem
}

// 辅助函数：转换变量
func toEntityVariables(variables map[string]TemplateVariable) map[string]entity.TemplateVariable {
	if variables == nil {
		return nil
	}
	result := make(map[string]entity.TemplateVariable)
	for key, variable := range variables {
		result[key] = entity.TemplateVariable{
			Label:       variable.Label,
			Type:        variable.Type,
			Required:    variable.Required,
			Description: variable.Description,
		}
	}
	return result
}

func fromEntityVariables(variables map[string]entity.TemplateVariable) map[string]TemplateVariable {
	if variables == nil {
		return nil
	}
	result := make(map[string]TemplateVariable)
	for key, variable := range variables {
		result[key] = TemplateVariable{
			Label:       variable.Label,
			Type:        variable.Type,
			Required:    variable.Required,
			Description: variable.Description,
		}
	}
	return result
}

// PublishTemplateRequest 发布模板请求
type PublishTemplateRequest struct {
	TenantID   int64 `json:"-"` // 由服务端从JWT token设置
	TemplateID int64 `json:"-"` // 由路径参数传递
	UserID     int64 `json:"-"` // 由服务端设置
}

// PublishTemplateResponse 发布模板响应
type PublishTemplateResponse struct {
	Success     bool      `json:"success"`
	Message     string    `json:"message"`
	PublishedAt time.Time `json:"published_at"`
}

// DisableTemplateRequest 停用模板请求
type DisableTemplateRequest struct {
	TenantID   int64 `json:"-"` // 由服务端从JWT token设置
	TemplateID int64 `json:"-"` // 由路径参数传递
	UserID     int64 `json:"-"` // 由服务端设置
}

// DisableTemplateResponse 停用模板响应
type DisableTemplateResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// EnableTemplateRequest 启用模板请求
type EnableTemplateRequest struct {
	TenantID   int64 `json:"-"` // 由服务端从JWT token设置
	TemplateID int64 `json:"-"` // 由路径参数传递
	UserID     int64 `json:"-"` // 由服务端设置
}

// EnableTemplateResponse 启用模板响应
type EnableTemplateResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// GetVersionsRequest 获取版本历史请求
type GetVersionsRequest struct {
	TenantID     int64  `json:"-"` // 由服务端从JWT token设置
	TemplateCode string `json:"-"` // 由路径参数传递
	UserID       int64  `json:"-"` // 由服务端设置
}

// GetVersionsResponse 获取版本历史响应
type GetVersionsResponse struct {
	Versions []TemplateResponse `json:"versions"`
	Total    int64              `json:"total"`
}

// RestoreVersionRequest 恢复版本请求
type RestoreVersionRequest struct {
	TenantID      int64  `json:"-"`                                          // 由服务端从JWT token设置
	TemplateCode  string `json:"-"`                                          // 由路径参数传递
	Version       int64  `json:"version" binding:"required"`                 // 要恢复的版本号
	VersionRemark string `json:"version_remark,omitempty" binding:"max=500"` // 版本备注
	UserID        int64  `json:"-"`                                          // 由服务端设置
}

// RestoreVersionResponse 恢复版本响应
type RestoreVersionResponse struct {
	Success    bool      `json:"success"`
	Message    string    `json:"message"`
	NewVersion int64     `json:"new_version"`
	RestoredAt time.Time `json:"restored_at"`
}

// GetTemplateVariablesRequest 获取模板变量请求
type GetTemplateVariablesRequest struct {
	TemplateID int64 `json:"template_id" binding:"min=0"` // 模板ID，0表示只获取系统变量
}

// GetTemplateVariablesResponse 获取模板变量响应
type GetTemplateVariablesResponse []TemplateVariableInfo

// TemplateVariableInfo 模板变量信息
type TemplateVariableInfo struct {
	Name         string `json:"name"`          // 变量名
	Label        string `json:"label"`         // 显示名称
	Type         string `json:"type"`          // 变量类型
	Description  string `json:"description"`   // 变量描述
	Required     bool   `json:"required"`      // 是否必填
	DefaultValue string `json:"default_value"` // 默认值
	Category     string `json:"category"`      // 变量分类（system:系统变量, template:模板变量, custom:自定义变量）
}
