package service

import (
	"context"
	"fmt"
	"platforms-pkg/usercontext"
	"strings"
	"time"

	"platforms-email/internal/application/template/dto"
	emailErrors "platforms-email/internal/domain/errors"
	"platforms-email/internal/domain/template/entity"
	"platforms-email/internal/domain/template/repository"

	"platforms-pkg/logiface"
)

// TemplateApplicationService 模板应用服务
type TemplateApplicationService struct {
	templateRepo repository.Repository
	logger       logiface.Logger
	// 添加变量缓存
	variableCache map[string]cacheEntry
}

// cacheEntry 缓存条目
type cacheEntry struct {
	variables []dto.TemplateVariableInfo
	expiresAt time.Time
}

// NewTemplateApplicationService 创建模板应用服务
func NewTemplateApplicationService(templateRepo repository.Repository, logger logiface.Logger) *TemplateApplicationService {
	return &TemplateApplicationService{
		templateRepo:  templateRepo,
		logger:        logger,
		variableCache: make(map[string]cacheEntry),
	}
}

// CreateTemplate 创建模板
func (s *TemplateApplicationService) CreateTemplate(ctx context.Context, req *dto.CreateTemplateRequest) (*dto.CreateTemplateResponse, error) {
	// 从上下文获取租户ID，如果请求中没有设置
	if req.TenantID == 0 {
		tenantID, ok := usercontext.GetTenantID(ctx)
		if !ok || tenantID == 0 {
			s.logger.Error(ctx, "Tenant ID not found in context", logiface.String("name", req.Name))
			return nil, emailErrors.NewEmailError(emailErrors.CodeTenantNotFound, "租户信息未找到，请重新登录")
		}
		req.TenantID = tenantID
	}

	// 从上下文获取用户ID，如果请求中没有设置
	if req.CreatedBy == 0 {
		userID, ok := usercontext.GetUserID(ctx)
		if !ok || userID == 0 {
			s.logger.Error(ctx, "User ID not found in context", logiface.String("name", req.Name))
			return nil, emailErrors.NewSystemError("get_user_id", "用户信息未找到，请重新登录")
		}
		req.CreatedBy = userID
	}

	s.logger.Info(ctx, "Creating template", logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))

	// 检查模板名称是否已存在
	exists, err := s.templateRepo.CheckNameExists(ctx, req.TenantID, req.Name, 0)
	if err != nil {
		s.logger.Error(ctx, "Failed to check template name", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewSystemError("check_template_name", err.Error())
	}

	if exists {
		return nil, emailErrors.NewTemplateNameExistsError(req.Name)
	}

	// 创建模板实体
	template, err := req.ToEntity()
	if err != nil {
		return nil, emailErrors.NewSystemError("create_template_entity", err.Error())
	}

	// 验证模板
	if err := template.Validate(); err != nil {
		s.logger.Warn(ctx, "Template validation failed", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
	}

	// 保存模板
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.Error(ctx, "Failed to create template", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewSystemError("create_template", err.Error())
	}

	// 构建响应
	response := &dto.CreateTemplateResponse{}
	response.FromEntity(template)

	s.logger.Info(ctx, "Template created successfully", logiface.Int64("template_id", template.ID), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
	return response, nil
}

// GetTemplate 获取模板
func (s *TemplateApplicationService) GetTemplate(ctx context.Context, req *dto.GetTemplateRequest) (*dto.GetTemplateResponse, error) {
	tenantId, _ := usercontext.GetTenantID(ctx)
	s.logger.Info(ctx, "Getting template", logiface.Int64("tenant_id", tenantId), logiface.Int64("template_id", req.TemplateID))

	// 获取模板
	template, err := s.templateRepo.Get(ctx, tenantId, req.TemplateID)
	if err != nil {
		s.logger.Info(ctx, "Template not found", logiface.Int64("tenant_id", tenantId), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 构建响应
	response := &dto.GetTemplateResponse{}
	response.FromEntity(template)

	return response, nil
}

// ListTemplates 获取模板列表
func (s *TemplateApplicationService) ListTemplates(ctx context.Context, req *dto.ListTemplateRequest) ([]*dto.ListTemplateResponse, int64, error) {
	tenantId, _ := usercontext.GetTenantID(ctx)
	s.logger.Info(ctx, "Getting template list", logiface.Int64("tenant_id", tenantId))

	// 构建筛选条件
	filter := &entity.TemplateFilter{
		Type:      req.Type,
		Status:    req.Status,
		Search:    req.Search,
		SortBy:    req.SortBy,
		SortOrder: req.SortOrder,
		Page:      req.Page,
		PageSize:  req.PageSize,
	}

	// 获取模板列表
	templates, total, err := s.templateRepo.List(ctx, tenantId, filter)
	if err != nil {
		s.logger.Error(ctx, "Failed to get template list", logiface.Error(err), logiface.Int64("tenant_id", tenantId))
		return nil, 0, emailErrors.NewSystemError("get_template_list", err.Error())
	}

	// 构建响应
	responses := make([]*dto.ListTemplateResponse, len(templates))
	for i, template := range templates {
		response := &dto.ListTemplateResponse{}
		response.FromEntity(template)
		responses[i] = response
	}

	return responses, total, nil
}

// UpdateTemplate 更新模板
func (s *TemplateApplicationService) UpdateTemplate(ctx context.Context, req *dto.UpdateTemplateRequest) (*dto.UpdateTemplateResponse, error) {
	s.logger.Info(ctx, "Updating template", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.ID), logiface.String("update_mode", req.UpdateMode))
	// 获取现有模板
	existingTemplate, err := s.templateRepo.Get(ctx, req.TenantID, req.ID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for update", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.ID))
		return nil, emailErrors.NewTemplateNotFoundError(req.ID)
	}

	// 根据更新模式决定更新策略
	switch req.UpdateMode {
	case "variables_only":
		// 仅更新变量，跳过其他字段的校验
		if req.Variables != nil {
			// 转换变量
			existingTemplate.Variables = make(map[string]entity.TemplateVariable)
			for name, v := range req.Variables {
				existingTemplate.Variables[name] = entity.TemplateVariable{
					Label:       v.Label,
					Type:        v.Type,
					Required:    v.Required,
					Description: v.Description,
				}
			}
		}
		existingTemplate.UpdatedAt = time.Now()
		existingTemplate.Version++ // 增加版本号

		// 变量模式不进行完整校验
		s.logger.Info(ctx, "Variables-only update mode, skipping full validation", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.ID))

	default:
		// 完整更新模式（默认行为）
		// 检查名称是否重复（排除当前模板）
		if req.Name != "" && req.Name != existingTemplate.Name {
			exists, err := s.templateRepo.CheckNameExists(ctx, req.TenantID, req.Name, req.ID)
			if err != nil {
				s.logger.Error(ctx, "Failed to check template name", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
				return nil, emailErrors.NewSystemError("check_template_name", err.Error())
			}

			if exists {
				s.logger.Info(ctx, "Template name already exists", logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
				return nil, emailErrors.NewTemplateNameExistsError(req.Name)
			}
		}

		// 更新模板字段
		if req.Name != "" {
			existingTemplate.Name = req.Name
		}
		if req.Type != 0 {
			existingTemplate.Type = entity.TemplateType(req.Type)
		}
		if req.Subject != "" {
			existingTemplate.Subject = req.Subject
		}
		if req.HTMLContent != "" {
			existingTemplate.HTMLContent = req.HTMLContent
		}
		if req.PlainTextContent != "" {
			existingTemplate.PlainTextContent = req.PlainTextContent
		}
		if req.Description != "" {
			existingTemplate.Description = req.Description
		}
		if req.Status != 0 {
			existingTemplate.Status = entity.TemplateStatus(req.Status)
		}
		if req.AccountID != 0 {
			existingTemplate.AccountID = req.AccountID
		}
		if req.Variables != nil {
			// 转换变量
			existingTemplate.Variables = make(map[string]entity.TemplateVariable)
			for name, v := range req.Variables {
				existingTemplate.Variables[name] = entity.TemplateVariable{
					Label:       v.Label,
					Type:        v.Type,
					Required:    v.Required,
					Description: v.Description,
				}
			}
		}
		existingTemplate.IsResponsive = req.IsResponsive
		existingTemplate.UpdatedAt = time.Now()
		existingTemplate.Version++ // 增加版本号

		// 验证模板（仅在完整更新模式下）
		if err := existingTemplate.Validate(); err != nil {
			s.logger.Warn(ctx, "Template validation failed", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.ID))
			return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
		}
	}

	// 保存更新
	if err := s.templateRepo.Update(ctx, existingTemplate); err != nil {
		s.logger.Error(ctx, "Failed to update template", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.ID))
		return nil, emailErrors.NewSystemError("update_template", err.Error())
	}

	// 构建响应
	response := &dto.UpdateTemplateResponse{}
	response.FromEntity(existingTemplate)

	// 清除变量缓存
	s.ClearVariableCache(req.TenantID, req.ID)

	s.logger.Info(ctx, "Template updated successfully", logiface.Int64("template_id", req.ID), logiface.Int64("tenant_id", req.TenantID), logiface.String("update_mode", req.UpdateMode))
	return response, nil
}

// DeleteTemplate 删除模板
func (s *TemplateApplicationService) DeleteTemplate(ctx context.Context, req *dto.DeleteTemplateRequest) (*dto.DeleteTemplateResponse, error) {
	s.logger.Info(ctx, "Deleting template", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.TemplateID))

	// 获取模板
	template, err := s.templateRepo.Get(ctx, req.TenantID, req.TemplateID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for deletion", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 删除模板
	if err := s.templateRepo.Delete(ctx, req.TenantID, req.TemplateID); err != nil {
		s.logger.Error(ctx, "Failed to delete template", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("delete_template", err.Error())
	}

	// 构建响应
	response := &dto.DeleteTemplateResponse{
		TemplateID: template.ID,
		TenantID:   int64(template.TenantID),
		DeletedAt:  template.DeletedAt,
	}

	s.logger.Info(ctx, "Template deleted successfully", logiface.Int64("template_id", req.TemplateID), logiface.Int64("tenant_id", req.TenantID))
	return response, nil
}

// CloneTemplate 克隆模板
func (s *TemplateApplicationService) CloneTemplate(ctx context.Context, req *dto.CloneTemplateRequest) (*dto.CloneTemplateResponse, error) {
	s.logger.Info(ctx, "Cloning template", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.TemplateID))

	// 获取源模板
	sourceTemplate, err := s.templateRepo.Get(ctx, req.TenantID, req.TemplateID)
	if err != nil {
		s.logger.Info(ctx, "Source template not found for cloning", logiface.Int64("tenant_id", req.TenantID), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 检查名称是否已存在
	exists, err := s.templateRepo.CheckNameExists(ctx, req.TenantID, req.Name, 0)
	if err != nil {
		s.logger.Error(ctx, "Failed to check template name", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewSystemError("check_template_name", err.Error())
	}

	if exists {
		s.logger.Info(ctx, "Template name already exists for cloning", logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewTemplateNameExistsError(req.Name)
	}

	// 创建新模板
	newTemplate := &entity.EmailTemplate{
		TenantID:           int64(req.TenantID),
		TemplateCode:       sourceTemplate.TemplateCode + "_copy",
		AccountID:          sourceTemplate.AccountID,
		Name:               req.Name,
		Type:               sourceTemplate.Type,
		Status:             entity.TemplateStatusDraft, // 新克隆的模板默认为草稿状态
		Subject:            sourceTemplate.Subject,
		HTMLContent:        sourceTemplate.HTMLContent,
		PlainTextContent:   sourceTemplate.PlainTextContent,
		Variables:          make(map[string]entity.TemplateVariable),
		RateLimitPerMinute: sourceTemplate.RateLimitPerMinute,
		RateLimitPerHour:   sourceTemplate.RateLimitPerHour,
		RateLimitPerDay:    sourceTemplate.RateLimitPerDay,
		ThumbnailURL:       sourceTemplate.ThumbnailURL,
		IsResponsive:       sourceTemplate.IsResponsive,
		Description:        sourceTemplate.Description,
		Version:            0, // 新模板版本为0（当前版本）
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// 复制变量
	for key, variable := range sourceTemplate.Variables {
		newTemplate.Variables[key] = entity.TemplateVariable{
			Label:       variable.Label,
			Type:        variable.Type,
			Required:    variable.Required,
			Description: variable.Description,
		}
	}

	// 验证模板
	if err := newTemplate.Validate(); err != nil {
		s.logger.Warn(ctx, "Template validation failed", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
	}

	// 保存模板
	if err := s.templateRepo.Create(ctx, newTemplate); err != nil {
		s.logger.Error(ctx, "Failed to create cloned template", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewSystemError("create_cloned_template", err.Error())
	}

	// 构建响应
	response := &dto.CloneTemplateResponse{}
	response.FromEntity(newTemplate)

	s.logger.Info(ctx, "Template cloned successfully", logiface.Int64("source_id", req.TemplateID), logiface.Int64("new_id", newTemplate.ID), logiface.Int64("tenant_id", req.TenantID))
	return response, nil
}

// renderTemplate 渲染模板
func (s *TemplateApplicationService) renderTemplate(content string, variables map[string]string) string {
	// 简单的变量替换实现
	result := content
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}
	return result
}

// ========== 带权限检查的方法 ==========

// CreateTemplateWithPermission 创建模板（带权限检查）
func (s *TemplateApplicationService) CreateTemplateWithPermission(ctx context.Context, req *dto.CreateTemplateRequest, userID int64) (*dto.CreateTemplateResponse, error) {
	// 从上下文获取租户ID，如果请求中没有设置
	if req.TenantID == 0 {
		tenantID, ok := usercontext.GetTenantID(ctx)
		if !ok || tenantID == 0 {
			s.logger.Error(ctx, "Tenant ID not found in context", logiface.String("name", req.Name))
			return nil, emailErrors.NewEmailError(emailErrors.CodeTenantNotFound, "租户信息未找到，请重新登录")
		}
		req.TenantID = tenantID
	}

	// 从上下文获取用户ID，如果请求中没有设置
	if req.CreatedBy == 0 {
		if userID == 0 {
			userIDFromCtx, ok := usercontext.GetUserID(ctx)
			if !ok || userIDFromCtx == 0 {
				s.logger.Error(ctx, "User ID not found in context", logiface.String("name", req.Name))
				return nil, emailErrors.NewSystemError("get_user_id", "用户信息未找到，请重新登录")
			}
			userID = userIDFromCtx
		}
		req.CreatedBy = userID
	}

	s.logger.Info(ctx, "Creating template with permission check",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("name", req.Name),
		logiface.Int64("user_id", userID))

	// 检查模板名称是否已存在
	exists, err := s.templateRepo.CheckNameExists(ctx, req.TenantID, req.Name, 0)
	if err != nil {
		s.logger.Error(ctx, "Failed to check template name", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewSystemError("check_template_name", err.Error())
	}

	if exists {
		s.logger.Info(ctx, "Template name already exists with permission check", logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
		return nil, emailErrors.NewTemplateNameExistsError(req.Name)
	}

	// 创建模板实体
	template, err := req.ToEntity()
	if err != nil {
		return nil, emailErrors.NewSystemError("create_template_entity", err.Error())
	}

	// 设置创建时间和版本
	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()
	template.Version = 1

	// 保存模板（带权限检查）
	if err := s.templateRepo.CreateWithPermission(ctx, template, userID); err != nil {
		s.logger.Error(ctx, "Failed to create template", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return nil, emailErrors.NewSystemError("create_template", err.Error())
	}

	s.logger.Info(ctx, "Template created successfully", logiface.Int64("template_id", template.ID))

	// 构建响应
	response := &dto.CreateTemplateResponse{}
	response.FromEntity(template)

	return response, nil
}

// GetTemplateWithPermission 获取模板（带权限检查）
func (s *TemplateApplicationService) GetTemplateWithPermission(ctx context.Context, req *dto.GetTemplateRequest, userID int64) (*dto.GetTemplateResponse, error) {
	tenantId, _ := usercontext.GetTenantID(ctx)
	s.logger.Info(ctx, "Getting template with permission check",
		logiface.Int64("tenant_id", tenantId),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("user_id", userID))

	// 获取模板（带权限检查）
	template, err := s.templateRepo.GetWithPermission(ctx, tenantId, req.TemplateID, userID)
	if err != nil {
		s.logger.Info(ctx, "Template not found with permission check", logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 构建响应
	response := &dto.GetTemplateResponse{}
	response.FromEntity(template)

	return response, nil
}

// UpdateTemplateWithPermission 更新模板（带权限检查）
func (s *TemplateApplicationService) UpdateTemplateWithPermission(ctx context.Context, req *dto.UpdateTemplateRequest, userID int64) (*dto.UpdateTemplateResponse, error) {
	s.logger.Info(ctx, "Updating template with permission check",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_id", req.ID),
		logiface.Int64("user_id", userID))

	// 获取现有模板（带权限检查）
	template, err := s.templateRepo.GetWithPermission(ctx, req.TenantID, req.ID, userID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for update with permission", logiface.Int64("template_id", req.ID))
		return nil, emailErrors.NewTemplateNotFoundError(req.ID)
	}

	// 检查名称是否已被其他模板使用
	if req.Name != "" && req.Name != template.Name {
		exists, err := s.templateRepo.CheckNameExists(ctx, req.TenantID, req.Name, req.ID)
		if err != nil {
			s.logger.Error(ctx, "Failed to check template name", logiface.Error(err))
			return nil, emailErrors.NewSystemError("check_template_name", err.Error())
		}
		if exists {
			s.logger.Info(ctx, "Template name already exists for update", logiface.Int64("tenant_id", req.TenantID), logiface.String("name", req.Name))
			return nil, emailErrors.NewTemplateNameExistsError(req.Name)
		}
		template.Name = req.Name
	}

	// 更新其他字段
	if req.Subject != "" {
		template.Subject = req.Subject
	}
	if req.HTMLContent != "" {
		template.HTMLContent = req.HTMLContent
	}
	if req.PlainTextContent != "" {
		template.PlainTextContent = req.PlainTextContent
	}
	if req.Description != "" {
		template.Description = req.Description
	}
	if req.Status != 0 {
		template.Status = entity.TemplateStatus(req.Status)
	}
	if req.AccountID != 0 {
		template.AccountID = req.AccountID
	}
	if req.Variables != nil {
		// 转换变量
		entityVariables := make(map[string]entity.TemplateVariable)
		for key, variable := range req.Variables {
			entityVariables[key] = entity.TemplateVariable{
				Label:       variable.Label,
				Type:        variable.Type,
				Required:    variable.Required,
				Description: variable.Description,
			}
		}
		template.Variables = entityVariables
	}
	if req.ThumbnailURL != "" {
		template.ThumbnailURL = req.ThumbnailURL
	}

	// 更新时间和版本
	template.UpdatedAt = time.Now()
	template.Version++

	// 保存更新（带权限检查）
	if err := s.templateRepo.UpdateWithPermission(ctx, template, userID); err != nil {
		s.logger.Error(ctx, "Failed to update template", logiface.Error(err), logiface.Int64("template_id", req.ID))
		return nil, emailErrors.NewSystemError("update_template", err.Error())
	}

	s.logger.Info(ctx, "Template updated successfully", logiface.Int64("template_id", template.ID))

	// 构建响应
	response := &dto.UpdateTemplateResponse{}
	response.FromEntity(template)

	// 清除变量缓存
	s.ClearVariableCache(req.TenantID, req.ID)

	return response, nil
}

// DeleteTemplateWithPermission 删除模板（带权限检查）
func (s *TemplateApplicationService) DeleteTemplateWithPermission(ctx context.Context, req *dto.DeleteTemplateRequest, userID int64) (*dto.DeleteTemplateResponse, error) {
	s.logger.Info(ctx, "Deleting template with permission check",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("user_id", userID))

	// 删除模板（带权限检查）
	if err := s.templateRepo.DeleteWithPermission(ctx, req.TenantID, req.TemplateID, userID); err != nil {
		s.logger.Error(ctx, "Failed to delete template", logiface.Error(err), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("delete_template", err.Error())
	}

	s.logger.Info(ctx, "Template deleted successfully", logiface.Int64("template_id", req.TemplateID))

	return &dto.DeleteTemplateResponse{
		TemplateID: req.TemplateID,
		TenantID:   req.TenantID,
		DeletedAt:  &time.Time{},
	}, nil
}

// ListTemplatesWithPermission 获取模板列表（带权限检查）
func (s *TemplateApplicationService) ListTemplatesWithPermission(ctx context.Context, req *dto.ListTemplateRequest, userID int64) (*dto.ListTemplateResponse, error) {
	tenantId, _ := usercontext.GetTenantID(ctx)
	s.logger.Info(ctx, "Listing templates with permission check",
		logiface.Int64("tenant_id", tenantId),
		logiface.Int64("user_id", userID))

	// 构建过滤条件
	filter := &entity.TemplateFilter{
		Type:      req.Type,
		Status:    req.Status,
		Search:    req.Search,
		SortBy:    req.SortBy,
		SortOrder: req.SortOrder,
		Page:      req.Page,
		PageSize:  req.PageSize,
	}

	// 获取模板列表（带权限检查）
	templates, total, err := s.templateRepo.ListWithPermission(ctx, tenantId, filter, userID)
	if err != nil {
		s.logger.Error(ctx, "Failed to list templates", logiface.Error(err), logiface.Int64("tenant_id", tenantId))
		return nil, emailErrors.NewSystemError("get_template_list", err.Error())
	}

	// 构建响应
	response := &dto.ListTemplateResponse{}

	// 转换模板列表
	templateResponses := make([]dto.TemplateResponse, len(templates))
	for i, template := range templates {
		templateResponses[i].FromEntity(template)
	}

	// 这里需要根据实际的ListTemplateResponse结构来设置响应
	// 由于当前的ListTemplateResponse只是继承了TemplateResponse，可能需要调整
	s.logger.Info(ctx, "Templates listed successfully",
		logiface.Int64("total", total),
		logiface.Int("count", len(templates)))

	return response, nil
}

// PublishTemplate 发布模板
func (s *TemplateApplicationService) PublishTemplate(ctx context.Context, req *dto.PublishTemplateRequest) (*dto.PublishTemplateResponse, error) {
	s.logger.Info(ctx, "Publishing template",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("user_id", req.UserID))

	// 获取模板（带权限检查）
	template, err := s.templateRepo.GetWithPermission(ctx, req.TenantID, req.TemplateID, req.UserID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for publishing", logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 检查是否可以发布
	if !template.CanPublish() {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidStatus, "模板当前状态无法发布")
	}

	// 发布模板
	if err := template.Publish(req.UserID); err != nil {
		return nil, emailErrors.NewSystemError("publish_template", err.Error())
	}

	// 保存更新
	if err := s.templateRepo.UpdateWithPermission(ctx, template, req.UserID); err != nil {
		s.logger.Error(ctx, "Failed to save published template", logiface.Error(err), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("save_template", err.Error())
	}

	s.logger.Info(ctx, "Template published successfully", logiface.Int64("template_id", req.TemplateID))

	return &dto.PublishTemplateResponse{
		Success:     true,
		Message:     "模板发布成功",
		PublishedAt: *template.PublishedAt,
	}, nil
}

// DisableTemplate 停用模板
func (s *TemplateApplicationService) DisableTemplate(ctx context.Context, req *dto.DisableTemplateRequest) (*dto.DisableTemplateResponse, error) {
	s.logger.Info(ctx, "Disabling template",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("user_id", req.UserID))

	// 获取模板（带权限检查）
	template, err := s.templateRepo.GetWithPermission(ctx, req.TenantID, req.TemplateID, req.UserID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for disabling", logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 检查是否可以停用
	if !template.CanDisable() {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidStatus, "模板当前状态无法停用")
	}

	// 停用模板
	if err := template.Disable(req.UserID); err != nil {
		return nil, emailErrors.NewSystemError("disable_template", err.Error())
	}

	// 保存更新
	if err := s.templateRepo.UpdateWithPermission(ctx, template, req.UserID); err != nil {
		s.logger.Error(ctx, "Failed to save disabled template", logiface.Error(err), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("save_template", err.Error())
	}

	s.logger.Info(ctx, "Template disabled successfully", logiface.Int64("template_id", req.TemplateID))

	return &dto.DisableTemplateResponse{
		Success: true,
		Message: "模板停用成功",
	}, nil
}

// EnableTemplate 启用模板
func (s *TemplateApplicationService) EnableTemplate(ctx context.Context, req *dto.EnableTemplateRequest) (*dto.EnableTemplateResponse, error) {
	s.logger.Info(ctx, "Enabling template",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("user_id", req.UserID))

	// 获取模板（带权限检查）
	template, err := s.templateRepo.GetWithPermission(ctx, req.TenantID, req.TemplateID, req.UserID)
	if err != nil {
		s.logger.Info(ctx, "Template not found for enabling", logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(req.TemplateID)
	}

	// 启用模板
	if err := template.Enable(req.UserID); err != nil {
		return nil, emailErrors.NewSystemError("enable_template", err.Error())
	}

	// 保存更新
	if err := s.templateRepo.UpdateWithPermission(ctx, template, req.UserID); err != nil {
		s.logger.Error(ctx, "Failed to save enabled template", logiface.Error(err), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("save_template", err.Error())
	}

	s.logger.Info(ctx, "Template enabled successfully", logiface.Int64("template_id", req.TemplateID))

	return &dto.EnableTemplateResponse{
		Success: true,
		Message: "模板启用成功",
	}, nil
}

// GetVersions 获取模板版本历史
func (s *TemplateApplicationService) GetVersions(ctx context.Context, req *dto.GetVersionsRequest) (*dto.GetVersionsResponse, error) {
	s.logger.Info(ctx, "Getting template versions",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("template_code", req.TemplateCode),
		logiface.Int64("user_id", req.UserID))

	// TODO: 需要在仓储层实现GetVersionsByCode方法
	// versions, err := s.templateRepo.GetVersionsByCode(ctx, req.TenantID, req.TemplateCode)
	// if err != nil {
	// 	s.logger.Error(ctx, "Failed to get template versions", logiface.Error(err), logiface.String("template_code", req.TemplateCode))
	// 	return nil, fmt.Errorf("获取版本历史失败: %w", err)
	// }

	// 暂时返回空列表
	response := &dto.GetVersionsResponse{
		Versions: make([]dto.TemplateResponse, 0),
		Total:    0,
	}

	s.logger.Info(ctx, "Template versions retrieved successfully",
		logiface.String("template_code", req.TemplateCode),
		logiface.Int("count", 0))

	return response, nil
}

// RestoreVersion 恢复到指定版本
func (s *TemplateApplicationService) RestoreVersion(ctx context.Context, req *dto.RestoreVersionRequest) (*dto.RestoreVersionResponse, error) {
	s.logger.Info(ctx, "Restoring template version",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("template_code", req.TemplateCode),
		logiface.Int64("version", req.Version),
		logiface.Int64("user_id", req.UserID))

	// TODO: 需要在仓储层实现相应的方法
	// 获取当前版本模板
	// currentTemplate, err := s.templateRepo.GetCurrentByCode(ctx, req.TenantID, req.TemplateCode)
	// if err != nil {
	// 	s.logger.Error(ctx, "Failed to get current template", logiface.Error(err), logiface.String("template_code", req.TemplateCode))
	// 	return nil, fmt.Errorf("获取当前模板失败: %w", err)
	// }

	// 获取要恢复的历史版本
	// historicalTemplate, err := s.templateRepo.GetByCodeAndVersion(ctx, req.TenantID, req.TemplateCode, req.Version)
	// if err != nil {
	// 	s.logger.Error(ctx, "Failed to get historical template", logiface.Error(err),
	// 		logiface.String("template_code", req.TemplateCode),
	// 		logiface.Int64("version", req.Version))
	// 	return nil, fmt.Errorf("获取历史版本失败: %w", err)
	// }

	// 暂时返回功能未实现错误
	return nil, emailErrors.NewSystemError("restore_version", "功能尚未完全实现")
}

// GetTemplateVariables 获取模板变量列表
func (s *TemplateApplicationService) GetTemplateVariables(ctx context.Context, req *dto.GetTemplateVariablesRequest) (dto.GetTemplateVariablesResponse, error) {
	// 从上下文获取租户ID
	tenantID, ok := usercontext.GetTenantID(ctx)
	if !ok || tenantID == 0 {
		s.logger.Error(ctx, "Tenant ID not found in context", logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewEmailError(emailErrors.CodeTenantNotFound, "租户信息未找到，请重新登录")
	}

	// 如果templateId为0，只返回系统变量
	if req.TemplateID == 0 {
		s.logger.Info(ctx, "Getting system variables only", logiface.Int64("tenant_id", tenantID))
		systemVariables := s.getSystemVariables()
		return systemVariables, nil
	}

	// 参数验证
	if req.TemplateID < 0 {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateNotFound, "模板ID无效")
	}

	s.logger.Info(ctx, "Getting template variables", logiface.Int64("tenant_id", tenantID), logiface.Int64("template_id", req.TemplateID))

	// 检查缓存
	cacheKey := fmt.Sprintf("%d_%d", tenantID, req.TemplateID)
	if entry, exists := s.variableCache[cacheKey]; exists && time.Now().Before(entry.expiresAt) {
		s.logger.Debug(ctx, "Template variables retrieved from cache",
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", req.TemplateID))
		return entry.variables, nil
	}

	// 获取模板
	template, err := s.templateRepo.Get(ctx, tenantID, req.TemplateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get template", logiface.Error(err), logiface.Int64("tenant_id", tenantID), logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewTemplateNotFoundError(fmt.Sprintf("%d", req.TemplateID))
	}

	// 检查模板状态
	if template.Status == entity.TemplateStatusDeleted {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateDeleted, "模板已删除")
	}

	// 构建变量列表
	variables := make([]dto.TemplateVariableInfo, 0)

	// 1. 添加系统公共变量
	systemVariables := s.getSystemVariables()
	variables = append(variables, systemVariables...)

	// 2. 添加模板自定义变量
	if len(template.Variables) > 0 {
		for name, variable := range template.Variables {
			// 跳过空变量名
			if name == "" {
				continue
			}

			// 使用存储的label字段，如果没有则生成默认label
			label := variable.Label
			if label == "" {
				label = s.generateVariableLabel(name)
			}

			variables = append(variables, dto.TemplateVariableInfo{
				Name:         name,
				Label:        label,
				Type:         variable.Type,
				Description:  variable.Description,
				Required:     variable.Required,
				DefaultValue: "", // 模板变量不设置默认值
				Category:     "template",
			})
		}
	}

	// 缓存结果（5分钟过期）
	s.variableCache[cacheKey] = cacheEntry{
		variables: variables,
		expiresAt: time.Now().Add(5 * time.Minute),
	}

	s.logger.Info(ctx, "Template variables retrieved successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int("total_variables", len(variables)))

	return variables, nil
}

// ClearVariableCache 清除变量缓存
func (s *TemplateApplicationService) ClearVariableCache(tenantID, templateID int64) {
	cacheKey := fmt.Sprintf("%d_%d", tenantID, templateID)
	delete(s.variableCache, cacheKey)
	s.logger.Debug(context.Background(), "Variable cache cleared",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))
}

// getSystemVariables 获取系统公共变量
func (s *TemplateApplicationService) getSystemVariables() []dto.TemplateVariableInfo {
	return []dto.TemplateVariableInfo{
		{
			Name:         "company_name",
			Label:        "公司名称",
			Type:         "string",
			Description:  "租户公司名称",
			Required:     false,
			DefaultValue: "", // 从租户信息获取
			Category:     "system",
		},
		{
			Name:         "current_date",
			Label:        "当前日期",
			Type:         "string",
			Description:  "当前日期，格式：YYYY-MM-DD",
			Required:     false,
			DefaultValue: "", // 动态生成
			Category:     "system",
		},
		{
			Name:         "current_time",
			Label:        "当前时间",
			Type:         "string",
			Description:  "当前时间，格式：HH:MM:SS",
			Required:     false,
			DefaultValue: "", // 动态生成
			Category:     "system",
		},
		{
			Name:         "support_email",
			Label:        "客服邮箱",
			Type:         "string",
			Description:  "客服联系邮箱",
			Required:     false,
			DefaultValue: "", // 从租户配置获取
			Category:     "system",
		},
	}
}

// generateVariableLabel 生成变量显示名称
func (s *TemplateApplicationService) generateVariableLabel(name string) string {
	// 系统变量映射
	systemVariableLabels := map[string]string{
		"company_name":  "公司名称",
		"current_date":  "当前日期",
		"current_time":  "当前时间",
		"support_email": "客服邮箱",
	}

	// 如果是系统变量，返回预定义的标签
	if label, exists := systemVariableLabels[name]; exists {
		return label
	}

	// 对于其他变量，使用简单的转换规则
	// 将下划线替换为空格，首字母大写
	label := strings.ReplaceAll(name, "_", " ")
	words := strings.Fields(label)
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}

	return strings.Join(words, " ")
}
