package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"platforms-email/internal/application/email/dto"
	"platforms-email/internal/domain/email/entity"
	"platforms-email/internal/domain/email/repository"
	"platforms-email/internal/domain/email/value_object"
	emailErrors "platforms-email/internal/domain/errors"
	templateEntity "platforms-email/internal/domain/template/entity"
	templateRepo "platforms-email/internal/domain/template/repository"
	"platforms-email/internal/infrastructure/external"
	"platforms-pkg/common"
	"platforms-pkg/logiface"
	"platforms-pkg/usercontext"
)

// EmailApplicationService 邮件应用服务
type EmailApplicationService struct {
	emailRepo          repository.Repository
	templateRepo       templateRepo.Repository
	emailAccountRepo   repository.EmailAccountRepository
	emailSenderFactory *external.EmailSenderFactory
	userClient         *external.UserServiceClient
	entityFactory      *entity.EntityFactory
	logger             logiface.Logger
}

// NewEmailApplicationService 创建邮件应用服务
func NewEmailApplicationService(
	emailRepo repository.Repository,
	templateRepo templateRepo.Repository,
	emailAccountRepo repository.EmailAccountRepository,
	emailSenderFactory *external.EmailSenderFactory,
	userClient *external.UserServiceClient,
	entityFactory *entity.EntityFactory,
	logger logiface.Logger,
) *EmailApplicationService {
	return &EmailApplicationService{
		emailRepo:          emailRepo,
		templateRepo:       templateRepo,
		emailAccountRepo:   emailAccountRepo,
		emailSenderFactory: emailSenderFactory,
		userClient:         userClient,
		entityFactory:      entityFactory,
		logger:             logger,
	}
}

// SendTemplateEmail 发送模板邮件
func (s *EmailApplicationService) SendTemplateEmail(ctx context.Context, tenantID int64, templateCode string, toAddresses []string, variables map[string]interface{}) (string, error) {
	// 构建租户ID列表，优先查询租户自己的配置，如果没有则使用系统配置
	var tenantIDs []int64
	if tenantID == common.SystemTenantID {
		// 如果参数就是系统租户ID，只传一个
		tenantIDs = []int64{tenantID}
	} else {
		// 否则传两个：租户ID和系统租户ID
		tenantIDs = []int64{tenantID, common.SystemTenantID}
	}

	// 创建过滤器，支持多个租户ID查询和精确的模板代码查询
	filter := &templateEntity.TemplateFilter{
		TenantIDs:    tenantIDs,
		TemplateCode: templateCode,
	}

	// 获取模板列表
	templates, _, err := s.templateRepo.List(ctx, tenantID, filter)
	if err != nil {
		s.logger.Error(ctx, "Failed to get templates",
			logiface.Error(err),
			logiface.String("template_code", templateCode),
			logiface.Int64("tenant_id", tenantID))
		return "", emailErrors.NewTemplateNotFoundError(templateCode)
	}

	// 如果没有找到任何模板
	if len(templates) == 0 {
		s.logger.Error(ctx, "Template not found",
			logiface.String("template_code", templateCode),
			logiface.Int64("tenant_id", tenantID))
		return "", emailErrors.NewTemplateNotFoundError(templateCode)
	}

	// 优先选择租户自己的模板，如果没有则使用系统模板
	var template *templateEntity.EmailTemplate
	for _, t := range templates {
		if t.TenantID == tenantID {
			// 找到租户自己的模板，优先使用
			template = t
			break
		}
	}

	// 如果没有找到租户自己的模板，使用第一个可用的模板（通常是系统模板）
	if template == nil {
		template = templates[0]
		s.logger.Info(ctx, "Using system template as fallback",
			logiface.String("template_code", templateCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_tenant_id", template.TenantID))
	}

	// 合并系统变量到用户变量中
	mergedVariables := s.mergeSystemVariables(ctx, variables)

	// 渲染模板内容
	htmlContent, textContent, subject, err := s.renderTemplate(template, mergedVariables)
	if err != nil {
		s.logger.Error(ctx, "Failed to render template",
			logiface.Error(err),
			logiface.String("template_code", templateCode))
		return "", emailErrors.NewSystemError("render_template", err.Error())
	}

	// 使用实体工厂创建邮件消息 - 自动生成分布式ID
	message, err := s.entityFactory.NewTemplateEmailMessage(
		ctx,
		tenantID,
		template.ID,
		toAddresses[0], // 暂时只处理第一个收件人
		variables,
	)
	if err != nil {
		s.logger.Error(ctx, "Failed to create email message",
			logiface.Error(err),
			logiface.String("to_address", toAddresses[0]))
		return "", emailErrors.NewSystemError("create_email_message", err.Error())
	}

	// 设置邮件内容
	message.HTMLContent = htmlContent
	message.TextContent = textContent
	message.Subject = subject
	if len(toAddresses) > 1 {
		message.CcAddresses = toAddresses[1:]
	}

	// 获取邮件账户信息
	account, err := s.emailAccountRepo.FindByID(ctx, template.AccountID)
	if err != nil {
		s.logger.Info(ctx, "Email account not found",
			logiface.Int64("account_id", template.AccountID))
		return "", emailErrors.NewAccountNotFoundError(template.AccountID)
	}

	// 设置发件人信息
	message.FromAddress = account.FromAddress

	// 保存邮件消息
	if err := s.emailRepo.Save(ctx, message); err != nil {
		s.logger.Error(ctx, "Failed to save email message",
			logiface.Error(err),
			logiface.Int64("email_id", message.EmailID))
		return "", emailErrors.NewSystemError("save_email_message", "邮件保存失败，请稍后重试")
	}

	// 发送邮件
	if err := s.emailSenderFactory.SendEmail(ctx, account, message); err != nil {
		s.logger.Error(ctx, "Failed to send email",
			logiface.Error(err),
			logiface.Int64("email_id", message.EmailID),
			logiface.String("to", message.ToAddress))

		// 更新邮件状态为失败
		message.Status = "failed"
		s.emailRepo.UpdateStatus(ctx, message.EmailID, "failed")

		return "", emailErrors.NewEmailSendFailedError(err.Error())
	}

	// 更新邮件状态为已发送
	message.Status = "sent"
	s.emailRepo.UpdateStatus(ctx, message.EmailID, "sent")

	s.logger.Info(ctx, "Template email sent successfully",
		logiface.Int64("email_id", message.EmailID),
		logiface.String("to", message.ToAddress),
		logiface.String("subject", message.Subject),
		logiface.String("template_code", templateCode),
		logiface.Int64("template_tenant_id", template.TenantID))

	return fmt.Sprintf("%d", message.EmailID), nil
}

// SendEmail 直接发送邮件
func (s *EmailApplicationService) SendEmail(ctx context.Context, tenantID int64, request *dto.SendEmailRequest) (*dto.SendEmailResponse, error) {
	// 使用实体工厂创建邮件消息 - 自动生成分布式ID
	message, err := s.entityFactory.NewEmailMessage(ctx, tenantID, request.ToAddress, request.Subject)
	if err != nil {
		s.logger.Error(ctx, "Failed to create email message",
			logiface.Error(err),
			logiface.String("to_address", request.ToAddress))
		return nil, emailErrors.NewSystemError("create_email_message", "邮件创建失败")
	}

	// 设置邮件内容
	message.HTMLContent = request.HTMLContent
	message.TextContent = request.TextContent
	message.FromAddress = request.FromAddress
	if len(request.CcAddresses) > 0 {
		message.CcAddresses = request.CcAddresses
	}
	if len(request.BccAddresses) > 0 {
		message.BccAddresses = request.BccAddresses
	}

	// 保存邮件消息
	if err := s.emailRepo.Save(ctx, message); err != nil {
		s.logger.Error(ctx, "Failed to save email message",
			logiface.Error(err),
			logiface.Int64("email_id", message.EmailID))
		return nil, emailErrors.NewSystemError("save_email_message", "邮件保存失败，请稍后重试")
	}

	// 获取邮件账户信息
	account, err := s.emailAccountRepo.FindByID(ctx, request.AccountID)
	if err != nil {
		s.logger.Info(ctx, "Email account not found",
			logiface.Int64("account_id", request.AccountID))
		return nil, emailErrors.NewAccountNotFoundError(request.AccountID)
	}

	// 发送邮件
	if err := s.emailSenderFactory.SendEmail(ctx, account, message); err != nil {
		s.logger.Error(ctx, "Failed to send email",
			logiface.Error(err),
			logiface.Int64("email_id", message.EmailID),
			logiface.String("to", message.ToAddress))

		// 更新邮件状态为失败
		message.Status = "failed"
		s.emailRepo.UpdateStatus(ctx, message.EmailID, "failed")

		return nil, emailErrors.NewEmailSendFailedError(err.Error())
	}

	// 更新邮件状态为已发送
	message.Status = "sent"
	s.emailRepo.UpdateStatus(ctx, message.EmailID, "sent")

	s.logger.Info(ctx, "Email sent successfully",
		logiface.Int64("email_id", message.EmailID),
		logiface.String("to", message.ToAddress),
		logiface.String("subject", message.Subject))

	return &dto.SendEmailResponse{
		EmailID: message.EmailID,
		Status:  message.Status,
	}, nil
}

// GetEmailStatus 获取邮件状态
func (s *EmailApplicationService) GetEmailStatus(ctx context.Context, emailID int64) (*dto.EmailStatusResponse, error) {
	message, err := s.emailRepo.FindByEmailID(ctx, emailID)
	if err != nil {
		s.logger.Info(ctx, "Email not found",
			logiface.Int64("email_id", emailID))
		return nil, emailErrors.NewEmailNotFoundError(emailID)
	}

	if message == nil {
		return nil, emailErrors.NewEmailNotFoundError(emailID)
	}

	return &dto.EmailStatusResponse{
		EmailID:    message.EmailID,
		Status:     message.Status,
		SentAt:     message.SentAt,
		ErrorMsg:   message.ErrorMsg,
		RetryCount: int(message.RetryCount),
	}, nil
}

// GetEmailStatistics 获取邮件统计信息
func (s *EmailApplicationService) GetEmailStatistics(ctx context.Context, params *value_object.StatisticsQueryParams) (*dto.EmailStatisticsResponse, error) {
	s.logger.Info(ctx, "Getting email statistics",
		logiface.Int64("tenant_id", params.TenantID))

	statistics, err := s.emailRepo.GetStatistics(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "Failed to get email statistics",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, emailErrors.NewSystemError("get_email_statistics", err.Error())
	}

	return &dto.EmailStatisticsResponse{
		TenantID:     statistics.TenantID,
		TotalSent:    statistics.TotalSent,
		TotalFailed:  statistics.TotalFailed,
		TotalPending: statistics.TotalPending,
		LastSentAt:   statistics.LastSentAt,
		LastFailedAt: statistics.LastFailedAt,
		StatusCounts: statistics.StatusCounts,
		DailyStats:   statistics.DailyStats,
		MonthlyStats: statistics.MonthlyStats,
	}, nil
}

// CheckTemplateExists 检查模板是否存在
func (s *EmailApplicationService) CheckTemplateExists(ctx context.Context, tenantID int64, templateCode string) (bool, error) {
	// 创建过滤器，支持多个租户ID查询和精确的模板代码查询
	filter := &templateEntity.TemplateFilter{
		TenantIDs:    []int64{tenantID, common.SystemTenantID},
		TemplateCode: templateCode,
	}

	// 获取模板列表
	templates, _, err := s.templateRepo.List(ctx, tenantID, filter)
	if err != nil {
		return false, err
	}

	// 由于使用了精确的 TemplateCode 查询，如果有结果就说明模板存在
	return len(templates) > 0, nil
}

// renderTemplate 渲染模板内容
func (s *EmailApplicationService) renderTemplate(tmpl *templateEntity.EmailTemplate, variables map[string]interface{}) (string, string, string, error) {
	// 将 interface{} 类型的变量转换为 string 类型
	stringVariables := make(map[string]string)
	for k, v := range variables {
		if v != nil {
			stringVariables[k] = fmt.Sprintf("%v", v)
		} else {
			stringVariables[k] = ""
		}
	}

	// 使用简单的变量替换实现，支持 {{variable_name}} 格式
	subject := s.renderTemplateContent(tmpl.Subject, stringVariables)
	htmlContent := s.renderTemplateContent(tmpl.HTMLContent, stringVariables)
	textContent := s.renderTemplateContent(tmpl.PlainTextContent, stringVariables)

	return subject, htmlContent, textContent, nil
}

// renderTemplateContent 渲染模板内容（使用 {{variable_name}} 格式）
func (s *EmailApplicationService) renderTemplateContent(content string, variables map[string]string) string {
	// 简单的变量替换实现
	result := content
	for key, value := range variables {
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, value)
	}
	return result
}

// mergeSystemVariables 融合系统变量到用户变量中
func (s *EmailApplicationService) mergeSystemVariables(ctx context.Context, userVariables map[string]interface{}) map[string]interface{} {
	// 创建合并后的变量映射
	mergedVariables := make(map[string]interface{})

	// 首先复制用户变量
	for k, v := range userVariables {
		mergedVariables[k] = v
	}

	// 获取当前时间
	now := time.Now()

	// 添加系统变量
	mergedVariables["current_date"] = now.Format("2006-01-02")
	mergedVariables["current_time"] = now.Format("15:04:05")

	// 尝试从用户上下文中获取租户ID
	var tenantID int64
	if tenantInfo, ok := usercontext.GetTenantInfo(ctx); ok {
		tenantID = tenantInfo.TenantID
		mergedVariables["company_name"] = tenantInfo.TenantName
	} else {
		// 如果没有租户信息，设置默认值
		mergedVariables["company_name"] = ""
		mergedVariables["support_email"] = ""
		s.logger.Debug(ctx, "No tenant info in context, using default values")
		return mergedVariables
	}

	// 通过gRPC调用获取租户详细信息
	if s.userClient != nil && tenantID > 0 {
		tenantDetailResp, err := s.userClient.GetTenantDetailInfo(ctx, tenantID)
		if err != nil {
			s.logger.Warn(ctx, "Failed to get tenant detail info via gRPC, using default values",
				logiface.Error(err),
				logiface.Int64("tenant_id", tenantID))
			mergedVariables["support_email"] = ""
			mergedVariables["service_email"] = ""
			mergedVariables["contact_person"] = ""
			mergedVariables["contact_phone"] = ""
			mergedVariables["website"] = ""
			mergedVariables["address"] = ""
		} else if tenantDetailResp != nil && tenantDetailResp.Data != nil {
			// 使用租户详细信息
			tenantDetail := tenantDetailResp.Data
			mergedVariables["support_email"] = tenantDetail.ServiceEmail
			mergedVariables["service_email"] = tenantDetail.ServiceEmail
			mergedVariables["contact_person"] = tenantDetail.ContactPerson
			mergedVariables["contact_phone"] = tenantDetail.ContactPhone
			mergedVariables["website"] = tenantDetail.Website
			mergedVariables["address"] = tenantDetail.Address
			mergedVariables["system_name"] = tenantDetail.SystemName
			mergedVariables["tenant_type"] = tenantDetail.Type

			s.logger.Debug(ctx, "Tenant detail info retrieved via gRPC",
				logiface.Int64("tenant_id", tenantID),
				logiface.String("service_email", tenantDetail.ServiceEmail),
				logiface.String("system_name", tenantDetail.SystemName))
		}
	} else {
		// 如果没有用户服务客户端或租户ID，设置默认值
		mergedVariables["support_email"] = ""
		mergedVariables["service_email"] = ""
		mergedVariables["contact_person"] = ""
		mergedVariables["contact_phone"] = ""
		mergedVariables["website"] = ""
		mergedVariables["address"] = ""
	}

	s.logger.Debug(ctx, "System variables merged",
		logiface.String("current_date", mergedVariables["current_date"].(string)),
		logiface.String("current_time", mergedVariables["current_time"].(string)),
		logiface.String("company_name", mergedVariables["company_name"].(string)),
		logiface.String("support_email", mergedVariables["support_email"].(string)))

	return mergedVariables
}
