package service

import (
	"context"
	"fmt"
	"math"
	"strings"
	"text/template"
	"time"

	"platforms-email/internal/application/email/dto"
	"platforms-email/internal/domain/email/entity"
	"platforms-email/internal/domain/email/repository"
	emailErrors "platforms-email/internal/domain/errors"
	templateEntity "platforms-email/internal/domain/template/entity"
	templateRepo "platforms-email/internal/domain/template/repository"
	"platforms-email/internal/infrastructure/external"
	"platforms-pkg/logiface"
)

// EmailAccountApplicationService 邮件账号应用服务
type EmailAccountApplicationService struct {
	emailAccountRepo   repository.EmailAccountRepository
	templateRepo       templateRepo.Repository
	emailSenderFactory *external.EmailSenderFactory
	entityFactory      *entity.EntityFactory
	logger             logiface.Logger
}

// NewEmailAccountApplicationService 创建邮件账号应用服务
func NewEmailAccountApplicationService(
	emailAccountRepo repository.EmailAccountRepository,
	templateRepo templateRepo.Repository,
	entityFactory *entity.EntityFactory,
	logger logiface.Logger,
) *EmailAccountApplicationService {
	return &EmailAccountApplicationService{
		emailAccountRepo:   emailAccountRepo,
		templateRepo:       templateRepo,
		emailSenderFactory: external.NewEmailSenderFactory(logger),
		entityFactory:      entityFactory,
		logger:             logger,
	}
}

// CreateEmailAccount 创建邮件账号
func (s *EmailAccountApplicationService) CreateEmailAccount(ctx context.Context, tenantId int64, req *dto.CreateEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	// 检查账号名称是否已存在
	existingAccount, err := s.emailAccountRepo.FindByName(ctx, tenantId, req.Name)
	if err != nil && err != entity.ErrAccountNotFound {
		return nil, emailErrors.NewSystemError("check_account_name", err.Error())
	}
	if existingAccount != nil {
		return nil, entity.ErrAccountAlreadyExists
	}

	// 使用实体工厂创建邮件账号实体 - 自动生成分布式ID
	account, err := s.entityFactory.NewEmailAccount(ctx, tenantId, req.Name, entity.AccountType(req.Type), req.Provider, req.FromAddress)
	if err != nil {
		return nil, emailErrors.NewSystemError("create_email_account", err.Error())
	}

	// 设置SMTP配置
	if req.Type == int(entity.AccountTypeSMTP) {
		account.SetSMTPConfig(req.Host, req.Port, req.Username, req.Password)
	}

	// 设置其他属性
	account.FromName = req.FromName
	account.ReplyToAddress = req.ReplyToAddress
	account.IsSSL = req.IsSSL
	account.IsActive = req.IsActive

	// 设置限制
	account.SetLimits(req.DailyLimit, req.MonthlyLimit)

	// 设置配置
	if req.Config != nil {
		account.UpdateConfig(req.Config)
	}

	// 验证账号
	if err := account.Validate(); err != nil {
		return nil, emailErrors.NewSystemError("validate_email_account", err.Error())
	}

	// 保存到数据库
	if err := s.emailAccountRepo.Save(ctx, account); err != nil {
		return nil, emailErrors.NewSystemError("save_email_account", err.Error())
	}

	return s.toEmailAccountResponse(account), nil
}

// GetEmailAccount 获取邮件账号
func (s *EmailAccountApplicationService) GetEmailAccount(ctx context.Context, req *dto.GetEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	return s.toEmailAccountResponse(account), nil
}

// UpdateEmailAccount 更新邮件账号
func (s *EmailAccountApplicationService) UpdateEmailAccount(ctx context.Context, tenantId int64, req *dto.UpdateEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	// 获取现有账号
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查名称是否已被其他账号使用
	if req.Name != "" && req.Name != account.Name {
		existingAccount, err := s.emailAccountRepo.FindByName(ctx, tenantId, req.Name)
		if err != nil && err != entity.ErrAccountNotFound {
			return nil, emailErrors.NewSystemError("check_account_name", err.Error())
		}
		if existingAccount != nil && existingAccount.ID != account.ID {
			return nil, entity.ErrAccountAlreadyExists
		}
		account.Name = req.Name
	}

	// 更新基本信息
	if req.Provider != "" {
		account.Provider = req.Provider
	}
	if req.FromAddress != "" {
		account.FromAddress = req.FromAddress
	}
	if req.FromName != "" {
		account.FromName = req.FromName
	}
	if req.ReplyToAddress != "" {
		account.ReplyToAddress = req.ReplyToAddress
	}

	// 更新SMTP配置
	if req.Host != "" {
		account.Host = req.Host
	}
	if req.Port > 0 {
		account.Port = req.Port
	}
	if req.Username != "" {
		account.Username = req.Username
	}
	if req.Password != "" {
		account.Password = req.Password
	}

	// 更新SSL和激活状态
	if req.IsSSL != nil {
		account.IsSSL = *req.IsSSL
	}
	if req.IsActive != nil {
		if *req.IsActive {
			account.Activate()
		} else {
			account.Deactivate()
		}
	}

	// 更新限制
	if req.DailyLimit != nil {
		account.DailyLimit = *req.DailyLimit
	}
	if req.MonthlyLimit != nil {
		account.MonthlyLimit = *req.MonthlyLimit
	}

	// 更新配置
	if req.Config != nil {
		account.UpdateConfig(req.Config)
	}

	// 验证账号
	if err := account.Validate(); err != nil {
		return nil, emailErrors.NewSystemError("validate_email_account", err.Error())
	}

	// 保存更新
	if err := s.emailAccountRepo.Update(ctx, account); err != nil {
		return nil, emailErrors.NewSystemError("update_email_account", err.Error())
	}

	return s.toEmailAccountResponse(account), nil
}

// ListEmailAccounts 获取邮件账号列表
func (s *EmailAccountApplicationService) ListEmailAccounts(ctx context.Context, tenantId int64, req *dto.ListEmailAccountsRequest) (*dto.ListEmailAccountsResponse, error) {
	accounts, total, err := s.emailAccountRepo.FindByTenantID(ctx, tenantId, req.Pagination.Offset(), req.Pagination.Limit())
	if err != nil {
		return nil, emailErrors.NewSystemError("get_email_account_list", err.Error())
	}

	// 转换为响应DTO
	accountResponses := make([]*dto.EmailAccountResponse, len(accounts))
	for i, account := range accounts {
		accountResponses[i] = s.toEmailAccountResponse(account)
	}

	// 计算总页数
	totalPages := int64(math.Ceil(float64(total) / float64(req.Pagination.Limit())))

	return &dto.ListEmailAccountsResponse{
		Accounts:   accountResponses,
		Total:      total,
		Page:       req.Pagination.Page,
		PageSize:   req.Pagination.Limit(),
		TotalPages: totalPages,
	}, nil
}

// TestEmailAccount 测试邮件账号
func (s *EmailAccountApplicationService) TestEmailAccount(ctx context.Context, tenantId int64, req *dto.TestEmailAccountRequest) (*dto.TestEmailAccountResponse, error) {
	// 获取邮件账号
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查账号是否属于当前租户
	if account.TenantID != tenantId {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 查询账户验证模板
	template, err := s.templateRepo.GetByCode(ctx, 0, "account_verification") // 系统级模板，租户ID为0
	if err != nil {
		s.logger.Info(ctx, "Account verification template not found",
			logiface.String("template_code", "account_verification"))
		return nil, emailErrors.NewSystemError("get_account_verification_template", err.Error())
	}

	if template == nil {
		return nil, emailErrors.NewTemplateNotFoundError("account_verification")
	}

	// 准备模板变量数据
	currentTime := time.Now().Format("2006年01月02日 15:04:05")
	templateVariables := map[string]interface{}{
		"account_name": account.Name,
		"account_type": getAccountTypeName(account.Type),
		"provider":     account.Provider,
		"from_address": account.FromAddress,
		"current_time": currentTime,
		"account_id":   account.ID,
	}

	// 渲染模板内容
	subject, htmlContent, textContent, err := s.renderTemplate(template, templateVariables)
	if err != nil {
		s.logger.Warn(ctx, "Template rendering failed",
			logiface.String("template_code", "account_verification"))
		return nil, emailErrors.NewEmailError(emailErrors.CodeTemplateRenderFailed, err.Error())
	}

	// 创建验证邮件
	message, err := s.entityFactory.NewEmailMessage(
		ctx,
		tenantId,
		req.TestEmail, // 收件人
		subject,       // 使用模板渲染的主题
	)
	if err != nil {
		return nil, emailErrors.NewSystemError("create_verification_email", err.Error())
	}

	// 设置邮件内容
	message.FromAddress = account.FromAddress
	message.HTMLContent = htmlContent
	message.TextContent = textContent

	// 统一使用SendEmail发送测试邮件
	var testSuccess bool
	var testMessage string

	err = s.emailSenderFactory.SendEmail(ctx, account, message)
	if err != nil {
		testSuccess = false
		testMessage = fmt.Sprintf("测试邮件发送失败: %s", err.Error())
		s.logger.Error(ctx, "Email account test failed",
			logiface.Error(err),
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
			logiface.String("test_email", req.TestEmail),
			logiface.String("account_type", fmt.Sprintf("%d", account.Type)),
			logiface.String("provider", account.Provider))
		return nil, err
	} else {
		testSuccess = true
		testMessage = "测试邮件发送成功，请检查您的邮箱"
		s.logger.Info(ctx, "Email account test successful",
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
			logiface.String("test_email", req.TestEmail),
			logiface.String("account_type", fmt.Sprintf("%d", account.Type)),
			logiface.String("provider", account.Provider))
	}

	// 更新测试状态
	var testStatus entity.TestStatus
	if testSuccess {
		testStatus = entity.TestStatusSuccess
		// 测试成功时激活账户
		account.Activate()
	} else {
		testStatus = entity.TestStatusFailed
	}

	account.SetTestResult(testStatus, testMessage)
	if err := s.emailAccountRepo.Update(ctx, account); err != nil {
		s.logger.Error(ctx, "Failed to update test result",
			logiface.Error(err),
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)))
		// 不返回错误，因为测试本身可能成功，只是更新状态失败
	}

	return &dto.TestEmailAccountResponse{
		Success: testSuccess,
		Message: testMessage,
	}, nil
}

// getAccountTypeName 获取账户类型名称
func getAccountTypeName(accountType entity.AccountType) string {
	switch accountType {
	case entity.AccountTypeSMTP:
		return "SMTP"
	case entity.AccountTypeIMAP:
		return "IMAP"
	case entity.AccountTypePOP3:
		return "POP3"
	case entity.AccountTypeExchange:
		return "Exchange"
	case entity.AccountTypeAPI:
		return "API"
	default:
		return "未知类型"
	}
}

// DeleteEmailAccount 删除邮件账号
func (s *EmailAccountApplicationService) DeleteEmailAccount(ctx context.Context, tenantId int64, req *dto.DeleteEmailAccountRequest) error {
	// 检查账号是否存在
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查账号是否属于当前租户
	if account.TenantID != tenantId {
		return emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 删除账号
	if err := s.emailAccountRepo.Delete(ctx, account.ID); err != nil {
		return emailErrors.NewSystemError("delete_email_account", err.Error())
	}

	return nil
}

// toEmailAccountResponse 转换为响应DTO
func (s *EmailAccountApplicationService) toEmailAccountResponse(account *entity.EmailAccount) *dto.EmailAccountResponse {
	return &dto.EmailAccountResponse{
		ID:             account.ID,
		TenantID:       account.TenantID,
		Name:           account.Name,
		Type:           int(account.Type),
		Provider:       account.Provider,
		Host:           account.Host,
		Port:           account.Port,
		Username:       account.Username,
		FromAddress:    account.FromAddress,
		FromName:       account.FromName,
		ReplyToAddress: account.ReplyToAddress,
		IsSSL:          account.IsSSL,
		IsActive:       account.IsActive,
		DailyLimit:     account.DailyLimit,
		MonthlyLimit:   account.MonthlyLimit,
		SentToday:      account.SentToday,
		SentThisMonth:  account.SentThisMonth,
		LastSentAt:     account.LastSentAt,
		TestStatus:     int(account.TestStatus),
		TestMessage:    account.TestMessage,
		Config:         account.Config,
		CreatedAt:      account.CreatedAt,
		UpdatedAt:      account.UpdatedAt,
		CreatedBy:      account.CreatedBy,
		UpdatedBy:      account.UpdatedBy,
		Version:        account.Version,
		IsSystem:       account.IsSystem,
	}
}

// renderTemplate 渲染邮件模板
func (s *EmailAccountApplicationService) renderTemplate(tmpl *templateEntity.EmailTemplate, variables map[string]interface{}) (string, string, string, error) {
	// 渲染主题
	subjectTmpl, err := template.New("subject").Parse(tmpl.Subject)
	if err != nil {
		return "", "", "", fmt.Errorf("解析主题模板失败: %w", err)
	}

	var subjectBuffer strings.Builder
	if err := subjectTmpl.Execute(&subjectBuffer, variables); err != nil {
		return "", "", "", fmt.Errorf("渲染主题失败: %w", err)
	}
	subject := subjectBuffer.String()

	// 渲染HTML内容
	htmlTmpl, err := template.New("html").Parse(tmpl.HTMLContent)
	if err != nil {
		return "", "", "", fmt.Errorf("解析HTML模板失败: %w", err)
	}

	var htmlBuffer strings.Builder
	if err := htmlTmpl.Execute(&htmlBuffer, variables); err != nil {
		return "", "", "", fmt.Errorf("渲染HTML内容失败: %w", err)
	}
	htmlContent := htmlBuffer.String()

	// 渲染纯文本内容
	textTmpl, err := template.New("text").Parse(tmpl.PlainTextContent)
	if err != nil {
		return "", "", "", fmt.Errorf("解析纯文本模板失败: %w", err)
	}

	var textBuffer strings.Builder
	if err := textTmpl.Execute(&textBuffer, variables); err != nil {
		return "", "", "", fmt.Errorf("渲染纯文本内容失败: %w", err)
	}
	textContent := textBuffer.String()

	return subject, htmlContent, textContent, nil
}
