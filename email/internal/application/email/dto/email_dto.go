package dto

import "time"

// SendTemplateEmailRequest 发送模板邮件请求
type SendTemplateEmailRequest struct {
	TemplateCode string                 `json:"template_code" binding:"required"`
	ToAddress    string                 `json:"to_address" binding:"required,email"`
	CcAddresses  []string               `json:"cc_addresses,omitempty" binding:"omitempty,dive,email"`
	BccAddresses []string               `json:"bcc_addresses,omitempty" binding:"omitempty,dive,email"`
	Variables    map[string]interface{} `json:"variables,omitempty"`
	Attachments  []string               `json:"attachments,omitempty"`
}

// SendEmailRequest 直接发送邮件请求
type SendEmailRequest struct {
	AccountID    int64    `json:"account_id" binding:"required"`
	FromAddress  string   `json:"from_address" binding:"required,email"`
	FromName     string   `json:"from_name,omitempty"`
	ToAddress    string   `json:"to_address" binding:"required,email"`
	CcAddresses  []string `json:"cc_addresses,omitempty" binding:"omitempty,dive,email"`
	BccAddresses []string `json:"bcc_addresses,omitempty" binding:"omitempty,dive,email"`
	Subject      string   `json:"subject" binding:"required,min=1,max=200"`
	HTMLContent  string   `json:"html_content" binding:"required,min=1,max=10000"`
	TextContent  string   `json:"text_content,omitempty"`
	Attachments  []string `json:"attachments,omitempty"`
}

// SendEmailResponse 发送邮件响应
type SendEmailResponse struct {
	EmailID int64  `json:"email_id"`
	Status  string `json:"status"`
}

// EmailStatusResponse 邮件状态响应
type EmailStatusResponse struct {
	EmailID    int64      `json:"email_id"`
	Status     string     `json:"status"`
	SentAt     *time.Time `json:"sent_at,omitempty"`
	ErrorMsg   string     `json:"error_msg,omitempty"`
	RetryCount int        `json:"retry_count"`
}

// EmailStatisticsResponse 邮件统计响应
type EmailStatisticsResponse struct {
	TenantID     int64            `json:"tenant_id"`
	StatusCounts map[string]int64 `json:"status_counts"`
	LastSentAt   *time.Time       `json:"last_sent_at,omitempty"`
	LastFailedAt *time.Time       `json:"last_failed_at,omitempty"`
	DailyStats   map[string]int64 `json:"daily_stats"`
	MonthlyStats map[string]int64 `json:"monthly_stats"`
	TotalSent    int64            `json:"total_sent"`
	TotalFailed  int64            `json:"total_failed"`
	TotalPending int64            `json:"total_pending"`
}
