package handlers

import (
	"platforms-email/internal/application/template/dto"
	"platforms-email/internal/application/template/service"
	"strconv"

	emailErrors "platforms-email/internal/domain/errors"
	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"

	"github.com/gin-gonic/gin"
)

// TemplateHandlerExample 模板处理器示例 - 展示如何使用新的错误处理机制
type TemplateHandlerExample struct {
	templateService *service.TemplateApplicationService
	logger          logiface.Logger
}

// NewTemplateHandlerExample 创建模板处理器示例
func NewTemplateHandlerExample(templateService *service.TemplateApplicationService, logger logiface.Logger) *TemplateHandlerExample {
	return &TemplateHandlerExample{
		templateService: templateService,
		logger:          logger,
	}
}

// CreateTemplateExample 创建模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) CreateTemplateExample(c *gin.Context) {
	var req dto.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to create template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Created(c, result)
}

// GetTemplateExample 获取模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) GetTemplateExample(c *gin.Context) {
	// 从查询参数获取模板ID
	templateIDStr := c.Query("id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "Missing template ID parameter")
		commonResponse.FieldError(c, "id", "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid template ID format", logiface.Error(err))
		commonResponse.FieldError(c, "id", "模板ID格式错误")
		return
	}

	req := dto.GetTemplateRequest{
		TemplateID: templateID,
	}

	result, err := h.templateService.GetTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// UpdateTemplateExample 更新模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) UpdateTemplateExample(c *gin.Context) {
	var req dto.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.UpdateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// PublishTemplateExample 发布模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) PublishTemplateExample(c *gin.Context) {
	var req dto.PublishTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.PublishTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to publish template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// DeleteTemplateExample 删除模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) DeleteTemplateExample(c *gin.Context) {
	var req dto.DeleteTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	_, err := h.templateService.DeleteTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to delete template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, nil)
}

// CloneTemplateExample 克隆模板示例 - 使用新的错误处理机制
func (h *TemplateHandlerExample) CloneTemplateExample(c *gin.Context) {
	var req dto.CloneTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.CloneTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to clone template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Created(c, result)
}

// 示例：在业务逻辑中抛出具体的错误
func (h *TemplateHandlerExample) CreateTemplateWithCustomErrorExample(c *gin.Context) {
	var req dto.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 示例：检查模板名称是否已存在
	if req.Name == "existing_template" {
		// 抛出具体的邮件错误
		customErr := emailErrors.NewTemplateNameExistsError(req.Name)
		HandleEmailError(c, customErr)
		return
	}

	// 示例：检查模板代码是否已存在
	if req.TemplateCode == "existing_code" {
		// 抛出具体的邮件错误
		customErr := emailErrors.NewTemplateCodeExistsError(req.TemplateCode)
		HandleEmailError(c, customErr)
		return
	}

	result, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to create template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Created(c, result)
}

// 示例：在业务逻辑中抛出系统错误
func (h *TemplateHandlerExample) GetTemplateWithSystemErrorExample(c *gin.Context) {
	templateIDStr := c.Query("id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "Missing template ID parameter")
		commonResponse.BadRequest(c, "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid template ID format", logiface.Error(err))
		commonResponse.BadRequest(c, "模板ID格式错误")
		return
	}

	// 示例：模拟数据库连接失败
	if templateID == 999 {
		// 抛出系统错误
		systemErr := emailErrors.NewDatabaseError("get_template", "database connection failed")
		HandleEmailError(c, systemErr)
		return
	}

	// 示例：模拟第三方服务错误
	if templateID == 888 {
		// 抛出第三方服务错误
		thirdPartyErr := emailErrors.NewThirdPartyError("template_service", "service unavailable")
		HandleEmailError(c, thirdPartyErr)
		return
	}

	req := dto.GetTemplateRequest{
		TemplateID: templateID,
	}

	result, err := h.templateService.GetTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get template", logiface.Error(err))

		// 使用新的错误处理机制
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}
