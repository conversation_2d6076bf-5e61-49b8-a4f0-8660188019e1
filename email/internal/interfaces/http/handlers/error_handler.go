package handlers

import (
	"errors"
	emailErrors "platforms-email/internal/domain/errors"
	commonResponse "platforms-pkg/common/response"

	"github.com/gin-gonic/gin"
)

// HandleEmailError 处理邮件模块自定义错误
func HandleEmailError(c *gin.Context, err error) {
	var emailErr *emailErrors.EmailError
	if errors.As(err, &emailErr) {
		// 根据错误码返回相应的HTTP响应
		switch emailErr.Code {
		// 邮件发送相关错误 (200000-200099)
		case emailErrors.CodeEmailNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件不存在"))
		case emailErrors.CodeEmailAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件已存在"))
		case emailErrors.CodeEmailSendFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件发送失败"))
		case emailErrors.CodeEmailAlreadySent:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件已发送"))
		case emailErrors.CodeEmailCancelled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件已取消"))
		case emailErrors.CodeEmailScheduled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件已调度"))
		case emailErrors.CodeEmailDraft:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件草稿状态"))
		case emailErrors.CodeEmailInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "发件人地址无效")
		case emailErrors.CodeEmailInvalidToAddress:
			commonResponse.FieldError(c, "to_address", "收件人地址无效")
		case emailErrors.CodeEmailInvalidSubject:
			commonResponse.FieldError(c, "subject", "邮件主题无效")
		case emailErrors.CodeEmailInvalidContent:
			commonResponse.FieldError(c, "content", "邮件内容无效")
		case emailErrors.CodeEmailInvalidTemplate:
			commonResponse.FieldError(c, "template", "邮件模板无效")
		case emailErrors.CodeEmailQuotaExceeded:
			commonResponse.Error(c, emailErrors.CodeEmailQuotaExceeded, getErrorMessage(emailErr, "邮件配额超限"))
		case emailErrors.CodeEmailRateLimitExceeded:
			commonResponse.Error(c, commonResponse.CodeRateLimitExceeded, getErrorMessage(emailErr, "邮件发送频率超限"))
		case emailErrors.CodeEmailRetryFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件重试失败"))
		case emailErrors.CodeEmailMaxRetriesExceeded:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "超过最大重试次数"))
		case emailErrors.CodeEmailBounced:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件被退回"))
		case emailErrors.CodeEmailSpamDetected:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件被检测为垃圾邮件"))
		case emailErrors.CodeEmailBlocked:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件被阻止"))
		case emailErrors.CodeEmailTimeout:
			commonResponse.Error(c, commonResponse.CodeServiceTimeout, getErrorMessage(emailErr, "邮件发送超时"))
		case emailErrors.CodeEmailConnectionFailed:
			commonResponse.Error(c, commonResponse.CodeServiceUnavailable, getErrorMessage(emailErr, "邮件连接失败"))
		case emailErrors.CodeEmailAuthenticationFailed:
			commonResponse.Error(c, commonResponse.CodeAuthError, getErrorMessage(emailErr, "邮件认证失败"))
		case emailErrors.CodeEmailServerError:
			commonResponse.Error(c, commonResponse.CodeThirdPartyError, getErrorMessage(emailErr, "邮件服务器错误"))
		case emailErrors.CodeEmailInvalidAttachment:
			commonResponse.FieldError(c, "attachment", "邮件附件无效")
		case emailErrors.CodeEmailAttachmentTooLarge:
			commonResponse.FieldError(c, "attachment", "邮件附件过大")
		case emailErrors.CodeEmailInvalidRecipient:
			commonResponse.FieldError(c, "recipient", "邮件收件人无效")
		case emailErrors.CodeEmailBlacklisted:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件地址被拉黑"))
		case emailErrors.CodeEmailWhitelistRequired:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "需要白名单验证"))

		// 邮件模板相关错误 (200100-200199)
		case emailErrors.CodeTemplateNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "模板不存在"))
		case emailErrors.CodeTemplateAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "模板已存在"))
		case emailErrors.CodeTemplateNameExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "模板名称已存在"))
		case emailErrors.CodeTemplateCodeExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "模板代码已存在"))
		case emailErrors.CodeTemplateInvalidContent:
			commonResponse.Error(c, commonResponse.CodeMissingParam, getErrorMessage(emailErr, "模板内容无效"))
		case emailErrors.CodeTemplateInvalidType:
			commonResponse.FieldError(c, "type", "模板类型无效")
		case emailErrors.CodeTemplateInvalidStatus:
			commonResponse.FieldError(c, "status", "模板状态无效")
		case emailErrors.CodeTemplateNotPublished:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "模板未发布"))
		case emailErrors.CodeTemplateDisabled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "模板已禁用"))
		case emailErrors.CodeTemplateDeleted:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "模板已删除"))
		case emailErrors.CodeTemplateVersionNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "模板版本不存在"))
		case emailErrors.CodeTemplateDraftNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "模板草稿不存在"))
		case emailErrors.CodeTemplateVariableInvalid:
			commonResponse.FieldError(c, "variables", "模板变量无效")
		case emailErrors.CodeTemplateVariableMissing:
			commonResponse.FieldError(c, "variables", "模板变量缺失")
		case emailErrors.CodeTemplateRenderFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板渲染失败"))
		case emailErrors.CodeTemplatePreviewFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板预览失败"))
		case emailErrors.CodeTemplateCloneFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板克隆失败"))
		case emailErrors.CodeTemplateRestoreFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板恢复失败"))
		case emailErrors.CodeTemplatePublishFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板发布失败"))
		case emailErrors.CodeTemplateDisableFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板禁用失败"))
		case emailErrors.CodeTemplateDeleteFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板删除失败"))
		case emailErrors.CodeTemplateTestFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "模板测试失败"))

		// 邮件账号相关错误 (200200-200299)
		case emailErrors.CodeAccountNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "账号不存在"))
		case emailErrors.CodeAccountAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "账号已存在"))
		case emailErrors.CodeAccountNameExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "账号名称已存在"))
		case emailErrors.CodeAccountInactive:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "账号未激活"))
		case emailErrors.CodeAccountDisabled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "账号已禁用"))
		case emailErrors.CodeAccountDeleted:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "账号已删除"))
		case emailErrors.CodeAccountInvalidName:
			commonResponse.FieldError(c, "name", "账号名称无效")
		case emailErrors.CodeAccountInvalidProvider:
			commonResponse.FieldError(c, "provider", "账号提供商无效")
		case emailErrors.CodeAccountInvalidHost:
			commonResponse.FieldError(c, "host", "账号主机无效")
		case emailErrors.CodeAccountInvalidPort:
			commonResponse.FieldError(c, "port", "账号端口无效")
		case emailErrors.CodeAccountInvalidUsername:
			commonResponse.FieldError(c, "username", "账号用户名无效")
		case emailErrors.CodeAccountInvalidPassword:
			commonResponse.FieldError(c, "password", "账号密码无效")
		case emailErrors.CodeAccountInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "账号发件人地址无效")
		case emailErrors.CodeAccountTestFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "账号测试失败"))
		case emailErrors.CodeAccountConnectionFailed:
			commonResponse.Error(c, commonResponse.CodeServiceUnavailable, getErrorMessage(emailErr, "账号连接失败"))
		case emailErrors.CodeAccountAuthenticationFailed:
			commonResponse.Error(c, emailErrors.CodeAccountAuthenticationFailed, getErrorMessage(emailErr, "账号认证失败"))
		case emailErrors.CodeAccountQuotaExceeded:
			commonResponse.Error(c, emailErrors.CodeAccountQuotaExceeded, getErrorMessage(emailErr, "账号配额超限"))
		case emailErrors.CodeAccountRateLimitExceeded:
			commonResponse.Error(c, commonResponse.CodeRateLimitExceeded, getErrorMessage(emailErr, "账号频率限制超限"))
		case emailErrors.CodeAccountLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "账号数量已达上限"))
		case emailErrors.CodeAccountConfigInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, getErrorMessage(emailErr, "账号配置无效"))
		case emailErrors.CodeAccountSecurityInvalid:
			commonResponse.FieldError(c, "security", "账号安全设置无效")
		case emailErrors.CodeAccountSSLInvalid:
			commonResponse.FieldError(c, "ssl", "账号SSL设置无效")
		case emailErrors.CodeAccountTimeoutInvalid:
			commonResponse.FieldError(c, "timeout", "账号超时设置无效")

		// 租户相关错误 (200300-200399)
		case emailErrors.CodeTenantNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "租户不存在"))
		case emailErrors.CodeTenantDisabled:
			commonResponse.Error(c, commonResponse.CodeAccountDisabled, getErrorMessage(emailErr, "租户已禁用"))
		case emailErrors.CodeTenantExpired:
			commonResponse.Error(c, commonResponse.CodeAccountExpired, getErrorMessage(emailErr, "租户已过期"))
		case emailErrors.CodeTenantSuspended:
			commonResponse.Error(c, commonResponse.CodeAccountSuspended, getErrorMessage(emailErr, "租户已暂停"))
		case emailErrors.CodeTenantConfigNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "租户配置不存在"))
		case emailErrors.CodeTenantConfigInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, getErrorMessage(emailErr, "租户配置无效"))
		case emailErrors.CodeTenantQuotaExceeded:
			commonResponse.Error(c, emailErrors.CodeTenantQuotaExceeded, getErrorMessage(emailErr, "租户配额超限"))
		case emailErrors.CodeTenantSMTPTestFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "租户SMTP测试失败"))
		case emailErrors.CodeTenantEmailLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "租户邮件数量已达上限"))
		case emailErrors.CodeTenantStorageLimitReached:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, getErrorMessage(emailErr, "租户存储空间已达上限"))
		case emailErrors.CodeTenantTemplateLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "租户模板数量已达上限"))
		case emailErrors.CodeTenantAccountLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "租户账号数量已达上限"))
		case emailErrors.CodeTenantSettingsInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, getErrorMessage(emailErr, "租户设置无效"))
		case emailErrors.CodeTenantTimezoneInvalid:
			commonResponse.FieldError(c, "timezone", "租户时区无效")
		case emailErrors.CodeTenantLanguageInvalid:
			commonResponse.FieldError(c, "language", "租户语言无效")
		case emailErrors.CodeTenantCurrencyInvalid:
			commonResponse.FieldError(c, "currency", "租户货币无效")

		// 订阅者相关错误 (200400-200499)
		case emailErrors.CodeSubscriberNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "订阅者不存在"))
		case emailErrors.CodeSubscriberAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "订阅者已存在"))
		case emailErrors.CodeSubscriberEmailExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "订阅者邮箱已存在"))
		case emailErrors.CodeSubscriberInvalidEmail:
			commonResponse.FieldError(c, "email", "订阅者邮箱无效")
		case emailErrors.CodeSubscriberInvalidStatus:
			commonResponse.FieldError(c, "status", "订阅者状态无效")
		case emailErrors.CodeSubscriberUnsubscribed:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "订阅者已退订"))
		case emailErrors.CodeSubscriberBounced:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "订阅者已退回"))
		case emailErrors.CodeSubscriberSpam:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "订阅者标记为垃圾邮件"))
		case emailErrors.CodeSubscriberBlacklisted:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "订阅者被拉黑"))
		case emailErrors.CodeSubscriberLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "订阅者数量已达上限"))
		case emailErrors.CodeSubscriberImportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "订阅者导入失败"))
		case emailErrors.CodeSubscriberExportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "订阅者导出失败"))
		case emailErrors.CodeSubscriberGroupNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "订阅者分组不存在"))
		case emailErrors.CodeSubscriberGroupExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "订阅者分组已存在"))
		case emailErrors.CodeSubscriberGroupInvalid:
			commonResponse.FieldError(c, "group", "订阅者分组无效")

		// 邮件列表相关错误 (200500-200599)
		case emailErrors.CodeListNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件列表不存在"))
		case emailErrors.CodeListAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件列表已存在"))
		case emailErrors.CodeListNameExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件列表名称已存在"))
		case emailErrors.CodeListInvalidName:
			commonResponse.FieldError(c, "name", "邮件列表名称无效")
		case emailErrors.CodeListInvalidDescription:
			commonResponse.FieldError(c, "description", "邮件列表描述无效")
		case emailErrors.CodeListInvalidStatus:
			commonResponse.FieldError(c, "status", "邮件列表状态无效")
		case emailErrors.CodeListDisabled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件列表已禁用"))
		case emailErrors.CodeListDeleted:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件列表已删除"))
		case emailErrors.CodeListLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, getErrorMessage(emailErr, "邮件列表数量已达上限"))
		case emailErrors.CodeListSubscriberNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件列表订阅者不存在"))
		case emailErrors.CodeListSubscriberExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件列表订阅者已存在"))
		case emailErrors.CodeListImportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件列表导入失败"))
		case emailErrors.CodeListExportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件列表导出失败"))

		// 邮件活动相关错误 (200600-200699)
		case emailErrors.CodeCampaignNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件活动不存在"))
		case emailErrors.CodeCampaignAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件活动已存在"))
		case emailErrors.CodeCampaignNameExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, getErrorMessage(emailErr, "邮件活动名称已存在"))
		case emailErrors.CodeCampaignInvalidName:
			commonResponse.FieldError(c, "name", "邮件活动名称无效")
		case emailErrors.CodeCampaignInvalidStatus:
			commonResponse.FieldError(c, "status", "邮件活动状态无效")
		case emailErrors.CodeCampaignNotScheduled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动未调度"))
		case emailErrors.CodeCampaignScheduled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动已调度"))
		case emailErrors.CodeCampaignSending:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动发送中"))
		case emailErrors.CodeCampaignSent:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动已发送"))
		case emailErrors.CodeCampaignPaused:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动已暂停"))
		case emailErrors.CodeCampaignCancelled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动已取消"))
		case emailErrors.CodeCampaignDraft:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, getErrorMessage(emailErr, "邮件活动草稿状态"))
		case emailErrors.CodeCampaignTemplateNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件活动模板不存在"))
		case emailErrors.CodeCampaignListNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件活动列表不存在"))
		case emailErrors.CodeCampaignAccountNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "邮件活动账号不存在"))
		case emailErrors.CodeCampaignSendFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件活动发送失败"))
		case emailErrors.CodeCampaignScheduleFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件活动调度失败"))
		case emailErrors.CodeCampaignPauseFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件活动暂停失败"))
		case emailErrors.CodeCampaignCancelFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件活动取消失败"))
		case emailErrors.CodeCampaignDeleteFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "邮件活动删除失败"))

		// 统计分析相关错误 (200700-200799)
		case emailErrors.CodeStatisticsNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "统计数据不存在"))
		case emailErrors.CodeStatisticsQueryInvalid:
			commonResponse.Error(c, commonResponse.CodeValidationError, getErrorMessage(emailErr, "统计查询参数无效"))
		case emailErrors.CodeStatisticsDateInvalid:
			commonResponse.FieldError(c, "date", "统计日期无效")
		case emailErrors.CodeStatisticsRangeInvalid:
			commonResponse.FieldError(c, "range", "统计范围无效")
		case emailErrors.CodeStatisticsTypeInvalid:
			commonResponse.FieldError(c, "type", "统计类型无效")
		case emailErrors.CodeStatisticsExportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "统计导出失败"))
		case emailErrors.CodeStatisticsReportNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, getErrorMessage(emailErr, "统计报告不存在"))
		case emailErrors.CodeStatisticsReportFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, getErrorMessage(emailErr, "统计报告生成失败"))

		// 系统错误 (200900-200999)
		case emailErrors.CodeSystemError:
			commonResponse.Error(c, commonResponse.CodeSystemError, getErrorMessage(emailErr, "系统错误"))
		case emailErrors.CodeDatabaseError:
			commonResponse.Error(c, commonResponse.CodeDatabaseError, getErrorMessage(emailErr, "数据库错误"))
		case emailErrors.CodeCacheError:
			commonResponse.Error(c, commonResponse.CodeCacheError, getErrorMessage(emailErr, "缓存错误"))
		case emailErrors.CodeNetworkError:
			commonResponse.Error(c, commonResponse.CodeServiceUnavailable, getErrorMessage(emailErr, "网络错误"))
		case emailErrors.CodeServiceUnavailable:
			commonResponse.ServiceUnavailable(c, getErrorMessage(emailErr, "服务不可用"))
		case emailErrors.CodeServiceTimeout:
			commonResponse.Error(c, commonResponse.CodeServiceTimeout, getErrorMessage(emailErr, "服务超时"))
		case emailErrors.CodeServiceOverload:
			commonResponse.Error(c, commonResponse.CodeServiceOverload, getErrorMessage(emailErr, "服务过载"))
		case emailErrors.CodeThirdPartyError:
			commonResponse.Error(c, commonResponse.CodeThirdPartyError, getErrorMessage(emailErr, "第三方服务错误"))
		case emailErrors.CodeThirdPartyTimeout:
			commonResponse.Error(c, commonResponse.CodeThirdPartyTimeout, getErrorMessage(emailErr, "第三方服务超时"))
		case emailErrors.CodeThirdPartyUnavailable:
			commonResponse.Error(c, commonResponse.CodeThirdPartyUnavailable, getErrorMessage(emailErr, "第三方服务不可用"))
		case emailErrors.CodeInternalError:
			commonResponse.InternalError(c, err)
		case emailErrors.CodeUnexpectedError:
			commonResponse.Error(c, commonResponse.CodeSystemError, getErrorMessage(emailErr, "意外错误"))

		// 默认处理
		default:
			commonResponse.Error(c, commonResponse.CodeBusinessLogicError, emailErr.Message)
		}
	}
}

// IsEmailError 检查是否为邮件模块自定义错误
func IsEmailError(err error) bool {
	_, ok := err.(*emailErrors.EmailError)
	return ok
}

// GetEmailErrorCode 获取邮件错误码
func GetEmailErrorCode(err error) int {
	if emailErr, ok := err.(*emailErrors.EmailError); ok {
		return emailErr.Code
	}
	return 0
}

// GetEmailErrorMessage 获取邮件错误消息
func GetEmailErrorMessage(err error) string {
	if emailErr, ok := err.(*emailErrors.EmailError); ok {
		return emailErr.Message
	}
	return err.Error()
}

// getErrorMessage 获取错误消息，优先使用 EmailError 的 message，如果为空则使用默认消息
func getErrorMessage(emailErr *emailErrors.EmailError, defaultMsg string) string {
	if emailErr.Message != "" {
		return emailErr.Message
	}
	return defaultMsg
}
