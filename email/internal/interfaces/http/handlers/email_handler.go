package handlers

import (
	"strconv"
	"time"

	"platforms-email/internal/application/email/dto"
	"platforms-email/internal/application/email/service"
	"platforms-email/internal/domain/email/value_object"
	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/usercontext"

	"github.com/gin-gonic/gin"
)

// EmailHandler 邮件处理器
type EmailHandler struct {
	emailService *service.EmailApplicationService
}

// NewEmailHandler 创建邮件处理器
func NewEmailHandler(emailService *service.EmailApplicationService) *EmailHandler {
	return &EmailHandler{
		emailService: emailService,
	}
}

// SendTemplateEmail 基于模板发送邮件
func (h *EmailHandler) SendTemplateEmail(c *gin.Context) {
	var request dto.SendTemplateEmailRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 获取用户信息（中间件已确保用户已认证）
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	// 发送邮件 - 使用正确的参数
	result, err := h.emailService.SendTemplateEmail(c.Request.Context(), userInfo.TenantID, request.TemplateCode, []string{request.ToAddress}, request.Variables)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// SendEmail 直接发送邮件
func (h *EmailHandler) SendEmail(c *gin.Context) {
	var request dto.SendEmailRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 获取用户信息（中间件已确保用户已认证）
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	// 发送邮件
	result, err := h.emailService.SendEmail(c.Request.Context(), userInfo.TenantID, &request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// GetEmailStatus 获取邮件状态
func (h *EmailHandler) GetEmailStatus(c *gin.Context) {
	emailIDStr := c.Query("email_id")
	if emailIDStr == "" {
		commonResponse.FieldError(c, "email_id", "邮件ID不能为空")
		return
	}

	// 将字符串转换为int64
	emailID, err := strconv.ParseInt(emailIDStr, 10, 64)
	if err != nil {
		commonResponse.FieldError(c, "email_id", "邮件ID格式不正确")
		return
	}

	// 获取邮件状态
	result, err := h.emailService.GetEmailStatus(c.Request.Context(), emailID)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// GetEmailStatistics 获取邮件统计信息
func (h *EmailHandler) GetEmailStatistics(c *gin.Context) {
	// 获取用户信息（中间件已确保用户已认证）
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	// 创建查询参数对象
	params := value_object.NewStatisticsQueryParams(userInfo.TenantID)

	// 解析查询参数
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			params.StartDate = &startDate
		}
	}

	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			params.EndDate = &endDate
		}
	}

	if maxMonthsStr := c.Query("max_months"); maxMonthsStr != "" {
		if maxMonths, err := strconv.Atoi(maxMonthsStr); err == nil {
			params.MaxMonths = maxMonths
		}
	}

	if status := c.Query("status"); status != "" {
		params.Status = status
	}

	if accountIDStr := c.Query("account_id"); accountIDStr != "" {
		if accountID, err := strconv.ParseInt(accountIDStr, 10, 64); err == nil {
			params.AccountID = accountID
		}
	}

	if templateIDStr := c.Query("template_id"); templateIDStr != "" {
		if templateID, err := strconv.ParseInt(templateIDStr, 10, 64); err == nil {
			params.TemplateID = templateID
		}
	}

	// 获取邮件统计信息
	stats, err := h.emailService.GetEmailStatistics(c.Request.Context(), params)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, stats)
}
