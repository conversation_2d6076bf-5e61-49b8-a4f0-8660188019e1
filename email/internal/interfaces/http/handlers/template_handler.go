package handlers

import (
	"platforms-email/internal/application/template/dto"
	"platforms-email/internal/application/template/service"
	"platforms-pkg/usercontext"
	"strconv"

	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"

	"github.com/gin-gonic/gin"
)

// TemplateHandler 模板处理器
type TemplateHandler struct {
	templateService *service.TemplateApplicationService
	logger          logiface.Logger
}

// NewTemplateHandler 创建模板处理器
func NewTemplateHandler(templateService *service.TemplateApplicationService, logger logiface.Logger) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
		logger:          logger,
	}
}

// CreateTemplate 创建模板
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req dto.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.CreateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to create template", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	commonResponse.Created(c, result)
}

// GetTemplate 获取模板
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	// 从查询参数获取模板ID
	templateIDStr := c.Query("id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "Missing template ID parameter")
		commonResponse.FieldError(c, "id", "模板ID不能为空")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid template ID format", logiface.Error(err))
		commonResponse.FieldError(c, "id", "模板ID格式错误")
		return
	}

	req := dto.GetTemplateRequest{
		TemplateID: templateID,
	}

	result, err := h.templateService.GetTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get template", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// ListTemplates 获取模板列表
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	var req dto.ListTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	templates, total, err := h.templateService.ListTemplates(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get template list", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	// 计算分页信息
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}

	commonResponse.Paginated(c, templates, page, pageSize, int64(total))
}

// UpdateTemplate 更新模板
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	var req dto.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从 URL 查询参数获取更新模式
	if updateMode := c.Query("update_mode"); updateMode != "" {
		req.UpdateMode = updateMode
	}

	req.TenantID = usercontext.MustGetTenantID(c.Request.Context())
	result, err := h.templateService.UpdateTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update template", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	commonResponse.Updated(c, result)
}

// GetTemplateVariables 获取模板变量列表
func (h *TemplateHandler) GetTemplateVariables(c *gin.Context) {
	var req dto.GetTemplateVariablesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateService.GetTemplateVariables(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get template variables", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// DeleteTemplate 删除模板
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	var req dto.DeleteTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request body", logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	req.TenantID = usercontext.MustGetTenantID(c.Request.Context())
	_, err := h.templateService.DeleteTemplate(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to delete template", logiface.Error(err))
		HandleEmailError(c, err)
		return
	}

	commonResponse.Deleted(c)
}
