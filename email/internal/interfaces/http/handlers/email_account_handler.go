package handlers

import (
	"platforms-email/internal/application/email/dto"
	"platforms-email/internal/application/email/service"
	"platforms-email/internal/domain/email/entity"
	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"
	"platforms-pkg/usercontext"
	"strconv"

	"github.com/gin-gonic/gin"
)

// EmailAccountHandler 邮件账号处理器
type EmailAccountHandler struct {
	accountService *service.EmailAccountApplicationService
	logger         logiface.Logger
}

// NewEmailAccountHandler 创建邮件账号处理器
func NewEmailAccountHandler(accountService *service.EmailAccountApplicationService, logger logiface.Logger) *EmailAccountHandler {
	return &EmailAccountHandler{
		accountService: accountService,
		logger:         logger,
	}
}

// CreateEmailAccount 创建邮件账号
func (h *EmailAccountHandler) CreateEmailAccount(c *gin.Context) {
	var request dto.CreateEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	account, err := h.accountService.CreateEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		// 使用统一错误码处理
		switch err {
		case entity.ErrAccountAlreadyExists:
			commonResponse.UniqueConstraintError(c, "name", request.Name)
		case entity.ErrInvalidAccountName:
			commonResponse.FieldError(c, "name", "邮件账号名称无效")
		case entity.ErrInvalidProvider:
			commonResponse.FieldError(c, "provider", "服务提供商无效")
		case entity.ErrInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "发信地址无效")
		case entity.ErrInvalidSMTPHost:
			commonResponse.FieldError(c, "host", "SMTP服务器地址无效")
		case entity.ErrInvalidSMTPPort:
			commonResponse.FieldError(c, "port", "SMTP端口无效")
		case entity.ErrInvalidSMTPUsername:
			commonResponse.FieldError(c, "username", "SMTP用户名无效")
		case entity.ErrInvalidSMTPPassword:
			commonResponse.FieldError(c, "password", "SMTP密码无效")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Created(c, account)
}

// UpdateEmailAccount 更新邮件账号
func (h *EmailAccountHandler) UpdateEmailAccount(c *gin.Context) {
	var request dto.UpdateEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	account, err := h.accountService.UpdateEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		switch err {
		case entity.ErrAccountAlreadyExists:
			commonResponse.UniqueConstraintError(c, "name", request.Name)
		case entity.ErrInvalidAccountName:
			commonResponse.FieldError(c, "name", "邮件账号名称无效")
		case entity.ErrInvalidProvider:
			commonResponse.FieldError(c, "provider", "服务提供商无效")
		case entity.ErrInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "发信地址无效")
		case entity.ErrInvalidSMTPHost:
			commonResponse.FieldError(c, "host", "SMTP服务器地址无效")
		case entity.ErrInvalidSMTPPort:
			commonResponse.FieldError(c, "port", "SMTP端口无效")
		case entity.ErrInvalidSMTPUsername:
			commonResponse.FieldError(c, "username", "SMTP用户名无效")
		case entity.ErrInvalidSMTPPassword:
			commonResponse.FieldError(c, "password", "SMTP密码无效")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Updated(c, account)
}

// GetEmailAccount 获取邮件账号
func (h *EmailAccountHandler) GetEmailAccount(c *gin.Context) {
	accountIDStr := c.Query("account_id")
	if accountIDStr == "" {
		commonResponse.FieldError(c, "account_id", "账号ID不能为空")
		return
	}

	// 将accountID转换为int64
	accountID, err := strconv.ParseInt(accountIDStr, 10, 64)
	if err != nil {
		commonResponse.FieldError(c, "account_id", "账号ID格式错误")
		return
	}

	request := &dto.GetEmailAccountRequest{
		AccountID: accountID,
	}

	account, err := h.accountService.GetEmailAccount(c.Request.Context(), request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, account)
}

// GetEmailAccountList 获取邮件账号列表
func (h *EmailAccountHandler) GetEmailAccountList(c *gin.Context) {
	var request dto.ListEmailAccountsRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	// 验证分页参数
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 || request.PageSize > 100 {
		request.PageSize = 20
	}

	result, err := h.accountService.ListEmailAccounts(c.Request.Context(), tenantId, &request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Paginated(c, result.Accounts, result.Page, result.PageSize, result.Total)
}

// TestEmailAccount 测试邮件账号
func (h *EmailAccountHandler) TestEmailAccount(c *gin.Context) {
	var request dto.TestEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	result, err := h.accountService.TestEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		switch err {
		case entity.ErrAccountInactive:
			commonResponse.BusinessError(c, commonResponse.CodeBusinessLogicError, "邮件账号未激活")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Success(c, result)
}

// DeleteEmailAccount 删除邮件账号
func (h *EmailAccountHandler) DeleteEmailAccount(c *gin.Context) {
	var request dto.DeleteEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	err := h.accountService.DeleteEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Deleted(c)
}

// GetEmailAccountTypes 获取邮件账号类型列表
func (h *EmailAccountHandler) GetEmailAccountTypes(c *gin.Context) {
	// 返回支持的邮件账号类型
	types := []map[string]interface{}{
		{
			"type":        "smtp",
			"name":        "SMTP",
			"description": "通用SMTP邮件服务",
		},
		{
			"type":        "gmail",
			"name":        "Gmail",
			"description": "Google Gmail邮件服务",
		},
		{
			"type":        "outlook",
			"name":        "Outlook",
			"description": "Microsoft Outlook邮件服务",
		},
		{
			"type":        "qq",
			"name":        "QQ邮箱",
			"description": "腾讯QQ邮箱服务",
		},
		{
			"type":        "163",
			"name":        "163邮箱",
			"description": "网易163邮箱服务",
		},
	}

	commonResponse.Success(c, types)
}

// GetEmailAccountTestStatus 获取邮件账号测试状态列表
func (h *EmailAccountHandler) GetEmailAccountTestStatus(c *gin.Context) {
	// 返回支持的邮件账号测试状态
	statuses := []map[string]interface{}{
		{
			"status":      "pending",
			"name":        "待测试",
			"description": "账号配置完成，等待测试",
		},
		{
			"status":      "testing",
			"name":        "测试中",
			"description": "正在进行连接测试",
		},
		{
			"status":      "success",
			"name":        "测试成功",
			"description": "账号配置正确，连接测试成功",
		},
		{
			"status":      "failed",
			"name":        "测试失败",
			"description": "账号配置错误，连接测试失败",
		},
	}

	commonResponse.Success(c, statuses)
}
