# Email模块发送功能重构设计方案

## 1. 整体架构设计

### 1.1 架构图

```mermaid
graph TB
    subgraph "接口层 (Interface Layer)"
        A[HTTP API] --> B[gRPC API]
        B --> C[消息队列监听器]
    end
    
    subgraph "应用层 (Application Layer)"
        D[邮件应用服务]
        E[队列管理服务]
        F[模板渲染服务]
        G[批量发送服务]
    end
    
    subgraph "领域层 (Domain Layer)"
        H[邮件聚合根]
        I[模板聚合根]
        J[账户聚合根]
        K[发送策略]
        L[重试策略]
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        M[邮件仓储]
        N[模板仓储]
        O[账户仓储]
        P[发送器工厂]
        Q[消息队列]
        R[缓存服务]
    end
    
    subgraph "外部服务 (External Services)"
        S[SMTP服务]
        T[阿里云DM]
        U[其他邮件API]
    end
    
    A --> D
    B --> D
    C --> E
    D --> E
    D --> F
    D --> G
    E --> H
    F --> I
    G --> H
    H --> M
    I --> N
    J --> O
    K --> P
    P --> S
    P --> T
    P --> U
    E --> Q
    D --> R
```

### 1.2 分层职责

#### 接口层 (Interface Layer)
- **HTTP API**: 提供RESTful接口，处理Web请求
- **gRPC API**: 提供高性能的服务间通信接口
- **消息队列监听器**: 监听队列消息，触发邮件发送

#### 应用层 (Application Layer)
- **邮件应用服务**: 协调邮件发送的完整流程
- **队列管理服务**: 管理邮件发送队列，处理优先级和调度
- **模板渲染服务**: 处理邮件模板的渲染和变量替换
- **批量发送服务**: 处理批量邮件发送的业务逻辑

#### 领域层 (Domain Layer)
- **邮件聚合根**: 封装邮件相关的业务规则和不变性
- **模板聚合根**: 管理模板的生命周期和验证规则
- **账户聚合根**: 管理发送账户的配置和限制
- **发送策略**: 定义不同类型邮件的发送策略
- **重试策略**: 定义失败重试的业务规则

#### 基础设施层 (Infrastructure Layer)
- **仓储实现**: 数据持久化的具体实现
- **发送器工厂**: 根据账户类型创建对应的发送器
- **消息队列**: 异步消息处理
- **缓存服务**: 提高查询性能

## 2. 核心流程设计

### 2.1 邮件发送主流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API接口
    participant AppService as 应用服务
    participant QueueService as 队列服务
    participant TemplateService as 模板服务
    participant EmailRepo as 邮件仓储
    participant Queue as 消息队列
    participant Worker as 队列工作者
    participant Sender as 发送器
    
    Client->>API: 发送邮件请求
    API->>AppService: 调用发送服务
    AppService->>TemplateService: 渲染模板
    TemplateService-->>AppService: 返回渲染结果
    AppService->>EmailRepo: 保存邮件到队列表
    EmailRepo-->>AppService: 返回邮件ID
    AppService->>QueueService: 加入发送队列
    QueueService->>Queue: 推送到消息队列
    API-->>Client: 返回邮件ID
    
    Queue->>Worker: 消费队列消息
    Worker->>EmailRepo: 获取邮件详情
    Worker->>Sender: 执行实际发送
    Sender-->>Worker: 返回发送结果
    Worker->>EmailRepo: 更新发送状态
```

### 2.2 批量发送流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as API接口
    participant BatchService as 批量服务
    participant TemplateService as 模板服务
    participant EmailRepo as 邮件仓储
    participant QueueService as 队列服务
    
    Client->>API: 批量发送请求
    API->>BatchService: 调用批量服务
    BatchService->>TemplateService: 批量渲染模板
    TemplateService-->>BatchService: 返回渲染结果
    BatchService->>EmailRepo: 批量保存邮件
    EmailRepo-->>BatchService: 返回邮件ID列表
    BatchService->>QueueService: 批量加入队列
    API-->>Client: 返回批次ID和邮件ID列表
```

## 3. 关键接口设计

### 3.1 应用服务接口

```go
// EmailApplicationService 邮件应用服务接口
type EmailApplicationService interface {
    // SendTemplateEmail 发送模板邮件
    SendTemplateEmail(ctx context.Context, req *SendTemplateEmailRequest) (*SendEmailResponse, error)
    
    // SendDirectEmail 直接发送邮件
    SendDirectEmail(ctx context.Context, req *SendDirectEmailRequest) (*SendEmailResponse, error)
    
    // SendBatchEmails 批量发送邮件
    SendBatchEmails(ctx context.Context, req *SendBatchEmailsRequest) (*SendBatchEmailsResponse, error)
    
    // CancelEmail 取消邮件发送
    CancelEmail(ctx context.Context, emailID string) error
    
    // RetryEmail 重试发送邮件
    RetryEmail(ctx context.Context, emailID string) error
    
    // GetEmailStatus 获取邮件状态
    GetEmailStatus(ctx context.Context, emailID string) (*EmailStatusResponse, error)
    
    // GetEmailHistory 获取邮件发送历史
    GetEmailHistory(ctx context.Context, req *GetEmailHistoryRequest) (*GetEmailHistoryResponse, error)
}
```

### 3.2 队列管理服务接口

```go
// QueueManagementService 队列管理服务接口
type QueueManagementService interface {
    // EnqueueEmail 将邮件加入发送队列
    EnqueueEmail(ctx context.Context, emailID string, priority Priority) error
    
    // EnqueueBatchEmails 批量加入发送队列
    EnqueueBatchEmails(ctx context.Context, emailIDs []string, priority Priority) error
    
    // DequeueEmail 从队列中取出邮件
    DequeueEmail(ctx context.Context) (*QueuedEmail, error)
    
    // UpdateEmailStatus 更新邮件状态
    UpdateEmailStatus(ctx context.Context, emailID string, status EmailStatus) error
    
    // ScheduleRetry 安排重试
    ScheduleRetry(ctx context.Context, emailID string, retryAt time.Time) error
    
    // GetQueueStats 获取队列统计信息
    GetQueueStats(ctx context.Context) (*QueueStats, error)
}
```

### 3.3 模板渲染服务接口

```go
// TemplateRenderService 模板渲染服务接口
type TemplateRenderService interface {
    // RenderTemplate 渲染单个模板
    RenderTemplate(ctx context.Context, req *RenderTemplateRequest) (*RenderTemplateResponse, error)
    
    // RenderBatchTemplates 批量渲染模板
    RenderBatchTemplates(ctx context.Context, req *RenderBatchTemplatesRequest) (*RenderBatchTemplatesResponse, error)
    
    // ValidateTemplate 验证模板语法
    ValidateTemplate(ctx context.Context, template *Template) error
    
    // PreviewTemplate 预览模板效果
    PreviewTemplate(ctx context.Context, req *PreviewTemplateRequest) (*PreviewTemplateResponse, error)
}
```

## 4. 数据传输对象 (DTO) 设计

### 4.1 发送请求DTO

```go
// SendTemplateEmailRequest 模板邮件发送请求
type SendTemplateEmailRequest struct {
    TemplateCode  string                 `json:"template_code" validate:"required"`
    ToAddresses   []string              `json:"to_addresses" validate:"required,min=1"`
    CcAddresses   []string              `json:"cc_addresses,omitempty"`
    BccAddresses  []string              `json:"bcc_addresses,omitempty"`
    Variables     map[string]interface{} `json:"variables,omitempty"`
    Priority      Priority              `json:"priority,omitempty"`
    ScheduledAt   *time.Time            `json:"scheduled_at,omitempty"`
    Attachments   []Attachment          `json:"attachments,omitempty"`
}

// SendDirectEmailRequest 直接邮件发送请求
type SendDirectEmailRequest struct {
    AccountID     int64        `json:"account_id" validate:"required"`
    ToAddresses   []string     `json:"to_addresses" validate:"required,min=1"`
    CcAddresses   []string     `json:"cc_addresses,omitempty"`
    BccAddresses  []string     `json:"bcc_addresses,omitempty"`
    Subject       string       `json:"subject" validate:"required"`
    HTMLContent   string       `json:"html_content,omitempty"`
    TextContent   string       `json:"text_content,omitempty"`
    Priority      Priority     `json:"priority,omitempty"`
    ScheduledAt   *time.Time   `json:"scheduled_at,omitempty"`
    Attachments   []Attachment `json:"attachments,omitempty"`
}

// SendBatchEmailsRequest 批量邮件发送请求
type SendBatchEmailsRequest struct {
    TemplateCode string                   `json:"template_code" validate:"required"`
    Recipients   []BatchEmailRecipient    `json:"recipients" validate:"required,min=1"`
    Priority     Priority                 `json:"priority,omitempty"`
    ScheduledAt  *time.Time              `json:"scheduled_at,omitempty"`
}

// BatchEmailRecipient 批量邮件收件人
type BatchEmailRecipient struct {
    ToAddress    string                 `json:"to_address" validate:"required,email"`
    CcAddresses  []string              `json:"cc_addresses,omitempty"`
    BccAddresses []string              `json:"bcc_addresses,omitempty"`
    Variables    map[string]interface{} `json:"variables,omitempty"`
}
```

### 4.2 响应DTO

```go
// SendEmailResponse 邮件发送响应
type SendEmailResponse struct {
    EmailID   string      `json:"email_id"`
    Status    EmailStatus `json:"status"`
    Message   string      `json:"message,omitempty"`
    CreatedAt time.Time   `json:"created_at"`
}

// SendBatchEmailsResponse 批量邮件发送响应
type SendBatchEmailsResponse struct {
    BatchID     string              `json:"batch_id"`
    TotalCount  int                 `json:"total_count"`
    SuccessCount int                `json:"success_count"`
    FailedCount int                 `json:"failed_count"`
    EmailResults []SendEmailResponse `json:"email_results"`
    CreatedAt   time.Time           `json:"created_at"`
}

// EmailStatusResponse 邮件状态响应
type EmailStatusResponse struct {
    EmailID     string      `json:"email_id"`
    Status      EmailStatus `json:"status"`
    RetryCount  int         `json:"retry_count"`
    MaxRetries  int         `json:"max_retries"`
    ErrorMsg    string      `json:"error_msg,omitempty"`
    SentAt      *time.Time  `json:"sent_at,omitempty"`
    CreatedAt   time.Time   `json:"created_at"`
    UpdatedAt   time.Time   `json:"updated_at"`
}
```

## 5. 错误码设计

### 5.1 错误码分类

```go
// 邮件模块错误码范围: 200000-299999
const (
    // 发送相关错误 200000-209999
    CodeEmailSendFailed        = 200001 // 邮件发送失败
    CodeEmailQueueFull         = 200002 // 邮件队列已满
    CodeEmailRateLimitExceeded = 200003 // 发送频率超限
    CodeEmailAccountInactive   = 200004 // 邮件账户未激活
    CodeEmailAccountNotFound   = 200005 // 邮件账户不存在
    
    // 模板相关错误 210000-219999
    CodeTemplateNotFound       = 210001 // 模板不存在
    CodeTemplateRenderFailed   = 210002 // 模板渲染失败
    CodeTemplateVariableMissing = 210003 // 模板变量缺失
    CodeTemplateInactive       = 210004 // 模板未激活
    
    // 队列相关错误 220000-229999
    CodeQueueOperationFailed   = 220001 // 队列操作失败
    CodeEmailNotInQueue        = 220002 // 邮件不在队列中
    CodeEmailAlreadyCancelled  = 220003 // 邮件已取消
    CodeEmailAlreadySent       = 220004 // 邮件已发送
    
    // 系统相关错误 290000-299999
    CodeSystemError            = 290001 // 系统错误
    CodeDatabaseError          = 290002 // 数据库错误
    CodeValidationError        = 290003 // 参数验证错误
)
```

### 5.2 错误处理实现

```go
// EmailError 邮件错误结构
type EmailError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *EmailError) Error() string {
    return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
}

// 便捷错误创建函数
func NewEmailSendFailedError(details string) *EmailError {
    return &EmailError{
        Code:    CodeEmailSendFailed,
        Message: "邮件发送失败",
        Details: details,
    }
}

func NewTemplateNotFoundError(templateCode string) *EmailError {
    return &EmailError{
        Code:    CodeTemplateNotFound,
        Message: "模板不存在",
        Details: fmt.Sprintf("模板代码: %s", templateCode),
    }
}

// 统一错误处理器
func HandleEmailError(ctx context.Context, err error, logger logiface.Logger) *EmailError {
    if emailErr, ok := err.(*EmailError); ok {
        return emailErr
    }
    
    logger.Error(ctx, "Unexpected error occurred", logiface.Error(err))
    return &EmailError{
        Code:    CodeSystemError,
        Message: "系统内部错误",
        Details: "请稍后重试或联系管理员",
    }
}
```

## 6. 数据库查询与权限校验

### 6.1 安全查询实现

```go
// EmailRepository 邮件仓储接口
type EmailRepository interface {
    // Save 保存邮件（自动添加租户ID和软删除检查）
    Save(ctx context.Context, email *EmailMessage) error
    
    // FindByID 根据ID查找邮件（自动添加租户ID和软删除检查）
    FindByID(ctx context.Context, emailID string) (*EmailMessage, error)
    
    // FindByTenantAndStatus 根据租户和状态查找邮件
    FindByTenantAndStatus(ctx context.Context, tenantID int64, status EmailStatus, page, size int) ([]*EmailMessage, int64, error)
    
    // UpdateStatus 更新邮件状态
    UpdateStatus(ctx context.Context, emailID string, status EmailStatus) error
    
    // BatchSave 批量保存邮件
    BatchSave(ctx context.Context, emails []*EmailMessage) error
    
    // BatchUpdateStatus 批量更新状态
    BatchUpdateStatus(ctx context.Context, emailIDs []string, status EmailStatus) error
}

// 仓储实现示例
type emailRepositoryImpl struct {
    db     *gorm.DB
    logger logiface.Logger
}

// FindByID 安全查询实现
func (r *emailRepositoryImpl) FindByID(ctx context.Context, emailID string) (*EmailMessage, error) {
    // 从上下文获取租户ID
    tenantID := GetTenantIDFromContext(ctx)
    if tenantID == 0 {
        return nil, errors.New("租户ID不能为空")
    }
    
    var email EmailMessage
    err := r.db.WithContext(ctx).
        Where("email_id = ? AND tenant_id = ? AND deleted_at IS NULL", emailID, tenantID).
        First(&email).Error
    
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, nil
        }
        return nil, err
    }
    
    return &email, nil
}

// FindByTenantAndStatus 分页查询实现
func (r *emailRepositoryImpl) FindByTenantAndStatus(ctx context.Context, tenantID int64, status EmailStatus, page, size int) ([]*EmailMessage, int64, error) {
    var emails []*EmailMessage
    var total int64
    
    // 构建基础查询
    query := r.db.WithContext(ctx).
        Where("tenant_id = ? AND deleted_at IS NULL", tenantID)
    
    if status != "" {
        query = query.Where("status = ?", status)
    }
    
    // 获取总数
    if err := query.Model(&EmailMessage{}).Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 分页查询
    offset := (page - 1) * size
    if err := query.Offset(offset).Limit(size).Order("created_at DESC").Find(&emails).Error; err != nil {
        return nil, 0, err
    }
    
    return emails, total, nil
}
```

### 6.2 用户上下文管理

```go
// UserContext 用户上下文
type UserContext struct {
    UserID      int64  `json:"user_id"`
    TenantID    int64  `json:"tenant_id"`
    IsAdmin     bool   `json:"is_admin"`
    IsSuperUser bool   `json:"is_super_user"`
    Permissions []string `json:"permissions"`
}

// 上下文键
type contextKey string

const (
    UserContextKey contextKey = "user_context"
)

// GetUserContextFromContext 从上下文获取用户信息
func GetUserContextFromContext(ctx context.Context) *UserContext {
    if userCtx, ok := ctx.Value(UserContextKey).(*UserContext); ok {
        return userCtx
    }
    return nil
}

// GetTenantIDFromContext 从上下文获取租户ID
func GetTenantIDFromContext(ctx context.Context) int64 {
    if userCtx := GetUserContextFromContext(ctx); userCtx != nil {
        return userCtx.TenantID
    }
    return 0
}

// GetUserIDFromContext 从上下文获取用户ID
func GetUserIDFromContext(ctx context.Context) int64 {
    if userCtx := GetUserContextFromContext(ctx); userCtx != nil {
        return userCtx.UserID
    }
    return 0
}

// 权限检查中间件
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从JWT token或其他方式获取用户信息
        token := c.GetHeader("Authorization")
        if token == "" {
            c.JSON(401, gin.H{"error": "未授权访问"})
            c.Abort()
            return
        }
        
        // 解析token获取用户信息
        userCtx, err := parseToken(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "无效的访问令牌"})
            c.Abort()
            return
        }
        
        // 将用户上下文添加到请求上下文
        ctx := context.WithValue(c.Request.Context(), UserContextKey, userCtx)
        c.Request = c.Request.WithContext(ctx)
        
        c.Next()
    }
}
```

## 7. 队列化发送实现

### 7.1 队列服务实现

```go
// QueueService 队列服务实现
type QueueService struct {
    emailRepo    EmailRepository
    messageQueue MessageQueue
    logger       logiface.Logger
}

// EnqueueEmail 将邮件加入发送队列
func (s *QueueService) EnqueueEmail(ctx context.Context, emailID string, priority Priority) error {
    // 创建队列消息
    queueMessage := &QueueMessage{
        EmailID:   emailID,
        Priority:  priority,
        CreatedAt: time.Now(),
    }
    
    // 推送到消息队列
    if err := s.messageQueue.Publish(ctx, "email.send", queueMessage); err != nil {
        s.logger.Error(ctx, "Failed to enqueue email",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        return err
    }
    
    // 更新邮件状态为排队中
    if err := s.emailRepo.UpdateStatus(ctx, emailID, StatusQueued); err != nil {
        s.logger.Error(ctx, "Failed to update email status to queued",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        return err
    }
    
    s.logger.Info(ctx, "Email enqueued successfully",
        logiface.String("email_id", emailID),
        logiface.String("priority", string(priority)))
    
    return nil
}

// EnqueueBatchEmails 批量加入发送队列
func (s *QueueService) EnqueueBatchEmails(ctx context.Context, emailIDs []string, priority Priority) error {
    // 批量创建队列消息
    queueMessages := make([]*QueueMessage, len(emailIDs))
    for i, emailID := range emailIDs {
        queueMessages[i] = &QueueMessage{
            EmailID:   emailID,
            Priority:  priority,
            CreatedAt: time.Now(),
        }
    }
    
    // 批量推送到消息队列
    if err := s.messageQueue.PublishBatch(ctx, "email.send", queueMessages); err != nil {
        s.logger.Error(ctx, "Failed to enqueue batch emails",
            logiface.Int("count", len(emailIDs)),
            logiface.Error(err))
        return err
    }
    
    // 批量更新邮件状态
    if err := s.emailRepo.BatchUpdateStatus(ctx, emailIDs, StatusQueued); err != nil {
        s.logger.Error(ctx, "Failed to update batch email status to queued",
            logiface.Int("count", len(emailIDs)),
            logiface.Error(err))
        return err
    }
    
    s.logger.Info(ctx, "Batch emails enqueued successfully",
        logiface.Int("count", len(emailIDs)),
        logiface.String("priority", string(priority)))
    
    return nil
}
```

### 7.2 队列工作者实现

```go
// EmailWorker 邮件发送工作者
type EmailWorker struct {
    emailRepo      EmailRepository
    accountRepo    EmailAccountRepository
    senderFactory  *EmailSenderFactory
    messageQueue   MessageQueue
    retryStrategy  RetryStrategy
    logger         logiface.Logger
}

// Start 启动工作者
func (w *EmailWorker) Start(ctx context.Context) error {
    return w.messageQueue.Subscribe(ctx, "email.send", w.processEmail)
}

// processEmail 处理邮件发送
func (w *EmailWorker) processEmail(ctx context.Context, message *QueueMessage) error {
    emailID := message.EmailID
    
    // 获取邮件详情
    email, err := w.emailRepo.FindByID(ctx, emailID)
    if err != nil {
        w.logger.Error(ctx, "Failed to find email",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        return err
    }
    
    if email == nil {
        w.logger.Warn(ctx, "Email not found",
            logiface.String("email_id", emailID))
        return nil // 邮件不存在，跳过处理
    }
    
    // 检查邮件状态
    if email.Status != StatusQueued && email.Status != StatusRetrying {
        w.logger.Info(ctx, "Email status is not queued or retrying, skipping",
            logiface.String("email_id", emailID),
            logiface.String("status", string(email.Status)))
        return nil
    }
    
    // 更新状态为发送中
    if err := w.emailRepo.UpdateStatus(ctx, emailID, StatusSending); err != nil {
        w.logger.Error(ctx, "Failed to update email status to sending",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        return err
    }
    
    // 获取发送账户
    account, err := w.accountRepo.FindByID(ctx, email.AccountID)
    if err != nil {
        w.logger.Error(ctx, "Failed to find email account",
            logiface.String("email_id", emailID),
            logiface.Int64("account_id", email.AccountID),
            logiface.Error(err))
        w.handleSendFailure(ctx, email, err)
        return err
    }
    
    // 执行发送
    if err := w.senderFactory.SendEmail(ctx, account, email); err != nil {
        w.logger.Error(ctx, "Failed to send email",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        w.handleSendFailure(ctx, email, err)
        return err
    }
    
    // 发送成功，更新状态
    email.Status = StatusSent
    email.SentAt = time.Now()
    if err := w.emailRepo.UpdateStatus(ctx, emailID, StatusSent); err != nil {
        w.logger.Error(ctx, "Failed to update email status to sent",
            logiface.String("email_id", emailID),
            logiface.Error(err))
        return err
    }
    
    w.logger.Info(ctx, "Email sent successfully",
        logiface.String("email_id", emailID),
        logiface.String("to", email.ToAddress))
    
    return nil
}

// handleSendFailure 处理发送失败
func (w *EmailWorker) handleSendFailure(ctx context.Context, email *EmailMessage, sendErr error) {
    email.RetryCount++
    email.ErrorMsg = sendErr.Error()
    
    // 检查是否需要重试
    if email.RetryCount < email.MaxRetries {
        // 计算重试时间
        retryAt := w.retryStrategy.CalculateRetryTime(email.RetryCount)
        
        // 更新状态为重试中
        email.Status = StatusRetrying
        if err := w.emailRepo.UpdateStatus(ctx, email.EmailID, StatusRetrying); err != nil {
            w.logger.Error(ctx, "Failed to update email status to retrying",
                logiface.String("email_id", email.EmailID),
                logiface.Error(err))
        }
        
        // 安排重试
        w.scheduleRetry(ctx, email.EmailID, retryAt)
        
        w.logger.Info(ctx, "Email scheduled for retry",
            logiface.String("email_id", email.EmailID),
            logiface.Int("retry_count", email.RetryCount),
            logiface.Time("retry_at", retryAt))
    } else {
        // 重试次数已达上限，标记为失败
        email.Status = StatusFailed
        if err := w.emailRepo.UpdateStatus(ctx, email.EmailID, StatusFailed); err != nil {
            w.logger.Error(ctx, "Failed to update email status to failed",
                logiface.String("email_id", email.EmailID),
                logiface.Error(err))
        }
        
        w.logger.Error(ctx, "Email failed after max retries",
            logiface.String("email_id", email.EmailID),
            logiface.Int("retry_count", email.RetryCount),
            logiface.String("error", sendErr.Error()))
    }
}
```

## 8. 批量操作优化

### 8.1 批量发送服务

```go
// BatchEmailService 批量邮件服务
type BatchEmailService struct {
    emailRepo       EmailRepository
    templateService TemplateRenderService
    queueService    QueueManagementService
    logger          logiface.Logger
}

// SendBatchEmails 批量发送邮件
func (s *BatchEmailService) SendBatchEmails(ctx context.Context, req *SendBatchEmailsRequest) (*SendBatchEmailsResponse, error) {
    batchID := fmt.Sprintf("batch_%d", id.GenerateID())
    
    // 批量渲染模板
    renderReq := &RenderBatchTemplatesRequest{
        TemplateCode: req.TemplateCode,
        Recipients:   req.Recipients,
    }
    
    renderResp, err := s.templateService.RenderBatchTemplates(ctx, renderReq)
    if err != nil {
        s.logger.Error(ctx, "Failed to render batch templates",
            logiface.String("batch_id", batchID),
            logiface.Error(err))
        return nil, err
    }
    
    // 创建邮件实体列表
    emails := make([]*EmailMessage, len(renderResp.Results))
    emailIDs := make([]string, len(renderResp.Results))
    
    for i, result := range renderResp.Results {
        if result.Error != nil {
            s.logger.Warn(ctx, "Template render failed for recipient",
                logiface.String("batch_id", batchID),
                logiface.String("to_address", result.ToAddress),
                logiface.Error(result.Error))
            continue
        }
        
        email, err := entity.NewEmailMessage(
            result.ToAddress,
            renderResp.AccountID,
            result.Subject,
        )
        if err != nil {
            s.logger.Error(ctx, "Failed to create email message",
                logiface.String("batch_id", batchID),
                logiface.String("to_address", result.ToAddress),
                logiface.Error(err))
            continue
        }
        
        email.EmailID = fmt.Sprintf("%d", id.GenerateID())
        email.HTMLContent = result.HTMLContent
        email.TextContent = result.TextContent
        email.BatchID = batchID
        
        emails[i] = email
        emailIDs[i] = email.EmailID
    }
    
    // 批量保存邮件
    if err := s.emailRepo.BatchSave(ctx, emails); err != nil {
        s.logger.Error(ctx, "Failed to batch save emails",
            logiface.String("batch_id", batchID),
            logiface.Error(err))
        return nil, err
    }
    
    // 批量加入发送队列
    if err := s.queueService.EnqueueBatchEmails(ctx, emailIDs, req.Priority); err != nil {
        s.logger.Error(ctx, "Failed to enqueue batch emails",
            logiface.String("batch_id", batchID),
            logiface.Error(err))
        return nil, err
    }
    
    // 构建响应
    emailResults := make([]SendEmailResponse, len(emails))
    successCount := 0
    
    for i, email := range emails {
        if email != nil {
            emailResults[i] = SendEmailResponse{
                EmailID:   email.EmailID,
                Status:    email.Status,
                CreatedAt: email.CreatedAt,
            }
            successCount++
        }
    }
    
    response := &SendBatchEmailsResponse{
        BatchID:      batchID,
        TotalCount:   len(req.Recipients),
        SuccessCount: successCount,
        FailedCount:  len(req.Recipients) - successCount,
        EmailResults: emailResults,
        CreatedAt:    time.Now(),
    }
    
    s.logger.Info(ctx, "Batch emails processed",
        logiface.String("batch_id", batchID),
        logiface.Int("total", response.TotalCount),
        logiface.Int("success", response.SuccessCount),
        logiface.Int("failed", response.FailedCount))
    
    return response, nil
}
```

### 8.2 数据库批量操作

```go
// BatchSave 批量保存邮件
func (r *emailRepositoryImpl) BatchSave(ctx context.Context, emails []*EmailMessage) error {
    if len(emails) == 0 {
        return nil
    }
    
    // 使用事务确保数据一致性
    return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // 批量插入，每次最多1000条记录
        batchSize := 1000
        for i := 0; i < len(emails); i += batchSize {
            end := i + batchSize
            if end > len(emails) {
                end = len(emails)
            }
            
            batch := emails[i:end]
            if err := tx.CreateInBatches(batch, len(batch)).Error; err != nil {
                r.logger.Error(ctx, "Failed to batch insert emails",
                    logiface.Int("batch_start", i),
                    logiface.Int("batch_size", len(batch)),
                    logiface.Error(err))
                return err
            }
        }
        
        return nil
    })
}

// BatchUpdateStatus 批量更新状态
func (r *emailRepositoryImpl) BatchUpdateStatus(ctx context.Context, emailIDs []string, status EmailStatus) error {
    if len(emailIDs) == 0 {
        return nil
    }
    
    // 获取租户ID
    tenantID := GetTenantIDFromContext(ctx)
    if tenantID == 0 {
        return errors.New("租户ID不能为空")
    }
    
    // 批量更新
    result := r.db.WithContext(ctx).
        Model(&EmailMessage{}).
        Where("email_id IN ? AND tenant_id = ? AND deleted_at IS NULL", emailIDs, tenantID).
        Updates(map[string]interface{}{
            "status":     status,
            "updated_at": time.Now(),
        })
    
    if result.Error != nil {
        r.logger.Error(ctx, "Failed to batch update email status",
            logiface.Int("count", len(emailIDs)),
            logiface.String("status", string(status)),
            logiface.Error(result.Error))
        return result.Error
    }
    
    r.logger.Info(ctx, "Batch updated email status",
        logiface.Int("count", len(emailIDs)),
        logiface.Int64("affected_rows", result.RowsAffected),
        logiface.String("status", string(status)))
    
    return nil
}
```

## 9. 重试策略设计

### 9.1 重试策略接口

```go
// RetryStrategy 重试策略接口
type RetryStrategy interface {
    // CalculateRetryTime 计算重试时间
    CalculateRetryTime(retryCount int) time.Time
    
    // ShouldRetry 判断是否应该重试
    ShouldRetry(err error) bool
    
    // GetMaxRetries 获取最大重试次数
    GetMaxRetries() int
}

// ExponentialBackoffStrategy 指数退避重试策略
type ExponentialBackoffStrategy struct {
    BaseDelay  time.Duration
    MaxDelay   time.Duration
    Multiplier float64
    MaxRetries int
}

// CalculateRetryTime 计算重试时间
func (s *ExponentialBackoffStrategy) CalculateRetryTime(retryCount int) time.Time {
    delay := s.BaseDelay
    
    // 计算指数退避延迟
    for i := 0; i < retryCount; i++ {
        delay = time.Duration(float64(delay) * s.Multiplier)
        if delay > s.MaxDelay {
            delay = s.MaxDelay
            break
        }
    }
    
    // 添加随机抖动，避免雷群效应
    jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
    delay += jitter
    
    return time.Now().Add(delay)
}

// ShouldRetry 判断是否应该重试
func (s *ExponentialBackoffStrategy) ShouldRetry(err error) bool {
    // 根据错误类型判断是否应该重试
    if err == nil {
        return false
    }
    
    // 网络错误通常可以重试
    if strings.Contains(err.Error(), "network") ||
       strings.Contains(err.Error(), "timeout") ||
       strings.Contains(err.Error(), "connection") {
        return true
    }
    
    // 临时性错误可以重试
    if strings.Contains(err.Error(), "temporary") ||
       strings.Contains(err.Error(), "rate limit") {
        return true
    }
    
    // 认证错误、格式错误等不应该重试
    if strings.Contains(err.Error(), "authentication") ||
       strings.Contains(err.Error(), "invalid") ||
       strings.Contains(err.Error(), "malformed") {
        return false
    }
    
    return true // 默认重试
}

// GetMaxRetries 获取最大重试次数
func (s *ExponentialBackoffStrategy) GetMaxRetries() int {
    return s.MaxRetries
}
```

### 9.2 重试调度器

```go
// RetryScheduler 重试调度器
type RetryScheduler struct {
    queueService QueueManagementService
    scheduler    *cron.Cron
    logger       logiface.Logger
}

// NewRetryScheduler 创建重试调度器
func NewRetryScheduler(queueService QueueManagementService, logger logiface.Logger) *RetryScheduler {
    return &RetryScheduler{
        queueService: queueService,
        scheduler:    cron.New(),
        logger:       logger,
    }
}

// Start 启动调度器
func (s *RetryScheduler) Start() {
    // 每分钟检查一次需要重试的邮件
    s.scheduler.AddFunc("@every 1m", s.processRetryEmails)
    s.scheduler.Start()
}

// Stop 停止调度器
func (s *RetryScheduler) Stop() {
    s.scheduler.Stop()
}

// processRetryEmails 处理需要重试的邮件
func (s *RetryScheduler) processRetryEmails() {
    ctx := context.Background()
    
    // 查找需要重试的邮件
    retryEmails, err := s.queueService.GetRetryEmails(ctx, time.Now())
    if err != nil {
        s.logger.Error(ctx, "Failed to get retry emails", logiface.Error(err))
        return
    }
    
    if len(retryEmails) == 0 {
        return
    }
    
    s.logger.Info(ctx, "Processing retry emails", logiface.Int("count", len(retryEmails)))
    
    // 重新加入发送队列
    for _, email := range retryEmails {
        if err := s.queueService.EnqueueEmail(ctx, email.EmailID, email.Priority); err != nil {
            s.logger.Error(ctx, "Failed to enqueue retry email",
                logiface.String("email_id", email.EmailID),
                logiface.Error(err))
        }
    }
}
```

## 10. 监控与运维

### 10.1 性能监控

```go
// EmailMetrics 邮件指标
type EmailMetrics struct {
    // 发送统计
    TotalSent     int64 `json:"total_sent"`
    TotalFailed   int64 `json:"total_failed"`
    TotalQueued   int64 `json:"total_queued"`
    TotalRetrying int64 `json:"total_retrying"`
    
    // 性能指标
    AvgSendTime   time.Duration `json:"avg_send_time"`
    SuccessRate   float64       `json:"success_rate"`
    
    // 队列指标
    QueueLength   int64 `json:"queue_length"`
    ProcessingRate float64 `json:"processing_rate"`
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
    emailRepo EmailRepository
    logger    logiface.Logger
}

// CollectMetrics 收集指标
func (c *MetricsCollector) CollectMetrics(ctx context.Context, tenantID int64) (*EmailMetrics, error) {
    metrics := &EmailMetrics{}
    
    // 收集发送统计
    stats, err := c.emailRepo.GetSendStats(ctx, tenantID, time.Now().Add(-24*time.Hour), time.Now())
    if err != nil {
        return nil, err
    }
    
    metrics.TotalSent = stats.SentCount
    metrics.TotalFailed = stats.FailedCount
    metrics.TotalQueued = stats.QueuedCount
    metrics.TotalRetrying = stats.RetryingCount
    
    // 计算成功率
    total := metrics.TotalSent + metrics.TotalFailed
    if total > 0 {
        metrics.SuccessRate = float64(metrics.TotalSent) / float64(total)
    }
    
    return metrics, nil
}
```

### 10.2 健康检查

```go
// HealthChecker 健康检查器
type HealthChecker struct {
    emailRepo    EmailRepository
    queueService QueueManagementService
    logger       logiface.Logger
}

// CheckHealth 检查系统健康状态
func (h *HealthChecker) CheckHealth(ctx context.Context) *HealthStatus {
    status := &HealthStatus{
        Status:    "healthy",
        Timestamp: time.Now(),
        Checks:    make(map[string]CheckResult),
    }
    
    // 检查数据库连接
    if err := h.checkDatabase(ctx); err != nil {
        status.Checks["database"] = CheckResult{
            Status: "unhealthy",
            Error:  err.Error(),
        }
        status.Status = "unhealthy"
    } else {
        status.Checks["database"] = CheckResult{Status: "healthy"}
    }
    
    // 检查队列服务
    if err := h.checkQueue(ctx); err != nil {
        status.Checks["queue"] = CheckResult{
            Status: "unhealthy",
            Error:  err.Error(),
        }
        status.Status = "unhealthy"
    } else {
        status.Checks["queue"] = CheckResult{Status: "healthy"}
    }
    
    return status
}

type HealthStatus struct {
    Status    string                 `json:"status"`
    Timestamp time.Time              `json:"timestamp"`
    Checks    map[string]CheckResult `json:"checks"`
}

type CheckResult struct {
    Status string `json:"status"`
    Error  string `json:"error,omitempty"`
}
```

## 11. 测试策略

### 11.1 单元测试示例

```go
// EmailApplicationService 单元测试
func TestEmailApplicationService_SendTemplateEmail(t *testing.T) {
    tests := []struct {
        name           string
        tenantID       int64
        request        *dto.SendTemplateEmailRequest
        mockTemplate   *templateEntity.EmailTemplate
        mockAccount    *entity.EmailAccount
        templateError  error
        accountError   error
        sendError      error
        expectedError  error
        expectedStatus string
    }{
        {
            name:     "成功发送模板邮件",
            tenantID: 1,
            request: &dto.SendTemplateEmailRequest{
                TemplateCode: "welcome",
                ToAddress:    "<EMAIL>",
                Variables:    map[string]interface{}{"name": "张三"},
            },
            mockTemplate: &templateEntity.EmailTemplate{
                ID:          1,
                AccountID:   1,
                Subject:     "欢迎 {{.name}}",
                HTMLContent: "<p>欢迎 {{.name}}！</p>",
            },
            mockAccount: &entity.EmailAccount{
                ID:       1,
                Provider: "SMTP",
                IsActive: true,
            },
            expectedStatus: "queued",
        },
        {
            name:     "模板不存在",
            tenantID: 1,
            request: &dto.SendTemplateEmailRequest{
                TemplateCode: "nonexistent",
                ToAddress:    "<EMAIL>",
            },
            templateError: gorm.ErrRecordNotFound,
            expectedError: emailErrors.NewTemplateNotFoundError("nonexistent"),
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 创建mock对象
            mockEmailRepo := &MockEmailRepository{}
            mockTemplateRepo := &MockTemplateRepository{}
            mockAccountRepo := &MockEmailAccountRepository{}
            mockSenderFactory := &MockEmailSenderFactory{}
            mockLogger := &MockLogger{}
            
            // 设置mock期望
            if tt.templateError != nil {
                mockTemplateRepo.On("GetByCode", mock.Anything, tt.tenantID, tt.request.TemplateCode).Return(nil, tt.templateError)
            } else {
                mockTemplateRepo.On("GetByCode", mock.Anything, tt.tenantID, tt.request.TemplateCode).Return(tt.mockTemplate, nil)
            }
            
            if tt.mockTemplate != nil {
                mockEmailRepo.On("Save", mock.Anything, mock.AnythingOfType("*entity.EmailMessage")).Return(nil)
                
                if tt.accountError != nil {
                    mockAccountRepo.On("FindByID", mock.Anything, tt.mockTemplate.AccountID).Return(nil, tt.accountError)
                } else {
                    mockAccountRepo.On("FindByID", mock.Anything, tt.mockTemplate.AccountID).Return(tt.mockAccount, nil)
                }
                
                if tt.mockAccount != nil {
                    if tt.sendError != nil {
                        mockSenderFactory.On("SendEmail", mock.Anything, tt.mockAccount, mock.AnythingOfType("*entity.EmailMessage")).Return(tt.sendError)
                        mockEmailRepo.On("UpdateStatus", mock.Anything, mock.AnythingOfType("string"), "failed").Return(nil)
                    } else {
                        mockSenderFactory.On("SendEmail", mock.Anything, tt.mockAccount, mock.AnythingOfType("*entity.EmailMessage")).Return(nil)
                        mockEmailRepo.On("UpdateStatus", mock.Anything, mock.AnythingOfType("string"), "sent").Return(nil)
                    }
                }
            }
            
            // 创建服务实例
            service := NewEmailApplicationService(
                mockEmailRepo,
                mockTemplateRepo,
                mockAccountRepo,
                mockSenderFactory,
                mockLogger,
            )
            
            // 执行测试
            response, err := service.SendTemplateEmail(context.Background(), tt.tenantID, tt.request)
            
            // 验证结果
            if tt.expectedError != nil {
                assert.Error(t, err)
                assert.Equal(t, tt.expectedError.Error(), err.Error())
                assert.Nil(t, response)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, response)
                assert.Equal(t, tt.expectedStatus, response.Status)
                assert.NotEmpty(t, response.EmailID)
            }
            
            // 验证mock调用
            mockTemplateRepo.AssertExpectations(t)
            mockEmailRepo.AssertExpectations(t)
            mockAccountRepo.AssertExpectations(t)
            mockSenderFactory.AssertExpectations(t)
        })
    }
}
```

### 11.2 集成测试示例

```go
// 集成测试
func TestEmailSendingIntegration(t *testing.T) {
    // 设置测试数据库
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    // 创建测试数据
    account := createTestEmailAccount(t, db)
    template := createTestEmailTemplate(t, db, account.ID)
    
    // 创建服务实例
    emailRepo := persistence.NewEmailRepository(db, logger)
    templateRepo := persistence.NewTemplateRepository(db, logger)
    accountRepo := persistence.NewEmailAccountRepository(db, logger)
    senderFactory := external.NewEmailSenderFactory(logger)
    
    service := NewEmailApplicationService(
        emailRepo,
        templateRepo,
        accountRepo,
        senderFactory,
        logger,
    )
    
    // 执行测试
    ctx := context.WithValue(context.Background(), UserContextKey, &UserContext{
        TenantID: 1,
        UserID:   1,
    })
    
    request := &dto.SendTemplateEmailRequest{
        TemplateCode: template.TemplateCode,
        ToAddress:    "<EMAIL>",
        Variables: map[string]interface{}{
            "name": "测试用户",
        },
    }
    
    response, err := service.SendTemplateEmail(ctx, 1, request)
    
    // 验证结果
    assert.NoError(t, err)
    assert.NotNil(t, response)
    assert.NotEmpty(t, response.EmailID)
    
    // 验证数据库状态
    email, err := emailRepo.FindByID(ctx, response.EmailID)
    assert.NoError(t, err)
    assert.NotNil(t, email)
    assert.Equal(t, "<EMAIL>", email.ToAddress)
    assert.Contains(t, email.HTMLContent, "测试用户")
}
```

## 12. 部署与运维建议

### 12.1 配置管理

```yaml
# config/email.yaml
email:
  # 队列配置
  queue:
    provider: "redis"  # redis, rabbitmq, kafka
    redis:
      addr: "localhost:6379"
      password: ""
      db: 0
    batch_size: 100
    worker_count: 10
    
  # 重试配置
  retry:
    max_retries: 3
    base_delay: "1m"
    max_delay: "1h"
    multiplier: 2.0
    
  # 发送限制
  rate_limit:
    per_minute: 60
    per_hour: 1000
    per_day: 10000
    
  # 监控配置
  monitoring:
    metrics_enabled: true
    health_check_interval: "30s"
    alert_thresholds:
      error_rate: 0.05
      queue_length: 1000
```

### 12.2 Docker部署

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o email-service ./cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/email-service .
COPY --from=builder /app/config ./config

EXPOSE 8080 9090
CMD ["./email-service"]
```

### 12.3 Kubernetes部署

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-service
  namespace: platforms
spec:
  replicas: 3
  selector:
    matchLabels:
      app: email-service
  template:
    metadata:
      labels:
        app: email-service
    spec:
      containers:
      - name: email-service
        image: platforms/email-service:latest
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: db-host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: email-secrets
              key: db-password
        - name: REDIS_ADDR
          value: "redis-service:6379"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 13. 性能优化建议

### 13.1 数据库优化

1. **索引优化**
   - 在 `email_messages` 表的 `tenant_id`, `status`, `created_at` 字段上创建复合索引
   - 在 `email_id` 字段上创建唯一索引
   - 定期分析查询性能，优化慢查询

2. **分区策略**
   - 按时间对 `email_messages` 表进行分区
   - 定期清理历史数据

3. **读写分离**
   - 查询操作使用只读副本
   - 写操作使用主库

### 13.2 缓存策略

1. **模板缓存**
   - 将常用模板缓存到Redis
   - 设置合理的过期时间

2. **账户配置缓存**
   - 缓存邮件账户配置信息
   - 减少数据库查询

3. **统计数据缓存**
   - 缓存发送统计数据
   - 定期更新缓存

### 13.3 队列优化

1. **优先级队列**
   - 实现多级优先级队列
   - 高优先级邮件优先处理

2. **批量处理**
   - 批量从队列中获取邮件
   - 批量更新数据库状态

3. **负载均衡**
   - 多个工作者并行处理
   - 动态调整工作者数量

## 14. 安全设计

### 14.1 数据安全

1. **敏感信息加密**
   - 邮件内容加密存储
   - 账户密码加密存储

2. **访问控制**
   - 基于租户的数据隔离
   - 细粒度权限控制

3. **审计日志**
   - 记录所有操作日志
   - 支持审计追踪

### 14.2 传输安全

1. **TLS加密**
   - 所有网络传输使用TLS
   - 证书定期更新

2. **API安全**
   - JWT token认证
   - API限流保护

## 15. 扩展性设计

### 15.1 水平扩展

1. **无状态设计**
   - 服务实例无状态
   - 支持水平扩展

2. **数据库分片**
   - 按租户分片
   - 支持跨分片查询

3. **消息队列集群**
   - 队列服务集群部署
   - 高可用保障

### 15.2 功能扩展

1. **插件机制**
   - 支持自定义发送器
   - 支持自定义模板引擎

2. **多渠道支持**
   - 短信发送
   - 推送通知
   - 微信消息

## 16. 总结

本重构方案基于DDD架构设计，保留了现有数据库结构，实现了以下核心功能：

1. **队列化发送**：异步处理，提高系统吞吐量
2. **批量操作**：支持批量发送、取消、重试
3. **智能重试**：指数退避策略，提高成功率
4. **状态管理**：完整的邮件生命周期管理
5. **监控运维**：全面的监控和健康检查
6. **安全保障**：多层次安全防护
7. **高可扩展**：支持水平扩展和功能扩展

该方案遵循了Clean Architecture原则，具有良好的可测试性、可维护性和可扩展性，能够满足企业级邮件发送系统的需求。