# 邮件系统完整流程设计文档

## 概述

本文档描述了邮件系统的完整业务流程，包括账户管理、模板管理、邮件发送、统计分析等核心功能模块的详细流程和状态管理。

## 1. 系统架构概览

```
邮件系统
├── 账户管理模块
│   ├── 账户创建与配置
│   ├── 账户测试与验证
│   └── 账户状态管理
├── 模板管理模块
│   ├── 模板创建与编辑
│   ├── 模板版本管理
│   └── 模板测试与发布
├── 发送管理模块
│   ├── 单邮件发送
│   ├── 批量邮件发送
│   └── 定时邮件发送
├── 营销活动模块
│   ├── 活动创建
│   ├── 受众管理
│   └── 活动执行
└── 数据分析模块
    ├── 发送统计
    ├── 效果分析
    └── 报表生成
```

## 2. 账户管理模块

### 2.1 账户创建流程

#### 阶段1：基础信息配置
```
用户操作 → 填写账户信息 → 系统验证 → 创建账户
```

**配置信息**:
- 账户名称（必填）
- 邮箱地址（必填）
- 账户类型（SMTP/API）
- 租户ID（系统自动获取）

**验证规则**:
- 邮箱格式验证
- 账户名称唯一性检查
- 租户权限验证

#### 阶段2：服务器配置
**SMTP类型配置**:
- SMTP服务器地址
- 端口号
- 加密方式（SSL/TLS/无）
- 用户名
- 密码/授权码

**API类型配置**:
- API服务商（aliyun等）
- API密钥
- 发送域名
- 配置参数

#### 阶段3：账户初始化
- **初始状态**: `TestStatusUntested` + `IsActive = true`
- **权限设置**: 根据用户角色设置账户权限
- **系统标记**: 标记是否为系统级账户

### 2.2 账户测试流程

#### 测试触发
```
用户操作 → 选择测试账户 → 输入测试邮箱 → 执行测试 → 更新状态
```

#### 测试执行步骤
1. **连接测试**: 验证服务器连接
2. **认证测试**: 验证用户凭据
3. **发送测试**: 发送测试邮件
4. **结果验证**: 检查发送结果

#### 测试结果处理
- **成功**: `TestStatusSuccess` + 记录测试时间
- **失败**: `TestStatusFailed` + 记录错误信息
- **超时**: `TestStatusFailed` + 网络超时标记

### 2.3 账户状态管理

#### 状态定义
```go
// 测试状态
const (
    TestStatusUntested TestStatus = iota  // 0 - 未测试
    TestStatusSuccess                     // 1 - 测试成功
    TestStatusFailed                      // 2 - 测试失败
)

// 激活状态
IsActive bool // true - 激活, false - 停用
```

#### 状态流转图
```
创建账户
    ↓
[未测试 + 激活] → 测试接口 → [测试成功 + 激活] → 可用于发送
    ↓                    ↓
[测试失败 + 激活] ← 重新测试 ← [测试失败 + 激活]
    ↓
[停用状态] ← 手动停用/系统停用
```

#### 状态检查规则
- **发送邮件**: 仅限 `TestStatusSuccess` + `IsActive = true`
- **测试功能**: 仅限 `IsActive = true`
- **配置修改**: 所有状态都可以修改配置

## 3. 模板管理模块

### 3.1 模板创建流程

#### 阶段1：基础信息设置
```
用户操作 → 填写模板信息 → 系统验证 → 创建模板
```

**模板信息**:
- 模板名称（必填）
- 模板代码（必填，唯一）
- 模板类型（HTML/纯文本）
- 关联账户（必选）
- 模板描述

**验证规则**:
- 模板名称唯一性检查
- 模板代码格式验证
- 账户状态验证

#### 阶段2：内容编辑
**HTML模板**:
- 富文本编辑器
- HTML源码编辑
- 变量占位符支持
- 样式预览

**纯文本模板**:
- 文本编辑器
- 变量占位符支持
- 格式预览

#### 阶段3：变量配置
**系统变量**:
- `{{.UserName}}` - 用户名
- `{{.Email}}` - 邮箱地址
- `{{.TenantName}}` - 租户名称
- `{{.CurrentTime}}` - 当前时间

**自定义变量**:
- 用户定义的变量
- 变量类型验证
- 默认值设置

### 3.2 模板测试流程

#### 测试准备
```
选择模板 → 选择测试账户 → 填写测试数据 → 执行测试 → 查看结果
```

#### 测试数据配置
- 变量值设置
- 收件人配置
- 测试邮件主题

#### 测试执行
1. **模板渲染**: 使用测试数据渲染模板
2. **内容验证**: 检查渲染结果
3. **发送测试**: 发送测试邮件
4. **结果反馈**: 返回测试结果

### 3.3 模板版本管理

#### 版本控制
- **版本号**: 自动递增，当前生效的状态为0，历史状态为时间戳，每次查询只查0的版本号
- **变更记录**: 记录每次修改
- **回滚支持**: 支持版本回滚
- **发布状态**: 草稿/已发布/已停用

#### 版本流转
```
创建模板 → 编辑内容 → 保存草稿 → 测试验证 → 发布使用
    ↓
版本更新 ← 修改内容 ← 创建新版本 ← 测试验证 ← 发布新版本
```

## 4. 发送管理模块

### 4.1 单邮件发送流程

#### 发送准备
```
选择模板 → 选择账户 → 填写收件人 → 设置变量 → 发送邮件
```

#### 发送验证
1. **模板验证**: 检查模板状态和内容
2. **账户验证**: 检查账户测试状态
3. **收件人验证**: 验证邮箱格式
4. **变量验证**: 检查必需变量

#### 发送执行
1. **模板渲染**: 使用变量渲染邮件内容
2. **邮件构建**: 构建完整的邮件对象
3. **发送执行**: 调用发送服务
4. **结果记录**: 记录发送结果

### 4.2 批量邮件发送流程

#### 批量准备
```
选择模板 → 选择账户 → 上传收件人列表 → 设置变量映射 → 配置发送策略
```

#### 收件人管理
- **列表格式**: CSV/Excel文件
- **数据验证**: 邮箱格式、重复检查
- **变量映射**: 列名与变量对应
- **分批处理**: 按批次发送

#### 发送策略
- **并发控制**: 限制同时发送数量
- **频率限制**: 控制发送间隔
- **错误处理**: 失败重试机制
- **进度跟踪**: 实时发送进度

### 4.3 定时邮件发送流程

#### 定时配置
```
选择模板 → 选择账户 → 设置收件人 → 配置定时规则 → 创建定时任务
```

#### 定时规则
- **一次性**: 指定具体时间
- **周期性**: 每天/每周/每月
- **条件触发**: 基于事件触发
- **时区处理**: 支持多时区

#### 任务管理
- **任务状态**: 待执行/执行中/已完成/已取消
- **执行监控**: 实时执行状态
- **失败处理**: 自动重试机制
- **任务修改**: 支持修改和取消

