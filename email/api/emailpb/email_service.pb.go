// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: emailpb/email_service.proto

package emailpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 发送模板邮件请求
type SendTemplateEmailRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户ID
	TenantId int64 `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 模板代码
	TemplateCode string `protobuf:"bytes,2,opt,name=template_code,json=templateCode,proto3" json:"template_code,omitempty"`
	// 收件人邮箱列表
	To []string `protobuf:"bytes,3,rep,name=to,proto3" json:"to,omitempty"`
	// 模板变量
	Variables map[string]string `protobuf:"bytes,4,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// 请求ID（可选，用于日志追踪）
	RequestId     string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTemplateEmailRequest) Reset() {
	*x = SendTemplateEmailRequest{}
	mi := &file_emailpb_email_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTemplateEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTemplateEmailRequest) ProtoMessage() {}

func (x *SendTemplateEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTemplateEmailRequest.ProtoReflect.Descriptor instead.
func (*SendTemplateEmailRequest) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendTemplateEmailRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *SendTemplateEmailRequest) GetTemplateCode() string {
	if x != nil {
		return x.TemplateCode
	}
	return ""
}

func (x *SendTemplateEmailRequest) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SendTemplateEmailRequest) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *SendTemplateEmailRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// 发送模板邮件响应
type SendTemplateEmailResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 邮件发送结果数据
	Data          *EmailSendResult `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTemplateEmailResponse) Reset() {
	*x = SendTemplateEmailResponse{}
	mi := &file_emailpb_email_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTemplateEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTemplateEmailResponse) ProtoMessage() {}

func (x *SendTemplateEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTemplateEmailResponse.ProtoReflect.Descriptor instead.
func (*SendTemplateEmailResponse) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendTemplateEmailResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendTemplateEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendTemplateEmailResponse) GetData() *EmailSendResult {
	if x != nil {
		return x.Data
	}
	return nil
}

// 邮件发送结果
type EmailSendResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消息ID
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 发送状态
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// 发送时间
	SendTime      int64 `protobuf:"varint,3,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailSendResult) Reset() {
	*x = EmailSendResult{}
	mi := &file_emailpb_email_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailSendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailSendResult) ProtoMessage() {}

func (x *EmailSendResult) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailSendResult.ProtoReflect.Descriptor instead.
func (*EmailSendResult) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{2}
}

func (x *EmailSendResult) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *EmailSendResult) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *EmailSendResult) GetSendTime() int64 {
	if x != nil {
		return x.SendTime
	}
	return 0
}

// 检查模板是否存在请求
type CheckTemplateExistsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户ID
	TenantId int64 `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 模板代码
	TemplateCode  string `protobuf:"bytes,2,opt,name=template_code,json=templateCode,proto3" json:"template_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckTemplateExistsRequest) Reset() {
	*x = CheckTemplateExistsRequest{}
	mi := &file_emailpb_email_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckTemplateExistsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplateExistsRequest) ProtoMessage() {}

func (x *CheckTemplateExistsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplateExistsRequest.ProtoReflect.Descriptor instead.
func (*CheckTemplateExistsRequest) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckTemplateExistsRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *CheckTemplateExistsRequest) GetTemplateCode() string {
	if x != nil {
		return x.TemplateCode
	}
	return ""
}

// 检查模板是否存在响应
type CheckTemplateExistsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 模板存在结果数据
	Data          *TemplateExistsResult `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckTemplateExistsResponse) Reset() {
	*x = CheckTemplateExistsResponse{}
	mi := &file_emailpb_email_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckTemplateExistsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplateExistsResponse) ProtoMessage() {}

func (x *CheckTemplateExistsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplateExistsResponse.ProtoReflect.Descriptor instead.
func (*CheckTemplateExistsResponse) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckTemplateExistsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckTemplateExistsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckTemplateExistsResponse) GetData() *TemplateExistsResult {
	if x != nil {
		return x.Data
	}
	return nil
}

// 模板存在结果
type TemplateExistsResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否存在
	Exists bool `protobuf:"varint,1,opt,name=exists,proto3" json:"exists,omitempty"`
	// 模板信息（如果存在）
	TemplateInfo  *TemplateInfo `protobuf:"bytes,2,opt,name=template_info,json=templateInfo,proto3" json:"template_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TemplateExistsResult) Reset() {
	*x = TemplateExistsResult{}
	mi := &file_emailpb_email_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateExistsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateExistsResult) ProtoMessage() {}

func (x *TemplateExistsResult) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateExistsResult.ProtoReflect.Descriptor instead.
func (*TemplateExistsResult) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{5}
}

func (x *TemplateExistsResult) GetExists() bool {
	if x != nil {
		return x.Exists
	}
	return false
}

func (x *TemplateExistsResult) GetTemplateInfo() *TemplateInfo {
	if x != nil {
		return x.TemplateInfo
	}
	return nil
}

// 模板信息
type TemplateInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 模板ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 模板代码
	TemplateCode string `protobuf:"bytes,2,opt,name=template_code,json=templateCode,proto3" json:"template_code,omitempty"`
	// 模板名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 模板类型
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,5,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 创建时间
	CreatedAt int64 `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt     int64 `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TemplateInfo) Reset() {
	*x = TemplateInfo{}
	mi := &file_emailpb_email_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateInfo) ProtoMessage() {}

func (x *TemplateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_emailpb_email_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateInfo.ProtoReflect.Descriptor instead.
func (*TemplateInfo) Descriptor() ([]byte, []int) {
	return file_emailpb_email_service_proto_rawDescGZIP(), []int{6}
}

func (x *TemplateInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TemplateInfo) GetTemplateCode() string {
	if x != nil {
		return x.TemplateCode
	}
	return ""
}

func (x *TemplateInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TemplateInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TemplateInfo) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *TemplateInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *TemplateInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

var File_emailpb_email_service_proto protoreflect.FileDescriptor

const file_emailpb_email_service_proto_rawDesc = "" +
	"\n" +
	"\x1bemailpb/email_service.proto\x12\x05email\"\x97\x02\n" +
	"\x18SendTemplateEmailRequest\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12#\n" +
	"\rtemplate_code\x18\x02 \x01(\tR\ftemplateCode\x12\x0e\n" +
	"\x02to\x18\x03 \x03(\tR\x02to\x12L\n" +
	"\tvariables\x18\x04 \x03(\v2..email.SendTemplateEmailRequest.VariablesEntryR\tvariables\x12\x1d\n" +
	"\n" +
	"request_id\x18\x05 \x01(\tR\trequestId\x1a<\n" +
	"\x0eVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\x19SendTemplateEmailResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04data\x18\x03 \x01(\v2\x16.email.EmailSendResultR\x04data\"e\n" +
	"\x0fEmailSendResult\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1b\n" +
	"\tsend_time\x18\x03 \x01(\x03R\bsendTime\"^\n" +
	"\x1aCheckTemplateExistsRequest\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12#\n" +
	"\rtemplate_code\x18\x02 \x01(\tR\ftemplateCode\"|\n" +
	"\x1bCheckTemplateExistsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x04data\x18\x03 \x01(\v2\x1b.email.TemplateExistsResultR\x04data\"h\n" +
	"\x14TemplateExistsResult\x12\x16\n" +
	"\x06exists\x18\x01 \x01(\bR\x06exists\x128\n" +
	"\rtemplate_info\x18\x02 \x01(\v2\x13.email.TemplateInfoR\ftemplateInfo\"\xc8\x01\n" +
	"\fTemplateInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12#\n" +
	"\rtemplate_code\x18\x02 \x01(\tR\ftemplateCode\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x1d\n" +
	"\n" +
	"is_enabled\x18\x05 \x01(\bR\tisEnabled\x12\x1d\n" +
	"\n" +
	"created_at\x18\x06 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\a \x01(\x03R\tupdatedAt2\xc4\x01\n" +
	"\fEmailService\x12V\n" +
	"\x11SendTemplateEmail\x12\x1f.email.SendTemplateEmailRequest\x1a .email.SendTemplateEmailResponse\x12\\\n" +
	"\x13CheckTemplateExists\x12!.email.CheckTemplateExistsRequest\x1a\".email.CheckTemplateExistsResponseB\x1dZ\x1bplatforms-email/api/emailpbb\x06proto3"

var (
	file_emailpb_email_service_proto_rawDescOnce sync.Once
	file_emailpb_email_service_proto_rawDescData []byte
)

func file_emailpb_email_service_proto_rawDescGZIP() []byte {
	file_emailpb_email_service_proto_rawDescOnce.Do(func() {
		file_emailpb_email_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_emailpb_email_service_proto_rawDesc), len(file_emailpb_email_service_proto_rawDesc)))
	})
	return file_emailpb_email_service_proto_rawDescData
}

var file_emailpb_email_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_emailpb_email_service_proto_goTypes = []any{
	(*SendTemplateEmailRequest)(nil),    // 0: email.SendTemplateEmailRequest
	(*SendTemplateEmailResponse)(nil),   // 1: email.SendTemplateEmailResponse
	(*EmailSendResult)(nil),             // 2: email.EmailSendResult
	(*CheckTemplateExistsRequest)(nil),  // 3: email.CheckTemplateExistsRequest
	(*CheckTemplateExistsResponse)(nil), // 4: email.CheckTemplateExistsResponse
	(*TemplateExistsResult)(nil),        // 5: email.TemplateExistsResult
	(*TemplateInfo)(nil),                // 6: email.TemplateInfo
	nil,                                 // 7: email.SendTemplateEmailRequest.VariablesEntry
}
var file_emailpb_email_service_proto_depIdxs = []int32{
	7, // 0: email.SendTemplateEmailRequest.variables:type_name -> email.SendTemplateEmailRequest.VariablesEntry
	2, // 1: email.SendTemplateEmailResponse.data:type_name -> email.EmailSendResult
	5, // 2: email.CheckTemplateExistsResponse.data:type_name -> email.TemplateExistsResult
	6, // 3: email.TemplateExistsResult.template_info:type_name -> email.TemplateInfo
	0, // 4: email.EmailService.SendTemplateEmail:input_type -> email.SendTemplateEmailRequest
	3, // 5: email.EmailService.CheckTemplateExists:input_type -> email.CheckTemplateExistsRequest
	1, // 6: email.EmailService.SendTemplateEmail:output_type -> email.SendTemplateEmailResponse
	4, // 7: email.EmailService.CheckTemplateExists:output_type -> email.CheckTemplateExistsResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_emailpb_email_service_proto_init() }
func file_emailpb_email_service_proto_init() {
	if File_emailpb_email_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_emailpb_email_service_proto_rawDesc), len(file_emailpb_email_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_emailpb_email_service_proto_goTypes,
		DependencyIndexes: file_emailpb_email_service_proto_depIdxs,
		MessageInfos:      file_emailpb_email_service_proto_msgTypes,
	}.Build()
	File_emailpb_email_service_proto = out.File
	file_emailpb_email_service_proto_goTypes = nil
	file_emailpb_email_service_proto_depIdxs = nil
}
