// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/emailpb/email_service.proto

package emailpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 发送模板邮件请求
type SendTemplateEmailRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 租户ID
	TenantId string `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 模板代码
	TemplateCode string `protobuf:"bytes,2,opt,name=template_code,json=templateCode,proto3" json:"template_code,omitempty"`
	// 收件人邮箱列表
	To []string `protobuf:"bytes,3,rep,name=to,proto3" json:"to,omitempty"`
	// 模板变量
	Variables map[string]string `protobuf:"bytes,4,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// 请求ID（可选，用于日志追踪）
	RequestId     string `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTemplateEmailRequest) Reset() {
	*x = SendTemplateEmailRequest{}
	mi := &file_api_emailpb_email_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTemplateEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTemplateEmailRequest) ProtoMessage() {}

func (x *SendTemplateEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_emailpb_email_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTemplateEmailRequest.ProtoReflect.Descriptor instead.
func (*SendTemplateEmailRequest) Descriptor() ([]byte, []int) {
	return file_api_emailpb_email_service_proto_rawDescGZIP(), []int{0}
}

func (x *SendTemplateEmailRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *SendTemplateEmailRequest) GetTemplateCode() string {
	if x != nil {
		return x.TemplateCode
	}
	return ""
}

func (x *SendTemplateEmailRequest) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SendTemplateEmailRequest) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

func (x *SendTemplateEmailRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

// 发送模板邮件响应
type SendTemplateEmailResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// 消息
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 邮件发送结果数据
	Data          *EmailSendResult `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTemplateEmailResponse) Reset() {
	*x = SendTemplateEmailResponse{}
	mi := &file_api_emailpb_email_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTemplateEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTemplateEmailResponse) ProtoMessage() {}

func (x *SendTemplateEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_emailpb_email_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTemplateEmailResponse.ProtoReflect.Descriptor instead.
func (*SendTemplateEmailResponse) Descriptor() ([]byte, []int) {
	return file_api_emailpb_email_service_proto_rawDescGZIP(), []int{1}
}

func (x *SendTemplateEmailResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendTemplateEmailResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SendTemplateEmailResponse) GetData() *EmailSendResult {
	if x != nil {
		return x.Data
	}
	return nil
}

// 邮件发送结果
type EmailSendResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消息ID
	MessageId string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// 发送状态
	Status        string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmailSendResult) Reset() {
	*x = EmailSendResult{}
	mi := &file_api_emailpb_email_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmailSendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailSendResult) ProtoMessage() {}

func (x *EmailSendResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_emailpb_email_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailSendResult.ProtoReflect.Descriptor instead.
func (*EmailSendResult) Descriptor() ([]byte, []int) {
	return file_api_emailpb_email_service_proto_rawDescGZIP(), []int{2}
}

func (x *EmailSendResult) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *EmailSendResult) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

var File_api_emailpb_email_service_proto protoreflect.FileDescriptor

const file_api_emailpb_email_service_proto_rawDesc = "" +
	"\n" +
	"\x1fapi/emailpb/email_service.proto\x12\x05email\"\x97\x02\n" +
	"\x18SendTemplateEmailRequest\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12#\n" +
	"\rtemplate_code\x18\x02 \x01(\tR\ftemplateCode\x12\x0e\n" +
	"\x02to\x18\x03 \x03(\tR\x02to\x12L\n" +
	"\tvariables\x18\x04 \x03(\v2..email.SendTemplateEmailRequest.VariablesEntryR\tvariables\x12\x1d\n" +
	"\n" +
	"request_id\x18\x05 \x01(\tR\trequestId\x1a<\n" +
	"\x0eVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\x19SendTemplateEmailResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04data\x18\x03 \x01(\v2\x16.email.EmailSendResultR\x04data\"H\n" +
	"\x0fEmailSendResult\x12\x1d\n" +
	"\n" +
	"message_id\x18\x01 \x01(\tR\tmessageId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status2f\n" +
	"\fEmailService\x12V\n" +
	"\x11SendTemplateEmail\x12\x1f.email.SendTemplateEmailRequest\x1a .email.SendTemplateEmailResponseB\x1dZ\x1bplatforms-email/api/emailpbb\x06proto3"

var (
	file_api_emailpb_email_service_proto_rawDescOnce sync.Once
	file_api_emailpb_email_service_proto_rawDescData []byte
)

func file_api_emailpb_email_service_proto_rawDescGZIP() []byte {
	file_api_emailpb_email_service_proto_rawDescOnce.Do(func() {
		file_api_emailpb_email_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_emailpb_email_service_proto_rawDesc), len(file_api_emailpb_email_service_proto_rawDesc)))
	})
	return file_api_emailpb_email_service_proto_rawDescData
}

var file_api_emailpb_email_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_emailpb_email_service_proto_goTypes = []any{
	(*SendTemplateEmailRequest)(nil),  // 0: email.SendTemplateEmailRequest
	(*SendTemplateEmailResponse)(nil), // 1: email.SendTemplateEmailResponse
	(*EmailSendResult)(nil),           // 2: email.EmailSendResult
	nil,                               // 3: email.SendTemplateEmailRequest.VariablesEntry
}
var file_api_emailpb_email_service_proto_depIdxs = []int32{
	3, // 0: email.SendTemplateEmailRequest.variables:type_name -> email.SendTemplateEmailRequest.VariablesEntry
	2, // 1: email.SendTemplateEmailResponse.data:type_name -> email.EmailSendResult
	0, // 2: email.EmailService.SendTemplateEmail:input_type -> email.SendTemplateEmailRequest
	1, // 3: email.EmailService.SendTemplateEmail:output_type -> email.SendTemplateEmailResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_emailpb_email_service_proto_init() }
func file_api_emailpb_email_service_proto_init() {
	if File_api_emailpb_email_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_emailpb_email_service_proto_rawDesc), len(file_api_emailpb_email_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_emailpb_email_service_proto_goTypes,
		DependencyIndexes: file_api_emailpb_email_service_proto_depIdxs,
		MessageInfos:      file_api_emailpb_email_service_proto_msgTypes,
	}.Build()
	File_api_emailpb_email_service_proto = out.File
	file_api_emailpb_email_service_proto_goTypes = nil
	file_api_emailpb_email_service_proto_depIdxs = nil
}
