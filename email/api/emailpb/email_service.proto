syntax = "proto3";

package email;

option go_package = "platforms-email/api/emailpb";

// 邮件服务
service EmailService {
    // 发送模板邮件
    rpc SendTemplateEmail(SendTemplateEmailRequest) returns (SendTemplateEmailResponse);
}

// 发送模板邮件请求
message SendTemplateEmailRequest {
    // 租户ID
    string tenant_id = 1;
    // 模板代码
    string template_code = 2;
    // 收件人邮箱列表
    repeated string to = 3;
    // 模板变量
    map<string, string> variables = 4;
    // 请求ID（可选，用于日志追踪）
    string request_id = 5;
}

// 发送模板邮件响应
message SendTemplateEmailResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 邮件发送结果数据
    EmailSendResult data = 3;
}

// 邮件发送结果
message EmailSendResult {
    // 消息ID
    string message_id = 1;
    // 发送状态
    string status = 2;
}