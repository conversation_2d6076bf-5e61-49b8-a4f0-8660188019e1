# Emails 模块启动流程改进

## 概述

本文档总结了 emails 模块启动流程的改进，使其符合微服务最佳实践。

## 改进前的问题

### 原有启动流程的问题

1. **缺少完整的生命周期管理**
   - 没有明确的应用生命周期划分
   - 启动和关闭逻辑混合在一起

2. **优雅关闭机制不完善**
   - 缺少关闭超时控制
   - 没有按顺序关闭资源
   - 缺少错误处理

3. **错误处理不够统一**
   - 使用 `panic` 处理初始化错误
   - 缺少错误包装和上下文信息

4. **缺少健康检查**
   - 没有健康检查端点
   - 无法监控服务状态

5. **代码结构不够清晰**
   - 所有逻辑都在 `main` 函数中
   - 缺少模块化设计

## 改进后的架构

### 应用结构设计

```go
type Application struct {
    config           *config.Config
    logger           logiface.Logger
    container        *container.DependencyContainer
    httpServer       *http.Server
    otelShutdown     func(context.Context) error
    gormLoggerAdapter *common_database.GormLoggerAdapter
    ctx              context.Context
    cancel           context.CancelFunc
}
```

### 生命周期管理

1. **初始化阶段** (`Initialize`)
   - 解析命令行参数
   - 加载配置
   - 初始化日志系统
   - 初始化 OpenTelemetry
   - 启动配置热更新监听
   - 初始化依赖注入容器
   - 初始化 gRPC 客户端管理器
   - 构建 HTTP 服务器

2. **启动阶段** (`Start`)
   - 启动 HTTP 服务器
   - 记录启动成功日志

3. **运行阶段** (`WaitForShutdown`)
   - 等待关闭信号
   - 处理上下文取消

4. **关闭阶段** (`Shutdown`)
   - 关闭 HTTP 服务器
   - 关闭依赖注入容器
   - 关闭 gRPC 客户端管理器
   - 关闭 OpenTelemetry

## 主要改进点

### 1. 完整的优雅关闭机制

```go
func (app *Application) Shutdown() error {
    app.logger.Info(app.ctx, "Shutting down application...")

    // 设置关闭超时
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    // 1. 关闭 HTTP 服务器
    if app.httpServer != nil {
        if err := app.httpServer.Shutdown(ctx); err != nil {
            app.logger.Error(ctx, "HTTP server shutdown failed", logiface.Error(err))
        } else {
            app.logger.Info(ctx, "HTTP server shutdown completed")
        }
    }

    // 2. 关闭依赖注入容器
    if app.container != nil {
        if err := app.container.Close(); err != nil {
            app.logger.Error(ctx, "Dependency container close failed", logiface.Error(err))
        } else {
            app.logger.Info(ctx, "Dependency container closed")
        }
    }

    // 3. 关闭 gRPC 客户端管理器
    if manager := grpcregistry.GetGlobalManager(); manager != nil {
        manager.Close()
        app.logger.Info(ctx, "gRPC client manager closed")
    }

    // 4. 关闭 OpenTelemetry
    if app.otelShutdown != nil {
        if err := app.otelShutdown(ctx); err != nil {
            app.logger.Error(ctx, "OpenTelemetry shutdown failed", logiface.Error(err))
        } else {
            app.logger.Info(ctx, "OpenTelemetry shutdown completed")
        }
    }

    app.logger.Info(ctx, "Application shutdown completed")
    return nil
}
```

### 2. 统一的错误处理

```go
// 初始化应用
if err := app.Initialize(); err != nil {
    log.Fatalf("Failed to initialize application: %v", err)
}

// 启动应用
if err := app.Start(); err != nil {
    log.Fatalf("Failed to start application: %v", err)
}
```

### 3. 健康检查端点

```go
func (app *Application) healthCheck(c *gin.Context) {
    // 检查数据库连接
    if app.container.Infrastructure.DB != nil {
        sqlDB, err := app.container.Infrastructure.DB.DB()
        if err != nil || sqlDB.Ping() != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "unhealthy",
                "error":  "database connection failed",
            })
            return
        }
    }

    // 检查 gRPC 客户端连接
    if app.container.Infrastructure.UserClient != nil {
        if err := app.container.Infrastructure.UserClient.Health(app.ctx); err != nil {
            c.JSON(http.StatusServiceUnavailable, gin.H{
                "status": "unhealthy",
                "error":  "grpc client connection failed",
            })
            return
        }
    }

    c.JSON(http.StatusOK, gin.H{
        "status":    "healthy",
        "service":   ServiceName,
        "timestamp": time.Now().Unix(),
    })
}
```

### 4. 配置热更新监听

```go
func (app *Application) startConfigWatcher() {
    go func() {
        if err := config.ListenNacosConfigChange(ServiceName); err != nil {
            app.logger.Warn(app.ctx, "Failed to start nacos config change listener", logiface.Error(err))
        }
    }()

    // 启动日志级别监听
    go app.watchLogLevelFromNacos()
}
```

### 5. 中间件配置

```go
// 创建用户和租户信息提供者
userInfoProvider := &UserInfoProviderAdapter{container: app.container}
tenantInfoProvider := &TenantInfoProviderAdapter{container: app.container}

middlewareConfig := &httpmiddleware.MiddlewareConfig{
    ServiceName:           ServiceName,
    EnableAccessLog:       true,
    EnableRequestID:       true,
    EnableSecurityHeaders: true,
    EnableRecovery:        true,
    EnableMetrics:         true,
    EnableRequestSize:     true,
    MaxRequestSize:        10 * 1024 * 1024, // 10MB
    EnableUserInfo:        true,
    UserInfoProvider:      userInfoProvider,
    TenantInfoProvider:    tenantInfoProvider,
    Logger:                app.logger,
}
httpmiddleware.SetupCommonMiddleware(router, middlewareConfig)
```

### 6. 用户和租户信息提供者

```go
// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
    container *container.DependencyContainer
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string) *httpmiddleware.AuthedUser {
    // 使用 UserClient 获取用户信息
    userInfo, err := a.container.Infrastructure.UserClient.GetUserInfoByToken(ctx, token)
    if err != nil || userInfo == nil || userInfo.Code != 0 {
        return nil
    }

    return &httpmiddleware.AuthedUser{
        UserId:   userInfo.Data.UserId,
        Username: userInfo.Data.Username,
        RealName: userInfo.Data.RealName,
        Email:    userInfo.Data.Email,
        TenantId: userInfo.Data.TenantId,
    }
}

// TenantInfoProviderAdapter 租户信息提供者适配器
type TenantInfoProviderAdapter struct {
    container *container.DependencyContainer
}

// GetTenantInfo 实现 TenantInfoProvider 接口
func (a *TenantInfoProviderAdapter) GetTenantInfo(ctx context.Context, tenantCode string) *httpmiddleware.TenantInfo {
    // 使用 UserClient 获取租户信息
    tenantInfo, err := a.container.Infrastructure.UserClient.GetTenantInfoByCode(ctx, tenantCode)
    if err != nil || tenantInfo == nil || tenantInfo.Code != 0 {
        return nil
    }

    return &httpmiddleware.TenantInfo{
        TenantId:   tenantInfo.Data.TenantId,
        TenantCode: tenantInfo.Data.TenantCode,
        TenantName: tenantInfo.Data.TenantName,
    }
}
```

## 改进效果

### 1. 可维护性提升
- 清晰的代码结构和职责分离
- 模块化的设计便于测试和维护
- 统一的错误处理策略

### 2. 可靠性提升
- 完整的优雅关闭机制
- 健康检查确保服务状态监控
- 配置热更新支持动态调整

### 3. 可观测性提升
- 完整的日志记录
- OpenTelemetry 链路追踪
- 健康检查端点

### 4. 生产就绪
- 30秒优雅关闭超时
- 按顺序关闭资源
- 错误处理和恢复机制

## 使用方式

### 启动应用

```bash
# 编译
go build -o bin/email-platform cmd/main.go

# 运行
./bin/email-platform
```

### 健康检查

```bash
# 检查服务健康状态
curl http://localhost:8080/health
```

### 优雅关闭

```bash
# 发送 SIGTERM 信号
kill -TERM <pid>

# 或发送 SIGINT 信号
kill -INT <pid>
```

## 总结

通过这次改进，emails 模块的启动流程达到了生产级别的标准：

1. **完整的生命周期管理**：初始化、启动、运行、关闭四个阶段
2. **优雅关闭机制**：确保所有资源正确释放
3. **健康检查**：提供服务状态监控
4. **配置热更新**：支持动态配置调整
5. **统一错误处理**：结构化的错误处理和日志记录
6. **可观测性**：完整的日志、监控、链路追踪
7. **用户认证集成**：完整的用户和租户信息提供者

### 关键改进点

- **用户认证中间件**：通过 `UserInfoProvider` 和 `TenantInfoProvider` 实现统一的用户认证
- **gRPC 客户端集成**：使用 `UserClient` 获取用户和租户信息
- **中间件配置完整**：所有必要的中间件都已正确配置
- **错误处理健壮**：对 gRPC 调用失败进行了适当的错误处理

这个改进后的启动流程可以作为其他微服务项目的参考模板。 