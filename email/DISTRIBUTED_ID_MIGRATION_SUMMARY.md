# Email模块分布式ID迁移总结

## 概述

本次迁移将email模块从使用雪花算法改为使用gRPC分布式ID生成器，实现了类似user模块的ID生成架构。

## 已完成的修改

### 1. 创建了ID生成器接口和实体工厂

#### 1.1 ID生成器接口 (`email/internal/domain/email/entity/id_generator_interface.go`)
- 定义了`IDGenerator`接口，包含业务特定的ID生成方法
- 创建了`EntityFactory`实体工厂，使用依赖注入的ID生成器
- 提供了`NewEmailAccount`、`NewEmailMessage`、`NewTemplateEmailMessage`等方法

#### 1.2 ID生成器实现 (`email/internal/infrastructure/id_generator/email_id_generator.go`)
- 实现了`EmailIDGenerator`，使用gRPC客户端调用分布式ID服务
- 支持生成邮件账号ID、邮件消息ID、邮件模板ID
- 提供通用ID生成方法和临时ID生成方法（使用雪花算法）

### 2. 修改了应用服务

#### 2.1 EmailAccountApplicationService
- 注入了`EntityFactory`
- 修改`CreateEmailAccount`方法使用实体工厂创建账号
- 自动生成分布式ID而不是雪花算法ID

#### 2.2 EmailApplicationService
- 注入了`EntityFactory`
- 修改`SendTemplateEmail`和`SendEmail`方法使用实体工厂创建邮件消息
- 自动生成分布式ID而不是雪花算法ID

### 3. 更新了依赖容器

#### 3.1 依赖容器结构 (`email/internal/infrastructure/container/dependency_container.go`)
- 添加了`Domain`层，包含`IDGenerator`和`EntityFactory`
- 更新了初始化逻辑，注入ID生成器和实体工厂
- 修改了应用服务的创建，传入实体工厂

## 架构优势

### 1. 统一性
- 与user模块保持一致的ID生成架构
- 使用相同的gRPC分布式ID服务
- 统一的依赖注入模式

### 2. 可维护性
- ID生成逻辑集中管理
- 通过接口实现依赖倒置
- 便于后续扩展和维护

### 3. 分布式特性
- 使用分布式ID生成器，避免ID冲突
- 支持多租户隔离
- 高性能的ID生成

## 使用方式

### 1. 创建邮件账号
```go
// 使用实体工厂创建账号，自动生成分布式ID
account, err := entityFactory.NewEmailAccount(ctx, tenantID, name, accountType, provider, fromAddress)
```

### 2. 创建邮件消息
```go
// 使用实体工厂创建邮件消息，自动生成分布式ID
message, err := entityFactory.NewEmailMessage(ctx, tenantID, toAddress, subject)
```

### 3. 创建模板邮件消息
```go
// 使用实体工厂创建模板邮件消息，自动生成分布式ID
message, err := entityFactory.NewTemplateEmailMessage(ctx, tenantID, templateID, toAddress, variables)
```

## 数据库迁移

### 1. 迁移文件 (`email/migrations/0002_distributed_id_for_accounts.sql`)
- 修改`email_accounts`表的`account_id`字段为BIGINT类型
- 重新创建唯一索引
- 支持分布式ID存储

## 注意事项

### 1. 兼容性
- 现有数据需要迁移到新的分布式ID
- 需要更新相关的查询和索引
- 确保API接口的向后兼容

### 2. 性能
- 分布式ID生成需要网络调用
- 建议实现本地缓存机制
- 监控ID生成服务的性能

### 3. 错误处理
- 网络调用失败时的降级策略
- ID生成失败时的重试机制
- 日志记录和监控

## 后续工作

### 1. 测试
- 单元测试覆盖新的ID生成逻辑
- 集成测试验证分布式ID生成
- 性能测试评估ID生成性能

### 2. 监控
- 添加ID生成相关的监控指标
- 监控gRPC调用的成功率
- 监控ID生成的延迟

### 3. 文档
- 更新API文档
- 编写运维文档
- 更新开发指南

## 总结

本次迁移成功实现了email模块的分布式ID生成，与user模块保持了一致的架构设计。通过依赖注入和实体工厂模式，实现了良好的可维护性和扩展性。后续需要完善测试、监控和文档工作。 