### **邮件发送功能重构详细设计方案**

#### 1. 背景与目标

本次重构旨在根据 `EMAIL_SYSTEM_ARCHITECTURE_PROMPT.md` 的规范，将现有邮件发送功能升级为一套高可用、可扩展、可观测的现代化邮件服务架构。核心目标是：

*   **业务与技术解耦**：将邮件的业务逻辑（如模板渲染、发送请求）与技术实现（如实际的SMTP/API发送、失败重试）彻底分离。
*   **异步化与高吞吐**：通过数据库实现发送队列，API调用时仅需入队，即可快速响应，由后台工作进程负责实际发送，提升系统吞吐量和用户体验。
*   **多渠道支持与扩展性**：通过策略与工厂模式，轻松支持并切换不同的邮件服务商（如 SMTP、SendGrid、Mailgun 等），并为未来扩展新渠道提供便利。
*   **可靠性与可观测性**：实现完善的自动重试、状态跟踪、错误处理和结构化日志机制，确保邮件发送的可靠性，并使运维监控变得简单。

#### 2. 整体架构设计

我们将遵循 DDD 与 Clean Architecture 的思想，强化各层职责，实现一个事件驱动、异步处理的邮件发送流程。

##### 2.1 架构分层职责

*   **Interfaces (接口层)**:
    *   **职责**: 对外暴露 gRPC/HTTP 接口，负责请求的接收、校验、DTO与领域对象的转换。
    *   **组件**: `grpc/handler.go`, `http/handler.go`。
    *   **改造点**: 定义新的 `SendEmail` 接口，接收批量发送请求。从 `context` 中解析 `tenant_id` 和 `user_id` 并传递给应用层。

*   **Application (应用层)**:
    *   **职责**: 编排领域服务和基础设施服务，完成核心业务用例。不包含具体业务规则。
    *   **组件**: `service/email_app_service.go`。
    *   **改造点**: 实现 `SendEmail` 用例。它将调用领域服务进行模板渲染和邮件对象构建，然后通过仓储接口将邮件实体持久化到 `email_messages` 表（状态为 `PENDING`）。**这是API同步操作的终点**。

*   **Domain (领域层)**:
    *   **职责**: 定义核心领域实体（`EmailMessage`）、值对象、领域事件以及仓储接口（`EmailRepository`）。包含核心业务规则。
    *   **组件**: `entity/email_message.go`, `repository/email_repository.go`。
    *   **改造点**: 保持实体与数据库表结构一致，重点定义 `EmailRepository` 接口，提供如 `CreateBatch`, `FindPendingEmails`, `UpdateStatus` 等方法。

*   **Infrastructure (基础设施层)**:
    *   **职责**: 提供所有与外部世界交互的具体实现，包括数据库访问、邮件发送器、缓存、消息队列等。
    *   **组件**: `persistence/gorm_email_repository.go`, `sender/provider_factory.go`, `sender/smtp_provider.go`, `sender/sendgrid_provider.go`。
    *   **改造点**:
        1.  实现 `EmailRepository` 接口，处理数据库操作。
        2.  创建 `sender` 包，定义统一的 `Provider` 接口，并为不同邮件服务商提供具体实现（策略模式）。
        3.  创建 `ProviderFactory`，根据配置动态选择并实例化对应的邮件发送 Provider（工厂模式）。

##### 2.2 核心流程架构图 (Mermaid)

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Interfaces as 接口层 (gRPC/HTTP)
    participant Application as 应用层 (EmailAppService)
    participant Domain as 领域层 (EmailMessage, EmailRepository)
    participant Infrastructure as 基础设施层 (GORM, Sender)
    participant DB as 数据库 (email_messages)
    participant Worker as 后台工作进程

    box "同步流程 (API调用)"
        Client->>Interfaces: 调用 SendEmail(template_id, recipients, vars)
        Interfaces->>Application: SendEmail(ctx, dto)
        Application->>Domain: 1. 获取模板, 渲染内容
        Application->>Domain: 2. 构建 EmailMessage 实体列表
        Application->>Domain: 3. 调用 EmailRepository.CreateBatch(entities)
        Domain-->>Infrastructure: (GORM)
        Infrastructure->>DB: INSERT INTO email_messages ... (status='PENDING')
        DB-->>Infrastructure: Success
        Infrastructure-->>Application: Success
        Application-->>Interfaces: Success
        Interfaces-->>Client: 响应成功 (e.g., RequestID)
    end

    box "异步流程 (后台发送)"
        Worker->>Infrastructure: 定时调用 EmailRepository.FindPendingEmails()
        Infrastructure->>DB: SELECT * FROM email_messages WHERE status='PENDING' ...
        DB-->>Infrastructure: 返回待发送邮件列表
        Infrastructure-->>Worker: 返回列表
        loop 遍历每封邮件
            Worker->>Infrastructure: 调用 SenderFactory.GetProvider()
            Infrastructure-->>Worker: 返回具体的邮件发送Provider (e.g., SMTP)
            Worker->>Infrastructure: 调用 Provider.Send(email)
            Infrastructure-->>Worker: 发送成功/失败
            alt 发送成功
                Worker->>Infrastructure: EmailRepository.UpdateStatus(id, 'SENT')
                Infrastructure->>DB: UPDATE email_messages SET status='SENT' ...
            else 发送失败
                Worker->>Infrastructure: EmailRepository.UpdateStatus(id, 'FAILED', retry+1)
                Infrastructure->>DB: UPDATE email_messages SET status='FAILED', retry_count=...
            end
        end
    end

```

#### 3. 核心功能实现方案

##### 3.1 异步发送与后台工作进程 (Worker)

我们将创建一个新的 `main` 入口，作为后台工作进程（Worker）。

*   **启动**: `cmd/worker/main.go`。
*   **职责**:
    1.  定期轮询 `email_messages` 表，获取 `status` 为 `PENDING` 且 `retry_count < max_retries` 的邮件。
    2.  为保证高可用，应使用分布式锁（如 Redis 或 Etcd）确保同一时间只有一个 Worker 实例在执行轮询和处理任务。
    3.  批量获取待处理邮件，并使用 goroutine 池并发处理发送任务。
    4.  调用 `Sender` 发送邮件，并根据结果更新数据库中的状态。

##### 3.2 邮件发送器 (Sender Provider)

*   **接口定义 (`internal/infrastructure/sender/provider.go`)**:
    ```go
    package sender

    import (
        "context"
        "YOUR_PROJECT/internal/domain/entity"
    )

    // Provider 定义了邮件发送器的统一接口
    type Provider interface {
        Send(ctx context.Context, email *entity.EmailMessage) error
    }
    ```
*   **工厂实现 (`internal/infrastructure/sender/factory.go`)**:
    ```go
    package sender

    // NewProvider 根据配置创建并返回一个邮件发送器实例
    func NewProvider(config *Config) (Provider, error) {
        switch config.ProviderType {
        case "smtp":
            return NewSmtpProvider(config.SMTP)
        case "sendgrid":
            return NewSendGridProvider(config.SendGrid)
        default:
            return nil, errors.New("unknown email provider")
        }
    }
    ```
    这使得添加新的服务商只需实现 `Provider` 接口并更新工厂即可，符合开闭原则。

##### 3.3 批量发送、重试与取消

*   **批量发送**: 接口层接收 `recipients` 数组，应用层循环构建多个 `EmailMessage` 实体，最后调用 `EmailRepository.CreateBatch` 方法，在单个事务中将它们全部插入数据库。
*   **自动重试**: Worker 在发送失败时，会增加 `retry_count` 并将状态更新为 `FAILED`。下一次轮询时，如果 `retry_count < max_retries`，邮件会被再次尝试发送。
*   **手动重试**: 可以提供一个API，接收 `email_id`，由应用层服务将指定邮件的 `status` 从 `FAILED` 改回 `PENDING` 并重置 `retry_count`。
*   **取消发送**: 提供一个API，接收 `email_id`，应用层服务将 `status` 为 `PENDING` 的邮件更新为 `CANCELLED`。Worker 将忽略此状态的邮件。

#### 4. 关键接口与数据结构定义

*   **DTO (`internal/interfaces/grpc/dto/email.go`)**:
    ```go
    type SendEmailRequest struct {
        TemplateID      string              `json:"template_id"`
        ToAddresses     []string            `json:"to_addresses"`
        Variables       map[string]string   `json:"variables"`
        // ... 其他字段
    }
    ```
*   **应用服务接口 (`internal/application/service/email_app_service.go`)**:
    ```go
    type EmailApplicationService interface {
        SendEmail(ctx context.Context, req *dto.SendEmailRequest) (*dto.SendEmailResponse, error)
        CancelEmail(ctx context.Context, req *dto.CancelEmailRequest) error
    }
    ```
*   **仓储接口 (`internal/domain/repository/email_repository.go`)**:
    ```go
    package repository

    type EmailRepository interface {
        CreateBatch(ctx context.Context, emails []*entity.EmailMessage) error
        FindPendingEmails(ctx context.Context, limit int) ([]*entity.EmailMessage, error)
        UpdateStatus(ctx context.Context, id uint64, status string, newRetryCount int, errorMsg string) error
        // ... 其他查询方法
    }
    ```

#### 5. ���据库与权限设计

*   **用户/租户ID获取**: 在接口层的中间件中，从 `token` 或 `session` 解析出 `user_id` 和 `tenant_id`，并通过 `context.WithValue` 注入到 `context.Context` 中。
*   **仓储层实现**: 在 `GORM` 的实现中，使用 `Scopes` 或基础 `*gorm.DB` 对象来统一添加查询条件。
    ```go
    // internal/infrastructure/persistence/gorm_base_repository.go
    func TenantScope(ctx context.Context) func(db *gorm.DB) *gorm.DB {
        tenantID, ok := ctx.Value("tenant_id").(string)
        if !ok {
            // 返回一个必定失败的查询，防止数据泄露
            return func(db *gorm.DB) *gorm.DB {
                return db.Where("1 = 0")
            }
        }
        return func(db *gorm.DB) *gorm.DB {
            return db.Where("tenant_id = ?", tenantID)
        }
    }

    // 在具体查询中使用
    db.WithContext(ctx).Scopes(TenantScope(ctx)).Where("status = ?", "PENDING").Find(&emails)
    ```

#### 6. 错误处理与日志

*   **错误码**: 在 `pkg/ierr` (或类似包) 中定义邮件模块的错误。
    ```go
    // 20xxxx: 邮件模块错误
    var (
        ErrTemplateNotFound = ierr.New(200101, "template not found")
        ErrProviderFailed   = ierr.New(200201, "email provider failed to send")
    )
    ```
*   **结构化日志**: 使用 `uber-go/zap` 或类似库。在中间件中注入 `request_id`，并在 `context` 中传递。日志中必须包含 `request_id`, `tenant_id`, `user_id`, `error_code`, `error_message`, `stack_trace`。

#### 7. 可测试性与运维

*   **可测试性**:
    *   所有依赖（如 Repository, Sender Provider）都通过接口注入，在单元测试中可以轻松使用 Mock 对象。
    *   应用层和领域层的测试不应依赖任何数据库或外部服务。
    *   使用表驱动测试来覆盖各种输入和边界条件。
*   **运维与监控**:
    *   **Metrics**: 使用 Prometheus 监控以下关键指标：
        *   `email_queue_pending_count`: 待发送邮件数量。
        *   `email_send_success_total`: 发送成功总数（按 provider 区分）。
        *   `email_send_failure_total`: 发送失败总数（按 provider 和错误类型区分）。
        *   `email_send_duration_seconds`: 邮件发送耗时（P99, P95）。
    *   **Alerting**:
        *   当 `email_queue_pending_count` 持续高于阈值时告警。
        *   当 `email_send_failure_total` 速率突增时告警。

#### 8. 下一步计划

1.  **评审与确认**: 与团队评审此设计方案，确保达成共识。
2.  **接口与实体定义**: 优先完成 `domain` 层实体和 `repository` 接口的��义。
3.  **基础设施实现**: 并行实现 `persistence` 层的 GORM Repository 和 `sender` 层的 Provider。
4.  **应用层开发**: 实现 `EmailAppService`，编排业务逻辑。
5.  **后台工作进程开发**: 创建 `cmd/worker`，实现异步发送逻辑。
6.  **接口层适配**: 修改 `interfaces` 层的 gRPC/HTTP Handler，接入新的应用服务。
7.  **测试编写**: 编写单元测试和集成测试，确保代码质量。
8.  **部署与观察**: 灰度上线，密切关注监控和日志，验证系统稳定性。

---

这份设计方案严格遵循了您提出的各项要求，提供了一个清晰、健壮且可扩展的邮件发送系统架构。如果您对其中任何细节有疑问，或者希望进行调整，请随时提出。
