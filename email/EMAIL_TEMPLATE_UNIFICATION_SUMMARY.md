# Email模块模板发送邮件统一化重构总结

## 重构目标

将email模块中多个功能类似的模板发送邮件实现统一化，消除重复代码，提高代码复用性和维护性。

## 重构前的问题

### 1. 重复的模板渲染实现
- `email_application_service.go` 中的 `renderTemplate` 方法
- `email_account_application_service.go` 中的 `renderTemplate` 方法  
- `template_application_service.go` 中的 `renderTemplate` 方法

### 2. 分散的邮件发送逻辑
- 通用模板邮件发送
- 账户测试邮件发送
- 直接邮件发送
- 各种不同的实现方式

### 3. 代码重复
- 模板渲染逻辑重复
- 邮件创建和发送逻辑重复
- 错误处理逻辑重复

## 重构后的统一架构

### 1. 统一的模板渲染服务

**文件**: `email/internal/application/template/service/template_renderer_service.go`

**功能**:
- `RenderTemplate()` - 完整的模板渲染（包含模板函数）
- `RenderSimpleTemplate()` - 简单变量替换（用于预览）
- `RenderTextTemplate()` - 文本模板渲染（带函数支持）
- `ValidateTemplateVariables()` - 模板变量验证
- `RenderTemplateForPreview()` - 预览渲染

**特点**:
- 统一的模板函数映射
- 支持HTML和纯文本渲染
- 完整的错误处理
- 变量验证功能

### 2. 统一的邮件发送服务

**文件**: `email/internal/application/email/service/unified_email_service.go`

**功能**:
- `SendTemplateEmail()` - 基于模板发送邮件
- `SendDirectEmail()` - 直接发送邮件（不使用模板）
- `SendAccountTestEmail()` - 发送账户测试邮件

**特点**:
- 统一的邮件创建逻辑
- 统一的发送流程
- 统一的错误处理
- 统一的状态管理

### 3. 重构后的服务依赖关系

```
UnifiedEmailService
├── TemplateRendererService (模板渲染)
├── EmailRepository (邮件存储)
├── TemplateRepository (模板存储)
├── EmailAccountRepository (账户存储)
└── EmailSenderFactory (邮件发送)

EmailApplicationService (已重构)
├── TemplateRendererService (使用统一渲染服务)
└── 其他依赖...

EmailAccountApplicationService (已重构)
├── TemplateRendererService (使用统一渲染服务)
└── 其他依赖...
```

## 重构效果

### 1. 代码复用性提升
- 模板渲染逻辑统一，避免重复实现
- 邮件发送流程统一，减少代码重复
- 错误处理逻辑统一，提高一致性

### 2. 维护性提升
- 单一职责原则：每个服务专注于特定功能
- 依赖注入：便于测试和扩展
- 统一接口：便于理解和维护

### 3. 扩展性提升
- 新增模板渲染功能只需修改TemplateRendererService
- 新增邮件发送场景只需在UnifiedEmailService中添加方法
- 支持装饰器模式扩展功能

## 使用示例

### 1. 使用统一邮件发送服务

```go
// 创建统一邮件发送服务
unifiedService := NewUnifiedEmailService(
    emailRepo,
    templateRepo,
    emailAccountRepo,
    emailSenderFactory,
    templateRenderer,
    logger,
)

// 发送模板邮件
response, err := unifiedService.SendTemplateEmail(ctx, tenantID, request)

// 发送账户测试邮件
testResponse, err := unifiedService.SendAccountTestEmail(ctx, tenantID, accountID, testEmail)
```

### 2. 使用统一模板渲染服务

```go
// 创建模板渲染服务
renderer := service.NewTemplateRendererService(logger)

// 渲染完整模板
result, err := renderer.RenderTemplate(ctx, template, variables)

// 简单变量替换
content := renderer.RenderSimpleTemplate(templateContent, variables)
```

## 迁移指南

### 1. 现有服务迁移
- `EmailApplicationService` 已迁移到使用 `TemplateRendererService`
- `EmailAccountApplicationService` 已迁移到使用 `TemplateRendererService`
- 旧的 `renderTemplate` 方法已删除

### 2. 新功能开发
- 优先使用 `UnifiedEmailService` 进行邮件发送
- 优先使用 `TemplateRendererService` 进行模板渲染
- 避免重复实现模板渲染逻辑

### 3. 接口兼容性
- 保持现有API接口不变
- 内部实现已统一化
- 向后兼容，无需修改调用方代码

## 总结

通过这次重构，我们成功地将email模块中多个功能类似的模板发送邮件实现统一化，实现了：

1. **代码复用**: 消除了重复的模板渲染和邮件发送逻辑
2. **架构清晰**: 明确了各服务的职责和依赖关系
3. **易于维护**: 统一的实现方式便于后续维护和扩展
4. **向后兼容**: 保持了现有接口的兼容性

这种统一化的架构设计符合DDD原则，提高了代码质量和系统的可维护性。 

---

# AI邮件系统分层架构设计提示词

你是一个资深后端系统架构师，请基于如下约束和现有邮件系统，输出一份详细的邮件发送分层架构设计方案，内容需涵盖整体架构、核心流程、关键接口、异常处理、扩展性、测试与运维建议等。请严格遵循以下要求：

---

### 1. 使用的数据库和表

- **数据库类型**：MySQL
- **核心表**：
  - `email_messages`：邮件发送队列表，字段包括（但不限于）`id`, `email_id`, `tenant_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`
  - `email_templates`：邮件模板表
  - `email_accounts`：邮件发送账号表

**注意：** 设计方案中不得修改现有表结构。

---

### 2. 项目规范

- 遵循**领域驱动设计（DDD）**与**Clean Architecture**，分层包括：领域层、应用层、基础设施层、接口层。
- 代码分层清晰，职责单一，依赖倒置，接口驱动。
- 发送逻辑分为两层：底层为直接内容发送，上层为模板渲染与队列入库，所有邮件发送均通过`email_messages`表实现队列化。
- 支持批量发送、取消、重试、状态查询等功能。
- 仅允许在应用服务、队列服务、模板服务等指定边界内修改和扩展，不得更改数据库表结构及与表结构强相关的领域实体。

---

### 3. 代码规范

- 使用**Go语言**，风格需符合Go社区最佳实践。
- 代码需模块化、可测试、接口驱动，禁止全局变量，依赖注入。
- 错误处理需显式、可追踪，禁止直接暴露系统错误给用户。
- 业务逻辑与基础设施解耦，所有外部依赖通过接口抽象。
- 关键接口、结构体、方法需有GoDoc风格注释。
- 单元测试需覆盖所有核心逻辑，采用表驱动测试。

---

### 4. 错误码与异常处理

- 错误码需分为系统级（5xx）、业务级（1xxx-9xxx），并与项目统一错误码规范对齐。
- 所有API响应需使用统一结构体，禁止直接返回系统异常信息。
- 日志需结构化，包含请求ID、错误详情、堆栈信息等，便于追踪和分析。

---

### 5. 修改边界

- **允许修改**：应用服务层、队列服务、模板服务、发送器工厂、接口适配层、单元测试。
- **禁止修改**：数据库表结构、领域实体的持久化字段、与表结构强相关的迁移脚本。
- **扩展方式**：如需新增功能，须通过组合、装饰器、适配器等设计模式实现，禁止破坏原有接口契约。

---

### 6. 输出要求

- 需输出**整体架构图**（可用Mermaid代码块表示）
- 详细描述各层职责、核心流程、关键接口与数据流
- 说明异常处理、错误码设计、批量/取消/重试等功能实现要点
- 给出关键代码结构示例（接口、服务、DTO等）
- 说明如何保证可测试性与可维护性
- 提出运维与监控建议

---

**请严格按照上述要求，输出一份高质量、可落地的邮件系统分层发送架构设计方案。** 