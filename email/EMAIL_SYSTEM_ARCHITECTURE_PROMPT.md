# Email系统分层架构设计AI提示词

你是一个资深后端系统架构师，请基于如下约束和现有邮件系统，输出一份详细的邮件发送分层架构设计方案，内容需涵盖整体架构、核心流程、关键接口、异常处理、扩展性、测试与运维建议等。请严格遵循以下要求：

---

### 1. 使用的数据库和表

- **数据库类型**：MySQL
- **核心表**：
  - `email_messages`：邮件发送队列表，字段包括（但不限于）`id`, `email_id`, `tenant_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`
  - `email_templates`：邮件模板表
  - `email_accounts`：邮件发送账号表

**注意：** 设计方案中不得修改现有表结构。

#### 1.1 现有功能保留要求
- **email_messages表功能**：必须保留邮件队列管理、状态跟踪、重试机制、变量存储、附件支持、多收件人支持等功能。
- **email_templates表功能**：必须保留模板管理、变量定义、版本控制、状态管理、权限控制、速率限制等功能。
- **email_accounts表功能**：必须保留账号管理、SMTP/API配置、测试功能、配额管理、系统账号标识等功能。
- **租户配置功能**：必须保留租户级别的配置管理、SMTP验证、使用统计等功能。
- **统计分析功能**：必须保留邮件发送统计、状态统计、时间范围查询、多维度分析等功能。

---

### 2. 项目规范

- 遵循**领域驱动设计（DDD）**与**Clean Architecture**，分层包括：领域层、应用层、基础设施层、接口层。
- 代码分层清晰，职责单一，依赖倒置，接口驱动。
- 发送逻辑分为两层：底层为直接内容发送，上层为模板渲染与队列入库，所有邮件发送均通过`email_messages`表实现队列化。
- 支持批量发送、取消、重试、状态查询等功能。
- 仅允许在应用服务、队列服务、模板服务等指定边界内修改和扩展，不得更改数据库表结构及与表结构强相关的领域实体。

---

### 3. 代码规范与质量要求

- 使用**Go语言**，风格需符合Go社区最佳实践。
- 代码需模块化、可测试、接口驱动，禁止全局变量，依赖注入。
- 错误处理需显式、可追踪，禁止直接暴露系统错误给用户。
- 业务逻辑与基础设施解耦，所有外部依赖通过接口抽象。
- 关键接口、结构体、方法需有GoDoc风格注释。
- 单元测试需覆盖所有核心逻辑，采用表驱动测试。

#### 3.1 抽象设计质量要求
- **合理抽象**：设计需体现高内聚低耦合原则，通过接口定义契约，实现与抽象分离。
- **设计模式应用**：合理使用工厂模式、策略模式、装饰器模式、适配器模式等，避免过度设计。
- **单一职责**：每个类/函数只负责一个明确的功能，避免职责混乱。
- **开闭原则**：对扩展开放，对修改关闭，通过接口和组合实现功能扩展。

#### 3.2 数据库层设计质量要求
- **可重用性**：数据库操作层需设计为可重用的组件，支持不同业务场景的复用。
- **批量操作优先**：优先使用批量SQL操作（如`INSERT ... ON DUPLICATE KEY UPDATE`、批量更新等），避免循环操作数据库。
- **内存处理限制**：只有在无法通过SQL实现或内存处理存在巨大风险时，才允许循环操作数据库。
- **查询优化**：合理使用索引、JOIN、子查询等，避免N+1查询问题。
- **事务管理**：合理使用数据库事务，确保数据一致性，避免长事务。

#### 3.3 性能与资源管理要求
- **连接池管理**：合理配置数据库连接池，避免连接泄漏。
- **缓存策略**：对频繁查询的数据实施合理的缓存策略。
- **异步处理**：对于耗时操作，采用异步处理模式。
- **资源释放**：确保所有资源（数据库连接、文件句柄等）得到正确释放。

---

### 4. 错误码与异常处理（细节要求）

- 必须使用项目统一的错误码体系，邮件模块错误码范围为**200000-299999**，并按功能模块分类（如发送、模板、账号、租户、系统等）。
- 错误类型需包含`code`、`message`、`details`字段，所有API响应需使用统一结构体，禁止直接返回系统异常信息。
- 错误处理需通过便捷错误创建函数（如`NewEmailError`、`NewTemplateNotFoundError`等）和统一错误处理器（如`HandleEmailError`）实现。
- 日志需结构化，包含请求ID、错误详情、堆栈信息等，便于追踪和分析。
- 设计方案需明确异常处理流程、错误码分层、错误响应示例。

---

### 5. 数据库查询与权限校验（细节要求）

- 所有数据库查询必须带有`tenant_id`和`deleted_at IS NULL`等条件，确保多租户隔离和软删除安全。
- 查询涉及系统资源时，需根据用户ID和权限进行过滤（如`is_system`字段、超级管理员校验等）。
- 用户ID、租户ID必须通过**中间件或context**获取，禁止前端传递，需在设计中明确用户身份和租户信息的获取方式。
- 查询需支持分页、排序、模糊搜索等常见业务需求。
- 设计方案需给出数据库查询的安全性、权限控制、用户/租户信息获取的实现要点。

---

### 6. 修改边界

- **允许修改**：应用服务层、队列服务、模板服务、发送器工厂、接口适配层、单元测试。
- **禁止修改**：数据库表结构、领域实体的持久化字段、与表结构强相关的迁移脚本。
- **扩展方式**：如需新增功能，须通过组合、装饰器、适配器等设计模式实现，禁止破坏原有接口契约。

---

### 7. 输出要求

- 需输出**整体架构图**（可用Mermaid代码块表示）
- 详细描述各层职责、核心流程、关键接口与数据流
- 说明异常处理、错误码设计、批量/取消/重试等功能实现要点
- 给出关键代码结构示例（接口、服务、DTO等）
- 说明如何保证可测试性与可维护性
- 提出运维与监控建议
- 明确数据库查询、用户/租户ID获取、权限校验、错误码与异常处理等细节要求的实现方式
- 详细说明抽象设计、数据库层可重用性、批量操作策略等质量要求的实现方案
- 明确说明如何保留现有表的所有功能，确保重构后功能完整性

---

**请严格按照上述要求，输出一份高质量、可落地的邮件系统分层发送架构设计方案。** 