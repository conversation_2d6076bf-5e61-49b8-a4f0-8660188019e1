package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"platforms-email/api/emailpb"
	"platforms-email/internal/infrastructure/config"
	"platforms-email/internal/infrastructure/container"
	"platforms-email/internal/interfaces/http/routes"
	common_database "platforms-pkg/db"
	"platforms-pkg/grpcmiddleware"
	"platforms-pkg/grpcregistry"
	"platforms-pkg/httpmiddleware"
	"platforms-pkg/logiface"
	"platforms-pkg/otel"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	gormLogger "gorm.io/gorm/logger"
)

const ServiceName = "platforms-email"

func main() {
	// 创建应用实例
	app := NewApplication()

	// 初始化应用
	if err := app.Initialize(); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// 启动应用
	if err := app.Start(); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}

	// 等待关闭信号
	app.WaitForShutdown()

	// 优雅关闭
	if err := app.Shutdown(); err != nil {
		log.Printf("Error during shutdown: %v", err)
		os.Exit(1)
	}
}

// Application 应用主结构
type Application struct {
	config            *config.Config
	logger            logiface.Logger
	accessLogger      logiface.Logger
	container         *container.DependencyContainer
	httpServer        *http.Server
	grpcServer        *grpc.Server
	grpcRegistry      *grpcregistry.ServiceRegistry
	otelShutdown      func(context.Context) error
	gormLoggerAdapter *common_database.GormLoggerAdapter
	ctx               context.Context
	cancel            context.CancelFunc
}

// NewApplication 创建应用实例
func NewApplication() *Application {
	ctx, cancel := context.WithCancel(context.Background())
	return &Application{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Initialize 初始化应用
func (app *Application) Initialize() error {
	// 1. 解析命令行参数
	flag.Parse()

	// 2. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}
	cfg.ServiceName = ServiceName
	app.config = cfg

	// 3. 初始化日志
	if err := app.initLogger(); err != nil {
		return fmt.Errorf("failed to init logger: %w", err)
	}

	// 4. 初始化 OpenTelemetry
	if err := app.initOpenTelemetry(); err != nil {
		return fmt.Errorf("failed to init OpenTelemetry: %w", err)
	}

	// 5. 启动配置热更新监听
	app.startConfigWatcher()

	// 6. 初始化依赖注入容器
	if err := app.initDependencyContainer(); err != nil {
		return fmt.Errorf("failed to init dependency container: %w", err)
	}

	// 7. 初始化 gRPC 客户端管理器
	if err := app.initGRPCManager(); err != nil {
		return fmt.Errorf("failed to init gRPC manager: %w", err)
	}

	// 8. 构建 HTTP 服务器
	if err := app.buildHTTPServer(); err != nil {
		return fmt.Errorf("failed to build HTTP server: %w", err)
	}

	// 9. 构建 gRPC 服务器
	if err := app.buildGRPCServer(); err != nil {
		return fmt.Errorf("failed to build gRPC server: %w", err)
	}

	app.logger.Info(app.ctx, "Application initialized successfully",
		logiface.String("service", ServiceName),
		logiface.String("env", app.config.Server.Env))

	return nil
}

// initLogger 初始化日志系统
func (app *Application) initLogger() error {
	// 判断开发环境，强制所有日志输出到控制台
	devEnv := app.config.Server.Env
	if devEnv == "" && app.config.Log["app"].InitialFields != nil {
		if v, ok := app.config.Log["app"].InitialFields["env"].(string); ok {
			devEnv = v
		}
	}
	if devEnv == "dev" {
		for k, logCfg := range app.config.Log {
			logCfg.Output = "stdout"
			logCfg.Format = "console"
			app.config.Log[k] = logCfg
		}
	}

	logConfig := logiface.MultiLogConfig{
		App:    app.config.Log["app"],
		Access: app.config.Log["access"],
		Error:  app.config.Log["error"],
	}

	logiface.InitLogger(logConfig)
	app.logger = logiface.GetLogger()
	app.accessLogger = logiface.GetAccessLogger()
	httpmiddleware.SetAccessLogger(app.accessLogger)
	app.logger.Info(app.ctx, "Email platform starting...", logiface.String("service", ServiceName))
	return nil
}

// initOpenTelemetry 初始化 OpenTelemetry
func (app *Application) initOpenTelemetry() error {
	shutdown, err := otel.InitTracerProvider(ServiceName, app.config.Otel.Endpoint)
	if err != nil {
		app.logger.Error(app.ctx, "Failed to init OpenTelemetry", logiface.Error(err))
		return err
	}
	app.otelShutdown = shutdown
	return nil
}

// startConfigWatcher 启动配置监听
func (app *Application) startConfigWatcher() {
	go func() {
		if err := config.ListenNacosConfigChange(ServiceName); err != nil {
			app.logger.Warn(app.ctx, "Failed to start nacos config change listener", logiface.Error(err))
		}
	}()

	// 启动日志级别监听
	go app.watchLogLevelFromNacos()
}

// watchLogLevelFromNacos 监听日志级别变化
func (app *Application) watchLogLevelFromNacos() {
	config.RegisterConfigChangeCallback(func(newConfig *config.Config) {
		// 通过重新初始化logger来更新配置
		logiface.InitLogger(logiface.MultiLogConfig{
			App:    newConfig.Log["app"],
			Access: newConfig.Log["access"],
			Error:  newConfig.Log["error"],
		})
		app.logger.Info(app.ctx, "Application log level updated from Nacos", logiface.String("level", newConfig.Log["app"].Level))

		if app.gormLoggerAdapter != nil {
			var gormLogLevel gormLogger.LogLevel
			switch newConfig.Log["app"].Level {
			case "debug", "info":
				gormLogLevel = gormLogger.Info
			case "warn":
				gormLogLevel = gormLogger.Warn
			case "error":
				gormLogLevel = gormLogger.Error
			default:
				gormLogLevel = gormLogger.Warn
			}
			app.gormLoggerAdapter.SetLogLevel(gormLogLevel)
			app.logger.Info(app.ctx, "GORM log level updated from Nacos",
				logiface.String("app_level", newConfig.Log["app"].Level),
				logiface.Int("gorm_level", int(gormLogLevel)))
		}
	})
}

// initDependencyContainer 初始化依赖注入容器
func (app *Application) initDependencyContainer() error {
	app.container = container.NewDependencyContainer(app.config, app.logger)
	return app.container.Initialize(app.ctx)
}

// initGRPCManager 初始化 gRPC 客户端管理器
func (app *Application) initGRPCManager() error {
	// 初始化全局 gRPC 客户端管理器
	grpcregistry.InitGlobalManager(app.logger)

	// 批量订阅 gRPC 服务
	if err := grpcregistry.BatchSubscribeServices(app.config.GRPCSubscriptions, app.logger); err != nil {
		return fmt.Errorf("failed to batch subscribe grpc services: %w", err)
	}

	// 清理 gRPC 连接池，确保所有连接都使用新的 OpenTelemetry 配置
	// 注意：只有在所有服务都成功订阅后才清理连接池
	grpcregistry.ClearAllConnectionPoolsGlobal()
	app.logger.Info(app.ctx, "Cleared gRPC connection pools after OpenTelemetry initialization")

	app.logger.Info(app.ctx, "gRPC client manager initialized successfully")
	return nil
}

// buildHTTPServer 构建 HTTP 服务器
func (app *Application) buildHTTPServer() error {
	// 设置 Gin 模式
	gin.SetMode(gin.ReleaseMode)
	if app.config.Server.Env == "dev" {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()
	// 设置路由
	routerConfig := &routes.RouterConfig{
		EmailHandler:        app.container.Interfaces.EmailHandler,
		EmailAccountHandler: app.container.Interfaces.EmailAccountHandler,
		TenantHandler:       app.container.Interfaces.TenantHandler,
		TemplateHandler:     app.container.Interfaces.TemplateHandler,
		ServiceName:         ServiceName,
		AppLogger:           app.logger,
		Container:           app.container,
	}
	routes.SetupRoutes(router, routerConfig)

	// 添加健康检查
	router.GET("/health", app.healthCheck)

	// 创建 HTTP 服务器
	app.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", app.config.Server.Port),
		Handler:      router,
		ReadTimeout:  app.config.GetServerReadTimeout(),
		WriteTimeout: app.config.GetServerWriteTimeout(),
		IdleTimeout:  app.config.GetServerIdleTimeout(),
	}

	return nil
}

// buildGRPCServer 构建 gRPC 服务器
func (app *Application) buildGRPCServer() error {
	app.grpcServer = grpc.NewServer(
		grpcmiddleware.GRPCOtelServerOption(),
		grpc.ChainUnaryInterceptor(
			grpcmiddleware.AccessLogInterceptor(app.accessLogger, []string{"password", "token"}),
		),
	)

	// 注册 EmailService
	emailpb.RegisterEmailServiceServer(app.grpcServer, app.container.GetEmailGrpcServer())

	app.logger.Info(app.ctx, "gRPC server built successfully")
	return nil
}

// registerGRPCService 注册 gRPC 服务到 Nacos
func (app *Application) registerGRPCService() error {
	// Use grpcregistry to register the service with Nacos
	serviceConfig := &grpcregistry.GRPCServiceConfig{
		ServiceName:    app.config.GRPC.ServiceName,
		Port:           app.config.GRPC.Port,
		Group:          app.config.GRPC.Group,
		Namespace:      app.config.GRPC.Namespace,
		Weight:         app.config.GRPC.Weight,
		Metadata:       app.config.GRPC.Metadata,
		LocalIP:        app.config.GRPC.LocalIP,
		EnableRegister: true,
	}

	registry, err := grpcregistry.NewServiceRegistry(serviceConfig, app.logger)
	if err != nil {
		return fmt.Errorf("failed to create service registry: %w", err)
	}

	app.grpcRegistry = registry

	if err := registry.Register(app.ctx); err != nil {
		return fmt.Errorf("failed to register gRPC service with Nacos: %w", err)
	}

	app.logger.Info(app.ctx, "gRPC service registered with Nacos successfully",
		logiface.String("service_name", app.config.GRPC.ServiceName),
		logiface.Int("port", app.config.GRPC.Port))

	return nil
}

// healthCheck 健康检查处理器
func (app *Application) healthCheck(c *gin.Context) {
	// 检查数据库连接
	if app.container.Infrastructure.DB != nil {
		sqlDB, err := app.container.Infrastructure.DB.DB()
		if err != nil || sqlDB.Ping() != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "unhealthy",
				"error":  "database connection failed",
			})
			return
		}
	}

	// 检查 gRPC 客户端连接
	if app.container.Infrastructure.UserClient != nil {
		if err := app.container.Infrastructure.UserClient.Health(app.ctx); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "unhealthy",
				"error":  "grpc client connection failed",
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"service":   ServiceName,
		"timestamp": time.Now().Unix(),
	})
}

// Start 启动应用
func (app *Application) Start() error {
	// 启动 gRPC 服务器
	go func() {
		grpcPort := app.config.GRPC.Port
		lis, err := net.Listen("tcp", fmt.Sprintf(":%d", grpcPort))
		if err != nil {
			app.logger.Error(app.ctx, "Failed to listen on gRPC port", logiface.Error(err), logiface.Int("port", grpcPort))
			app.cancel()
			return
		}

		app.logger.Info(app.ctx, "Starting gRPC server", logiface.String("address", lis.Addr().String()))

		// Register gRPC service with Nacos if enabled
		app.logger.Info(app.ctx, "Checking gRPC registration configuration",
			logiface.String("service_name", app.config.GRPC.ServiceName),
			logiface.String("namespace", app.config.GRPC.Namespace),
			logiface.String("group", app.config.GRPC.Group))
		if err := app.registerGRPCService(); err != nil {
			app.logger.Error(app.ctx, "Failed to register gRPC service with Nacos", logiface.Error(err))
		} else {
			app.logger.Info(app.ctx, "gRPC service registration completed successfully")
		}
		if err := app.grpcServer.Serve(lis); err != nil {
			app.logger.Error(app.ctx, "gRPC server failed to start", logiface.Error(err))
			app.cancel()
		}
	}()

	// 启动 HTTP 服务器
	go func() {
		app.logger.Info(app.ctx, "Starting HTTP server", logiface.String("address", app.httpServer.Addr))
		if err := app.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			app.logger.Error(app.ctx, "HTTP server failed to start", logiface.Error(err))
			app.cancel() // 触发优雅关闭
		}
	}()

	app.logger.Info(app.ctx, "Application started successfully")
	return nil
}

// WaitForShutdown 等待关闭信号
func (app *Application) WaitForShutdown() {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		app.logger.Info(app.ctx, "Received shutdown signal")
	case <-app.ctx.Done():
		app.logger.Info(app.ctx, "Application context cancelled")
	}
}

// Shutdown 优雅关闭应用
func (app *Application) Shutdown() error {
	app.logger.Info(app.ctx, "Shutting down application...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 注销 gRPC 服务
	if app.grpcRegistry != nil {
		if err := app.grpcRegistry.Deregister(ctx); err != nil {
			app.logger.Warn(ctx, "gRPC service deregister failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "gRPC service deregistered")
		}
	}

	// 2. 关闭 gRPC 服务器
	if app.grpcServer != nil {
		app.grpcServer.GracefulStop()
		app.logger.Info(ctx, "gRPC server shutdown completed")
	}

	// 3. 关闭 HTTP 服务器
	if app.httpServer != nil {
		if err := app.httpServer.Shutdown(ctx); err != nil {
			app.logger.Error(ctx, "HTTP server shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "HTTP server shutdown completed")
		}
	}

	// 4. 关闭依赖注入容器
	if app.container != nil {
		if err := app.container.Close(); err != nil {
			app.logger.Error(ctx, "Dependency container close failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "Dependency container closed")
		}
	}

	// 5. 关闭 gRPC 客户端管理器
	if manager := grpcregistry.GetGlobalManager(); manager != nil {
		manager.Close()
		app.logger.Info(ctx, "gRPC client manager closed")
	}

	// 6. 关闭 OpenTelemetry
	if app.otelShutdown != nil {
		if err := app.otelShutdown(ctx); err != nil {
			app.logger.Error(ctx, "OpenTelemetry shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "OpenTelemetry shutdown completed")
		}
	}

	app.logger.Info(ctx, "Application shutdown completed")
	return nil
}
