package handlers

import (
	"ilike-backend/internal/domain/errors"
	commonResponse "platforms-pkg/common/response"

	"github.com/gin-gonic/gin"
)

// HandleILikeError 处理iLike模块的业务错误
// 将iLike特定的错误码映射到平台的通用错误码和HTTP状态码
func HandleILikeError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// 检查是否为iLike模块的错误
	if ilikeErr, ok := err.(*errors.ILikeError); ok {
		// 获取对应的通用错误码
		commonCode := errors.GetCommonErrorCode(ilikeErr.GetCode())

		// 根据具体错误类型提供更精确的处理
		switch ilikeErr.GetCode() {
		// 资源不存在
		case errors.CodeBookmarkNotFound,
			errors.CodeContentNotFound,
			errors.CodeCollectionNotFound,
			errors.CodeShareNotFound,
			errors.CodeTagNotFound,
			errors.CodeUserNotFound,
			errors.CodeFollowNotFound:
			commonResponse.NotFound(c, ilikeErr.GetMessage())

		// 资源已存在/冲突
		case errors.CodeBookmarkAlreadyExists,
			errors.CodeCollectionAlreadyExists,
			errors.CodeTagAlreadyExists,
			errors.CodeFollowAlreadyExists,
			errors.CodeBookmarkAlreadyLiked,
			errors.CodeBookmarkAlreadyCollected:
			commonResponse.Conflict(c, ilikeErr.GetMessage())

		// 参数验证错误
		case errors.CodeBookmarkInvalidURL,
			errors.CodeBookmarkInvalidTitle,
			errors.CodeContentInvalidFormat,
			errors.CodeCollectionInvalidName,
			errors.CodeCollectionInvalidDescription,
			errors.CodeTagInvalidName,
			errors.CodeTagInvalidColor,
			errors.CodeImportInvalidFormat:
			commonResponse.FieldError(c, "parameter", ilikeErr.GetMessage())

		// 权限相关错误
		case errors.CodeBookmarkPermissionDenied,
			errors.CodeCollectionPermissionDenied,
			errors.CodeSharePermissionDenied,
			errors.CodeFollowPermissionDenied:
			commonResponse.Forbidden(c, ilikeErr.GetMessage())

		// 内容大小限制
		case errors.CodeContentTooLarge,
			errors.CodeImportFileTooLarge,
			errors.CodeCollectionFull,
			errors.CodeTagTooMany:
			commonResponse.ErrorWithData(c, commonResponse.CodeSizeLimitExceeded, nil, ilikeErr.GetMessage())

		// 频率限制
		case errors.CodeRateLimitExceeded:
			commonResponse.TooManyRequests(c, 60) // 60秒后重试

		// 服务不可用
		case errors.CodeServiceUnavailable:
			commonResponse.ServiceUnavailable(c, ilikeErr.GetMessage())

		// 系统错误
		case errors.CodeStorageError,
			errors.CodeCacheError,
			errors.CodeIntegrationFailed:
			commonResponse.InternalError(c, err)

		// 内容处理失败
		case errors.CodeContentProcessingFailed,
			errors.CodeImportProcessingFailed,
			errors.CodeExportProcessingFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, ilikeErr.GetMessage())

		// 分享相关错误
		case errors.CodeShareExpired,
			errors.CodeShareInvalidToken,
			errors.CodeShareAlreadyDisabled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, ilikeErr.GetMessage())

		// 社交功能错误
		case errors.CodeFollowSelf,
			errors.CodeUserBlocked:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, ilikeErr.GetMessage())

		// 默认处理
		default:
			commonResponse.Error(c, commonCode, ilikeErr.GetMessage())
		}
		return
	}

	// 如果不是iLike模块的错误，按通用错误处理
	commonResponse.InternalError(c, err)
}

// FieldValidationError 返回字段验证错误
func FieldValidationError(c *gin.Context, field, message string) {
	commonResponse.FieldError(c, field, message)
}

// ResourceNotFound 返回资源不存在错误
func ResourceNotFound(c *gin.Context, resource string) {
	commonResponse.NotFound(c, resource)
}

// PermissionDenied 返回权限不足错误
func PermissionDenied(c *gin.Context, message string) {
	commonResponse.Forbidden(c, message)
}

// BusinessLogicError 返回业务逻辑错误
func BusinessLogicError(c *gin.Context, message string) {
	commonResponse.Error(c, commonResponse.CodeBusinessLogicError, message)
}

// ValidationError 返回验证错误
func ValidationError(c *gin.Context, field, message string, value interface{}) {
	commonResponse.FieldError(c, field, message, value)
}

// ConflictError 返回资源冲突错误
func ConflictError(c *gin.Context, message string) {
	commonResponse.Conflict(c, message)
}

// RateLimitError 返回频率限制错误
func RateLimitError(c *gin.Context, retryAfter int) {
	commonResponse.TooManyRequests(c, retryAfter)
}

// ServiceUnavailableError 返回服务不可用错误
func ServiceUnavailableError(c *gin.Context, message string) {
	commonResponse.ServiceUnavailable(c, message)
}
