package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"ilike-backend/internal/application/comment/dto"
	"ilike-backend/internal/application/comment/service"
	"platforms-pkg/common/response"
)

// CommentHandler 评论处理器
type CommentHandler struct {
	commentService *service.CommentApplicationService
}

// NewCommentHandler 创建评论处理器
func NewCommentHandler(commentService *service.CommentApplicationService) *CommentHandler {
	return &CommentHandler{
		commentService: commentService,
	}
}

// CreateComment 创建评论
// @Summary 创建评论
// @Description 用户创建新评论
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param request body dto.CommentCreateRequest true "创建评论请求"
// @Success 200 {object} httpResponse.Response{data=dto.CommentResponse}
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/create [post]
func (h *CommentHandler) CreateComment(c *gin.Context) {
	var req dto.CommentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	result, err := h.commentService.CreateComment(c.Request.Context(), &req)
	if err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, result)
}

// GetComment 获取评论详情
// @Summary 获取评论详情
// @Description 获取指定评论的详情信息
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param id query string true "评论ID"
// @Success 200 {object} httpResponse.Response{data=dto.CommentResponse}
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/detail [get]
func (h *CommentHandler) GetComment(c *gin.Context) {
	commentIDStr := c.Query("id")
	if commentIDStr == "" {
		common_response.FieldError(c, "id", "评论ID不能为空")
		return
	}

	commentID, err := strconv.ParseUint(commentIDStr, 10, 64)
	if err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	result, err := h.commentService.GetComment(c.Request.Context(), commentID)
	if err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, result)
}

// GetCommentsByRecord 获取记录的评论列表
// @Summary 获取记录的评论列表
// @Description 获取指定记录的评论列表，支持分页
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param record_id query string true "记录ID"
// @Param parent_id query string false "父评论ID（获取回复时使用）"
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} httpResponse.Response{data=dto.CommentListResponse}
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/list [get]
func (h *CommentHandler) GetCommentsByRecord(c *gin.Context) {
	var req dto.CommentListRequest

	// 解析record_id
	recordIDStr := c.Query("record_id")
	if recordIDStr == "" {
		common_response.FieldError(c, "record_id", "记录ID不能为空")
		return
	}
	recordID, err := strconv.ParseUint(recordIDStr, 10, 64)
	if err != nil {
		common_response.GinValidationError(c, err)
		return
	}
	req.RecordID = recordID

	// 解析parent_id（可选）
	parentIDStr := c.Query("parent_id")
	if parentIDStr != "" {
		parentID, err := strconv.ParseUint(parentIDStr, 10, 64)
		if err != nil {
			common_response.GinValidationError(c, err)
			return
		}
		req.ParentID = parentID
	}

	// 解析分页参数
	pageStr := c.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		page = 1
	}
	req.Page = page

	sizeStr := c.DefaultQuery("size", "20")
	size, err := strconv.Atoi(sizeStr)
	if err != nil || size <= 0 || size > 100 {
		size = 20
	}
	req.Size = size

	result, err := h.commentService.GetCommentsByRecord(c.Request.Context(), &req)
	if err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, result)
}

// UpdateComment 更新评论
// @Summary 更新评论
// @Description 更新评论内容
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param request body dto.CommentUpdateRequest true "更新评论请求"
// @Success 200 {object} httpResponse.Response{data=dto.CommentResponse}
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/update [post]
func (h *CommentHandler) UpdateComment(c *gin.Context) {
	var req dto.CommentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	result, err := h.commentService.UpdateComment(c.Request.Context(), &req)
	if err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, result)
}

// DeleteComment 删除评论
// @Summary 删除评论
// @Description 删除指定评论
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param request body dto.CommentDeleteRequest true "删除评论请求"
// @Success 200 {object} httpResponse.Response
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/delete [post]
func (h *CommentHandler) DeleteComment(c *gin.Context) {
	var req dto.CommentDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	if err := h.commentService.DeleteComment(c.Request.Context(), &req); err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, nil)
}

// LikeComment 点赞评论
// @Summary 点赞评论
// @Description 对指定评论进行点赞
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param request body dto.CommentLikeRequest true "点赞评论请求"
// @Success 200 {object} httpResponse.Response
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/like [post]
func (h *CommentHandler) LikeComment(c *gin.Context) {
	var req dto.CommentLikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	if err := h.commentService.LikeComment(c.Request.Context(), &req); err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, nil)
}

// UnlikeComment 取消点赞评论
// @Summary 取消点赞评论
// @Description 取消对指定评论的点赞
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param request body dto.CommentLikeRequest true "取消点赞评论请求"
// @Success 200 {object} httpResponse.Response
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/unlike [post]
func (h *CommentHandler) UnlikeComment(c *gin.Context) {
	var req dto.CommentLikeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	if err := h.commentService.UnlikeComment(c.Request.Context(), &req); err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, nil)
}

// GetUserComments 获取用户评论列表
// @Summary 获取用户评论列表
// @Description 获取指定用户的评论列表
// @Tags 评论管理
// @Accept json
// @Produce json
// @Param user_id query string true "用户ID"
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} httpResponse.Response{data=dto.CommentListResponse}
// @Failure 400 {object} httpResponse.Response
// @Router /api/ilike/comments/user [get]
func (h *CommentHandler) GetUserComments(c *gin.Context) {
	userIDStr := c.Query("user_id")
	if userIDStr == "" {
		common_response.FieldError(c, "user_id", "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		common_response.GinValidationError(c, err)
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		page = 1
	}

	sizeStr := c.DefaultQuery("size", "20")
	size, err := strconv.Atoi(sizeStr)
	if err != nil || size <= 0 || size > 100 {
		size = 20
	}

	result, err := h.commentService.GetUserComments(c.Request.Context(), userID, page, size)
	if err != nil {
		common_response.Error(c, common_response.CodeDatabaseError, err.Error())
		return
	}

	common_response.Success(c, result)
}