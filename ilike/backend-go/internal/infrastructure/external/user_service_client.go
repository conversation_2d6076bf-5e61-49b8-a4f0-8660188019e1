package external

import (
	"context"
	"fmt"

	"ilike-backend/internal/domain/errors"

	"platforms-pkg/grpcregistry"
	"platforms-pkg/logiface"
	"platforms-user/api/userpb"

	"google.golang.org/grpc"
)

// UserServiceClient 用户服务客户端包装器
type UserServiceClient struct {
	serviceName string
	logger      logiface.Logger
}

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(logger logiface.Logger) *UserServiceClient {
	return &UserServiceClient{
		serviceName: "platforms-user",
		logger:      logger,
	}
}

// GetUserClient 获取用户服务gRPC客户端
func (u *UserServiceClient) GetUserClient(ctx context.Context) (userpb.UserServiceClient, error) {
	// 使用全局管理器获取客户端
	client, err := grpcregistry.GetClientGlobal(ctx, u.serviceName, func(conn *grpc.ClientConn) interface{} {
		return userpb.NewUserServiceClient(conn)
	})
	if err != nil {
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("user service client acquisition failed: %v", err))
	}

	userClient, ok := client.(userpb.UserServiceClient)
	if !ok {
		return nil, errors.NewIntegrationFailedError("failed to cast to UserServiceClient")
	}

	return userClient, nil
}

// GetUserInfoByToken 根据token获取用户信息
func (u *UserServiceClient) GetUserInfoByToken(ctx context.Context, token string) (*userpb.GetUserInfoByTokenResponse, error) {
	client, err := u.GetUserClient(ctx)
	if err != nil {
		return nil, err
	}

	req := &userpb.GetUserInfoByTokenRequest{
		Token: token,
	}

	resp, err := client.GetUserInfoByToken(ctx, req)
	if err != nil {
		u.logger.Error(ctx, "Failed to get user info by token",
			logiface.Error(err),
			logiface.String("service", u.serviceName))
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("gRPC call failed: %v", err))
	}

	u.logger.Debug(ctx, "Got user info by token",
		logiface.String("service", u.serviceName),
		logiface.Int("code", int(resp.Code)))

	return resp, nil
}

// CheckUserPermissions 检查用户权限
func (u *UserServiceClient) CheckUserPermissions(ctx context.Context, userID int64, permissionCodes []string) (*userpb.CheckUserPermissionsResponse, error) {
	client, err := u.GetUserClient(ctx)
	if err != nil {
		return nil, err
	}

	req := &userpb.CheckUserPermissionsRequest{
		UserId:          userID,
		PermissionCodes: permissionCodes,
	}

	resp, err := client.CheckUserPermissions(ctx, req)
	if err != nil {
		u.logger.Error(ctx, "Failed to check user permissions",
			logiface.Error(err),
			logiface.String("service", u.serviceName),
			logiface.Int64("user_id", userID))
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("gRPC call failed: %v", err))
	}

	u.logger.Debug(ctx, "Checked user permissions",
		logiface.String("service", u.serviceName),
		logiface.Int64("user_id", userID),
		logiface.Int("permission_count", len(permissionCodes)),
		logiface.Int("code", int(resp.Code)))

	return resp, nil
}

// GetTenantInfoByCode 根据租户代码获取租户信息
func (u *UserServiceClient) GetTenantInfoByCode(ctx context.Context, tenantCode string) (*userpb.GetTenantInfoByCodeResponse, error) {
	client, err := u.GetUserClient(ctx)
	if err != nil {
		return nil, err
	}

	req := &userpb.GetTenantInfoByCodeRequest{
		TenantCode: tenantCode,
	}

	resp, err := client.GetTenantInfoByCode(ctx, req)
	if err != nil {
		u.logger.Error(ctx, "Failed to get tenant info by code",
			logiface.Error(err),
			logiface.String("service", u.serviceName),
			logiface.String("tenant_code", tenantCode))
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("gRPC call failed: %v", err))
	}

	u.logger.Debug(ctx, "Got tenant info by code",
		logiface.String("service", u.serviceName),
		logiface.String("tenant_code", tenantCode),
		logiface.Int("code", int(resp.Code)))

	return resp, nil
}

// Health 检查用户服务健康状态
func (u *UserServiceClient) Health(ctx context.Context) error {
	manager, err := grpcregistry.GetGlobalManager().GetServiceManager(u.serviceName)
	if err != nil {
		return errors.NewIntegrationFailedError(fmt.Sprintf("service not subscribed: %v", err))
	}

	return manager.Health(ctx)
}

// GetConnectionStats 获取连接统计信息
func (u *UserServiceClient) GetConnectionStats() map[string]interface{} {
	manager, err := grpcregistry.GetGlobalManager().GetServiceManager(u.serviceName)
	if err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	return manager.GetConnectionStats()
}

// Close 关闭客户端连接
func (u *UserServiceClient) Close() error {
	// 由于使用全局管理器，这里不需要手动关闭连接
	// 全局管理器会在程序退出时自动清理
	return nil
}
