package container

import (
	"context"
	"ilike-backend/internal/application/record/service"
	commentservice "ilike-backend/internal/application/comment/service"
	searchservice "ilike-backend/internal/application/search/service"
	tagservice "ilike-backend/internal/application/tag/service"
	"ilike-backend/internal/infrastructure/config"
	"ilike-backend/internal/infrastructure/database"
	"ilike-backend/internal/infrastructure/external"
	"ilike-backend/internal/infrastructure/persistence"
	persistenceRepository "ilike-backend/internal/infrastructure/persistence/repository"
	"ilike-backend/internal/interfaces/http/handler"
	"platforms-pkg/logiface"

	"gorm.io/gorm"
)

// InfrastructureContainer 管理基础设施依赖
// 只在启动阶段初始化
type InfrastructureContainer struct {
	Config      *config.Config
	Logger      logiface.Logger
	DB          *gorm.DB
	UsersClient *external.UserServiceClient
	IDGenClient *external.IDGeneratorClient // use interface, not pointer
}

// ApplicationContainer 管理应用服务依赖
type ApplicationContainer struct {
	RecordAppService     *service.RecordApplicationService
	RecordTypeAppService *service.RecordTypeApplicationService
	CategoryAppService   *service.CategoryApplicationService
	LikeAppService       *service.LikeApplicationService
	TagAppService        *tagservice.TagApplicationService
	WishlistAppService   *service.WishlistApplicationService
	SearchAppService     *searchservice.SearchApplicationService
	CommentAppService    *commentservice.CommentApplicationService
}

// InterfaceContainer 管理 handler 依赖
type InterfaceContainer struct {
	RecordHandler     *handler.RecordHandler
	RecordTypeHandler *handler.RecordTypeHandler
	CategoryHandler   *handler.CategoryHandler
	LikeHandler       *handler.LikeHandler
	TagHandler        *handler.TagHandler
	WishlistHandler   *handler.WishlistHandler
	SearchHandler     *handler.SearchHandler
	CommentHandler    *handler.CommentHandler
}

// DependencyContainer 主依赖注入容器
type DependencyContainer struct {
	Infrastructure *InfrastructureContainer
	Applications   *ApplicationContainer
	Interfaces     *InterfaceContainer
}

// NewDependencyContainer 创建依赖注入容器（结构体初始化，不做资源连接）
func NewDependencyContainer(cfg *config.Config, logger logiface.Logger) *DependencyContainer {
	infra := &InfrastructureContainer{
		Config:      cfg,
		Logger:      logger,
		DB:          nil, // 延迟初始化
		UsersClient: nil, // 延迟初始化
		IDGenClient: nil, // 延迟初始化
	}
	return &DependencyContainer{
		Infrastructure: infra,
		Applications:   &ApplicationContainer{},
		Interfaces:     &InterfaceContainer{},
	}
}

// Initialize 初始化所有依赖（推荐在main中调用）
func (c *DependencyContainer) Initialize(ctx context.Context) error {
	// 1. 初始化数据库
	if c.Infrastructure.DB == nil {
		mysql, err := database.NewMySQL(&c.Infrastructure.Config.Database, c.Infrastructure.Logger)
		if err != nil {
			return err
		}
		c.Infrastructure.DB = mysql.DB()
	}
	// 2. 初始化Users gRPC客户端
	if c.Infrastructure.UsersClient == nil {
		usersClient := external.NewUserServiceClient(c.Infrastructure.Logger)
		c.Infrastructure.UsersClient = usersClient
	}
	// 3. 初始化ID生成器
	if c.Infrastructure.IDGenClient == nil {
		c.Infrastructure.IDGenClient = external.NewIDGeneratorClient(c.Infrastructure.Logger)
	}
	// 4. 初始化仓储层
	db := c.Infrastructure.DB
	logger := c.Infrastructure.Logger
	idGenerator := c.Infrastructure.IDGenClient // already interface type
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}
	recordRepo := persistenceRepository.NewRecordRepository(db, logger, idGenerator)
	recordTypeRepo := persistenceRepository.NewRecordTypeRepository(db)
	categoryRepo := persistenceRepository.NewCategoryRepository(db, logger)
	tagRepo := persistence.NewTagRepository(db, logger, idGenerator)
	wishlistRepo := persistence.NewWishlistRepository(db, logger)
	likeRepo := persistence.NewLikeRepository(sqlDB, logger)
	recordTagRepo := persistence.NewRecordTagRepository(db, logger)
	searchHistoryRepo := persistence.NewSearchHistoryRepository(db, logger)
	commentRepo := persistenceRepository.NewCommentRepository(db)
	// 5. 初始化应用服务
	c.Applications.RecordAppService = service.NewRecordApplicationService(recordRepo, recordTypeRepo, recordTagRepo, logger)
	c.Applications.RecordTypeAppService = service.NewRecordTypeApplicationService(recordTypeRepo, logger)
	c.Applications.CategoryAppService = service.NewCategoryApplicationService(categoryRepo, logger)
	c.Applications.LikeAppService = service.NewLikeApplicationService(likeRepo, recordRepo, logger)
	c.Applications.TagAppService = tagservice.NewTagApplicationService(tagRepo, logger)
	c.Applications.WishlistAppService = service.NewWishlistApplicationService(recordRepo, wishlistRepo, logger)
	c.Applications.SearchAppService = searchservice.NewSearchApplicationService(recordRepo, tagRepo, searchHistoryRepo, logger)
	c.Applications.CommentAppService = commentservice.NewCommentApplicationService(commentRepo)
	// 6. 初始化 handler
	c.Interfaces.RecordHandler = handler.NewRecordHandler(c.Applications.RecordAppService, logger)
	c.Interfaces.RecordTypeHandler = handler.NewRecordTypeHandler(c.Applications.RecordTypeAppService, c.Applications.RecordAppService, logger)
	c.Interfaces.CategoryHandler = handler.NewCategoryHandler(c.Applications.CategoryAppService, logger)
	c.Interfaces.LikeHandler = handler.NewLikeHandler(c.Applications.LikeAppService, logger)
	c.Interfaces.TagHandler = handler.NewTagHandler(c.Applications.TagAppService, logger)
	c.Interfaces.WishlistHandler = handler.NewWishlistHandler(c.Applications.WishlistAppService, logger)
	c.Interfaces.SearchHandler = handler.NewSearchHandler(c.Applications.SearchAppService, logger)
	c.Interfaces.CommentHandler = handler.NewCommentHandler(c.Applications.CommentAppService)
	return nil
}

// Close 关闭所有资源（如数据库连接、gRPC连接等）
func (c *DependencyContainer) Close() error {
	if c.Infrastructure.DB != nil {
		sqlDB, err := c.Infrastructure.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	if c.Infrastructure.UsersClient != nil {
		c.Infrastructure.UsersClient.Close()
	}
	return nil
}
