package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"ilike-backend/internal/domain/comment/entity"
	commentRepo "ilike-backend/internal/domain/comment/repository"
	"ilike-backend/internal/domain/errors"
)

// CommentRepositoryImpl 评论仓储实现
type CommentRepositoryImpl struct {
	db *gorm.DB
}

// NewCommentRepository 创建评论仓储实现
func NewCommentRepository(db *gorm.DB) commentRepo.CommentRepository {
	return &CommentRepositoryImpl{
		db: db,
	}
}

// Create 创建评论
func (r *CommentRepositoryImpl) Create(ctx context.Context, comment *entity.Comment) error {
	if err := comment.ValidateCreate(); err != nil {
		return err
	}

	now := time.Now()
	comment.CreatedAt = now
	comment.UpdatedAt = now

	// 使用事务来创建评论和更新统计
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建评论
	if err := tx.Create(comment).Error; err != nil {
		tx.Rollback()
		return errors.NewStorageError(fmt.Sprintf("comment creation failed: %v", err))
	}

	// 如果是回复，更新父评论的回复计数
	if comment.IsReply() {
		if err := tx.Model(&entity.Comment{}).
			Where("id = ?", comment.ParentID).
			UpdateColumn("reply_count", gorm.Expr("reply_count + 1")).Error; err != nil {
			tx.Rollback()
			return errors.NewStorageError(fmt.Sprintf("parent comment reply count update failed: %v", err))
		}
	}

	return tx.Commit().Error
}

// GetByID 通过ID获取评论
func (r *CommentRepositoryImpl) GetByID(ctx context.Context, id uint64) (*entity.Comment, error) {
	var comment entity.Comment

	err := r.db.WithContext(ctx).
		Where("id = ? AND deleted_at IS NULL", id).
		First(&comment).Error

	if err == gorm.ErrRecordNotFound {
		return nil, errors.NewCommentNotFoundError(nil)
	}
	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("comment retrieval failed: %v", err))
	}

	return &comment, nil
}

// GetByRecordID 获取记录的评论列表
func (r *CommentRepositoryImpl) GetByRecordID(ctx context.Context, recordID uint64, limit, offset int) ([]*entity.Comment, error) {
	var comments []*entity.Comment

	err := r.db.WithContext(ctx).
		Where("record_id = ? AND deleted_at IS NULL", recordID).
		Order("created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&comments).Error

	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("record comments list retrieval failed: %v", err))
	}

	return comments, nil
}

// GetRepliesByParentID 获取父评论的回复列表
func (r *CommentRepositoryImpl) GetRepliesByParentID(ctx context.Context, parentID uint64, limit, offset int) ([]*entity.Comment, error) {
	var replies []*entity.Comment

	err := r.db.WithContext(ctx).
		Where("parent_id = ? AND deleted_at IS NULL", parentID).
		Order("created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&replies).Error

	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("replies list retrieval failed: %v", err))
	}

	return replies, nil
}

// GetByUserID 获取用户的评论列表
func (r *CommentRepositoryImpl) GetByUserID(ctx context.Context, userID uint64, limit, offset int) ([]*entity.Comment, error) {
	var comments []*entity.Comment

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND deleted_at IS NULL", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&comments).Error

	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("user comments list retrieval failed: %v", err))
	}

	return comments, nil
}

// Update 更新评论
func (r *CommentRepositoryImpl) Update(ctx context.Context, comment *entity.Comment) error {
	comment.UpdatedAt = time.Now()

	err := r.db.WithContext(ctx).
		Where("id = ? AND deleted_at IS NULL", comment.ID).
		Updates(comment).Error

	if err != nil {
		return errors.NewStorageError(fmt.Sprintf("comment update failed: %v", err))
	}

	return nil
}

// Delete 软删除评论
func (r *CommentRepositoryImpl) Delete(ctx context.Context, id uint64) error {
	now := time.Now()

	// 使用事务来删除评论和更新统计
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取评论信息
	var comment entity.Comment
	if err := tx.Where("id = ? AND deleted_at IS NULL", id).First(&comment).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			return errors.NewCommentNotFoundError(nil)
		}
		return errors.NewStorageError(fmt.Sprintf("comment retrieval failed: %v", err))
	}

	// 软删除评论
	if err := tx.Model(&entity.Comment{}).
		Where("id = ? AND deleted_at IS NULL", id).
		Update("deleted_at", now).Error; err != nil {
		tx.Rollback()
		return errors.NewStorageError(fmt.Sprintf("comment deletion failed: %v", err))
	}

	// 如果是回复，更新父评论的回复计数
	if comment.IsReply() {
		if err := tx.Model(&entity.Comment{}).
			Where("id = ?", comment.ParentID).
			UpdateColumn("reply_count", gorm.Expr("reply_count - 1")).Error; err != nil {
			tx.Rollback()
			return errors.NewStorageError(fmt.Sprintf("parent comment reply count update failed: %v", err))
		}
	}

	return tx.Commit().Error
}

// UpdateLikeCount 更新评论点赞数
func (r *CommentRepositoryImpl) UpdateLikeCount(ctx context.Context, commentID uint64, increment bool) error {
	expr := "like_count - 1"
	if increment {
		expr = "like_count + 1"
	}

	err := r.db.WithContext(ctx).Model(&entity.Comment{}).
		Where("id = ? AND deleted_at IS NULL", commentID).
		UpdateColumn("like_count", gorm.Expr(expr)).Error

	if err != nil {
		return errors.NewStorageError(fmt.Sprintf("comment like count update failed: %v", err))
	}

	return nil
}

// UpdateReplyCount 更新回复数
func (r *CommentRepositoryImpl) UpdateReplyCount(ctx context.Context, commentID uint64, increment bool) error {
	expr := "reply_count - 1"
	if increment {
		expr = "reply_count + 1"
	}

	err := r.db.WithContext(ctx).Model(&entity.Comment{}).
		Where("id = ? AND deleted_at IS NULL", commentID).
		UpdateColumn("reply_count", gorm.Expr(expr)).Error

	if err != nil {
		return errors.NewStorageError(fmt.Sprintf("reply count update failed: %v", err))
	}

	return nil
}

// CountByRecordID 获取记录的评论总数
func (r *CommentRepositoryImpl) CountByRecordID(ctx context.Context, recordID uint64) (int64, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&entity.Comment{}).
		Where("record_id = ? AND deleted_at IS NULL", recordID).
		Count(&count).Error

	if err != nil {
		return 0, errors.NewStorageError(fmt.Sprintf("record comments count retrieval failed: %v", err))
	}

	return count, nil
}

// CountRepliesByParentID 获取父评论的回复总数
func (r *CommentRepositoryImpl) CountRepliesByParentID(ctx context.Context, parentID uint64) (int64, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&entity.Comment{}).
		Where("parent_id = ? AND deleted_at IS NULL", parentID).
		Count(&count).Error

	if err != nil {
		return 0, errors.NewStorageError(fmt.Sprintf("replies count retrieval failed: %v", err))
	}

	return count, nil
}

// GetTopLevelComments 获取顶级评论（非回复）
func (r *CommentRepositoryImpl) GetTopLevelComments(ctx context.Context, recordID uint64, limit, offset int) ([]*entity.Comment, error) {
	var comments []*entity.Comment

	err := r.db.WithContext(ctx).
		Where("record_id = ? AND parent_id = 0 AND deleted_at IS NULL", recordID).
		Order("created_at ASC").
		Limit(limit).
		Offset(offset).
		Find(&comments).Error

	if err != nil {
		return nil, errors.NewStorageError(fmt.Sprintf("top level comments retrieval failed: %v", err))
	}

	return comments, nil
}
