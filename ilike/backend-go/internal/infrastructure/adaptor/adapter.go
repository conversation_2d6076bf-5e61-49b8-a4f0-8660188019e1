package adaptor

import (
	"context"
	"ilike-backend/internal/infrastructure/external"
	"platforms-pkg/httpmiddleware"
	"platforms-pkg/logiface"
)

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string) *httpmiddleware.AuthedUser {
	logger := logiface.GetLogger()
	userClient := a.UserClient

	resp, err := userClient.GetUserInfoByToken(ctx, token)
	if err != nil {
		logger.Warn(ctx, "Failed to get user info by token",
			logiface.Error(err),
			logiface.String("token_prefix", token[:min(len(token), 10)]+"..."))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid or expired token",
			logiface.Int("response_code", int(resp.Code)),
			logiface.String("response_message", resp.Message))
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   resp.Data.UserId,
		Username: resp.Data.Username,
		RealName: resp.Data.RealName,
		Email:    resp.Data.Email,
		TenantId: resp.Data.TenantId,
	}
}

// TenantInfoProviderAdapter 租户信息提供者适配器
type TenantInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetTenantInfo 实现 TenantInfoProvider 接口
func (a *TenantInfoProviderAdapter) GetTenantInfo(ctx context.Context, tenantCode string) *httpmiddleware.TenantInfo {
	logger := logiface.GetLogger()
	userClient := a.UserClient

	resp, err := userClient.GetTenantInfoByCode(ctx, tenantCode)
	if err != nil {
		logger.Warn(ctx, "Failed to get tenant info by code",
			logiface.Error(err),
			logiface.String("tenant_code", tenantCode))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid tenant code",
			logiface.Int("response_code", int(resp.Code)),
			logiface.String("response_message", resp.Message),
			logiface.String("tenant_code", tenantCode))
		return nil
	}

	return &httpmiddleware.TenantInfo{
		TenantId:   resp.Data.TenantId,
		TenantCode: resp.Data.TenantCode,
		TenantName: resp.Data.TenantName,
	}
}
