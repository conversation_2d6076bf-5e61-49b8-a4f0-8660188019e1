package entity

import (
	"time"

	"ilike-backend/internal/domain/errors"
)

// File 文件实体
type File struct {
	ID               uint64     `json:"id" db:"id"`
	UserID           uint64     `json:"user_id" db:"user_id"`
	Filename         string     `json:"filename" db:"filename"`
	OriginalFilename string     `json:"original_filename" db:"original_filename"`
	FilePath         string     `json:"file_path" db:"file_path"`
	FileSize         uint64     `json:"file_size" db:"file_size"`
	MimeType         string     `json:"mime_type" db:"mime_type"`
	FileType         string     `json:"file_type" db:"file_type"`
	HashValue        string     `json:"hash_value" db:"hash_value"`
	IsPublic         bool       `json:"is_public" db:"is_public"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt        *time.Time `json:"deleted_at" db:"deleted_at"`
}

// ValidateUpload 验证文件上传
func (f *File) ValidateUpload() error {
	if f.UserID == 0 {
		return errors.New(errors.CodeFileUploadFailed, "用户ID不能为空")
	}

	if f.Filename == "" {
		return errors.New(errors.CodeFileUploadFailed, "文件名不能为空")
	}

	if f.OriginalFilename == "" {
		return errors.New(errors.CodeFileUploadFailed, "原始文件名不能为空")
	}

	if f.FileSize == 0 {
		return errors.New(errors.CodeFileUploadFailed, "文件大小不能为0")
	}

	if f.MimeType == "" {
		return errors.New(errors.CodeFileUploadFailed, "MIME类型不能为空")
	}

	return nil
}

// SetPublic 设置文件为公开
func (f *File) SetPublic() {
	f.IsPublic = true
}

// SetPrivate 设置文件为私有
func (f *File) SetPrivate() {
	f.IsPublic = false
}

// IsOwner 检查是否为文件所有者
func (f *File) IsOwner(userID uint64) bool {
	return f.UserID == userID
}

// CanAccess 检查用户是否可以访问文件
func (f *File) CanAccess(userID uint64) bool {
	return f.IsPublic || f.IsOwner(userID)
}
