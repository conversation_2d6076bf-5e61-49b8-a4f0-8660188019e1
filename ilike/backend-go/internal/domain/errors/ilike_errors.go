package errors

import (
	"fmt"
)

// iLike模块错误码定义 - 使用120000-129999范围
// 按照平台规范，每个模块使用10万范围的数字

const (
	// 书签操作相关错误 (120000-120099)
	CodeBookmarkNotFound        = 120000 // 书签不存在
	CodeBookmarkAlreadyExists   = 120001 // 书签已存在
	CodeBookmarkInvalidURL      = 120002 // 无效的URL格式
	CodeBookmarkInvalidTitle    = 120003 // 无效的标题
	CodeBookmarkPermissionDenied = 120004 // 无权限访问书签
	CodeBookmarkAlreadyLiked    = 120005 // 已点赞过此书签
	CodeBookmarkNotLiked        = 120006 // 未点赞此书签
	CodeBookmarkAlreadyCollected = 120007 // 已收藏过此书签
	CodeBookmarkNotCollected    = 120008 // 未收藏此书签

	// 记录管理相关错误 (120020-120099)
	CodeRecordNotFound          = 120020 // 记录不存在
	CodeRecordAlreadyExists     = 120021 // 记录已存在
	CodeRecordInvalidTitle      = 120022 // 无效的记录标题
	CodeRecordInvalidContent    = 120023 // 无效的记录内容
	CodeRecordPermissionDenied  = 120024 // 无权限访问记录
	CodeRecordAlreadyLiked      = 120025 // 已点赞过此记录
	CodeRecordNotLiked          = 120026 // 未点赞此记录
	CodeRecordAlreadyFavorited  = 120027 // 已收藏此记录
	CodeRecordNotFavorited      = 120028 // 未收藏此记录
	CodeRecordInvalidSchema     = 120029 // 无效的记录架构
	CodeRecordValidationFailed  = 120030 // 记录验证失败

	// 内容管理相关错误 (120100-120199)
	CodeContentNotFound      = 120100 // 内容不存在
	CodeContentInvalidFormat = 120101 // 无效的内容格式
	CodeContentTooLarge      = 120102 // 内容过大
	CodeContentEmpty         = 120103 // 内容为空
	CodeContentProcessingFailed = 120104 // 内容处理失败

	// 收藏夹操作相关错误 (120200-120299)
	CodeCollectionNotFound        = 120200 // 收藏夹不存在
	CodeCollectionAlreadyExists   = 120201 // 收藏夹已存在
	CodeCollectionInvalidName     = 120202 // 无效的收藏夹名称
	CodeCollectionInvalidDescription = 120203 // 无效的收藏夹描述
	CodeCollectionPermissionDenied = 120204 // 无权限访问收藏夹
	CodeCollectionFull            = 120205 // 收藏夹已满
	CodeCollectionAlreadyPublic   = 120206 // 收藏夹已公开
	CodeCollectionAlreadyPrivate  = 120207 // 收藏夹已私密

	// 分享操作相关错误 (120300-120399)
	CodeShareNotFound      = 120300 // 分享不存在
	CodeShareExpired       = 120301 // 分享已过期
	CodeShareInvalidToken  = 120302 // 无效的分享令牌
	CodeSharePermissionDenied = 120303 // 无权限分享
	CodeShareAlreadyDisabled = 120304 // 分享已禁用

	// 标签操作相关错误 (120400-120499)
	CodeTagNotFound        = 120400 // 标签不存在
	CodeTagAlreadyExists   = 120401 // 标签已存在
	CodeTagInvalidName     = 120402 // 无效的标签名称
	CodeTagInvalidColor    = 120403 // 无效的标签颜色
	CodeTagTooMany         = 120404 // 标签数量过多
	CodeTagAlreadyAssigned = 120405 // 标签已分配
	CodeTagNotAssigned     = 120406 // 标签未分配

	// 评论相关错误 (120450-120499)
	CodeCommentNotFound          = 120450 // 评论不存在
	CodeCommentInvalidContent    = 120451 // 无效的评论内容
	CodeCommentPermissionDenied  = 120452 // 无权限访问评论
	CodeCommentAlreadyLiked      = 120453 // 已点赞过此评论
	CodeCommentNotLiked          = 120454 // 未点赞此评论
	CodeCommentTooLong           = 120455 // 评论内容过长
	CodeCommentValidationFailed  = 120456 // 评论验证失败

	// 社交功能相关错误 (120500-120599)
	CodeFollowNotFound        = 120500 // 关注关系不存在
	CodeFollowAlreadyExists   = 120501 // 已关注过此用户
	CodeFollowSelf            = 120502 // 不能关注自己
	CodeFollowPermissionDenied = 120503 // 无权限关注
	CodeUserNotFound          = 120504 // 用户不存在
	CodeUserBlocked           = 120505 // 用户已被拉黑

	// 文件相关错误 (120550-120599)
	CodeFileNotFound          = 120550 // 文件不存在
	CodeFileInvalidFormat     = 120551 // 无效的文件格式
	CodeFileTooLarge          = 120552 // 文件过大
	CodeFileUploadFailed      = 120553 // 文件上传失败
	CodeFilePermissionDenied  = 120554 // 无权限访问文件

	// 导入/导出相关错误 (120600-120699)
	CodeImportInvalidFormat = 120600 // 导入格式无效
	CodeImportFileTooLarge  = 120601 // 导入文件过大
	CodeImportProcessingFailed = 120602 // 导入处理失败
	CodeExportFormatNotSupported = 120603 // 导出格式不支持
	CodeExportProcessingFailed = 120604 // 导出处理失败

	// 系统集成相关错误 (120900-120999)
	CodeIntegrationFailed   = 120900 // 系统集成失败
	CodeStorageError        = 120901 // 存储错误
	CodeCacheError          = 120902 // 缓存错误
	CodeRateLimitExceeded   = 120903 // 请求频率超限
	CodeServiceUnavailable  = 120904 // 服务不可用
)

// 错误消息映射
var errorMessages = map[int]string{
	// 书签操作相关错误消息
	CodeBookmarkNotFound:        "书签不存在",
	CodeBookmarkAlreadyExists:   "书签已存在",
	CodeBookmarkInvalidURL:      "无效的URL格式",
	CodeBookmarkInvalidTitle:    "无效的标题",
	CodeBookmarkPermissionDenied: "无权限访问此书签",
	CodeBookmarkAlreadyLiked:    "已点赞过此书签",
	CodeBookmarkNotLiked:        "未点赞此书签",
	CodeBookmarkAlreadyCollected: "已收藏过此书签",
	CodeBookmarkNotCollected:    "未收藏此书签",

	// 记录管理相关错误消息
	CodeRecordNotFound:          "记录不存在",
	CodeRecordAlreadyExists:     "记录已存在",
	CodeRecordInvalidTitle:      "无效的记录标题",
	CodeRecordInvalidContent:    "无效的记录内容",
	CodeRecordPermissionDenied:  "无权限访问此记录",
	CodeRecordAlreadyLiked:      "已点赞过此记录",
	CodeRecordNotLiked:          "未点赞此记录",
	CodeRecordAlreadyFavorited:  "已收藏此记录",
	CodeRecordNotFavorited:      "未收藏此记录",
	CodeRecordInvalidSchema:     "无效的记录架构",
	CodeRecordValidationFailed:  "记录验证失败",

	// 内容管理相关错误消息
	CodeContentNotFound:      "内容不存在",
	CodeContentInvalidFormat: "无效的内容格式",
	CodeContentTooLarge:      "内容过大",
	CodeContentEmpty:         "内容不能为空",
	CodeContentProcessingFailed: "内容处理失败",

	// 收藏夹操作相关错误消息
	CodeCollectionNotFound:        "收藏夹不存在",
	CodeCollectionAlreadyExists:   "收藏夹已存在",
	CodeCollectionInvalidName:     "无效的收藏夹名称",
	CodeCollectionInvalidDescription: "无效的收藏夹描述",
	CodeCollectionPermissionDenied: "无权限访问此收藏夹",
	CodeCollectionFull:            "收藏夹已满",
	CodeCollectionAlreadyPublic:   "收藏夹已公开",
	CodeCollectionAlreadyPrivate:  "收藏夹已私密",

	// 分享操作相关错误消息
	CodeShareNotFound:      "分享不存在",
	CodeShareExpired:       "分享已过期",
	CodeShareInvalidToken:  "无效的分享令牌",
	CodeSharePermissionDenied: "无权限分享",
	CodeShareAlreadyDisabled: "分享已禁用",

	// 标签操作相关错误消息
	CodeTagNotFound:        "标签不存在",
	CodeTagAlreadyExists:   "标签已存在",
	CodeTagInvalidName:     "无效的标签名称",
	CodeTagInvalidColor:    "无效的标签颜色",
	CodeTagTooMany:         "标签数量过多",
	CodeTagAlreadyAssigned: "标签已分配",
	CodeTagNotAssigned:     "标签未分配",

	// 评论相关错误消息
	CodeCommentNotFound:          "评论不存在",
	CodeCommentInvalidContent:    "无效的评论内容",
	CodeCommentPermissionDenied:  "无权限访问此评论",
	CodeCommentAlreadyLiked:      "已点赞过此评论",
	CodeCommentNotLiked:          "未点赞此评论",
	CodeCommentTooLong:           "评论内容过长",
	CodeCommentValidationFailed:  "评论验证失败",

	// 社交功能相关错误消息
	CodeFollowNotFound:        "关注关系不存在",
	CodeFollowAlreadyExists:   "已关注过此用户",
	CodeFollowSelf:            "不能关注自己",
	CodeFollowPermissionDenied: "无权限关注",
	CodeUserNotFound:          "用户不存在",
	CodeUserBlocked:           "用户已被拉黑",

	// 文件相关错误消息
	CodeFileNotFound:          "文件不存在",
	CodeFileInvalidFormat:     "无效的文件格式",
	CodeFileTooLarge:          "文件过大",
	CodeFileUploadFailed:      "文件上传失败",
	CodeFilePermissionDenied:  "无权限访问此文件",

	// 导入/导出相关错误消息
	CodeImportInvalidFormat: "导入格式无效",
	CodeImportFileTooLarge:  "导入文件过大",
	CodeImportProcessingFailed: "导入处理失败",
	CodeExportFormatNotSupported: "导出格式不支持",
	CodeExportProcessingFailed: "导出处理失败",

	// 系统集成相关错误消息
	CodeIntegrationFailed:  "系统集成失败",
	CodeStorageError:       "存储错误",
	CodeCacheError:         "缓存错误",
	CodeRateLimitExceeded:  "请求频率超限，请稍后重试",
	CodeServiceUnavailable: "服务暂时不可用，请稍后重试",
}

// ILikeError iLike模块自定义错误
// 实现了 error 接口，包含错误码和消息
// 与 platforms-pkg/common/response 集成，提供统一的错误处理
type ILikeError struct {
	Code    int    // 业务错误码
	Message string // 用户友好的错误消息
	Details string // 内部详细错误信息（可选）
}

// Error 实现 error 接口
func (e *ILikeError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s", e.Message, e.Details)
	}
	return e.Message
}

// GetCode 获取错误码
func (e *ILikeError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
func (e *ILikeError) GetMessage() string {
	return e.Message
}

// GetDetails 获取详细错误信息
func (e *ILikeError) GetDetails() string {
	return e.Details
}

// New 创建新的iLike模块错误
func New(code int, details ...string) *ILikeError {
	message := errorMessages[code]
	if message == "" {
		message = "操作失败，请稍后重试"
	}

	var detail string
	if len(details) > 0 {
		detail = details[0]
	}

	return &ILikeError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// NewWithDetails 创建带详细信息的iLike模块错误
func NewWithDetails(code int, details string) *ILikeError {
	message := errorMessages[code]
	if message == "" {
		message = "操作失败，请稍后重试"
	}

	return &ILikeError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// GetErrorMessage 获取错误码对应的错误消息
func GetErrorMessage(code int) string {
	if message, exists := errorMessages[code]; exists {
		return message
	}
	return "操作失败，请稍后重试"
}

// IsILikeError 判断是否为iLike模块错误码
func IsILikeError(code int) bool {
	return code >= 120000 && code <= 129999
}

// GetCommonErrorCode 将iLike模块错误码映射到通用错误码
// 用于在handler层统一错误处理
func GetCommonErrorCode(ilikeErrorCode int) int {
	switch ilikeErrorCode {
	// 资源不存在错误 -> 资源不存在
	case CodeBookmarkNotFound, CodeContentNotFound, CodeCollectionNotFound,
		CodeShareNotFound, CodeTagNotFound, CodeFollowNotFound, CodeUserNotFound:
		return 40001 // CodeResourceNotFound

	// 资源已存在错误 -> 资源冲突
	case CodeBookmarkAlreadyExists, CodeCollectionAlreadyExists, CodeTagAlreadyExists,
		CodeFollowAlreadyExists, CodeBookmarkAlreadyLiked, CodeBookmarkAlreadyCollected:
		return 40002 // CodeResourceExists

	// 参数格式错误 -> 参数格式错误
	case CodeBookmarkInvalidURL, CodeBookmarkInvalidTitle, CodeContentInvalidFormat,
		CodeCollectionInvalidName, CodeCollectionInvalidDescription, CodeTagInvalidName,
		CodeTagInvalidColor:
		return 10006 // CodeParamFormatError

	// 权限相关错误 -> 权限不足
	case CodeBookmarkPermissionDenied, CodeCollectionPermissionDenied, CodeSharePermissionDenied,
		CodeFollowPermissionDenied:
		return 20010 // CodePermissionDenied

	// 操作失败 -> 操作失败
	case CodeContentProcessingFailed, CodeImportProcessingFailed, CodeExportProcessingFailed,
		CodeIntegrationFailed, CodeStorageError, CodeCacheError:
		return 10003 // CodeOperationFailed

	// 内容过大 -> 大小限制超限
	case CodeContentTooLarge, CodeImportFileTooLarge, CodeCollectionFull, CodeTagTooMany:
		return 40020 // CodeSizeLimitExceeded

	// 频率限制 -> 速率限制超限
	case CodeRateLimitExceeded:
		return 40018 // CodeRateLimitExceeded

	// 服务不可用 -> 服务不可用
	case CodeServiceUnavailable:
		return 50018 // CodeServiceUnavailable

	// 默认 -> 内部错误
	default:
		return 50001 // CodeInternalError
	}
}

// Bookmark Specific Error Constructors

// NewBookmarkNotFoundError 创建书签不存在错误
func NewBookmarkNotFoundError(bookmarkID interface{}) *ILikeError {
	return NewWithDetails(CodeBookmarkNotFound, fmt.Sprintf("书签ID: %v", bookmarkID))
}

// NewBookmarkAlreadyExistsError 创建书签已存在错误
func NewBookmarkAlreadyExistsError(url string) *ILikeError {
	return NewWithDetails(CodeBookmarkAlreadyExists, fmt.Sprintf("URL: %s", url))
}

// NewInvalidURLError 创建无效URL错误
func NewInvalidURLError(url string) *ILikeError {
	return NewWithDetails(CodeBookmarkInvalidURL, fmt.Sprintf("URL: %s", url))
}

// Collection Specific Error Constructors

// NewCollectionNotFoundError 创建收藏夹不存在错误
func NewCollectionNotFoundError(collectionID interface{}) *ILikeError {
	return NewWithDetails(CodeCollectionNotFound, fmt.Sprintf("收藏夹ID: %v", collectionID))
}

// NewCollectionAlreadyExistsError 创建收藏夹已存在错误
func NewCollectionAlreadyExistsError(name string) *ILikeError {
	return NewWithDetails(CodeCollectionAlreadyExists, fmt.Sprintf("名称: %s", name))
}

// NewCollectionFullError 创建收藏夹已满错误
func NewCollectionFullError(collectionID interface{}, maxSize int) *ILikeError {
	return NewWithDetails(CodeCollectionFull, fmt.Sprintf("收藏夹ID: %v, 最大容量: %d", collectionID, maxSize))
}

// Tag Specific Error Constructors

// NewTagNotFoundError 创建标签不存在错误
func NewTagNotFoundError(tagID interface{}) *ILikeError {
	return NewWithDetails(CodeTagNotFound, fmt.Sprintf("标签ID: %v", tagID))
}

// NewTagAlreadyExistsError 创建标签已存在错误
func NewTagAlreadyExistsError(name string) *ILikeError {
	return NewWithDetails(CodeTagAlreadyExists, fmt.Sprintf("名称: %s", name))
}

// NewTagTooManyError 创建标签过多错误
func NewTagTooManyError(maxTags int) *ILikeError {
	return NewWithDetails(CodeTagTooMany, fmt.Sprintf("最大标签数: %d", maxTags))
}

// Share Specific Error Constructors

// NewShareNotFoundError 创建分享不存在错误
func NewShareNotFoundError(shareID interface{}) *ILikeError {
	return NewWithDetails(CodeShareNotFound, fmt.Sprintf("分享ID: %v", shareID))
}

// NewShareExpiredError 创建分享已过期错误
func NewShareExpiredError(shareID interface{}) *ILikeError {
	return NewWithDetails(CodeShareExpired, fmt.Sprintf("分享ID: %v", shareID))
}

// NewShareInvalidTokenError 创建分享令牌无效错误
func NewShareInvalidTokenError(token string) *ILikeError {
	return NewWithDetails(CodeShareInvalidToken, fmt.Sprintf("令牌: %s", token))
}

// Social Specific Error Constructors

// NewUserNotFoundError 创建用户不存在错误
func NewUserNotFoundError(userID interface{}) *ILikeError {
	return NewWithDetails(CodeUserNotFound, fmt.Sprintf("用户ID: %v", userID))
}

// NewFollowSelfError 创建关注自己错误
func NewFollowSelfError(userID interface{}) *ILikeError {
	return NewWithDetails(CodeFollowSelf, fmt.Sprintf("用户ID: %v", userID))
}

// NewUserBlockedError 创建用户被拉黑错误
func NewUserBlockedError(userID interface{}) *ILikeError {
	return NewWithDetails(CodeUserBlocked, fmt.Sprintf("用户ID: %v", userID))
}

// Content Specific Error Constructors

// NewContentNotFoundError 创建内容不存在错误
func NewContentNotFoundError(contentID interface{}) *ILikeError {
	return NewWithDetails(CodeContentNotFound, fmt.Sprintf("内容ID: %v", contentID))
}

// NewContentTooLargeError 创建内容过大错误
func NewContentTooLargeError(size int64, maxSize int64) *ILikeError {
	return NewWithDetails(CodeContentTooLarge, fmt.Sprintf("当前大小: %d bytes, 最大限制: %d bytes", size, maxSize))
}

// Import/Export Specific Error Constructors

// NewImportInvalidFormatError 创建导入格式无效错误
func NewImportInvalidFormatError(format string) *ILikeError {
	return NewWithDetails(CodeImportInvalidFormat, fmt.Sprintf("格式: %s", format))
}

// NewImportFileTooLargeError 创建导入文件过大错误
func NewImportFileTooLargeError(size int64, maxSize int64) *ILikeError {
	return NewWithDetails(CodeImportFileTooLarge, fmt.Sprintf("文件大小: %d bytes, 最大限制: %d bytes", size, maxSize))
}

// System Specific Error Constructors

// NewRateLimitExceededError 创建频率限制错误
func NewRateLimitExceededError(limit int, window string) *ILikeError {
	return NewWithDetails(CodeRateLimitExceeded, fmt.Sprintf("限制: %d 次/%s", limit, window))
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(reason string) *ILikeError {
	return NewWithDetails(CodeServiceUnavailable, reason)
}

// NewIntegrationFailedError 创建系统集成失败错误
func NewIntegrationFailedError(details string) *ILikeError {
	return NewWithDetails(CodeIntegrationFailed, details)
}

// NewStorageError 创建存储错误
func NewStorageError(details string) *ILikeError {
	return NewWithDetails(CodeStorageError, details)
}

// Record Specific Error Constructors

// NewRecordNotFoundError 创建记录不存在错误
func NewRecordNotFoundError(recordID interface{}) *ILikeError {
	return NewWithDetails(CodeRecordNotFound, fmt.Sprintf("记录ID: %v", recordID))
}

// NewRecordPermissionDeniedError 创建记录权限拒绝错误
func NewRecordPermissionDeniedError(recordID interface{}) *ILikeError {
	return NewWithDetails(CodeRecordPermissionDenied, fmt.Sprintf("记录ID: %v", recordID))
}

// NewRecordAlreadyLikedError 创建记录已点赞错误
func NewRecordAlreadyLikedError(recordID interface{}) *ILikeError {
	return NewWithDetails(CodeRecordAlreadyLiked, fmt.Sprintf("记录ID: %v", recordID))
}

// NewRecordNotLikedError 创建记录未点赞错误
func NewRecordNotLikedError(recordID interface{}) *ILikeError {
	return NewWithDetails(CodeRecordNotLiked, fmt.Sprintf("记录ID: %v", recordID))
}

// NewRecordValidationFailedError 创建记录验证失败错误
func NewRecordValidationFailedError(details string) *ILikeError {
	return NewWithDetails(CodeRecordValidationFailed, details)
}

// Comment Specific Error Constructors

// NewCommentNotFoundError 创建评论不存在错误
func NewCommentNotFoundError(commentID interface{}) *ILikeError {
	return NewWithDetails(CodeCommentNotFound, fmt.Sprintf("评论ID: %v", commentID))
}

// NewCommentPermissionDeniedError 创建评论权限拒绝错误
func NewCommentPermissionDeniedError(commentID interface{}) *ILikeError {
	return NewWithDetails(CodeCommentPermissionDenied, fmt.Sprintf("评论ID: %v", commentID))
}

// NewCommentAlreadyLikedError 创建评论已点赞错误
func NewCommentAlreadyLikedError(commentID interface{}) *ILikeError {
	return NewWithDetails(CodeCommentAlreadyLiked, fmt.Sprintf("评论ID: %v", commentID))
}

// NewCommentNotLikedError 创建评论未点赞错误
func NewCommentNotLikedError(commentID interface{}) *ILikeError {
	return NewWithDetails(CodeCommentNotLiked, fmt.Sprintf("评论ID: %v", commentID))
}

// NewCommentTooLongError 创建评论过长错误
func NewCommentTooLongError(length, maxLength int) *ILikeError {
	return NewWithDetails(CodeCommentTooLong, fmt.Sprintf("当前长度: %d, 最大长度: %d", length, maxLength))
}

// NewCommentValidationFailedError 创建评论验证失败错误
func NewCommentValidationFailedError(details string) *ILikeError {
	return NewWithDetails(CodeCommentValidationFailed, details)
}

// File Specific Error Constructors

// NewFileNotFoundError 创建文件不存在错误
func NewFileNotFoundError(fileID interface{}) *ILikeError {
	return NewWithDetails(CodeFileNotFound, fmt.Sprintf("文件ID: %v", fileID))
}

// NewFilePermissionDeniedError 创建文件权限拒绝错误
func NewFilePermissionDeniedError(fileID interface{}) *ILikeError {
	return NewWithDetails(CodeFilePermissionDenied, fmt.Sprintf("文件ID: %v", fileID))
}

// NewFileTooLargeError 创建文件过大错误
func NewFileTooLargeError(size, maxSize int64) *ILikeError {
	return NewWithDetails(CodeFileTooLarge, fmt.Sprintf("文件大小: %d bytes, 最大限制: %d bytes", size, maxSize))
}

// NewFileUploadFailedError 创建文件上传失败错误
func NewFileUploadFailedError(reason string) *ILikeError {
	return NewWithDetails(CodeFileUploadFailed, reason)
}