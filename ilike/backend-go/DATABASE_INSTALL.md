# iLike 数据库安装指南

## 概述

本项目已移除代码中的数据库schema自动维护功能，改为手动维护数据库结构。所有数据库相关的操作都通过SQL文件进行管理。

## 数据库文件说明

### 主要文件

- `database-schema.sql`: 完整的数据库结构定义文件，包含所有表、索引和初始数据

### 已移除文件

- `database/migration/`: 迁移文件目录（已不再使用）
- `internal/infrastructure/database/mysql.go` 中的 `AutoMigrate()` 和 `CreateIndexes()` 方法

## 数据库安装步骤

### 1. 创建数据库

```sql
CREATE DATABASE ilike_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 执行完整结构安装

```bash
mysql -u username -p ilike_db < database-schema.sql
```

或者在MySQL客户端中：

```sql
USE ilike_db;
SOURCE /path/to/database-schema.sql;
```

### 3. 验证安装

检查表是否创建成功：

```sql
SHOW TABLES;
```

应该看到以下表：
- records (记录表)
- likes (点赞表) 
- tags (标签表)
- record_tags (记录标签关联表)
- files (文件表)
- comments (评论表)
- user_profiles (用户配置表)
- categories (分类表)
- record_types (记录类型表)
- record_wishlist (愿望清单表)
- search_history (搜索历史表)

## 数据库表说明

### 核心业务表

1. **records**: 用户记录的主表
2. **likes**: 点赞功能表，支持多种目标类型
3. **tags**: 标签管理表
4. **record_tags**: 记录与标签的多对多关联表
5. **comments**: 评论系统表，支持回复

### 分类管理表

6. **categories**: 层级分类表
7. **record_types**: 记录类型定义表

### 用户功能表

8. **user_profiles**: 用户个人资料扩展表
9. **record_wishlist**: 用户收藏愿望清单
10. **search_history**: 用户搜索历史记录

### 文件管理表

11. **files**: 文件上传和管理表

## 数据库特性

### 设计特点

1. **统一字符集**: 所有表使用 `utf8mb4` 字符集，支持emoji和特殊字符
2. **软删除**: 支持软删除功能（`deleted_at` 字段）
3. **时间戳**: 自动维护创建和更新时间
4. **索引优化**: 为常用查询字段添加了优化索引
5. **唯一约束**: 防止重复数据的唯一索引

### 安全特性

1. **主键自增**: 所有表使用bigint自增主键
2. **外键概念**: 通过应用层维护数据一致性
3. **字段约束**: 合理的字段长度和类型限制

## 初始数据

数据库文件包含以下初始数据：

### 默认标签 (tags)
- 生活、工作、学习、旅行、美食、运动、阅读、音乐、电影、技术

### 默认分类 (categories)  
- 默认分类、工作、生活、学习、娱乐

### 默认记录类型 (record_types)
- 笔记、日记、任务、想法、收藏、代码、图片、文档

### 示例记录 (records)
- 包含5条示例记录（仅供开发测试使用）

## 生产环境注意事项

### 部署前准备

1. **移除示例数据**: 生产环境部署前，删除或注释掉示例记录的INSERT语句
2. **数据备份**: 确保有完整的数据备份策略
3. **权限控制**: 合理设置数据库用户权限

### 性能优化

1. **索引监控**: 根据实际查询模式调整索引
2. **分区策略**: 对于大数据量表考虑分区
3. **连接池**: 合理配置数据库连接池参数

## 数据维护

### 结构变更

所有数据库结构变更都需要：

1. 修改 `database-schema.sql` 文件
2. 创建增量SQL脚本用于现有环境升级
3. 更新相关的模型文件
4. 进行充分测试

### 版本管理

建议采用版本化的SQL文件管理方式：

- `database-schema-v1.0.sql`
- `database-schema-v1.1.sql`  
- `upgrade-v1.0-to-v1.1.sql`

## 故障排除

### 常见问题

1. **字符集问题**: 确保数据库、表和连接都使用utf8mb4
2. **权限问题**: 确保数据库用户有足够的权限
3. **存储引擎**: 确保使用InnoDB存储引擎

### 检查命令

```sql
-- 检查字符集
SHOW CREATE DATABASE ilike_db;

-- 检查表结构
SHOW CREATE TABLE records;

-- 检查索引
SHOW INDEX FROM records;
```

## 联系支持

如遇到数据库相关问题，请联系技术团队并提供：

1. 错误日志
2. 数据库版本信息
3. 表结构信息
4. 复现步骤