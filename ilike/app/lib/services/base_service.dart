import 'dart:io';
import 'package:flutter/material.dart';
import 'http_client.dart';
import '../config/env_config.dart';

/// 服务基类
/// 提供统一的 HTTP 请求方法，所有服务都应该继承此类
abstract class BaseService {
  /// HTTP 客户端实例
  final HttpClient _httpClient = HttpClient.instance;

  /// 获取服务类型，用于确定使用哪个 API 端点
  ServiceType get serviceType => ServiceType.content;

  /// 获取服务的基础 URL
  String get baseUrl => EnvManager.buildServiceApiUrl(serviceType, '');

  /// 构建完整的 API 端点
  String buildEndpoint(String path) {
    if (path.startsWith('http')) {
      return path; // 已经是完整 URL
    }
    
    // 确保路径以 / 开头
    final normalizedPath = path.startsWith('/') ? path : '/$path';
    return EnvManager.buildServiceApiUrl(serviceType, normalizedPath);
  }

  /// GET 请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.get<T>(
        fullEndpoint,
        queryParameters: queryParameters,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError('GET', fullEndpoint, e);
      rethrow;
    }
  }

  /// POST 请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.post<T>(
        fullEndpoint,
        data: data,
        queryParameters: queryParameters,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError('POST', fullEndpoint, e);
      rethrow;
    }
  }

  /// PUT 请求
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.put<T>(
        fullEndpoint,
        data: data,
        queryParameters: queryParameters,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError('PUT', fullEndpoint, e);
      rethrow;
    }
  }

  /// DELETE 请求
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.delete<T>(
        fullEndpoint,
        queryParameters: queryParameters,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError('DELETE', fullEndpoint, e);
      rethrow;
    }
  }

  /// 文件上传
  Future<ApiResponse<T>> upload<T>(
    String endpoint, {
    required File file,
    String fieldName = 'file',
    Map<String, dynamic>? extraData,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.upload<T>(
        fullEndpoint,
        file: file,
        fieldName: fieldName,
        extraData: extraData,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError('UPLOAD', fullEndpoint, e);
      rethrow;
    }
  }

  /// 通用请求方法
  Future<ApiResponse<T>> request<T>({
    required HttpMethod method,
    required String endpoint,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final fullEndpoint = buildEndpoint(endpoint);
    
    try {
      return await _httpClient.request<T>(
        method: method,
        endpoint: fullEndpoint,
        config: config,
        converter: converter,
      );
    } catch (e) {
      _logError(method.name.toUpperCase(), fullEndpoint, e);
      rethrow;
    }
  }

  /// 处理 API 响应
  /// 如果响应成功，返回数据；否则抛出异常
  T handleResponse<T>(ApiResponse<T> response, {T? defaultValue}) {
    if (response.isSuccess) {
      return response.data ?? defaultValue as T;
    } else {
      throw ApiException(
        code: response.code,
        message: response.message,
        fieldErrors: response.errors,
        requestId: response.requestId,
      );
    }
  }

  /// 处理列表响应
  List<T> handleListResponse<T>(
    ApiResponse<List<dynamic>> response,
    T Function(dynamic) converter, {
    List<T> defaultValue = const [],
  }) {
    if (response.isSuccess) {
      final data = response.data;
      if (data != null) {
        return data.map(converter).toList();
      }
      return defaultValue;
    } else {
      throw ApiException(
        code: response.code,
        message: response.message,
        fieldErrors: response.errors,
        requestId: response.requestId,
      );
    }
  }

  /// 处理分页响应
  Map<String, dynamic> handlePaginatedResponse<T>(
    ApiResponse<Map<String, dynamic>> response,
    T Function(dynamic) converter,
  ) {
    if (response.isSuccess) {
      final data = response.data;
      if (data != null) {
        final items = data['list'] as List<dynamic>? ?? [];
        final convertedItems = items.map(converter).toList();
        
        return {
          'list': convertedItems,
          'total': data['total'] ?? 0,
          'page': data['page'] ?? 1,
          'pageSize': data['pageSize'] ?? 20,
          'hasMore': data['hasMore'] ?? false,
          'nextCursor': data['nextCursor'],
        };
      }
      return {
        'list': <T>[],
        'total': 0,
        'page': 1,
        'pageSize': 20,
        'hasMore': false,
      };
    } else {
      throw ApiException(
        code: response.code,
        message: response.message,
        fieldErrors: response.errors,
        requestId: response.requestId,
      );
    }
  }

  /// 记录错误日志
  void _logError(String method, String endpoint, dynamic error) {
    if (EnvManager.config.enableLogging) {
      debugPrint('❌ $method $endpoint - Error: $error');
    }
  }

  /// 设置认证令牌
  void setAuthToken(String? token) {
    _httpClient.setAuthToken(token);
  }

  /// 清除缓存
  Future<void> clearCache() {
    return _httpClient.clearCache();
  }

  /// 获取 HTTP 客户端实例（用于高级用法）
  HttpClient get httpClient => _httpClient;

  /// 显示错误提示
  void showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示成功提示
  void showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示加载对话框
  Future<T?> showLoading<T>(
    BuildContext context,
    Future<T> Function() future,
  ) async {
    return showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    ).then((_) async {
      try {
        return await future();
      } finally {
        if (context.mounted) {
          Navigator.of(context).pop();
        }
      }
    });
  }

  /// 带加载提示的API调用
  Future<T> callWithLoading<T>(
    BuildContext context,
    Future<T> Function() apiCall, {
    String? errorMessage,
  }) async {
    final result = await showLoading(context, () async {
      try {
        return await apiCall();
      } catch (e) {
        if (context.mounted) {
          showError(context, e is ApiException ? e.message : (errorMessage ?? '请求失败'));
        }
        rethrow;
      }
    });
    
    if (result == null) {
      throw Exception('操作被取消');
    }
    
    return result;
  }
}

/// 用户服务基类
abstract class UserBaseService extends BaseService {
  @override
  ServiceType get serviceType => ServiceType.user;
}

/// 内容服务基类
abstract class ContentBaseService extends BaseService {
  @override
  ServiceType get serviceType => ServiceType.content;
}

/// OSS 服务基类
abstract class OSSBaseService extends BaseService {
  @override
  ServiceType get serviceType => ServiceType.oss;
}

/// 分页请求参数
class PaginationParams {
  final int page;
  final int pageSize;
  final String? sortBy;
  final String? sortOrder;

  const PaginationParams({
    this.page = 1,
    this.pageSize = 20,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toJson() {
    return {
      'page': page,
      'page_size': pageSize,
      if (sortBy != null) 'sort_by': sortBy,
      if (sortOrder != null) 'sort_order': sortOrder,
    };
  }

  PaginationParams copyWith({
    int? page,
    int? pageSize,
    String? sortBy,
    String? sortOrder,
  }) {
    return PaginationParams(
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}

/// 分页响应数据
class PaginatedData<T> {
  final List<T> list;
  final int total;
  final int page;
  final int pageSize;
  final int totalPages;

  PaginatedData({
    required this.list,
    required this.total,
    required this.page,
    required this.pageSize,
  }) : totalPages = (total / pageSize).ceil();

  bool get hasNextPage => page < totalPages;
  bool get hasPreviousPage => page > 1;

  factory PaginatedData.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) converter,
  ) {
    final list = (json['list'] as List?)?.map(converter).toList() ?? <T>[];
    final total = json['total'] as int? ?? 0;
    final page = json['page'] as int? ?? 1;
    final pageSize = json['page_size'] as int? ?? 20;

    return PaginatedData<T>(
      list: list,
      total: total,
      page: page,
      pageSize: pageSize,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'list': list,
      'total': total,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
    };
  }
}

/// 带分页的基础服务类
abstract class BasePaginatedService extends BaseService {
  /// 获取分页数据
  Future<PaginatedData<T>> getPaginated<T>(
    String endpoint, {
    PaginationParams? pagination,
    Map<String, dynamic>? additionalParams,
    T Function(dynamic)? converter,
    RequestConfig? config,
  }) async {
    final params = <String, dynamic>{};
    
    if (pagination != null) {
      params.addAll(pagination.toJson());
    }
    
    if (additionalParams != null) {
      params.addAll(additionalParams);
    }

    final response = await get<PaginatedData<T>>(
      endpoint,
      queryParameters: params,
      config: config,
      converter: (data) => PaginatedData.fromJson(data, converter ?? (item) => item as T),
    );

    return handleResponse(response);
  }

  /// 获取所有数据（自动分页）
  Future<List<T>> getAll<T>(
    String endpoint, {
    Map<String, dynamic>? additionalParams,
    T Function(dynamic)? converter,
    RequestConfig? config,
    int maxPages = 100, // 防止无限循环
  }) async {
    final allData = <T>[];
    int currentPage = 1;
    
    while (currentPage <= maxPages) {
      final pagination = PaginationParams(page: currentPage, pageSize: 100);
      final pageData = await getPaginated<T>(
        endpoint,
        pagination: pagination,
        additionalParams: additionalParams,
        converter: converter,
        config: config,
      );
      
      allData.addAll(pageData.list);
      
      if (!pageData.hasNextPage) {
        break;
      }
      
      currentPage++;
    }
    
    return allData;
  }
} 