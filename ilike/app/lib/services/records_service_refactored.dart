import 'dart:io';
import 'package:flutter/material.dart';
import 'base_service.dart';
import 'http_client.dart';
import 'oss_upload_service.dart';

/// 重构后的记录服务
/// 使用统一的 HTTP 客户端和基类
class RecordsService extends ContentBaseService {
  /// 单例实例
  static RecordsService? _instance;
  static RecordsService get instance => _instance ??= RecordsService._();
  
  RecordsService._();

  /// 初始化OSS服务
  Future<void> initializeOSS() async {
    try {
      await OSSUploadService.instance.initialize(baseUrl, '');
    } catch (e) {
      debugPrint('OSS初始化失败，将使用后端上传: $e');
    }
  }

  /// 获取所有记录
  Future<List<Map<String, dynamic>>> getAllRecords({
    int? cursor,
    int limit = 20,
  }) async {
    try {
      final response = await post<List<dynamic>>(
        '/api/records/list',
        data: {
          if (cursor != null) 'cursor': cursor,
          'limit': limit,
        },
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      if (response.isSuccess) {
        final items = response.data ?? [];
        
        // 处理游标分页响应
        final responseData = response.data as Map<String, dynamic>?;
        if (responseData != null) {
          _lastCursor = responseData['nextCursor'] as int?;
          _hasMoreRecords = responseData['hasMore'] as bool? ?? false;
        }
        
        return List<Map<String, dynamic>>.from(items);
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('获取记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取记录异常: $e');
      rethrow;
    }
  }

  /// 用于存储上次请求的游标
  int? _lastCursor;
  bool _hasMoreRecords = true;

  /// 获取上次请求的游标
  int? get lastCursor => _lastCursor;

  /// 获取更多记录（用于分页加载）
  Future<List<Map<String, dynamic>>> getMoreRecords({int limit = 20}) async {
    if (!_hasMoreRecords || _lastCursor == null) {
      return [];
    }
    return getAllRecords(cursor: _lastCursor, limit: limit);
  }

  /// 检查是否有更多记录
  bool get hasMoreRecords => _hasMoreRecords;

  /// 获取单个记录详情
  Future<Map<String, dynamic>> getRecord(int recordId) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/records/detail',
        data: {'recordId': recordId},
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('获取记录详情失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取记录详情异常: $e');
      rethrow;
    }
  }

  /// 创建新记录
  Future<Map<String, dynamic>> createRecord(
    Map<String, dynamic> record,
    String? imagePath,
  ) async {
    try {
      // 如果有图片，先上传图片
      String? imageUrl;
      if (imagePath != null && imagePath.isNotEmpty) {
        imageUrl = await _uploadImage(imagePath);
        record['imageUrl'] = imageUrl;
      }

      final response = await post<Map<String, dynamic>>(
        '/api/records/create',
        data: record,
      );

      if (response.isSuccess) {
        // 如果响应包含data字段，返回data；否则返回整个响应
        if (response.data != null) {
          return response.data!;
        }
        return <String, dynamic>{};
      } else {
        // 返回包含字段错误的Map
        return {
          'success': false,
          'message': response.message,
          'fieldErrors': response.errors?.map((e) => MapEntry(e.field, e.message)).toList(),
        };
      }
    } on ApiException catch (e) {
      debugPrint('创建记录失败: ${e.message}');
      return {
        'success': false,
        'message': e.message,
        'fieldErrors': e.fieldErrors,
      };
    } catch (e) {
      debugPrint('创建记录异常: $e');
      return {
        'success': false,
        'message': '创建记录失败，请稍后重试',
        'fieldErrors': <String, String>{},
      };
    }
  }

  /// 更新记录
  Future<Map<String, dynamic>> updateRecord(
    int recordId,
    Map<String, dynamic> record,
    String? newImagePath,
  ) async {
    try {
      // 如果有新图片，先上传图片
      if (newImagePath != null && newImagePath.isNotEmpty) {
        final imageUrl = await _uploadImage(newImagePath);
        record['imageUrl'] = imageUrl;
      }

      final response = await put<Map<String, dynamic>>(
        '/api/records/update',
        data: {'recordId': recordId, ...record},
      );

      if (response.isSuccess) {
        return response.data ?? <String, dynamic>{};
      } else {
        return {
          'success': false,
          'message': response.message,
          'fieldErrors': response.errors?.map((e) => MapEntry(e.field, e.message)).toList(),
        };
      }
    } on ApiException catch (e) {
      debugPrint('更新记录失败: ${e.message}');
      return {
        'success': false,
        'message': e.message,
        'fieldErrors': e.fieldErrors,
      };
    } catch (e) {
      debugPrint('更新记录异常: $e');
      return {
        'success': false,
        'message': '更新记录失败，请稍后重试',
        'fieldErrors': <String, String>{},
      };
    }
  }

  /// 删除记录
  Future<Map<String, dynamic>> deleteRecord(int recordId) async {
    try {
      final response = await post<dynamic>(
        '/api/records/delete',
        data: {'recordId': recordId},
      );

      if (response.isSuccess) {
        return {'success': true};
      } else {
        return {
          'success': false,
          'message': response.message,
        };
      }
    } on ApiException catch (e) {
      debugPrint('删除记录失败: ${e.message}');
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      debugPrint('删除记录异常: $e');
      return {
        'success': false,
        'message': '删除记录失败，请稍后重试',
      };
    }
  }

  /// 搜索记录
  Future<List<Map<String, dynamic>>> searchRecords(String keyword) async {
    try {
      final response = await post<List<dynamic>>(
        '/api/records/search',
        data: {'keyword': keyword},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('搜索记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('搜索记录异常: $e');
      rethrow;
    }
  }

  /// 获取最近记录
  Future<List<Map<String, dynamic>>> getRecentRecords({int limit = 10}) async {
    try {
      final response = await post<List<dynamic>>(
        '/api/records/recent',
        data: {'limit': limit},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取最近记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取最近记录异常: $e');
      rethrow;
    }
  }

  /// 收藏记录
  Future<Map<String, dynamic>> favoriteRecord(int recordId) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/records/favorite',
        data: {'recordId': recordId},
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('收藏记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('收藏记录异常: $e');
      rethrow;
    }
  }

  /// 取消收藏记录
  Future<Map<String, dynamic>> unfavoriteRecord(int recordId) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/records/unfavorite',
        data: {'recordId': recordId},
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('取消收藏记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('取消收藏记录异常: $e');
      rethrow;
    }
  }

  /// 上传图片
  Future<String?> _uploadImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('图片文件不存在: $imagePath');
      }

      final response = await upload<Map<String, dynamic>>(
        '/api/upload/image',
        file: file,
        fieldName: 'file',
      );

      if (response.isSuccess) {
        return response.data?['url'] as String?;
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } catch (e) {
      debugPrint('上传图片失败: $e');
      rethrow;
    }
  }

  /// 重置分页状态
  void resetPagination() {
    _lastCursor = null;
    _hasMoreRecords = true;
  }

  /// 获取分页数据（使用新的分页基类）
  Future<PaginatedData<Map<String, dynamic>>> getPaginatedRecords({
    PaginationParams? pagination,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      final params = <String, dynamic>{};
      
      if (pagination != null) {
        params.addAll(pagination.toJson());
      }
      
      if (additionalParams != null) {
        params.addAll(additionalParams);
      }

      final response = await post<Map<String, dynamic>>(
        '/api/records/list',
        data: params,
      );

      if (response.isSuccess) {
        final data = response.data ?? {};
        return PaginatedData.fromJson(
          data,
          (item) => Map<String, dynamic>.from(item),
        );
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('获取分页记录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取分页记录异常: $e');
      rethrow;
    }
  }
} 