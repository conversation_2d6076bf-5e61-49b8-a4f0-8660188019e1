import 'base_service.dart';
import '../config/api_config.dart';
import 'http_client.dart';

/// 用户模型
class User {
  final int id;
  final String name;
  final String email;
  final String? avatar;
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    required this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      avatar: json['avatar'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// 记录模型
class Record {
  final int id;
  final String title;
  final String? description;
  final String? imageUrl;
  final int userId;
  final DateTime createdAt;
  final DateTime updatedAt;

  Record({
    required this.id,
    required this.title,
    this.description,
    this.imageUrl,
    required this.userId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Record.fromJson(Map<String, dynamic> json) {
    return Record(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      userId: json['user_id'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'image_url': imageUrl,
      'user_id': userId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 用户服务示例
class UserService extends BaseService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  /// 获取用户信息（返回完整响应）
  Future<ApiResponse<User>> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        ApiUser.profile,
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }

  /// 获取用户信息（只返回数据）
  Future<User> getUserProfileData() async {
    return handleApiCallData(() async {
      final response = await get<User>(
        ApiUser.profile,
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }

  /// 更新用户信息
  Future<User> updateProfile({
    required String name,
    String? email,
  }) async {
    return handleApiCall(() async {
      final response = await put<User>(
        ApiUser.updateProfile,
        data: {
          'name': name,
          if (email != null) 'email': email,
        },
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '更新用户信息失败');
  }

  /// 上传头像
  Future<String> uploadAvatar(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        ApiUpload.avatar,
        filePath: filePath,
        fieldName: 'avatar',
        converter: (data) => data,
      );
      return response.data!['url'] as String;
    }, errorMessage: '上传头像失败');
  }

  /// 修改密码
  Future<void> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    await handleApiCall(() async {
      final response = await post<void>(
        ApiUser.changePassword,
        data: {
          'old_password': oldPassword,
          'new_password': newPassword,
        },
      );
      return response;
    }, errorMessage: '修改密码失败');
  }
}

/// 记录服务示例
class RecordService extends BasePaginatedService {
  static final RecordService _instance = RecordService._internal();
  factory RecordService() => _instance;
  RecordService._internal();

  /// 获取记录列表（分页）
  Future<PaginatedData<Record>> getRecords({
    PaginationParams? pagination,
    String? search,
    int? categoryId,
  }) async {
    final additionalParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      additionalParams['search'] = search;
    }
    if (categoryId != null) {
      additionalParams['category_id'] = categoryId;
    }

    return getPaginated<Record>(
      ApiRecord.list,
      pagination: pagination,
      additionalParams: additionalParams,
      converter: (data) => Record.fromJson(data),
    );
  }

  /// 获取所有记录（自动分页）
  Future<List<Record>> getAllRecords({
    String? search,
    int? categoryId,
  }) async {
    final additionalParams = <String, dynamic>{};
    if (search != null && search.isNotEmpty) {
      additionalParams['search'] = search;
    }
    if (categoryId != null) {
      additionalParams['category_id'] = categoryId;
    }

    return getAll<Record>(
      ApiRecord.list,
      additionalParams: additionalParams,
      converter: (data) => Record.fromJson(data),
    );
  }

  /// 获取记录详情
  Future<Record> getRecord(int id) async {
    return handleApiCall(() async {
      final response = await get<Record>(
        '${ApiRecord.detail}?id=$id',
        converter: (data) => Record.fromJson(data),
      );
      return response;
    }, errorMessage: '获取记录详情失败');
  }

  /// 创建记录
  Future<Record> createRecord({
    required String title,
    String? description,
    String? imageUrl,
  }) async {
    return handleApiCall(() async {
      final response = await post<Record>(
        ApiRecord.create,
        data: {
          'title': title,
          if (description != null) 'description': description,
          if (imageUrl != null) 'image_url': imageUrl,
        },
        converter: (data) => Record.fromJson(data),
      );
      return response;
    }, errorMessage: '创建记录失败');
  }

  /// 更新记录
  Future<Record> updateRecord({
    required int id,
    String? title,
    String? description,
    String? imageUrl,
  }) async {
    return handleApiCall(() async {
      final response = await put<Record>(
        '${ApiRecord.update}?id=$id',
        data: {
          if (title != null) 'title': title,
          if (description != null) 'description': description,
          if (imageUrl != null) 'image_url': imageUrl,
        },
        converter: (data) => Record.fromJson(data),
      );
      return response;
    }, errorMessage: '更新记录失败');
  }

  /// 删除记录
  Future<void> deleteRecord(int id) async {
    await handleApiCall(() async {
      final response = await delete<void>(
        '${ApiRecord.delete}?id=$id',
      );
      return response;
    }, errorMessage: '删除记录失败');
  }

  /// 搜索记录
  Future<List<Record>> searchRecords(String keyword) async {
    return handleApiCall(() async {
      final response = await get<List<Record>>(
        ApiRecord.search,
        queryParameters: {'keyword': keyword},
        converter: (data) => (data as List).map((item) => Record.fromJson(item)).toList(),
      );
      return response;
    }, errorMessage: '搜索记录失败');
  }

  /// 获取最近记录
  Future<List<Record>> getRecentRecords({int limit = 10}) async {
    return handleApiCall(() async {
      final response = await get<List<Record>>(
        ApiRecord.recent,
        queryParameters: {'limit': limit},
        converter: (data) => (data as List).map((item) => Record.fromJson(item)).toList(),
      );
      return response;
    }, errorMessage: '获取最近记录失败');
  }

  /// 收藏/取消收藏记录
  Future<void> toggleFavorite(int recordId) async {
    return handleApiCall(() async {
      // 这里需要先检查是否已收藏，然后决定调用哪个接口
      // 简化示例，直接调用收藏接口
      final response = await post<void>(
        ApiRecord.favorite,
        data: {'record_id': recordId},
      );
      return response;
    }, errorMessage: '操作失败');
  }
}

/// 文件上传服务示例
class UploadService extends BaseService {
  static final UploadService _instance = UploadService._internal();
  factory UploadService() => _instance;
  UploadService._internal();

  /// 上传图片
  Future<String> uploadImage(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        ApiUpload.image,
        filePath: filePath,
        fieldName: 'image',
        converter: (data) => data['url'] as String,
      );
      return response;
    }, errorMessage: '上传图片失败');
  }

  /// 上传文件
  Future<String> uploadFile(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        ApiUpload.file,
        filePath: filePath,
        fieldName: 'file',
        converter: (data) => data['url'] as String,
      );
      return response;
    }, errorMessage: '上传文件失败');
  }

  /// 上传到OSS
  Future<String> uploadToOSS(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        ApiUpload.oss,
        filePath: filePath,
        fieldName: 'file',
        converter: (data) => data['url'] as String,
      );
      return response;
    }, errorMessage: '上传到OSS失败');
  }
}

/// 服务工厂
class ServiceFactory {
  static final UserService _userService = UserService();
  static final RecordService _recordService = RecordService();
  static final UploadService _uploadService = UploadService();

  static UserService get userService => _userService;
  static RecordService get recordService => _recordService;
  static UploadService get uploadService => _uploadService;

  /// 设置所有服务的认证令牌
  static void setAuthToken(String? token) {
    _userService.setAuthToken(token);
    _recordService.setAuthToken(token);
    _uploadService.setAuthToken(token);
  }

  /// 清除所有缓存
  static Future<void> clearAllCache() async {
    await _userService.httpClient.clearCache();
    await _recordService.httpClient.clearCache();
    await _uploadService.httpClient.clearCache();
  }
} 