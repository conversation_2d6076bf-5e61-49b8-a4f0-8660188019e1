import 'package:flutter/material.dart';
import 'example_service.dart';
import 'http_client.dart';
import 'base_service.dart';

/// HTTP客户端使用示例
class HttpClientUsageExample extends StatefulWidget {
  @override
  _HttpClientUsageExampleState createState() => _HttpClientUsageExampleState();
}

class _HttpClientUsageExampleState extends State<HttpClientUsageExample> {
  final UserService _userService = ServiceFactory.userService;
  final RecordService _recordService = ServiceFactory.recordService;
  final UploadService _uploadService = ServiceFactory.uploadService;

  User? _user;
  List<Record> _records = [];
  bool _loading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
    _loadRecords();
  }

  /// 加载用户信息
  Future<void> _loadUserProfile() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final user = await _userService.getUserProfile();
      setState(() {
        _user = user;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = e is ApiException ? e.message : '加载用户信息失败';
        _loading = false;
      });
    }
  }

  /// 加载记录列表
  Future<void> _loadRecords() async {
    try {
      final pagination = PaginationParams(page: 1, pageSize: 10);
      final recordsData = await _recordService.getRecords(pagination: pagination);
      setState(() {
        _records = recordsData.list;
      });
    } catch (e) {
      print('加载记录失败: $e');
    }
  }

  /// 创建新记录
  Future<void> _createRecord() async {
    try {
      final record = await _recordService.callWithLoading(
        context,
        () => _recordService.createRecord(
          title: '新记录 ${DateTime.now().millisecondsSinceEpoch}',
          description: '这是一个测试记录',
        ),
        errorMessage: '创建记录失败',
      );

      setState(() {
        _records.insert(0, record);
      });

      _recordService.showSuccess(context, '记录创建成功');
    } catch (e) {
      // 错误已经在callWithLoading中处理
    }
  }

  /// 更新用户信息
  Future<void> _updateUserProfile() async {
    if (_user == null) return;

    try {
      final updatedUser = await _userService.callWithLoading(
        context,
        () => _userService.updateProfile(
          name: '${_user!.name} (已更新)',
        ),
        errorMessage: '更新用户信息失败',
      );

      setState(() {
        _user = updatedUser;
      });

      _userService.showSuccess(context, '用户信息更新成功');
    } catch (e) {
      // 错误已经在callWithLoading中处理
    }
  }

  /// 删除记录
  Future<void> _deleteRecord(int recordId) async {
    try {
      await _recordService.callWithLoading(
        context,
        () => _recordService.deleteRecord(recordId),
        errorMessage: '删除记录失败',
      );

      setState(() {
        _records.removeWhere((record) => record.id == recordId);
      });

      _recordService.showSuccess(context, '记录删除成功');
    } catch (e) {
      // 错误已经在callWithLoading中处理
    }
  }

  /// 搜索记录
  Future<void> _searchRecords(String keyword) async {
    if (keyword.isEmpty) {
      _loadRecords();
      return;
    }

    try {
      final searchResults = await _recordService.searchRecords(keyword);
      setState(() {
        _records = searchResults;
      });
    } catch (e) {
      _recordService.showError(context, '搜索失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('HTTP客户端使用示例'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () {
              _loadUserProfile();
              _loadRecords();
            },
          ),
        ],
      ),
      body: _loading
          ? Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _buildContent(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createRecord,
        child: Icon(Icons.add),
        tooltip: '创建记录',
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          SizedBox(height: 16),
          Text(
            _error!,
            style: TextStyle(fontSize: 16, color: Colors.red),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadUserProfile,
            child: Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUserSection(),
          SizedBox(height: 24),
          _buildSearchSection(),
          SizedBox(height: 16),
          _buildRecordsSection(),
        ],
      ),
    );
  }

  Widget _buildUserSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '用户信息',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            if (_user != null) ...[
              ListTile(
                leading: CircleAvatar(
                  backgroundImage: _user!.avatar != null
                      ? NetworkImage(_user!.avatar!)
                      : null,
                  child: _user!.avatar == null
                      ? Text(_user!.name[0].toUpperCase())
                      : null,
                ),
                title: Text(_user!.name),
                subtitle: Text(_user!.email),
                trailing: IconButton(
                  icon: Icon(Icons.edit),
                  onPressed: _updateUserProfile,
                ),
              ),
            ] else ...[
              Text('用户信息加载中...'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '搜索记录',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                hintText: '输入关键词搜索记录...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onSubmitted: _searchRecords,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '记录列表',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                Text('共 ${_records.length} 条记录'),
              ],
            ),
            SizedBox(height: 16),
            if (_records.isEmpty)
              Center(
                child: Text(
                  '暂无记录',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: _records.length,
                itemBuilder: (context, index) {
                  final record = _records[index];
                  return ListTile(
                    leading: record.imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              record.imageUrl!,
                              width: 50,
                              height: 50,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  width: 50,
                                  height: 50,
                                  color: Colors.grey[300],
                                  child: Icon(Icons.image, color: Colors.grey),
                                );
                              },
                            ),
                          )
                        : Container(
                            width: 50,
                            height: 50,
                            color: Colors.grey[300],
                            child: Icon(Icons.article, color: Colors.grey),
                          ),
                    title: Text(record.title),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (record.description != null)
                          Text(
                            record.description!,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        Text(
                          '创建时间: ${_formatDate(record.createdAt)}',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    trailing: PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'delete') {
                          _deleteRecord(record.id);
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('删除'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// 错误处理示例
class ErrorHandlingExample extends StatelessWidget {
  final UserService _userService = ServiceFactory.userService;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('错误处理示例')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () => _handleApiException(context),
              child: Text('触发API异常'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _handleNetworkException(context),
              child: Text('触发网络异常'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _handleFieldErrors(context),
              child: Text('触发字段错误'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleApiException(BuildContext context) async {
    try {
      // 模拟API异常
      await _userService.getUserProfile();
    } on ApiException catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('API异常: ${e.message}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _handleNetworkException(BuildContext context) async {
    try {
      // 模拟网络异常
      await _userService.getUserProfile();
    } on NetworkException catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('网络异常: ${e.message}'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  Future<void> _handleFieldErrors(BuildContext context) async {
    try {
      // 模拟字段错误
      await _userService.updateProfile(name: '');
    } on ApiException catch (e) {
      if (e.fieldErrors != null && e.fieldErrors!.isNotEmpty) {
        final errorMessages = e.fieldErrors!
            .map((error) => '${error.field}: ${error.message}')
            .join('\n');
        
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('字段错误'),
            content: Text(errorMessages),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('确定'),
              ),
            ],
          ),
        );
      }
    }
  }
}

/// 缓存使用示例
class CacheUsageExample extends StatelessWidget {
  final RecordService _recordService = ServiceFactory.recordService;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('缓存使用示例')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: () => _loadWithCache(context),
              child: Text('加载数据（启用缓存）'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadWithoutCache(context),
              child: Text('加载数据（禁用缓存）'),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _clearCache(context),
              child: Text('清除缓存'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadWithCache(BuildContext context) async {
    try {
      final records = await _recordService.getRecords(
        pagination: PaginationParams(page: 1, pageSize: 10),
        config: RequestConfig(
          enableCache: true,
          cacheDuration: Duration(minutes: 5),
        ),
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载成功，共 ${records.list.length} 条记录'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _loadWithoutCache(BuildContext context) async {
    try {
      final records = await _recordService.getRecords(
        pagination: PaginationParams(page: 1, pageSize: 10),
        config: RequestConfig(enableCache: false),
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载成功，共 ${records.list.length} 条记录'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加载失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _clearCache(BuildContext context) async {
    try {
      await ServiceFactory.clearAllCache();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('缓存已清除'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('清除缓存失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
} 