# Flutter HTTP 客户端重构迁移指南

## 概述

本指南将帮助您将现有的 HTTP 请求实现迁移到统一的 `http_client.dart` 架构。

## 重构目标

1. **统一 HTTP 请求实现** - 所有服务都使用 `http_client.dart`
2. **保持 API 兼容性** - 重构后不影响现有业务逻辑
3. **提升代码质量** - 统一错误处理、日志记录、缓存等
4. **简化维护** - 减少重复代码，统一配置管理

## 新的架构

### 1. 服务基类 (`BaseService`)

所有服务都应该继承 `BaseService` 或其子类：

```dart
// 内容服务
class RecordsService extends ContentBaseService {
  // 实现具体方法
}

// 用户服务
class AuthService extends UserBaseService {
  // 实现具体方法
}

// OSS 服务
class OSSService extends OSSBaseService {
  // 实现具体方法
}
```

### 2. 统一的 HTTP 客户端 (`HttpClient`)

- 基于 Dio 实现
- 支持统一响应结构 `ApiResponse<T>`
- 内置缓存、重试、日志、错误处理
- 支持文件上传、认证拦截器等

### 3. 统一响应结构

```dart
class ApiResponse<T> {
  final int code;           // 业务状态码
  final String message;     // 响应消息
  final T? data;           // 响应数据
  final Map<String, dynamic>? meta;  // 元数据信息
  final List<FieldError>? errors;    // 字段错误
  final String? requestId;  // 请求ID
  final int timestamp;      // 时间戳
}
```

## 迁移步骤

### 第一步：继承基类

将现有服务类改为继承 `BaseService`：

```dart
// 之前
class RecordsService {
  final String baseUrl;
  final String token;
  late ApiClient _apiClient;
  
  RecordsService({required this.baseUrl, required this.token}) {
    _apiClient = ApiClient(baseUrl: baseUrl, token: token);
  }
}

// 之后
class RecordsService extends ContentBaseService {
  static RecordsService? _instance;
  static RecordsService get instance => _instance ??= RecordsService._();
  
  RecordsService._();
}
```

### 第二步：替换 HTTP 请求方法

将现有的 HTTP 请求替换为基类方法：

```dart
// 之前
Future<List<Map<String, dynamic>>> getAllRecords() async {
  final response = await _apiClient.post<Map<String, dynamic>>(
    '/api/records/list',
    body,
  );
  return List<Map<String, dynamic>>.from(response['items']);
}

// 之后
Future<List<Map<String, dynamic>>> getAllRecords() async {
  final response = await post<List<dynamic>>(
    '/api/records/list',
    data: body,
    converter: (data) => List<Map<String, dynamic>>.from(data),
  );
  
  return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
}
```

### 第三步：更新错误处理

使用统一的错误处理：

```dart
// 之前
try {
  final response = await _apiClient.post('/api/records/create', record);
  return response;
} on ApiException catch (e) {
  return {
    'success': false,
    'message': e.message,
    'fieldErrors': e.fieldErrors,
  };
}

// 之后
try {
  final response = await post<Map<String, dynamic>>(
    '/api/records/create',
    data: record,
  );
  
  return handleResponse(response, defaultValue: <String, dynamic>{});
} on ApiException catch (e) {
  debugPrint('创建记录失败: ${e.message}');
  rethrow;
}
```

### 第四步：更新文件上传

使用统一的文件上传方法：

```dart
// 之前
final formData = FormData.fromMap({
  'file': await MultipartFile.fromFile(filePath),
  ...extraData,
});
final response = await _apiClient.post('/api/upload/image', formData);

// 之后
final file = File(filePath);
final response = await upload<Map<String, dynamic>>(
  '/api/upload/image',
  file: file,
  fieldName: 'file',
  extraData: extraData,
);
```

## 迁移检查清单

### 基础迁移
- [ ] 继承正确的基类 (`BaseService`, `UserBaseService`, `ContentBaseService`, `OSSBaseService`)
- [ ] 移除旧的 `ApiClient` 依赖
- [ ] 更新构造函数为单例模式
- [ ] 移除 `baseUrl` 和 `token` 参数（由基类管理）

### HTTP 请求迁移
- [ ] 将 `_apiClient.get()` 替换为 `get()`
- [ ] 将 `_apiClient.post()` 替换为 `post()`
- [ ] 将 `_apiClient.put()` 替换为 `put()`
- [ ] 将 `_apiClient.delete()` 替换为 `delete()`
- [ ] 将文件上传替换为 `upload()`

### 响应处理迁移
- [ ] 使用 `handleResponse()` 处理成功响应
- [ ] 使用 `handleListResponse()` 处理列表响应
- [ ] 使用 `handlePaginatedResponse()` 处理分页响应
- [ ] 更新错误处理逻辑

### 配置迁移
- [ ] 移除硬编码的 URL
- [ ] 使用环境配置管理 API 端点
- [ ] 更新认证令牌管理

## 示例迁移

### 完整的服务迁移示例

```dart
// 迁移前
class RecordsService {
  final String baseUrl;
  final String token;
  late ApiClient _apiClient;

  RecordsService({required this.baseUrl, required this.token}) {
    _apiClient = ApiClient(baseUrl: baseUrl, token: token);
  }

  Future<List<Map<String, dynamic>>> getAllRecords() async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/api/records/list',
        {'limit': 20},
      );
      return List<Map<String, dynamic>>.from(response['items']);
    } on ApiException catch (e) {
      debugPrint('获取记录失败: ${e.message}');
      rethrow;
    }
  }
}

// 迁移后
class RecordsService extends ContentBaseService {
  static RecordsService? _instance;
  static RecordsService get instance => _instance ??= RecordsService._();
  
  RecordsService._();

  Future<List<Map<String, dynamic>>> getAllRecords() async {
    try {
      final response = await post<List<dynamic>>(
        '/api/records/list',
        data: {'limit': 20},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );
      
      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取记录失败: ${e.message}');
      rethrow;
    }
  }
}
```

## 优势

### 1. 统一错误处理
- 所有服务使用相同的错误处理逻辑
- 统一的异常类型和错误信息格式
- 自动处理网络错误和业务错误

### 2. 统一日志记录
- 所有 HTTP 请求都有统一的日志格式
- 便于调试和问题排查
- 支持不同环境的日志级别配置

### 3. 统一缓存管理
- 支持请求缓存，提升性能
- 统一的缓存策略和过期时间
- 自动缓存管理

### 4. 统一重试机制
- 自动重试失败的请求
- 可配置的重试次数和延迟
- 智能重试策略

### 5. 统一认证管理
- 统一的 token 管理
- 自动处理认证失败的情况
- 支持 token 刷新

## 注意事项

1. **保持 API 兼容性** - 确保重构后的公共接口保持不变
2. **测试验证** - 重构后必须进行充分的测试
3. **渐进式迁移** - 可以逐个服务进行迁移，不需要一次性完成
4. **文档更新** - 更新相关的文档和注释

## 故障排除

### 常见问题

1. **类型转换错误**
   - 确保使用正确的 `converter` 函数
   - 检查响应数据的实际类型

2. **认证失败**
   - 检查 token 是否正确设置
   - 验证 API 端点配置

3. **缓存问题**
   - 检查缓存键的生成逻辑
   - 验证缓存过期时间设置

### 调试技巧

1. 启用详细日志记录
2. 使用 `debugPrint` 输出关键信息
3. 检查网络请求的详细信息
4. 验证响应数据的结构

## 总结

通过使用统一的 `http_client.dart` 架构，您可以：

- 减少重复代码
- 统一错误处理和日志记录
- 提升代码质量和可维护性
- 简化配置管理
- 增强应用的稳定性和性能

按照本指南进行迁移，您将获得一个更加健壮和可维护的 HTTP 请求架构。 