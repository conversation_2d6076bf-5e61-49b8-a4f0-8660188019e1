# Flutter HTTP 请求重构方案

## 当前状态分析

### 现有的 HTTP 客户端实现
- ✅ `http_client.dart` - 基于 Dio 的统一 HTTP 客户端，功能完善
- ✅ 支持统一响应结构 `ApiResponse<T>`
- ✅ 支持缓存、重试、日志、错误处理
- ✅ 支持文件上传、认证拦截器等

### 需要重构的服务
- ❌ `api_client.dart` - 使用 `http` 包，需要迁移到 `http_client.dart`
- ❌ `records_service.dart` - 使用 `api_client.dart`，需要重构
- ❌ `auth_service.dart` - 需要统一使用 `http_client.dart`
- ❌ 其他服务文件 - 需要统一使用 `http_client.dart`

## 重构目标

1. **统一 HTTP 请求实现** - 所有服务都使用 `http_client.dart`
2. **保持 API 兼容性** - 重构后不影响现有业务逻辑
3. **提升代码质量** - 统一错误处理、日志记录、缓存等
4. **简化维护** - 减少重复代码，统一配置管理

## 重构步骤

### 第一步：创建服务基类
创建 `BaseService` 类，提供通用的 HTTP 请求方法

### 第二步：重构现有服务
将 `records_service.dart`、`auth_service.dart` 等重构为使用 `http_client.dart`

### 第三步：更新配置
统一 API 配置管理，确保所有服务使用相同的配置

### 第四步：测试验证
确保重构后的功能正常工作

## 实施计划

### 1. 创建服务基类
```dart
// base_service.dart
abstract class BaseService {
  final HttpClient _httpClient = HttpClient.instance;
  
  // 通用请求方法
  Future<ApiResponse<T>> get<T>(String endpoint, {Map<String, dynamic>? queryParameters, T Function(dynamic)? converter});
  Future<ApiResponse<T>> post<T>(String endpoint, {dynamic data, Map<String, dynamic>? queryParameters, T Function(dynamic)? converter});
  // ... 其他方法
}
```

### 2. 重构 RecordsService
```dart
// records_service.dart
class RecordsService extends BaseService {
  // 使用统一的 HTTP 客户端
  Future<List<Map<String, dynamic>>> getAllRecords({int? cursor, int limit = 20}) async {
    final response = await post<List<dynamic>>(
      '/api/records/list',
      data: {'cursor': cursor, 'limit': limit},
      converter: (data) => List<Map<String, dynamic>>.from(data),
    );
    
    if (response.isSuccess) {
      return response.data ?? [];
    } else {
      throw ApiException(
        code: response.code,
        message: response.message,
        fieldErrors: response.errors?.map((e) => MapEntry(e.field, e.message)).toMap(),
      );
    }
  }
}
```

### 3. 重构 AuthService
```dart
// auth_service.dart
class AuthService extends BaseService {
  Future<Map<String, dynamic>> login(String username, String password) async {
    final response = await post<Map<String, dynamic>>(
      '/api/user/auth/login',
      data: {'username': username, 'password': password},
    );
    
    if (response.isSuccess) {
      return response.data ?? {};
    } else {
      throw ApiException(
        code: response.code,
        message: response.message,
        fieldErrors: response.errors?.map((e) => MapEntry(e.field, e.message)).toMap(),
      );
    }
  }
}
```

## 优势

### 1. 统一错误处理
- 所有服务使用相同的错误处理逻辑
- 统一的异常类型和错误信息格式

### 2. 统一日志记录
- 所有 HTTP 请求都有统一的日志格式
- 便于调试和问题排查

### 3. 统一缓存管理
- 支持请求缓存，提升性能
- 统一的缓存策略和过期时间

### 4. 统一重试机制
- 自动重试失败的请求
- 可配置的重试次数和延迟

### 5. 统一认证管理
- 统一的 token 管理
- 自动处理认证失败的情况

## 迁移指南

### 迁移步骤
1. 继承 `BaseService` 类
2. 将现有的 HTTP 请求方法替换为基类方法
3. 更新错误处理逻辑
4. 测试验证功能

### 注意事项
1. 保持现有 API 接口不变
2. 确保错误处理逻辑正确
3. 验证所有功能正常工作
4. 更新相关文档

## 测试计划

### 单元测试
- 测试所有服务方法
- 验证错误处理逻辑
- 测试缓存和重试功能

### 集成测试
- 测试与后端 API 的交互
- 验证认证和授权功能
- 测试文件上传功能

### 性能测试
- 测试缓存性能提升
- 验证重试机制的有效性
- 测试并发请求处理 