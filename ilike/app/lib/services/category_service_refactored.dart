import 'package:flutter/material.dart';
import 'base_service.dart';
import 'http_client.dart';
import '../models/category_node.dart';

/// 重构后的分类服务
/// 负责管理记录分类的层级结构
class CategoryService extends ContentBaseService {
  /// 单例实例
  static CategoryService? _instance;
  static CategoryService get instance => _instance ??= CategoryService._();
  
  CategoryService._();

  /// 获取所有分类（树形结构）
  Future<List<CategoryNode>> fetchCategories() async {
    try {
      final response = await get<List<dynamic>>(
        '/api/records/categories',
        converter: (data) => List<CategoryNode>.from(data.map((e) => CategoryNode.fromJson(e))),
      );

      return handleListResponse(response, (item) => CategoryNode.fromJson(item));
    } on ApiException catch (e) {
      debugPrint('获取分类失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取分类异常: $e');
      return [];
    }
  }

  /// 获取分类列表（平铺结构）
  Future<List<Map<String, dynamic>>> getCategoryList({
    bool? enabled,
    int? parentId,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};
      if (enabled != null) {
        queryParameters['enabled'] = enabled;
      }
      if (parentId != null) {
        queryParameters['parentId'] = parentId;
      }

      final response = await get<List<dynamic>>(
        '/api/categories/list',
        queryParameters: queryParameters,
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取分类列表失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取分类列表异常: $e');
      return [];
    }
  }

  /// 获取分类详情
  Future<Map<String, dynamic>?> getCategoryDetail(int categoryId) async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/api/categories/detail',
        queryParameters: {'categoryId': categoryId},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('获取分类详情失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('获取分类详情失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('获取分类详情异常: $e');
      return null;
    }
  }

  /// 创建分类
  Future<Map<String, dynamic>?> createCategory({
    required String name,
    String? description,
    int? parentId,
    String? icon,
    String? color,
    int? sortOrder,
    bool enabled = true,
  }) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/categories/create',
        data: {
          'name': name,
          'description': description,
          'parentId': parentId,
          'icon': icon,
          'color': color,
          'sortOrder': sortOrder,
          'enabled': enabled,
        },
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('创建分类失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('创建分类失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('创建分类异常: $e');
      return null;
    }
  }

  /// 更新分类
  Future<Map<String, dynamic>?> updateCategory({
    required int categoryId,
    String? name,
    String? description,
    int? parentId,
    String? icon,
    String? color,
    int? sortOrder,
    bool? enabled,
  }) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/categories/update',
        data: {
          'categoryId': categoryId,
          'name': name,
          'description': description,
          'parentId': parentId,
          'icon': icon,
          'color': color,
          'sortOrder': sortOrder,
          'enabled': enabled,
        },
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('更新分类失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('更新分类失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('更新分类异常: $e');
      return null;
    }
  }

  /// 删除分类
  Future<bool> deleteCategory(int categoryId) async {
    try {
      final response = await post<dynamic>(
        '/api/categories/delete',
        data: {'categoryId': categoryId},
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('删除分类失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('删除分类异常: $e');
      return false;
    }
  }

  /// 移动分类
  Future<bool> moveCategory(int categoryId, int newParentId) async {
    try {
      final response = await post<dynamic>(
        '/api/categories/move',
        data: {
          'categoryId': categoryId,
          'newParentId': newParentId,
        },
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('移动分类失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('移动分类异常: $e');
      return false;
    }
  }

  /// 启用/禁用分类
  Future<bool> toggleCategory(int categoryId, bool enabled) async {
    try {
      final response = await post<dynamic>(
        '/api/categories/toggle',
        data: {
          'categoryId': categoryId,
          'enabled': enabled,
        },
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('切换分类状态失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('切换分类状态异常: $e');
      return false;
    }
  }

  /// 获取子分类
  Future<List<Map<String, dynamic>>> getSubCategories(int parentId) async {
    try {
      final response = await get<List<dynamic>>(
        '/api/categories/children',
        queryParameters: {'parentId': parentId},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取子分类失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取子分类异常: $e');
      return [];
    }
  }

  /// 获取分类路径
  Future<List<Map<String, dynamic>>> getCategoryPath(int categoryId) async {
    try {
      final response = await get<List<dynamic>>(
        '/api/categories/path',
        queryParameters: {'categoryId': categoryId},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取分类路径失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取分类路径异常: $e');
      return [];
    }
  }

  /// 搜索分类
  Future<List<Map<String, dynamic>>> searchCategories(String keyword) async {
    try {
      final response = await post<List<dynamic>>(
        '/api/categories/search',
        data: {'keyword': keyword},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('搜索分类失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('搜索分类异常: $e');
      return [];
    }
  }

  /// 获取分类统计信息
  Future<Map<String, dynamic>?> getCategoryStats(int categoryId) async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/api/categories/stats',
        queryParameters: {'categoryId': categoryId},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('获取分类统计失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('获取分类统计失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('获取分类统计异常: $e');
      return null;
    }
  }

  /// 批量操作分类
  Future<bool> batchOperation({
    required List<int> categoryIds,
    required String operation,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final response = await post<dynamic>(
        '/api/categories/batch',
        data: {
          'categoryIds': categoryIds,
          'operation': operation,
          'parameters': parameters,
        },
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('批量操作分类失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('批量操作分类异常: $e');
      return false;
    }
  }

  /// 导入分类
  Future<Map<String, dynamic>?> importCategories(List<Map<String, dynamic>> categories) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/categories/import',
        data: {'categories': categories},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('导入分类失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('导入分类失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('导入分类异常: $e');
      return null;
    }
  }

  /// 导出分类
  Future<String?> exportCategories({String format = 'json'}) async {
    try {
      final response = await get<String>(
        '/api/categories/export',
        queryParameters: {'format': format},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('导出分类失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('导出分类失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('导出分类异常: $e');
      return null;
    }
  }
} 