import 'package:flutter/material.dart';
import 'base_service.dart';
import 'http_client.dart';

/// 重构后的 Schema 服务
/// 负责获取和管理动态表单的 Schema 配置
/// 注意：Flutter 端永远操作最新版本的 Schema，版本管理由后端处理
class SchemaService extends ContentBaseService {
  /// 单例实例
  static SchemaService? _instance;
  static SchemaService get instance => _instance ??= SchemaService._();
  
  SchemaService._();

  /// 根据分类ID获取Schema配置（最新版本）
  Future<Map<String, dynamic>?> getSchemaByCategory(
    int categoryId, {
    BuildContext? context,
  }) async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/api/schemas/getByCategory',
        queryParameters: {'categoryId': categoryId},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('获取Schema配置失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('获取Schema配置失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('获取Schema配置异常: $e');
      return null;
    }
  }

  /// 获取所有可用的Schema列表（最新版本）
  Future<List<Map<String, dynamic>>> getAllSchemas({
    bool? enabled,
    BuildContext? context,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};
      if (enabled != null) {
        queryParameters['enabled'] = enabled;
      }

      final response = await get<List<dynamic>>(
        '/api/schemas/list',
        queryParameters: queryParameters,
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取Schema列表失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取Schema列表异常: $e');
      return [];
    }
  }

  /// 创建新的Schema配置
  Future<Map<String, dynamic>?> createSchema({
    required int categoryId,
    required String name,
    String? description,
    required List<Map<String, dynamic>> fields,
    List<Map<String, dynamic>>? groups,
    String? createdBy,
    Map<String, dynamic>? config,
    BuildContext? context,
  }) async {
    try {
      final requestBody = {
        'categoryId': categoryId,
        'name': name,
        'description': description,
        'fields': fields,
        'groups': groups,
        'createdBy': createdBy,
        'config': config,
      };

      final response = await post<Map<String, dynamic>>(
        '/api/schemas/create',
        data: requestBody,
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('创建Schema失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('创建Schema失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('创建Schema异常: $e');
      return null;
    }
  }

  /// 更新Schema配置（更新最新版本）
  Future<Map<String, dynamic>?> updateSchema({
    required int categoryId,
    String? name,
    String? description,
    List<Map<String, dynamic>>? fields,
    List<Map<String, dynamic>>? groups,
    bool? enabled,
    String? createdBy,
    String? changeLog,
    Map<String, dynamic>? config,
    BuildContext? context,
  }) async {
    try {
      final requestBody = {
        'categoryId': categoryId,
        'name': name,
        'description': description,
        'fields': fields,
        'groups': groups,
        'enabled': enabled,
        'createdBy': createdBy,
        'changeLog': changeLog,
        'config': config,
      };

      final response = await post<Map<String, dynamic>>(
        '/api/schemas/update',
        data: requestBody,
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('更新Schema失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('更新Schema失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('更新Schema异常: $e');
      return null;
    }
  }

  /// 删除Schema配置
  Future<bool> deleteSchema(int categoryId, {BuildContext? context}) async {
    try {
      final response = await post<dynamic>(
        '/api/schemas/delete',
        data: {'categoryId': categoryId},
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('删除Schema失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('删除Schema异常: $e');
      return false;
    }
  }

  /// 获取Schema字段配置
  Future<List<Map<String, dynamic>>> getSchemaFields(int categoryId) async {
    try {
      final response = await get<List<dynamic>>(
        '/api/schemas/fields',
        queryParameters: {'categoryId': categoryId},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取Schema字段失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取Schema字段异常: $e');
      return [];
    }
  }

  /// 获取Schema详情
  Future<Map<String, dynamic>?> getSchemaDetail(int categoryId) async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/api/schemas/detail',
        queryParameters: {'categoryId': categoryId},
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('获取Schema详情失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('获取Schema详情失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('获取Schema详情异常: $e');
      return null;
    }
  }

  /// 启用/禁用Schema
  Future<bool> toggleSchema(int categoryId, bool enabled) async {
    try {
      final response = await post<dynamic>(
        '/api/schemas/toggle',
        data: {
          'categoryId': categoryId,
          'enabled': enabled,
        },
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('切换Schema状态失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('切换Schema状态异常: $e');
      return false;
    }
  }

  /// 复制Schema配置
  Future<Map<String, dynamic>?> copySchema({
    required int sourceCategoryId,
    required int targetCategoryId,
    String? newName,
    String? description,
  }) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/schemas/copy',
        data: {
          'sourceCategoryId': sourceCategoryId,
          'targetCategoryId': targetCategoryId,
          'newName': newName,
          'description': description,
        },
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('复制Schema失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('复制Schema失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('复制Schema异常: $e');
      return null;
    }
  }

  /// 获取Schema版本历史
  Future<List<Map<String, dynamic>>> getSchemaHistory(int categoryId) async {
    try {
      final response = await get<List<dynamic>>(
        '/api/schemas/history',
        queryParameters: {'categoryId': categoryId},
        converter: (data) => List<Map<String, dynamic>>.from(data),
      );

      return handleListResponse(response, (item) => Map<String, dynamic>.from(item));
    } on ApiException catch (e) {
      debugPrint('获取Schema历史失败: ${e.message}');
      return [];
    } catch (e) {
      debugPrint('获取Schema历史异常: $e');
      return [];
    }
  }

  /// 回滚到指定版本的Schema
  Future<bool> rollbackSchema(int categoryId, String version) async {
    try {
      final response = await post<dynamic>(
        '/api/schemas/rollback',
        data: {
          'categoryId': categoryId,
          'version': version,
        },
      );

      return response.isSuccess;
    } on ApiException catch (e) {
      debugPrint('回滚Schema失败: ${e.message}');
      return false;
    } catch (e) {
      debugPrint('回滚Schema异常: $e');
      return false;
    }
  }

  /// 获取默认的记录创建Schema（用于测试）
  Map<String, dynamic> getDefaultRecordSchema() {
    return {
      "id": "default-record-schema",
      "name": "默认记录Schema",
      "description": "用于记录创建的默认表单结构",
      "version": "1.0.0",
      "enabled": true,
      "fields": [
        {
          "id": "section_additional",
          "name": "附加信息",
          "type": "TITLE",
          "required": false,
          "order": 1,
          "description": "记录的附加信息",
          "config": {
            "level": 2,
            "style": {
              "fontSize": "18px",
              "fontWeight": "bold",
              "color": "#333333",
              "marginBottom": "16px"
            }
          }
        },
        {
          "id": "brand",
          "name": "品牌",
          "type": "TEXT",
          "required": false,
          "order": 2,
          "description": "产品或服务的品牌",
          "validationRules": {"maxLength": 50}
        },
        {
          "id": "model",
          "name": "型号/规格",
          "type": "TEXT",
          "required": false,
          "order": 3,
          "description": "产品的型号或规格",
          "validationRules": {"maxLength": 100}
        },
        {
          "id": "price",
          "name": "价格",
          "type": "NUMBER",
          "required": false,
          "order": 4,
          "description": "产品或服务的价格",
          "validationRules": {
            "min": 0,
            "max": 999999,
            "precision": 2
          }
        },
        {
          "id": "description",
          "name": "详细描述",
          "type": "TEXTAREA",
          "required": false,
          "order": 5,
          "description": "详细的描述信息",
          "validationRules": {"maxLength": 1000}
        },
        {
          "id": "tags",
          "name": "标签",
          "type": "TAGS",
          "required": false,
          "order": 6,
          "description": "相关标签",
          "config": {
            "maxTags": 10,
            "suggestions": ["重要", "紧急", "待处理", "已完成"]
          }
        },
        {
          "id": "rating",
          "name": "评分",
          "type": "RATING",
          "required": false,
          "order": 7,
          "description": "评分（1-5星）",
          "config": {
            "maxRating": 5,
            "showText": true
          }
        },
        {
          "id": "attachments",
          "name": "附件",
          "type": "FILE_UPLOAD",
          "required": false,
          "order": 8,
          "description": "相关文件附件",
          "config": {
            "maxFiles": 5,
            "maxFileSize": 10485760, // 10MB
            "allowedTypes": ["image/*", "application/pdf", "text/*"]
          }
        }
      ],
      "groups": [
        {
          "id": "basic_info",
          "name": "基本信息",
          "description": "记录的基本信息",
          "fields": ["brand", "model", "price"]
        },
        {
          "id": "details",
          "name": "详细信息",
          "description": "记录的详细信息",
          "fields": ["description", "tags", "rating"]
        },
        {
          "id": "attachments_group",
          "name": "附件",
          "description": "相关附件",
          "fields": ["attachments"]
        }
      ],
      "config": {
        "layout": "vertical",
        "showGroups": true,
        "allowAnonymous": false,
        "autoSave": true,
        "autoSaveInterval": 30000 // 30秒
      }
    };
  }

  /// 验证Schema配置
  Future<Map<String, dynamic>> validateSchema(Map<String, dynamic> schema) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/schemas/validate',
        data: schema,
      );

      if (response.isSuccess) {
        return response.data ?? {};
      } else {
        return {
          'valid': false,
          'errors': response.errors,
          'message': response.message,
        };
      }
    } on ApiException catch (e) {
      return {
        'valid': false,
        'message': e.message,
        'errors': e.fieldErrors,
      };
    } catch (e) {
      return {
        'valid': false,
        'message': '验证Schema时发生异常: $e',
      };
    }
  }

  /// 导出Schema配置
  Future<String?> exportSchema(int categoryId, {String format = 'json'}) async {
    try {
      final response = await get<String>(
        '/api/schemas/export',
        queryParameters: {
          'categoryId': categoryId,
          'format': format,
        },
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('导出Schema失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('导出Schema失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('导出Schema异常: $e');
      return null;
    }
  }

  /// 导入Schema配置
  Future<Map<String, dynamic>?> importSchema({
    required String schemaData,
    required int categoryId,
    String? name,
    String? description,
  }) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/schemas/import',
        data: {
          'schemaData': schemaData,
          'categoryId': categoryId,
          'name': name,
          'description': description,
        },
      );

      if (response.isSuccess) {
        return response.data;
      } else {
        debugPrint('导入Schema失败: ${response.message}');
        return null;
      }
    } on ApiException catch (e) {
      debugPrint('导入Schema失败: ${e.message}');
      return null;
    } catch (e) {
      debugPrint('导入Schema异常: $e');
      return null;
    }
  }
} 