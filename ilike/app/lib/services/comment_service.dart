import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../models/comment.dart';
import '../services/api_client.dart';
import '../utils/logger.dart';

class CommentService {
  final ApiClient _apiClient;
  final Logger _logger = Logger();

  CommentService(this._apiClient);

  /// 创建评论
  Future<Comment> createComment(CommentCreateRequest request) async {
    try {
      _logger.info('Creating comment for record ${request.recordId}');
      
      final response = await _apiClient.post(
        '${ApiConfig.baseUrl}/api/ilike/comments/create',
        body: request.toJson(),
      );

      if (response['code'] == 200) {
        return Comment.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create comment');
      }
    } catch (e) {
      _logger.error('Error creating comment: $e');
      rethrow;
    }
  }

  /// 获取评论详情
  Future<Comment> getComment(int commentId) async {
    try {
      _logger.info('Getting comment $commentId');
      
      final response = await _apiClient.get(
        '${ApiConfig.baseUrl}/api/ilike/comments/detail',
        queryParameters: {'id': commentId.toString()},
      );

      if (response['code'] == 200) {
        return Comment.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get comment');
      }
    } catch (e) {
      _logger.error('Error getting comment: $e');
      rethrow;
    }
  }

  /// 获取记录的评论列表
  Future<CommentListResponse> getCommentsByRecord({
    required int recordId,
    int? parentId,
    int page = 1,
    int size = 20,
  }) async {
    try {
      _logger.info('Getting comments for record $recordId, page $page');
      
      final queryParameters = {
        'record_id': recordId.toString(),
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (parentId != null && parentId > 0) {
        queryParameters['parent_id'] = parentId.toString();
      }

      final response = await _apiClient.get(
        '${ApiConfig.baseUrl}/api/ilike/comments/list',
        queryParameters: queryParameters,
      );

      if (response['code'] == 200) {
        return CommentListResponse.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get comments');
      }
    } catch (e) {
      _logger.error('Error getting comments: $e');
      rethrow;
    }
  }

  /// 更新评论
  Future<Comment> updateComment({
    required int commentId,
    required String content,
  }) async {
    try {
      _logger.info('Updating comment $commentId');
      
      final response = await _apiClient.post(
        '${ApiConfig.baseUrl}/api/ilike/comments/update',
        body: {
          'id': commentId,
          'content': content,
        },
      );

      if (response['code'] == 200) {
        return Comment.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update comment');
      }
    } catch (e) {
      _logger.error('Error updating comment: $e');
      rethrow;
    }
  }

  /// 删除评论
  Future<void> deleteComment(int commentId) async {
    try {
      _logger.info('Deleting comment $commentId');
      
      final response = await _apiClient.post(
        '${ApiConfig.baseUrl}/api/ilike/comments/delete',
        body: {'id': commentId},
      );

      if (response['code'] != 200) {
        throw Exception(response['message'] ?? 'Failed to delete comment');
      }
    } catch (e) {
      _logger.error('Error deleting comment: $e');
      rethrow;
    }
  }

  /// 点赞评论
  Future<void> likeComment(int commentId) async {
    try {
      _logger.info('Liking comment $commentId');
      
      final response = await _apiClient.post(
        '${ApiConfig.baseUrl}/api/ilike/comments/like',
        body: {'comment_id': commentId},
      );

      if (response['code'] != 200) {
        throw Exception(response['message'] ?? 'Failed to like comment');
      }
    } catch (e) {
      _logger.error('Error liking comment: $e');
      rethrow;
    }
  }

  /// 取消点赞评论
  Future<void> unlikeComment(int commentId) async {
    try {
      _logger.info('Unliking comment $commentId');
      
      final response = await _apiClient.post(
        '${ApiConfig.baseUrl}/api/ilike/comments/unlike',
        body: {'comment_id': commentId},
      );

      if (response['code'] != 200) {
        throw Exception(response['message'] ?? 'Failed to unlike comment');
      }
    } catch (e) {
      _logger.error('Error unliking comment: $e');
      rethrow;
    }
  }

  /// 获取用户评论列表
  Future<CommentListResponse> getUserComments({
    required int userId,
    int page = 1,
    int size = 20,
  }) async {
    try {
      _logger.info('Getting comments for user $userId, page $page');
      
      final response = await _apiClient.get(
        '${ApiConfig.baseUrl}/api/ilike/comments/user',
        queryParameters: {
          'user_id': userId.toString(),
          'page': page.toString(),
          'size': size.toString(),
        },
      );

      if (response['code'] == 200) {
        return CommentListResponse.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to get user comments');
      }
    } catch (e) {
      _logger.error('Error getting user comments: $e');
      rethrow;
    }
  }
}