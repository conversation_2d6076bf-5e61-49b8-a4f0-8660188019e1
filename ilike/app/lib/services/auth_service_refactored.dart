import 'package:flutter/material.dart';
import 'base_service.dart';
import 'http_client.dart';

/// 重构后的认证服务
/// 使用统一的 HTTP 客户端和基类
class AuthService extends UserBaseService {
  /// 单例实例
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();

  /// 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/login',
        data: {
          'username': username,
          'password': password,
        },
      );

      if (response.isSuccess) {
        final data = response.data ?? {};
        
        // 如果响应包含 token，设置认证令牌
        final token = data['token'] as String?;
        if (token != null) {
          setAuthToken(token);
        }
        
        return data;
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('登录失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('登录异常: $e');
      rethrow;
    }
  }

  /// 用户注册
  Future<Map<String, dynamic>> register(Map<String, dynamic> userData) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/register',
        data: userData,
      );

      if (response.isSuccess) {
        final data = response.data ?? {};
        
        // 如果响应包含 token，设置认证令牌
        final token = data['token'] as String?;
        if (token != null) {
          setAuthToken(token);
        }
        
        return data;
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('注册失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('注册异常: $e');
      rethrow;
    }
  }

  /// 刷新令牌
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.isSuccess) {
        final data = response.data ?? {};
        
        // 更新认证令牌
        final token = data['token'] as String?;
        if (token != null) {
          setAuthToken(token);
        }
        
        return data;
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('刷新令牌失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('刷新令牌异常: $e');
      rethrow;
    }
  }

  /// 忘记密码
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/forgot-password',
        data: {'email': email},
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('忘记密码失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('忘记密码异常: $e');
      rethrow;
    }
  }

  /// 重置密码
  Future<Map<String, dynamic>> resetPassword(String token, String newPassword) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/reset-password',
        data: {
          'token': token,
          'new_password': newPassword,
        },
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('重置密码失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('重置密码异常: $e');
      rethrow;
    }
  }

  /// 发送 MFA 验证码
  Future<Map<String, dynamic>> sendMFACode(String method) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/send-mfa-code',
        data: {'method': method},
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('发送MFA验证码失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('发送MFA验证码异常: $e');
      rethrow;
    }
  }

  /// 验证 MFA 验证码
  Future<Map<String, dynamic>> verifyMFACode(String code) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/verify-mfa-code',
        data: {'code': code},
      );

      if (response.isSuccess) {
        final data = response.data ?? {};
        
        // 如果验证成功，设置认证令牌
        final token = data['token'] as String?;
        if (token != null) {
          setAuthToken(token);
        }
        
        return data;
      } else {
        throw ApiException(
          code: response.code,
          message: response.message,
          fieldErrors: response.errors,
          requestId: response.requestId,
        );
      }
    } on ApiException catch (e) {
      debugPrint('验证MFA验证码失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('验证MFA验证码异常: $e');
      rethrow;
    }
  }

  /// 用户登出
  Future<Map<String, dynamic>> logout() async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/auth/logout',
      );

      // 清除认证令牌
      setAuthToken(null);

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('登出失败: ${e.message}');
      // 即使登出失败，也要清除本地令牌
      setAuthToken(null);
      rethrow;
    } catch (e) {
      debugPrint('登出异常: $e');
      // 即使登出异常，也要清除本地令牌
      setAuthToken(null);
      rethrow;
    }
  }

  /// 获取用户信息
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/api/user/profile',
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('获取用户信息失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('获取用户信息异常: $e');
      rethrow;
    }
  }

  /// 更新用户信息
  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> profileData) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/profile',
        data: profileData,
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('更新用户信息失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('更新用户信息异常: $e');
      rethrow;
    }
  }

  /// 修改密码
  Future<Map<String, dynamic>> changePassword(String oldPassword, String newPassword) async {
    try {
      final response = await post<Map<String, dynamic>>(
        '/api/user/change-password',
        data: {
          'old_password': oldPassword,
          'new_password': newPassword,
        },
      );

      return handleResponse(response, defaultValue: <String, dynamic>{});
    } on ApiException catch (e) {
      debugPrint('修改密码失败: ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('修改密码异常: $e');
      rethrow;
    }
  }

  /// 检查用户是否已登录
  bool get isLoggedIn {
    // 这里可以根据实际需求检查本地存储的令牌
    return true; // 简化实现
  }

  /// 清除所有认证信息
  void clearAuth() {
    setAuthToken(null);
    clearCache();
  }
} 