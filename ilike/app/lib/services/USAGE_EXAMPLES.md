# Flutter HTTP 客户端重构使用示例

## 概述

本文档提供了重构后的 Flutter HTTP 客户端和服务的使用示例，包括基本用法、错误处理、缓存、重试等高级功能。

## 1. 基本使用

### 1.1 初始化服务工厂

```dart
import 'package:ilike/services/service_factory.dart';

void main() async {
  // 初始化所有服务
  await ServiceFactory.initialize();
  
  runApp(MyApp());
}
```

### 1.2 获取服务实例

```dart
// 通过服务工厂获取实例
final authService = ServiceFactory.authService;
final recordsService = ServiceFactory.recordsService;
final schemaService = ServiceFactory.schemaService;
final categoryService = ServiceFactory.categoryService;
```

## 2. 认证服务使用示例

### 2.1 用户登录

```dart
class LoginWidget extends StatefulWidget {
  @override
  _LoginWidgetState createState() => _LoginWidgetState();
}

class _LoginWidgetState extends State<LoginWidget> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await ServiceFactory.authService.login(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (result['success'] == true) {
        // 登录成功，跳转到主页
        Navigator.pushReplacementNamed(context, '/home');
      } else {
        // 显示错误信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result['message'] ?? '登录失败')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('登录失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('登录')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(labelText: '邮箱'),
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入邮箱';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(labelText: '密码'),
                obscureText: true,
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入密码';
                  return null;
                },
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _login,
                child: _isLoading 
                  ? CircularProgressIndicator() 
                  : Text('登录'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### 2.2 用户注册

```dart
class RegisterWidget extends StatefulWidget {
  @override
  _RegisterWidgetState createState() => _RegisterWidgetState();
}

class _RegisterWidgetState extends State<RegisterWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final result = await ServiceFactory.authService.register(
        name: _nameController.text,
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (result['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('注册成功，请登录')),
        );
        Navigator.pop(context); // 返回登录页面
      } else {
        // 显示字段错误
        if (result['fieldErrors'] != null) {
          final fieldErrors = result['fieldErrors'] as List<MapEntry<String, String>>;
          for (final error in fieldErrors) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('${error.key}: ${error.value}')),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(result['message'] ?? '注册失败')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('注册失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('注册')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(labelText: '姓名'),
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入姓名';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(labelText: '邮箱'),
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入邮箱';
                  if (!value!.contains('@')) return '请输入有效的邮箱';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(labelText: '密码'),
                obscureText: true,
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入密码';
                  if (value!.length < 6) return '密码至少6位';
                  return null;
                },
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _register,
                child: _isLoading 
                  ? CircularProgressIndicator() 
                  : Text('注册'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## 3. 记录服务使用示例

### 3.1 记录列表

```dart
class RecordsListWidget extends StatefulWidget {
  @override
  _RecordsListWidgetState createState() => _RecordsListWidgetState();
}

class _RecordsListWidgetState extends State<RecordsListWidget> {
  List<Map<String, dynamic>> _records = [];
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  Future<void> _loadRecords() async {
    if (_isLoading) return;

    setState(() => _isLoading = true);

    try {
      final records = await ServiceFactory.recordsService.getAllRecords(
        limit: 20,
      );
      
      setState(() {
        _records = records;
        _hasMore = ServiceFactory.recordsService.hasMoreRecords;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载记录失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMoreRecords() async {
    if (_isLoading || !_hasMore) return;

    setState(() => _isLoading = true);

    try {
      final moreRecords = await ServiceFactory.recordsService.getMoreRecords();
      
      setState(() {
        _records.addAll(moreRecords);
        _hasMore = ServiceFactory.recordsService.hasMoreRecords;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载更多记录失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('记录列表'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => Navigator.pushNamed(context, '/records/create'),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ServiceFactory.recordsService.resetPagination();
          await _loadRecords();
        },
        child: ListView.builder(
          itemCount: _records.length + (_hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _records.length) {
              // 加载更多指示器
              if (_isLoading) {
                return Center(child: CircularProgressIndicator());
              } else {
                _loadMoreRecords();
                return SizedBox.shrink();
              }
            }

            final record = _records[index];
            return ListTile(
              title: Text(record['title'] ?? '无标题'),
              subtitle: Text(record['description'] ?? '无描述'),
              leading: record['imageUrl'] != null
                ? Image.network(
                    record['imageUrl'],
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                  )
                : Icon(Icons.image),
              onTap: () => Navigator.pushNamed(
                context, 
                '/records/detail',
                arguments: record['id'],
              ),
            );
          },
        ),
      ),
    );
  }
}
```

### 3.2 创建记录

```dart
class CreateRecordWidget extends StatefulWidget {
  @override
  _CreateRecordWidgetState createState() => _CreateRecordWidgetState();
}

class _CreateRecordWidgetState extends State<CreateRecordWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  String? _selectedImagePath;
  bool _isLoading = false;

  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    
    if (image != null) {
      setState(() => _selectedImagePath = image.path);
    }
  }

  Future<void> _createRecord() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final record = {
        'title': _titleController.text,
        'description': _descriptionController.text,
      };

      final result = await ServiceFactory.recordsService.createRecord(
        record,
        _selectedImagePath,
      );

      if (result['success'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建成功')),
        );
        Navigator.pop(context);
      } else {
        // 显示字段错误
        if (result['fieldErrors'] != null) {
          final fieldErrors = result['fieldErrors'] as List<MapEntry<String, String>>;
          for (final error in fieldErrors) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('${error.key}: ${error.value}')),
            );
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(result['message'] ?? '创建失败')),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('创建记录')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _titleController,
                decoration: InputDecoration(labelText: '标题'),
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入标题';
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(labelText: '描述'),
                maxLines: 3,
                validator: (value) {
                  if (value?.isEmpty ?? true) return '请输入描述';
                  return null;
                },
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: Icon(Icons.image),
                    label: Text('选择图片'),
                  ),
                  SizedBox(width: 16),
                  if (_selectedImagePath != null)
                    Expanded(
                      child: Text(
                        '已选择图片',
                        style: TextStyle(color: Colors.green),
                      ),
                    ),
                ],
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _createRecord,
                child: _isLoading 
                  ? CircularProgressIndicator() 
                  : Text('创建记录'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## 4. 模式服务使用示例

### 4.1 模式列表

```dart
class SchemaListWidget extends StatefulWidget {
  @override
  _SchemaListWidgetState createState() => _SchemaListWidgetState();
}

class _SchemaListWidgetState extends State<SchemaListWidget> {
  List<Map<String, dynamic>> _schemas = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSchemas();
  }

  Future<void> _loadSchemas() async {
    setState(() => _isLoading = true);

    try {
      final schemas = await ServiceFactory.schemaService.getAllSchemas();
      setState(() => _schemas = schemas);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载模式失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('模式列表'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => Navigator.pushNamed(context, '/schemas/create'),
          ),
        ],
      ),
      body: _isLoading
        ? Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadSchemas,
            child: ListView.builder(
              itemCount: _schemas.length,
              itemBuilder: (context, index) {
                final schema = _schemas[index];
                return ListTile(
                  title: Text(schema['name'] ?? '无名称'),
                  subtitle: Text(schema['description'] ?? '无描述'),
                  trailing: Text(schema['version'] ?? 'v1.0'),
                  onTap: () => Navigator.pushNamed(
                    context,
                    '/schemas/detail',
                    arguments: schema['id'],
                  ),
                );
              },
            ),
          ),
    );
  }
}
```

## 5. 分类服务使用示例

### 5.1 分类树

```dart
class CategoryTreeWidget extends StatefulWidget {
  @override
  _CategoryTreeWidgetState createState() => _CategoryTreeWidgetState();
}

class _CategoryTreeWidgetState extends State<CategoryTreeWidget> {
  List<Map<String, dynamic>> _categories = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() => _isLoading = true);

    try {
      final categories = await ServiceFactory.categoryService.getCategoryTree();
      setState(() => _categories = categories);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载分类失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildCategoryItem(Map<String, dynamic> category, int level) {
    final children = category['children'] as List<dynamic>? ?? [];
    
    return Column(
      children: [
        ListTile(
          leading: Icon(Icons.folder),
          title: Text(category['name'] ?? '无名称'),
          subtitle: Text(category['description'] ?? '无描述'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('${children.length}'),
              Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
          onTap: () {
            // 处理分类点击
            Navigator.pushNamed(
              context,
              '/categories/detail',
              arguments: category['id'],
            );
          },
        ),
        if (children.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(left: 16.0 * (level + 1)),
            child: Column(
              children: children.map<Widget>((child) {
                return _buildCategoryItem(
                  Map<String, dynamic>.from(child),
                  level + 1,
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('分类树'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => Navigator.pushNamed(context, '/categories/create'),
          ),
        ],
      ),
      body: _isLoading
        ? Center(child: CircularProgressIndicator())
        : RefreshIndicator(
            onRefresh: _loadCategories,
            child: ListView.builder(
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                return _buildCategoryItem(_categories[index], 0);
              },
            ),
          ),
    );
  }
}
```

## 6. 高级功能使用示例

### 6.1 缓存配置

```dart
// 使用缓存配置
final response = await ServiceFactory.recordsService.getAllRecords(
  limit: 20,
  config: RequestConfig(
    enableCache: true,
    cacheDuration: Duration(minutes: 30),
  ),
);
```

### 6.2 重试配置

```dart
// 自定义重试配置
final response = await ServiceFactory.authService.login(
  email: '<EMAIL>',
  password: 'password',
  config: RequestConfig(
    retryCount: 5,
    retryDelay: Duration(seconds: 2),
  ),
);
```

### 6.3 错误处理

```dart
try {
  final result = await ServiceFactory.recordsService.createRecord(record, imagePath);
  // 处理成功结果
} on ApiException catch (e) {
  // 处理API错误
  print('API错误: ${e.message}');
  print('错误码: ${e.code}');
  print('字段错误: ${e.fieldErrors}');
} on NetworkException catch (e) {
  // 处理网络错误
  print('网络错误: ${e.message}');
  print('状态码: ${e.statusCode}');
} catch (e) {
  // 处理其他错误
  print('未知错误: $e');
}
```

### 6.4 服务状态监控

```dart
// 检查服务状态
final status = ServiceFactory.getServiceStatus();
print('认证服务状态: ${status['auth']}');
print('记录服务状态: ${status['records']}');
print('模式服务状态: ${status['schema']}');
print('分类服务状态: ${status['category']}');

// 清除所有缓存
await ServiceFactory.clearAllCaches();

// 更新认证令牌
ServiceFactory.updateAuthToken('new_token_here');
```

## 7. 最佳实践

### 7.1 错误处理最佳实践

```dart
class ApiErrorHandler {
  static void handleError(BuildContext context, dynamic error) {
    String message = '操作失败';
    
    if (error is ApiException) {
      message = error.message;
      
      // 处理特定错误码
      switch (error.code) {
        case 401:
          // 跳转到登录页面
          Navigator.pushReplacementNamed(context, '/login');
          return;
        case 403:
          message = '权限不足';
          break;
        case 404:
          message = '资源不存在';
          break;
        case 500:
          message = '服务器错误，请稍后重试';
          break;
      }
    } else if (error is NetworkException) {
      message = '网络连接失败，请检查网络设置';
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
```

### 7.2 加载状态管理

```dart
class LoadingState extends ChangeNotifier {
  bool _isLoading = false;
  String? _loadingMessage;

  bool get isLoading => _isLoading;
  String? get loadingMessage => _loadingMessage;

  void startLoading([String? message]) {
    _isLoading = true;
    _loadingMessage = message;
    notifyListeners();
  }

  void stopLoading() {
    _isLoading = false;
    _loadingMessage = null;
    notifyListeners();
  }
}
```

### 7.3 响应式UI更新

```dart
class RecordsProvider extends ChangeNotifier {
  List<Map<String, dynamic>> _records = [];
  bool _isLoading = false;
  String? _error;

  List<Map<String, dynamic>> get records => _records;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadRecords() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _records = await ServiceFactory.recordsService.getAllRecords();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> createRecord(Map<String, dynamic> record, String? imagePath) async {
    try {
      final result = await ServiceFactory.recordsService.createRecord(record, imagePath);
      if (result['success'] == true) {
        await loadRecords(); // 重新加载列表
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }
}
```

## 8. 总结

重构后的 HTTP 客户端提供了以下优势：

1. **统一接口**: 所有服务使用相同的 HTTP 客户端和响应格式
2. **错误处理**: 统一的错误处理机制，支持字段级错误
3. **缓存支持**: 内置缓存机制，提高性能
4. **重试机制**: 自动重试失败的请求
5. **日志记录**: 详细的请求和响应日志
6. **类型安全**: 强类型支持，减少运行时错误
7. **易于测试**: 清晰的接口设计，便于单元测试
8. **可扩展性**: 模块化设计，易于添加新功能

通过使用这些示例，您可以快速上手重构后的 HTTP 客户端，并构建高质量的 Flutter 应用。 