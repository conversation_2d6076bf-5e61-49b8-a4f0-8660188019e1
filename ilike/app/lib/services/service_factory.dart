import 'package:flutter/material.dart';
import 'auth_service_refactored.dart';
import 'records_service_refactored.dart';
import 'schema_service_refactored.dart';
import 'category_service_refactored.dart';
import 'http_client.dart';

/// 服务工厂类
/// 统一管理所有重构后的服务实例
class ServiceFactory {
  static ServiceFactory? _instance;
  static ServiceFactory get instance => _instance ??= ServiceFactory._();
  
  ServiceFactory._();

  /// 初始化所有服务
  /// 在应用启动时调用
  Future<void> initialize() async {
    debugPrint('初始化服务工厂...');
    
    // 初始化 HTTP 客户端
    final httpClient = HttpClient.instance;
    
    // 设置默认请求头
    httpClient.setDefaultHeaders({
      'X-App-Version': '1.0.0',
      'X-Platform': 'flutter',
    });
    
    // 初始化各个服务
    await _initializeServices();
    
    debugPrint('服务工厂初始化完成');
  }

  /// 初始化各个服务
  Future<void> _initializeServices() async {
    try {
      // 初始化记录服务
      await RecordsService.instance.initializeOSS();
      
      debugPrint('所有服务初始化完成');
    } catch (e) {
      debugPrint('服务初始化失败: $e');
    }
  }

  /// 获取认证服务
  AuthService get auth => AuthService.instance;

  /// 获取记录服务
  RecordsService get records => RecordsService.instance;

  /// 获取 Schema 服务
  SchemaService get schema => SchemaService.instance;

  /// 获取分类服务
  CategoryService get category => CategoryService.instance;

  /// 设置认证令牌
  /// 会同时设置到所有服务中
  void setAuthToken(String? token) {
    auth.setAuthToken(token);
    records.setAuthToken(token);
    schema.setAuthToken(token);
    category.setAuthToken(token);
  }

  /// 清除认证信息
  void clearAuth() {
    auth.clearAuth();
    records.setAuthToken(null);
    schema.setAuthToken(null);
    category.setAuthToken(null);
  }

  /// 清除所有缓存
  Future<void> clearAllCache() async {
    await auth.clearCache();
    await records.clearCache();
    await schema.clearCache();
    await category.clearCache();
  }

  /// 获取 HTTP 客户端实例
  HttpClient get httpClient => HttpClient.instance;

  /// 检查网络连接状态
  Future<bool> checkNetworkConnection() async {
    try {
      // 这里可以实现网络连接检查逻辑
      // 例如发送一个简单的 ping 请求
      return true;
    } catch (e) {
      debugPrint('网络连接检查失败: $e');
      return false;
    }
  }

  /// 获取服务状态信息
  Map<String, dynamic> getServiceStatus() {
    return {
      'auth': {
        'initialized': true,
        'hasToken': auth.isLoggedIn,
      },
      'records': {
        'initialized': true,
        'hasMoreRecords': records.hasMoreRecords,
        'lastCursor': records.lastCursor,
      },
      'schema': {
        'initialized': true,
      },
      'category': {
        'initialized': true,
      },
      'httpClient': {
        'initialized': true,
        'baseUrl': httpClient.dio.options.baseUrl,
      },
    };
  }

  /// 重置所有服务状态
  void resetAllServices() {
    records.resetPagination();
    clearAuth();
  }

  /// 销毁服务工厂
  /// 在应用退出时调用
  Future<void> dispose() async {
    debugPrint('销毁服务工厂...');
    
    // 清除认证信息
    clearAuth();
    
    // 清除缓存
    await clearAllCache();
    
    debugPrint('服务工厂销毁完成');
  }
}

/// 服务访问器
/// 提供便捷的服务访问方法
class Services {
  static ServiceFactory get factory => ServiceFactory.instance;
  
  static AuthService get auth => factory.auth;
  static RecordsService get records => factory.records;
  static SchemaService get schema => factory.schema;
  static CategoryService get category => factory.category;
  static HttpClient get http => factory.httpClient;
  
  /// 初始化服务
  static Future<void> initialize() => factory.initialize();
  
  /// 设置认证令牌
  static void setAuthToken(String? token) => factory.setAuthToken(token);
  
  /// 清除认证信息
  static void clearAuth() => factory.clearAuth();
  
  /// 清除所有缓存
  static Future<void> clearAllCache() => factory.clearAllCache();
  
  /// 重置所有服务状态
  static void resetAllServices() => factory.resetAllServices();
  
  /// 销毁服务
  static Future<void> dispose() => factory.dispose();
} 