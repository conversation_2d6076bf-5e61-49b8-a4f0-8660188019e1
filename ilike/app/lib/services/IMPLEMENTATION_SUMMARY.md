# Flutter 统一HTTP请求实现总结

## 实现概述

我已经为Flutter端实现了一个完整的统一HTTP请求系统，**确保返回完整的`ApiResponse<T>`对象**，而不是只返回数据。这样设计可以让业务代码访问分页参数、校验失败信息、请求ID等完整的响应信息。

## 核心文件结构

```
lib/services/
├── http_client.dart          # 统一HTTP客户端
├── base_service.dart         # 基础服务类
├── example_service.dart      # 示例服务类
├── usage_example.dart        # 使用示例
├── README.md                 # 详细文档
├── API_RESPONSE_USAGE_GUIDE.md  # API响应使用指南
└── IMPLEMENTATION_SUMMARY.md    # 本文件
```

## 关键设计决策

### 1. 返回完整ApiResponse（重要）

```dart
// ✅ 正确：返回完整的ApiResponse
Future<ApiResponse<User>> getUserProfile() async {
  return handleApiCall(() async {
    final response = await get<User>(
      '/api/v1/user/profile',
      converter: (data) => User.fromJson(data),
    );
    return response; // 返回完整的ApiResponse
  }, errorMessage: '获取用户信息失败');
}

// 使用方式
final response = await userService.getUserProfile();
if (response.isSuccess) {
  final user = response.data!;
  print('用户: ${user.name}');
  print('请求ID: ${response.requestId}');
  print('分页信息: ${response.meta}');
} else {
  print('业务错误: ${response.message}');
  if (response.hasErrors) {
    // 处理字段错误
    response.errors!.forEach((error) {
      print('${error.field}: ${error.message}');
    });
  }
}
```

### 2. 提供两种方法

```dart
class UserService extends BaseService {
  /// 返回完整响应（推荐）
  Future<ApiResponse<User>> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }

  /// 只返回数据（简化场景）
  Future<User> getUserProfileData() async {
    return handleApiCallData(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }
}
```

## 核心特性

### 1. 统一响应结构

```dart
class ApiResponse<T> {
  final int code;           // 业务状态码
  final String message;     // 响应消息
  final T? data;           // 响应数据
  final Map<String, dynamic>? meta;  // 元数据（分页、请求ID等）
  final List<FieldError>? errors;    // 字段错误
  final String? requestId;  // 请求ID
  final int timestamp;      // 时间戳

  bool get isSuccess => code == 0;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}
```

### 2. 完整的错误处理

```dart
// 分层错误处理
try {
  final response = await userService.getUserProfile();
  
  if (response.isSuccess) {
    // 处理成功响应
    handleSuccess(response.data!);
  } else {
    // 处理业务错误
    handleBusinessError(response);
  }
} on ApiException catch (e) {
  // 处理API异常
  handleApiException(e);
} on NetworkException catch (e) {
  // 处理网络异常
  handleNetworkException(e);
} catch (e) {
  // 处理其他异常
  handleUnknownException(e);
}
```

### 3. 分页数据处理

```dart
class RecordService extends BasePaginatedService {
  Future<ApiResponse<PaginatedData<Record>>> getRecords({
    PaginationParams? pagination,
    String? search,
  }) async {
    return handleApiCall(() async {
      final response = await get<PaginatedData<Record>>(
        '/api/v1/records',
        queryParameters: {
          ...?pagination?.toJson(),
          if (search != null) 'search': search,
        },
        converter: (data) => PaginatedData.fromJson(data, Record.fromJson),
      );
      return response;
    }, errorMessage: '获取记录失败');
  }
}

// 使用分页
final response = await recordService.getRecords(
  pagination: PaginationParams(page: 1, pageSize: 20),
  search: '关键词',
);

if (response.isSuccess) {
  final recordsData = response.data!;
  print('总记录数: ${recordsData.total}');
  print('当前页: ${recordsData.page}');
  print('总页数: ${recordsData.totalPages}');
  print('是否有下一页: ${recordsData.hasNextPage}');
  
  for (final record in recordsData.list) {
    print('记录: ${record.title}');
  }
}
```

### 4. 字段错误处理

```dart
// 处理字段验证错误
if (response.hasErrors) {
  final fieldErrors = <String, String>{};
  for (final error in response.errors!) {
    fieldErrors[error.field] = error.message;
  }
  
  // 显示字段错误
  if (fieldErrors.containsKey('username')) {
    showFieldError('username', fieldErrors['username']!);
  }
  if (fieldErrors.containsKey('email')) {
    showFieldError('email', fieldErrors['email']!);
  }
}
```

## 使用场景示例

### 1. 用户认证

```dart
class AuthService extends BaseService {
  Future<ApiResponse<User>> login({
    required String email,
    required String password,
  }) async {
    return handleApiCall(() async {
      final response = await post<User>(
        '/api/v1/auth/login',
        data: {
          'email': email,
          'password': password,
        },
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '登录失败');
  }
}

// 使用
final response = await authService.login(
  email: '<EMAIL>',
  password: 'password',
);

if (response.isSuccess) {
  final user = response.data!;
  // 保存用户信息
  await saveUserInfo(user);
  // 保存token
  await saveToken(response.meta?['token']);
} else {
  // 显示错误信息
  showError(response.message);
  // 处理字段错误
  if (response.hasErrors) {
    response.errors!.forEach((error) {
      showFieldError(error.field, error.message);
    });
  }
}
```

### 2. 文件上传

```dart
class UploadService extends BaseService {
  Future<ApiResponse<String>> uploadImage(String filePath) async {
    return handleApiCall(() async {
      final response = await upload<Map<String, dynamic>>(
        '/api/v1/upload/image',
        filePath: filePath,
        fieldName: 'image',
        converter: (data) => data,
      );
      return response.data!['url'] as String;
    }, errorMessage: '上传图片失败');
  }
}

// 使用
final response = await uploadService.uploadImage('/path/to/image.jpg');
if (response.isSuccess) {
  final imageUrl = response.data!;
  print('图片上传成功: $imageUrl');
  print('请求ID: ${response.requestId}');
} else {
  print('上传失败: ${response.message}');
}
```

### 3. 列表数据加载

```dart
class ProductService extends BasePaginatedService {
  Future<ApiResponse<PaginatedData<Product>>> getProducts({
    PaginationParams? pagination,
    String? category,
    String? search,
  }) async {
    return handleApiCall(() async {
      final response = await get<PaginatedData<Product>>(
        '/api/v1/products',
        queryParameters: {
          ...?pagination?.toJson(),
          if (category != null) 'category': category,
          if (search != null) 'search': search,
        },
        converter: (data) => PaginatedData.fromJson(data, Product.fromJson),
      );
      return response;
    }, errorMessage: '获取商品列表失败');
  }
}

// 使用
final response = await productService.getProducts(
  pagination: PaginationParams(page: 1, pageSize: 20),
  category: 'electronics',
  search: 'phone',
);

if (response.isSuccess) {
  final productsData = response.data!;
  
  // 更新UI
  setState(() {
    _products = productsData.list;
    _total = productsData.total;
    _currentPage = productsData.page;
    _hasNextPage = productsData.hasNextPage;
  });
  
  // 记录请求信息
  print('请求ID: ${response.requestId}');
  print('响应时间: ${response.timestamp}');
} else {
  // 显示错误
  showError(response.message);
}
```

## 最佳实践

### 1. 服务类设计

```dart
class UserService extends BaseService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  /// 获取用户信息（返回完整响应）
  Future<ApiResponse<User>> getUserProfile() async {
    return handleApiCall(() async {
      final response = await get<User>(
        '/api/v1/user/profile',
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '获取用户信息失败');
  }

  /// 更新用户信息（返回完整响应）
  Future<ApiResponse<User>> updateProfile({
    required String name,
    String? email,
  }) async {
    return handleApiCall(() async {
      final response = await put<User>(
        '/api/v1/user/profile',
        data: {
          'name': name,
          if (email != null) 'email': email,
        },
        converter: (data) => User.fromJson(data),
      );
      return response;
    }, errorMessage: '更新用户信息失败');
  }
}
```

### 2. 错误处理

```dart
Future<void> handleUserOperation() async {
  try {
    final response = await userService.getUserProfile();
    
    if (response.isSuccess) {
      // 处理成功响应
      handleSuccess(response.data!);
    } else {
      // 处理业务错误
      handleBusinessError(response);
    }
  } on ApiException catch (e) {
    // 处理API异常
    handleApiException(e);
  } on NetworkException catch (e) {
    // 处理网络异常
    handleNetworkException(e);
  } catch (e) {
    // 处理其他异常
    handleUnknownException(e);
  }
}
```

### 3. UI集成

```dart
class UserProfileWidget extends StatefulWidget {
  @override
  _UserProfileWidgetState createState() => _UserProfileWidgetState();
}

class _UserProfileWidgetState extends State<UserProfileWidget> {
  User? _user;
  bool _loading = false;
  String? _error;
  Map<String, String> _fieldErrors = {};

  Future<void> _loadUserProfile() async {
    setState(() {
      _loading = true;
      _error = null;
      _fieldErrors.clear();
    });

    try {
      final response = await userService.getUserProfile();
      
      if (response.isSuccess) {
        setState(() {
          _user = response.data!;
          _loading = false;
        });
      } else {
        setState(() {
          _error = response.message;
          _loading = false;
        });
        
        // 处理字段错误
        if (response.hasErrors) {
          final fieldErrors = <String, String>{};
          for (final error in response.errors!) {
            fieldErrors[error.field] = error.message;
          }
          setState(() {
            _fieldErrors = fieldErrors;
          });
        }
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            Text(_error!, style: TextStyle(color: Colors.red)),
            ElevatedButton(
              onPressed: _loadUserProfile,
              child: Text('重试'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // 显示字段错误
        if (_fieldErrors.isNotEmpty) ...[
          Container(
            color: Colors.red[50],
            child: Column(
              children: _fieldErrors.entries.map((entry) {
                return Text(
                  '${entry.key}: ${entry.value}',
                  style: TextStyle(color: Colors.red),
                );
              }).toList(),
            ),
          ),
        ],
        
        // 显示用户信息
        if (_user != null) ...[
          ListTile(
            title: Text(_user!.name),
            subtitle: Text(_user!.email),
          ),
        ],
      ],
    );
  }
}
```

## 总结

这个HTTP客户端实现的关键优势：

1. **返回完整响应** - 确保业务代码可以访问所有响应信息
2. **类型安全** - 泛型支持，编译时类型检查
3. **完整错误处理** - 分层处理不同类型的错误
4. **分页支持** - 内置分页数据处理
5. **字段错误处理** - 支持字段级验证错误
6. **缓存支持** - 本地缓存提升性能
7. **文件上传** - 支持图片和文件上传
8. **拦截器** - 认证、日志、错误、重试拦截器
9. **环境配置** - 多环境支持
10. **UI集成** - 加载提示、错误提示等UI辅助功能

这个实现遵循了Flutter最佳实践，提供了完整的HTTP请求解决方案，可以立即在项目中使用。 