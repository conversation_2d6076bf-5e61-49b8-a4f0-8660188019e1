import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';

import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/env_config.dart';
import '../config/api_config.dart';

/// HTTP请求方法枚举
enum HttpMethod {
  get,
  post,
  put,
  delete,
  patch,
  head,
  options,
}

/// 请求配置
class RequestConfig {
  final Duration? connectTimeout;
  final Duration? receiveTimeout;
  final Duration? sendTimeout;
  final Map<String, dynamic>? headers;
  final Map<String, dynamic>? queryParameters;
  final dynamic data;
  final bool enableCache;
  final Duration? cacheDuration;
  final int? retryCount;
  final Duration? retryDelay;
  final bool enableLogging;

  const RequestConfig({
    this.connectTimeout,
    this.receiveTimeout,
    this.sendTimeout,
    this.headers,
    this.queryParameters,
    this.data,
    this.enableCache = false,
    this.cacheDuration,
    this.retryCount = 3,
    this.retryDelay,
    this.enableLogging = true,
  });

  RequestConfig copyWith({
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParameters,
    dynamic data,
    bool? enableCache,
    Duration? cacheDuration,
    int? retryCount,
    Duration? retryDelay,
    bool? enableLogging,
  }) {
    return RequestConfig(
      connectTimeout: connectTimeout ?? this.connectTimeout,
      receiveTimeout: receiveTimeout ?? this.receiveTimeout,
      sendTimeout: sendTimeout ?? this.sendTimeout,
      headers: headers ?? this.headers,
      queryParameters: queryParameters ?? this.queryParameters,
      data: data ?? this.data,
      enableCache: enableCache ?? this.enableCache,
      cacheDuration: cacheDuration ?? this.cacheDuration,
      retryCount: retryCount ?? this.retryCount,
      retryDelay: retryDelay ?? this.retryDelay,
      enableLogging: enableLogging ?? this.enableLogging,
    );
  }
}

/// 统一响应结构
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;
  final Map<String, dynamic>? meta;
  final List<FieldError>? errors;
  final String? requestId;
  final int timestamp;

  ApiResponse({
    required this.code,
    required this.message,
    this.data,
    this.meta,
    this.errors,
    this.requestId,
    required this.timestamp,
  });

  bool get isSuccess => code == 0;
  bool get hasErrors => errors != null && errors!.isNotEmpty;

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? converter) {
    final data = json['data'];
    T? convertedData;
    if (data != null && converter != null) {
      convertedData = converter(data);
    } else if (data != null) {
      convertedData = data as T;
    }

    List<FieldError>? fieldErrors;
    if (json['errors'] != null) {
      fieldErrors = (json['errors'] as List)
          .map((error) => FieldError.fromJson(error))
          .toList();
    }

    return ApiResponse<T>(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: convertedData,
      meta: json['meta'],
      errors: fieldErrors,
      requestId: json['meta']?['request_id'],
      timestamp: json['meta']?['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'data': data,
      'meta': meta,
      'errors': errors?.map((e) => e.toJson()).toList(),
      'request_id': requestId,
      'timestamp': timestamp,
    };
  }
}

/// 字段错误信息
class FieldError {
  final String field;
  final String message;

  FieldError({required this.field, required this.message});

  factory FieldError.fromJson(Map<String, dynamic> json) {
    return FieldError(
      field: json['field'] ?? '',
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'field': field,
      'message': message,
    };
  }
}

/// API异常类
class ApiException implements Exception {
  final int code;
  final String message;
  final List<FieldError>? fieldErrors;
  final String? requestId;
  final int? statusCode;
  final dynamic originalError;

  ApiException({
    required this.code,
    required this.message,
    this.fieldErrors,
    this.requestId,
    this.statusCode,
    this.originalError,
  });

  @override
  String toString() {
    final errorInfo = <String>[];
    errorInfo.add('Code: $code');
    errorInfo.add('Message: $message');
    if (requestId != null) errorInfo.add('RequestId: $requestId');
    if (statusCode != null) errorInfo.add('Status: $statusCode');
    if (fieldErrors != null && fieldErrors!.isNotEmpty) {
      errorInfo.add('FieldErrors: ${fieldErrors!.map((e) => '${e.field}: ${e.message}').join(', ')}');
    }
    return 'ApiException(${errorInfo.join(', ')})';
  }
}

/// 网络异常类
class NetworkException implements Exception {
  final String message;
  final int? statusCode;
  final String? requestId;
  final dynamic originalError;

  NetworkException({
    required this.message,
    this.statusCode,
    this.requestId,
    this.originalError,
  });

  @override
  String toString() {
    final errorInfo = <String>[];
    errorInfo.add('Message: $message');
    if (requestId != null) errorInfo.add('RequestId: $requestId');
    if (statusCode != null) errorInfo.add('Status: $statusCode');
    return 'NetworkException(${errorInfo.join(', ')})';
  }
}

/// 缓存管理器
class CacheManager {
  static const String _cachePrefix = 'http_cache_';
  static const Duration _defaultCacheDuration = Duration(hours: 1);

  static Future<void> setCache(String key, String data, Duration? duration) {
    return SharedPreferences.getInstance().then((prefs) {
      final cacheData = {
        'data': data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiresAt': DateTime.now()
            .add(duration ?? _defaultCacheDuration)
            .millisecondsSinceEpoch,
      };
      prefs.setString('$_cachePrefix$key', json.encode(cacheData));
    });
  }

  static Future<String?> getCache(String key) async {
    final prefs = await SharedPreferences.getInstance();
    final cacheData = prefs.getString('$_cachePrefix$key');
    if (cacheData == null) return null;

    try {
      final data = json.decode(cacheData) as Map<String, dynamic>;
             final expiresAt = data['expiresAt'] as int;
       
       if (DateTime.now().millisecondsSinceEpoch > expiresAt) {
        await prefs.remove('$_cachePrefix$key');
        return null;
      }
      
      return data['data'] as String;
    } catch (e) {
      await prefs.remove('$_cachePrefix$key');
      return null;
    }
  }

  static Future<void> clearCache() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith(_cachePrefix)) {
        await prefs.remove(key);
      }
    }
  }

  static Future<void> removeCache(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('$_cachePrefix$key');
  }
}

/// 统一HTTP客户端
class HttpClient {
  static HttpClient? _instance;
  static HttpClient get instance => _instance ??= HttpClient._();

  late final Dio _dio;
  late final Logger _logger;
  String? _authToken;
  final Map<String, dynamic> _defaultHeaders = {};

  HttpClient._() {
    _initDio();
    _initLogger();
  }

  /// 初始化Dio实例
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: EnvManager.apiBaseUrl,
      connectTimeout: ApiConfig.connectTimeout,
      receiveTimeout: ApiConfig.receiveTimeout,
      sendTimeout: ApiConfig.sendTimeout,
      headers: ApiConfig.getHeaders(),
      contentType: Headers.jsonContentType,
      responseType: ResponseType.json,
    ));

    // 添加拦截器
    _dio.interceptors.addAll([
      _AuthInterceptor(),
      _LoggingInterceptor(),
      _ErrorInterceptor(),
      _RetryInterceptor(),
    ]);
  }

  /// 初始化日志器
  void _initLogger() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
      level: EnvManager.config.enableLogging ? Level.debug : Level.off,
    );
  }

  /// 设置认证令牌
  void setAuthToken(String? token) {
    _authToken = token;
    if (token != null) {
      _dio.options.headers['Authorization'] = 'Bearer $token';
    } else {
      _dio.options.headers.remove('Authorization');
    }
  }

  /// 设置默认请求头
  void setDefaultHeaders(Map<String, dynamic> headers) {
    _defaultHeaders.addAll(headers);
    _dio.options.headers.addAll(headers);
  }

  /// 清除默认请求头
  void clearDefaultHeaders() {
    _defaultHeaders.clear();
    _dio.options.headers.clear();
    _dio.options.headers.addAll(ApiConfig.getHeaders());
    if (_authToken != null) {
      _dio.options.headers['Authorization'] = 'Bearer $_authToken';
    }
  }

  /// 发送HTTP请求
  Future<ApiResponse<T>> request<T>({
    required HttpMethod method,
    required String endpoint,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final requestConfig = config ?? const RequestConfig();
    final cacheKey = _generateCacheKey(method, endpoint, requestConfig);

    // 检查缓存
    if (requestConfig.enableCache) {
      final cachedData = await CacheManager.getCache(cacheKey);
      if (cachedData != null) {
        try {
          final jsonData = json.decode(cachedData);
          return ApiResponse.fromJson(jsonData, converter);
        } catch (e) {
          await CacheManager.removeCache(cacheKey);
        }
      }
    }

    // 构建请求选项
    final options = Options(
      method: method.name.toUpperCase(),
      headers: requestConfig.headers,
      sendTimeout: requestConfig.sendTimeout,
      receiveTimeout: requestConfig.receiveTimeout,
    );

    // 构建请求数据
    final data = requestConfig.data;
    final queryParameters = requestConfig.queryParameters;

    try {
      final response = await _dio.request(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );

      final apiResponse = ApiResponse.fromJson(response.data, converter);

      // 缓存响应
      if (requestConfig.enableCache && apiResponse.isSuccess) {
        await CacheManager.setCache(
          cacheKey,
          json.encode(apiResponse.toJson()),
          requestConfig.cacheDuration,
        );
      }

      return apiResponse;
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw NetworkException(
        message: '请求失败: $e',
        originalError: e,
      );
    }
  }

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) {
    return request<T>(
      method: HttpMethod.get,
      endpoint: endpoint,
      config: config?.copyWith(queryParameters: queryParameters),
      converter: converter,
    );
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) {
    return request<T>(
      method: HttpMethod.post,
      endpoint: endpoint,
      config: config?.copyWith(data: data, queryParameters: queryParameters),
      converter: converter,
    );
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) {
    return request<T>(
      method: HttpMethod.put,
      endpoint: endpoint,
      config: config?.copyWith(data: data, queryParameters: queryParameters),
      converter: converter,
    );
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) {
    return request<T>(
      method: HttpMethod.delete,
      endpoint: endpoint,
      config: config?.copyWith(queryParameters: queryParameters),
      converter: converter,
    );
  }

  /// 文件上传
  Future<ApiResponse<T>> upload<T>(
    String endpoint, {
    required File file,
    String fieldName = 'file',
    Map<String, dynamic>? extraData,
    RequestConfig? config,
    T Function(dynamic)? converter,
  }) async {
    final formData = FormData.fromMap({
      fieldName: await MultipartFile.fromFile(file.path),
      ...?extraData,
    });

    return request<T>(
      method: HttpMethod.post,
      endpoint: endpoint,
      config: config?.copyWith(
        data: formData,
        headers: {
          ...(config?.headers ?? {}),
          'Content-Type': 'multipart/form-data',
        },
      ),
      converter: converter,
    );
  }

  /// 生成缓存键
  String _generateCacheKey(HttpMethod method, String endpoint, RequestConfig config) {
    final components = [
      method.name,
      endpoint,
      if (config.queryParameters != null) json.encode(config.queryParameters),
      if (config.data != null) json.encode(config.data),
    ];
    return components.join('_');
  }

  /// 处理Dio错误
  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return NetworkException(
          message: '请求超时',
          statusCode: e.response?.statusCode,
          originalError: e,
        );
      case DioExceptionType.badResponse:
        return _handleResponseError(e);
      case DioExceptionType.cancel:
        return NetworkException(
          message: '请求已取消',
          originalError: e,
        );
      case DioExceptionType.connectionError:
        return NetworkException(
          message: '网络连接错误',
          originalError: e,
        );
      default:
        return NetworkException(
          message: '网络请求失败',
          originalError: e,
        );
    }
  }

  /// 处理响应错误
  Exception _handleResponseError(DioException e) {
    final statusCode = e.response?.statusCode;
    final responseData = e.response?.data;

    if (responseData is Map<String, dynamic> && responseData.containsKey('code')) {
      // 统一响应格式的错误
      final code = responseData['code'] as int;
      final message = responseData['message'] as String? ?? '请求失败';
      final requestId = responseData['meta']?['request_id'];

      List<FieldError>? fieldErrors;
      if (responseData['errors'] != null) {
        fieldErrors = (responseData['errors'] as List)
            .map((error) => FieldError.fromJson(error))
            .toList();
      }

      return ApiException(
        code: code,
        message: message,
        fieldErrors: fieldErrors,
        requestId: requestId,
        statusCode: statusCode,
        originalError: e,
      );
    } else {
      // HTTP状态码错误
      String message = '请求失败';
      if (statusCode == 401) {
        message = '未授权，请重新登录';
      } else if (statusCode == 403) {
        message = '权限不足';
      } else if (statusCode == 404) {
        message = '请求的资源不存在';
      } else if (statusCode == 500) {
        message = '服务器内部错误';
      } else if (statusCode != null) {
        message = 'HTTP错误: $statusCode';
      }

      return NetworkException(
        message: message,
        statusCode: statusCode,
        originalError: e,
      );
    }
  }

  /// 清除缓存
  Future<void> clearCache() => CacheManager.clearCache();

  /// 获取Dio实例（用于高级用法）
  Dio get dio => _dio;

  /// 获取日志器
  Logger get logger => _logger;
}

/// 认证拦截器
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 可以在这里添加认证逻辑
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 处理认证错误
    if (err.response?.statusCode == 401) {
      // 可以在这里处理token过期逻辑
    }
    handler.next(err);
  }
}

/// 日志拦截器
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (EnvManager.config.enableLogging) {
      final logger = Logger();
      logger.d('🚀 HTTP_REQUEST: ${options.method} ${options.uri.toString()}');
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (EnvManager.config.enableLogging) {
      final logger = Logger();
      logger.d('✅ HTTP_RESPONSE: ${response.statusCode} ${response.requestOptions.uri.toString()}');
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (EnvManager.config.enableLogging) {
      final logger = Logger();
      logger.e('❌ HTTP_ERROR: ${err.message} ${err.requestOptions.uri.toString()}');
    }
    handler.next(err);
  }
}

/// 错误拦截器
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 统一错误处理逻辑
    handler.next(err);
  }
}

/// 重试拦截器
class _RetryInterceptor extends Interceptor {
  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    final requestOptions = err.requestOptions;
    final retryCount = requestOptions.extra['retryCount'] ?? 3;
    final currentRetry = requestOptions.extra['currentRetry'] ?? 0;

    if (currentRetry < retryCount && _shouldRetry(err)) {
      final newOptions = requestOptions.copyWith(
        extra: {
          ...requestOptions.extra,
          'currentRetry': currentRetry + 1,
        },
      );

      // 延迟重试
      await Future.delayed(Duration(milliseconds: 1000 * ((currentRetry as int) + 1)));

      try {
        final response = await Dio().fetch(newOptions);
        handler.resolve(response);
        return;
      } catch (e) {
        handler.next(err);
        return;
      }
    }

    handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    return err.type == DioExceptionType.connectionTimeout ||
           err.type == DioExceptionType.sendTimeout ||
           err.type == DioExceptionType.receiveTimeout ||
           err.type == DioExceptionType.connectionError ||
           (err.type == DioExceptionType.badResponse && 
            err.response?.statusCode != null &&
            err.response!.statusCode! >= 500);
  }
} 