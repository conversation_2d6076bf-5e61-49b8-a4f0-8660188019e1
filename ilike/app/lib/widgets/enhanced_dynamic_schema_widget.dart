import 'package:flutter/material.dart';
import '../dynamic_form/dynamic_form_factory.dart';
import '../../models/schema.dart';
import '../../utils/logger.dart';

/// 增强的动态Schema组件
/// 支持条件显示、联动验证、自定义布局等高级功能
class EnhancedDynamicSchemaWidget extends StatefulWidget {
  /// Schema定义
  final SchemaModel schema;
  
  /// 初始数据
  final Map<String, dynamic>? initialData;
  
  /// 表单变更回调
  final Function(Map<String, dynamic>)? onChanged;
  
  /// 字段验证错误回调
  final Function(Map<String, String>)? onValidationErrors;
  
  /// 是否启用实时验证
  final bool enableRealtimeValidation;
  
  /// 自定义字段渲染器
  final Map<String, Widget Function(Map<String, dynamic>, dynamic, Function(String, dynamic))>? customRenderers;
  
  /// 布局模式 (列表/网格/分组)
  final SchemaLayoutMode layoutMode;
  
  /// 网格列数（仅在网格模式下有效）
  final int gridColumns;

  const EnhancedDynamicSchemaWidget({
    Key? key,
    required this.schema,
    this.initialData,
    this.onChanged,
    this.onValidationErrors,
    this.enableRealtimeValidation = true,
    this.customRenderers,
    this.layoutMode = SchemaLayoutMode.list,
    this.gridColumns = 2,
  }) : super(key: key);

  @override
  State<EnhancedDynamicSchemaWidget> createState() => _EnhancedDynamicSchemaWidgetState();
}

enum SchemaLayoutMode {
  list,
  grid,
  grouped,
  tabs,
}

class _EnhancedDynamicSchemaWidgetState extends State<EnhancedDynamicSchemaWidget>
    with TickerProviderStateMixin {
  final Logger _logger = Logger();
  late Map<String, dynamic> _formData;
  final Map<String, String> _validationErrors = {};
  final Map<String, bool> _fieldVisibility = {};
  late TabController? _tabController;
  
  @override
  void initState() {
    super.initState();
    _initializeFormData();
    _initializeFieldVisibility();
    _initializeTabController();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  void _initializeFormData() {
    _formData = widget.initialData?.map((key, value) => MapEntry(key, value)) ?? {};
    
    // 设置默认值
    for (final field in widget.schema.fields) {
      final fieldId = field['id'] as String;
      if (!_formData.containsKey(fieldId)) {
        final defaultValue = field['config']?['defaultValue'];
        if (defaultValue != null) {
          _formData[fieldId] = defaultValue;
        }
      }
    }
  }

  void _initializeFieldVisibility() {
    for (final field in widget.schema.fields) {
      final fieldId = field['id'] as String;
      _fieldVisibility[fieldId] = _isFieldVisible(field);
    }
  }

  void _initializeTabController() {
    if (widget.layoutMode == SchemaLayoutMode.tabs) {
      final groups = _getFieldGroups();
      _tabController = TabController(length: groups.length, vsync: this);
    }
  }

  bool _isFieldVisible(Map<String, dynamic> field) {
    final conditions = field['conditions'];
    if (conditions == null) return true;

    try {
      return _evaluateConditions(conditions);
    } catch (e) {
      _logger.error('Error evaluating field visibility conditions: $e');
      return true; // 默认显示
    }
  }

  bool _evaluateConditions(dynamic conditions) {
    if (conditions is Map<String, dynamic>) {
      final operator = conditions['operator'] ?? 'AND';
      final rules = conditions['rules'] as List<dynamic>? ?? [];

      if (operator == 'AND') {
        return rules.every((rule) => _evaluateRule(rule));
      } else if (operator == 'OR') {
        return rules.any((rule) => _evaluateRule(rule));
      }
    }
    return true;
  }

  bool _evaluateRule(dynamic rule) {
    if (rule is! Map<String, dynamic>) return true;

    final fieldId = rule['field'] as String?;
    final operator = rule['operator'] as String?;
    final value = rule['value'];

    if (fieldId == null || operator == null) return true;

    final fieldValue = _formData[fieldId];

    switch (operator) {
      case 'equals':
        return fieldValue == value;
      case 'not_equals':
        return fieldValue != value;
      case 'contains':
        return fieldValue?.toString().contains(value?.toString() ?? '') ?? false;
      case 'greater_than':
        return _compareNumbers(fieldValue, value, (a, b) => a > b);
      case 'less_than':
        return _compareNumbers(fieldValue, value, (a, b) => a < b);
      case 'is_empty':
        return fieldValue == null || fieldValue.toString().isEmpty;
      case 'is_not_empty':
        return fieldValue != null && fieldValue.toString().isNotEmpty;
      default:
        return true;
    }
  }

  bool _compareNumbers(dynamic a, dynamic b, bool Function(num, num) compareFn) {
    final numA = num.tryParse(a?.toString() ?? '');
    final numB = num.tryParse(b?.toString() ?? '');
    if (numA == null || numB == null) return false;
    return compareFn(numA, numB);
  }

  void _onFieldChanged(String fieldId, dynamic value) {
    setState(() {
      _formData[fieldId] = value;
      
      // 重新计算字段可见性
      _updateFieldVisibility();
      
      // 实时验证
      if (widget.enableRealtimeValidation) {
        _validateField(fieldId, value);
      }
    });

    widget.onChanged?.call(_formData);
    
    if (_validationErrors.isNotEmpty) {
      widget.onValidationErrors?.call(_validationErrors);
    }
  }

  void _updateFieldVisibility() {
    for (final field in widget.schema.fields) {
      final fieldId = field['id'] as String;
      final oldVisibility = _fieldVisibility[fieldId] ?? true;
      final newVisibility = _isFieldVisible(field);
      
      if (oldVisibility != newVisibility) {
        _fieldVisibility[fieldId] = newVisibility;
        
        // 如果字段变为不可见，清除其值和验证错误
        if (!newVisibility) {
          _formData.remove(fieldId);
          _validationErrors.remove(fieldId);
        }
      }
    }
  }

  void _validateField(String fieldId, dynamic value) {
    final field = widget.schema.fields.firstWhere(
      (f) => f['id'] == fieldId,
      orElse: () => <String, dynamic>{},
    );

    final validationRules = field['validationRules'] as Map<String, dynamic>?;
    final required = field['required'] as bool? ?? false;

    _validationErrors.remove(fieldId);

    // 必填验证
    if (required && (value == null || value.toString().isEmpty)) {
      _validationErrors[fieldId] = '${field['name']} 是必填项';
      return;
    }

    if (validationRules != null && value != null && value.toString().isNotEmpty) {
      // 长度验证
      final minLength = validationRules['minLength'] as int?;
      final maxLength = validationRules['maxLength'] as int?;
      final valueLength = value.toString().length;

      if (minLength != null && valueLength < minLength) {
        _validationErrors[fieldId] = '${field['name']} 长度不能少于 $minLength 个字符';
        return;
      }

      if (maxLength != null && valueLength > maxLength) {
        _validationErrors[fieldId] = '${field['name']} 长度不能超过 $maxLength 个字符';
        return;
      }

      // 数值范围验证
      final min = validationRules['min'] as num?;
      final max = validationRules['max'] as num?;
      final numValue = num.tryParse(value.toString());

      if (numValue != null) {
        if (min != null && numValue < min) {
          _validationErrors[fieldId] = '${field['name']} 不能小于 $min';
          return;
        }

        if (max != null && numValue > max) {
          _validationErrors[fieldId] = '${field['name']} 不能大于 $max';
          return;
        }
      }

      // 正则表达式验证
      final pattern = validationRules['pattern'] as String?;
      if (pattern != null) {
        final regex = RegExp(pattern);
        if (!regex.hasMatch(value.toString())) {
          final message = validationRules['patternMessage'] as String?;
          _validationErrors[fieldId] = message ?? '${field['name']} 格式不正确';
          return;
        }
      }

      // 自定义验证函数
      final customValidation = validationRules['customValidation'] as String?;
      if (customValidation != null) {
        final result = _executeCustomValidation(customValidation, value);
        if (result != null) {
          _validationErrors[fieldId] = result;
          return;
        }
      }
    }
  }

  String? _executeCustomValidation(String validationCode, dynamic value) {
    // 这里可以实现自定义验证逻辑
    // 例如：邮箱验证、身份证验证等
    try {
      switch (validationCode) {
        case 'email':
          final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
          return emailRegex.hasMatch(value.toString()) ? null : '邮箱格式不正确';
        case 'phone':
          final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
          return phoneRegex.hasMatch(value.toString()) ? null : '手机号格式不正确';
        case 'url':
          final urlRegex = RegExp(r'^https?://[^\s/$.?#].[^\s]*$');
          return urlRegex.hasMatch(value.toString()) ? null : 'URL格式不正确';
        default:
          return null;
      }
    } catch (e) {
      _logger.error('Custom validation error: $e');
      return null;
    }
  }

  List<Map<String, dynamic>> _getFieldGroups() {
    final groups = <String, List<Map<String, dynamic>>>{};
    
    for (final field in widget.schema.fields) {
      final groupName = field['group'] as String? ?? '默认分组';
      groups.putIfAbsent(groupName, () => []);
      groups[groupName]!.add(field);
    }

    return groups.entries.map((entry) => {
      'name': entry.key,
      'fields': entry.value,
    }).toList();
  }

  Widget _buildField(Map<String, dynamic> field) {
    final fieldId = field['id'] as String;
    
    if (!(_fieldVisibility[fieldId] ?? true)) {
      return const SizedBox.shrink();
    }

    // 检查是否有自定义渲染器
    final customRenderer = widget.customRenderers?[field['type']];
    if (customRenderer != null) {
      return customRenderer(field, _formData[fieldId], _onFieldChanged);
    }

    // 使用默认的动态表单工厂
    final fieldWidget = DynamicFormFactory.createField(
      field,
      _formData,
      _onFieldChanged,
    );

    // 添加验证错误显示
    final error = _validationErrors[fieldId];
    if (error != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          fieldWidget,
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 16),
            child: Text(
              error,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
        ],
      );
    }

    return fieldWidget;
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.layoutMode) {
      case SchemaLayoutMode.grid:
        return _buildGridLayout();
      case SchemaLayoutMode.grouped:
        return _buildGroupedLayout();
      case SchemaLayoutMode.tabs:
        return _buildTabsLayout();
      default:
        return _buildListLayout();
    }
  }

  Widget _buildListLayout() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.schema.fields.length,
      itemBuilder: (context, index) {
        final field = widget.schema.fields[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildField(field),
        );
      },
    );
  }

  Widget _buildGridLayout() {
    final visibleFields = widget.schema.fields
        .where((field) => _fieldVisibility[field['id']] ?? true)
        .toList();

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridColumns,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: visibleFields.length,
      itemBuilder: (context, index) {
        return _buildField(visibleFields[index]);
      },
    );
  }

  Widget _buildGroupedLayout() {
    final groups = _getFieldGroups();

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groups.length,
      itemBuilder: (context, index) {
        final group = groups[index];
        final groupName = group['name'] as String;
        final fields = group['fields'] as List<Map<String, dynamic>>;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  groupName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                ...fields.map((field) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: _buildField(field),
                )),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTabsLayout() {
    final groups = _getFieldGroups();

    return Column(
      children: [
        TabBar(
          controller: _tabController,
          tabs: groups.map((group) => Tab(text: group['name'] as String)).toList(),
          isScrollable: groups.length > 3,
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: groups.map((group) {
              final fields = group['fields'] as List<Map<String, dynamic>>;
              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: fields.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildField(fields[index]),
                  );
                },
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// 获取表单数据
  Map<String, dynamic> getFormData() {
    return Map<String, dynamic>.from(_formData);
  }

  /// 验证整个表单
  bool validateForm() {
    _validationErrors.clear();

    for (final field in widget.schema.fields) {
      final fieldId = field['id'] as String;
      
      if (!(_fieldVisibility[fieldId] ?? true)) continue;
      
      final value = _formData[fieldId];
      _validateField(fieldId, value);
    }

    setState(() {});
    widget.onValidationErrors?.call(_validationErrors);

    return _validationErrors.isEmpty;
  }

  /// 重置表单
  void resetForm() {
    setState(() {
      _formData.clear();
      _validationErrors.clear();
      _initializeFormData();
      _updateFieldVisibility();
    });

    widget.onChanged?.call(_formData);
  }
}