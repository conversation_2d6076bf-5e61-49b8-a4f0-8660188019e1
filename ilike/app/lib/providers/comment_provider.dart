import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import '../models/comment.dart';
import '../services/comment_service.dart';
import '../utils/logger.dart';

class CommentProvider with ChangeNotifier {
  final CommentService _commentService;
  final Logger _logger = Logger();

  CommentProvider(this._commentService);

  List<Comment> _comments = [];
  bool _isLoading = false;
  String? _error;
  bool _hasMore = true;
  int _currentPage = 1;
  final int _pageSize = 20;

  List<Comment> get comments => _comments;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasMore => _hasMore;

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误状态
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误状态
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 加载记录的评论列表
  Future<void> loadComments(int recordId, {bool refresh = false}) async {
    if (refresh) {
      _comments.clear();
      _currentPage = 1;
      _hasMore = true;
    }

    if (_isLoading || !_hasMore) return;

    try {
      _setLoading(true);
      _setError(null);

      final response = await _commentService.getCommentsByRecord(
        recordId: recordId,
        page: _currentPage,
        size: _pageSize,
      );

      if (refresh) {
        _comments = response.comments;
      } else {
        _comments.addAll(response.comments);
      }

      _hasMore = _currentPage < response.totalPages;
      _currentPage++;

      notifyListeners();
    } catch (e) {
      _logger.error('Error loading comments: $e');
      _setError('加载评论失败：$e');
    } finally {
      _setLoading(false);
    }
  }

  /// 加载回复列表
  Future<List<Comment>> loadReplies(int parentId) async {
    try {
      final response = await _commentService.getCommentsByRecord(
        recordId: 0, // 这里用parentId获取回复，后端会根据parent_id过滤
        parentId: parentId,
        page: 1,
        size: 100, // 回复通常较少，一次加载更多
      );
      return response.comments;
    } catch (e) {
      _logger.error('Error loading replies: $e');
      throw Exception('加载回复失败：$e');
    }
  }

  /// 创建评论
  Future<Comment> createComment({
    required int recordId,
    required String content,
    int? parentId,
  }) async {
    try {
      final request = CommentCreateRequest(
        recordId: recordId,
        content: content,
        parentId: parentId,
      );

      final newComment = await _commentService.createComment(request);

      // 如果是顶级评论，添加到列表开头
      if (parentId == null || parentId == 0) {
        _comments.insert(0, newComment);
        notifyListeners();
      }

      return newComment;
    } catch (e) {
      _logger.error('Error creating comment: $e');
      throw Exception('创建评论失败：$e');
    }
  }

  /// 更新评论
  Future<void> updateComment({
    required int commentId,
    required String content,
  }) async {
    try {
      final updatedComment = await _commentService.updateComment(
        commentId: commentId,
        content: content,
      );

      // 更新本地评论
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        _comments[index] = updatedComment;
        notifyListeners();
      }
    } catch (e) {
      _logger.error('Error updating comment: $e');
      throw Exception('更新评论失败：$e');
    }
  }

  /// 删除评论
  Future<void> deleteComment(int commentId) async {
    try {
      await _commentService.deleteComment(commentId);

      // 从本地列表移除
      _comments.removeWhere((c) => c.id == commentId);
      notifyListeners();
    } catch (e) {
      _logger.error('Error deleting comment: $e');
      throw Exception('删除评论失败：$e');
    }
  }

  /// 点赞评论
  Future<void> likeComment(int commentId) async {
    try {
      await _commentService.likeComment(commentId);

      // 更新本地点赞数
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        final comment = _comments[index];
        _comments[index] = Comment(
          id: comment.id,
          userId: comment.userId,
          recordId: comment.recordId,
          parentId: comment.parentId,
          content: comment.content,
          likeCount: comment.likeCount + 1,
          replyCount: comment.replyCount,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        );
        notifyListeners();
      }
    } catch (e) {
      _logger.error('Error liking comment: $e');
      throw Exception('点赞失败：$e');
    }
  }

  /// 取消点赞评论
  Future<void> unlikeComment(int commentId) async {
    try {
      await _commentService.unlikeComment(commentId);

      // 更新本地点赞数
      final index = _comments.indexWhere((c) => c.id == commentId);
      if (index != -1) {
        final comment = _comments[index];
        _comments[index] = Comment(
          id: comment.id,
          userId: comment.userId,
          recordId: comment.recordId,
          parentId: comment.parentId,
          content: comment.content,
          likeCount: math.max(0, comment.likeCount - 1),
          replyCount: comment.replyCount,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        );
        notifyListeners();
      }
    } catch (e) {
      _logger.error('Error unliking comment: $e');
      throw Exception('取消点赞失败：$e');
    }
  }

  /// 清空评论列表
  void clearComments() {
    _comments.clear();
    _currentPage = 1;
    _hasMore = true;
    _error = null;
    notifyListeners();
  }
}