import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:ilike/providers/auth_provider.dart';
import 'package:ilike/utils/network_exception_handler.dart';
import 'package:ilike/utils/form_validation_handler.dart';
import 'package:ilike/mixins/form_validation_mixin.dart';
import 'package:ilike/services/token_manager.dart';
import 'package:ilike/routes/app_router.dart';

class LoginForm extends StatefulWidget {
  final bool isPasswordLogin;

  const LoginForm({
    super.key,
    required this.isPasswordLogin,
  });

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> with FormValidationMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  bool _obscurePassword = true;
  bool _isLoading = false;
  bool _rememberMe = false;

  // 存储一般错误信息（用于在表单上方显示）
  String? _generalError;

  @override
  void initState() {
    super.initState();
    _loadRememberMeSettings();
  }

  /// 加载记住我设置
  void _loadRememberMeSettings() {
    final tokenManager = TokenManager();
    setState(() {
      _rememberMe = tokenManager.rememberMe;
      if (tokenManager.lastLoginUsername != null) {
        _usernameController.text = tokenManager.lastLoginUsername!;
      }
    });
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin() async {
    // 添加触觉反馈
    HapticFeedback.lightImpact();

    // 清空之前的错误
    clearFieldErrors(_formKey);
    setState(() {
      _generalError = null;
    });

    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        Map<String, dynamic> result;

        if (widget.isPasswordLogin) {
          // 使用用户名密码登录
          final username = _usernameController.text.trim();
          final password = _passwordController.text.trim();

          if (username.isEmpty || password.isEmpty) {
            setState(() {
              _generalError = '用户名和密码不能为空';
            });
            return;
          }

          result = await authProvider.login(username, password,
              rememberMe: _rememberMe);
        } else {
          // 使用手机验证码登录
          final phone = _phoneController.text.trim();
          final code = _verificationCodeController.text.trim();

          if (phone.isEmpty || code.isEmpty) {
            setState(() {
              _generalError = '手机号和验证码不能为空';
            });
            return;
          }

          // TODO: 实现真正的手机验证码登录
          // 这里暂时用手机号作为用户名，验证码作为密码
          result = await authProvider.login(phone, code);
        }

        if (mounted) {
          if (result['success'] == true) {
            // 登录成功，AuthWrapper会自动检测到认证状态变化并显示首页
            // 不需要手动导航，也不需要弹出SnackBar提示
            debugPrint('LoginForm: 登录成功，等待AuthWrapper响应状态变化');
            // 这里不做任何UI提示，直接依赖Provider状态切换
          } else {
            // 登录失败，处理错误信息
            _handleLoginError(result);
          }
        }
      } catch (e) {
        if (mounted) {
          // 检查是否为表单验证错误
          if (handleApiException(e, _formKey)) {
            // 表单验证错误已处理
            return;
          } else {
            setState(() {
              _generalError = '登录失败，请稍后重试';
            });
          }
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  /// 处理登录错误
  void _handleLoginError(Map<String, dynamic> result) {
    final message = result['message'] ?? '登录失败';
    final fieldErrors = result['fieldErrors'] as Map<String, dynamic>?;
    final code = result['code'] ?? result['errorCode'];
    
    setState(() {
      // 处理参数验证失败（code==10001）
      if (code == 10001 && fieldErrors != null && fieldErrors.isNotEmpty) {
        // 将每个field的message渲染到对应表单项下方
        final errors = <String, String>{};
        for (final entry in fieldErrors.entries) {
          errors[entry.key] = entry.value.toString();
        }
        // 如果没有username/phone/password等常用字段，则兜底显示到username下方
        if (!errors.containsKey('username') &&
            !errors.containsKey('phone') &&
            !errors.containsKey('password')) {
          errors['username'] = message;
        }
        setFieldErrors(errors);
        _generalError = null;
      } else if (fieldErrors != null && fieldErrors.isNotEmpty) {
        final errors = <String, String>{};
        for (final entry in fieldErrors.entries) {
          errors[entry.key] = entry.value.toString();
        }
        setFieldErrors(errors);
        _generalError = null;
      } else {
        // 检查错误消息是否包含username相关信息
        if (message.toLowerCase().contains('username') || 
            message.toLowerCase().contains('用户名') ||
            message.toLowerCase().contains('用户')) {
          // 如果错误与用户名相关，显示在用户名字段下方
          setFieldErrors({'username': message});
          _generalError = null;
        } else {
          // 其他错误显示在用户名字段下方
          if (widget.isPasswordLogin) {
            setFieldErrors({'username': message});
          } else {
            setFieldErrors({'phone': message});
          }
          _generalError = null;
        }
      }
    });

    // 强制表单重新验证以显示字段错误
    _formKey.currentState?.validate();
  }

  Future<void> _handleSendCode() async {
    if (_phoneController.text.length == 11) {
      try {
        setState(() {
          _isLoading = true;
          _generalError = null; // 清除之前的错误
        });

        // TODO: 实现发送验证码逻辑
        await Future.delayed(const Duration(seconds: 1));

        // 验证码发送成功，可以通过状态变化来提示用户
        // 不使用SnackBar，改为在UI中显示状态
      } catch (e) {
        if (mounted) {
          setState(() {
            // 使用网络异常处理器获取错误信息
            _generalError = NetworkExceptionHandler.getErrorMessage(e);
          });
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } else {
      setState(() {
        _generalError = '请输入正确的手机号';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Form(
      key: _formKey,
      child: Column(
        children: [
          // 显示一般错误信息（包括网络错误）
          if (_generalError != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _generalError!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (widget.isPasswordLogin) ...[
            _buildUsernameField(theme),
            const SizedBox(height: 16),
            _buildPasswordField(theme),
          ] else ...[
            _buildPhoneField(theme),
            const SizedBox(height: 16),
            _buildVerificationCodeField(theme),
          ],
          const SizedBox(height: 24),
          _buildLoginButton(theme),
          const SizedBox(height: 12),
          if (widget.isPasswordLogin) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    // 导航到注册页面
                    Navigator.of(context).pushNamed('/register');
                  },
                  child: const Text('注册账号'),
                ),
                TextButton(
                  onPressed: () {
                    // 导航到忘记密码页面
                    Navigator.of(context).pushNamed(AppRouter.forgotPassword);
                  },
                  child: const Text('忘记密码'),
                ),
              ],
            ),
          ],
          const SizedBox(height: 12),
          Row(
            children: [
              Checkbox(
                value: _rememberMe,
                onChanged: (value) {
                  setState(() {
                    _rememberMe = value ?? false;
                  });
                },
                activeColor: const Color(0xFFF2A69F),
              ),
              const Text('记住我'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUsernameField(ThemeData theme) {
    return TextFormField(
      controller: _usernameController,
      decoration: InputDecoration(
        labelText: '用户名',
        prefixIcon: const Icon(Icons.person_outline),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        errorText: getFieldError('username'),
      ),
      validator: createFieldValidator(
        'username',
        (value) {
          if (value == null || value.isEmpty) {
            return '请输入用户名';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildPhoneField(ThemeData theme) {
    return TextFormField(
      controller: _phoneController,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(11),
      ],
      decoration: InputDecoration(
        labelText: '手机号',
        prefixIcon: const Icon(Icons.phone_android_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        errorText: getFieldError('phone'),
      ),
      validator: createFieldValidator(
        'phone',
        (value) {
          if (value == null || value.isEmpty) {
            return '请输入手机号';
          }
          if (value.length != 11) {
            return '请输入正确的手机号';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildPasswordField(ThemeData theme) {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: '密码',
        prefixIcon: const Icon(Icons.lock_outline),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility_off : Icons.visibility,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        errorText: getFieldError('password'),
      ),
      validator: createFieldValidator(
        'password',
        (value) {
          if (value == null || value.isEmpty) {
            return '请输入密码';
          }
          if (value.length < 6) {
            return '密码长度不能小于6位';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildVerificationCodeField(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: _verificationCodeController,
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(6),
            ],
            decoration: InputDecoration(
              labelText: '验证码',
              prefixIcon: const Icon(Icons.security_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              errorText: getFieldError('verificationCode'),
            ),
            validator: createFieldValidator(
              'verificationCode',
              (value) {
                if (value == null || value.isEmpty) {
                  return '请输入验证码';
                }
                if (value.length != 6) {
                  return '请输入6位验证码';
                }
                return null;
              },
            ),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 120,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleSendCode,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('获取验证码'),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton(ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _isLoading ? null : _handleLogin,
          child: Container(
            decoration: BoxDecoration(
              color: _isLoading
                  ? theme.colorScheme.primary.withValues(alpha: 0.6)
                  : theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      widget.isPasswordLogin ? '登录' : '验证并登录',
                      style: const TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.41,
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
