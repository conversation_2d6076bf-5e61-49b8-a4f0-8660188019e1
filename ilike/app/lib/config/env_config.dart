import 'package:flutter/foundation.dart';

/// 环境类型
enum EnvType {
  dev, // 开发环境
  test, // 测试环境
  prod, // 生产环境
}

/// 服务端点配置
class ServiceEndpoints {
  /// 用户中心服务URL
  final String userServiceUrl;
  
  /// 内容中心服务URL
  final String contentServiceUrl;
  
  /// 其他服务URL (如需要)
  final String? ossServiceUrl;
  
  const ServiceEndpoints({
    required this.userServiceUrl,
    required this.contentServiceUrl,
    this.ossServiceUrl,
  });
  
  /// 统一服务端点配置 (生产环境使用)
  static ServiceEndpoints unified(String baseUrl) {
    return ServiceEndpoints(
      userServiceUrl: baseUrl,
      contentServiceUrl: baseUrl,
      ossServiceUrl: baseUrl,
    );
  }
  
  /// 分离服务端点配置 (开发环境使用)
  static const ServiceEndpoints separated = ServiceEndpoints(
    userServiceUrl: 'http://10.176.230.23:8084', // 用户中心
    contentServiceUrl: 'http://10.176.230.23:8080', // 内容中心
    ossServiceUrl: 'http://192.168.50.33:8082', // OSS服务
  );
  
  /// 根据服务类型获取对应的URL
  String getServiceUrl(ServiceType serviceType) {
    switch (serviceType) {
      case ServiceType.user:
        return userServiceUrl;
      case ServiceType.content:
        return contentServiceUrl;
      case ServiceType.oss:
        return ossServiceUrl ?? contentServiceUrl;
    }
  }
}

/// 服务类型枚举
enum ServiceType {
  user, // 用户中心
  content, // 内容中心  
  oss, // OSS服务
}

/// 环境配置类
/// 用于管理不同环境的配置参数
class EnvConfig {
  /// 当前环境类型
  final EnvType type;

  /// 服务端点配置
  final ServiceEndpoints serviceEndpoints;

  /// 是否启用日志
  final bool enableLogging;

  /// 是否启用性能监控
  final bool enablePerformanceMonitoring;

  /// 缓存有效期（小时）
  final int cacheDurationHours;

  /// 图片CDN基础URL
  final String imageCdnBaseUrl;

  /// 构造函数
  const EnvConfig({
    required this.type,
    required this.serviceEndpoints,
    this.enableLogging = true,
    this.enablePerformanceMonitoring = false,
    this.cacheDurationHours = 24,
    required this.imageCdnBaseUrl,
  });

  /// 开发环境配置
  static const EnvConfig dev = EnvConfig(
    type: EnvType.dev,
    serviceEndpoints: ServiceEndpoints.separated,
    enableLogging: true,
    enablePerformanceMonitoring: true,
    cacheDurationHours: 1,
    imageCdnBaseUrl: 'http://192.168.50.33:8081/images',
  );

  /// 测试环境配置
  static const EnvConfig test = EnvConfig(
    type: EnvType.test,
    serviceEndpoints: ServiceEndpoints(
      userServiceUrl: 'https://test-api.ilike-app.com',
      contentServiceUrl: 'https://test-api.ilike-app.com',
      ossServiceUrl: 'https://test-api.ilike-app.com',
    ),
    enableLogging: true,
    enablePerformanceMonitoring: true,
    cacheDurationHours: 2,
    imageCdnBaseUrl: 'https://test-cdn.ilike-app.com/images',
  );

  /// 生产环境配置
  static const EnvConfig prod = EnvConfig(
    type: EnvType.prod,
    serviceEndpoints: ServiceEndpoints(
      userServiceUrl: 'https://api.ilike-app.com',
      contentServiceUrl: 'https://api.ilike-app.com',
      ossServiceUrl: 'https://api.ilike-app.com',
    ),
    enableLogging: false,
    enablePerformanceMonitoring: true,
    cacheDurationHours: 24,
    imageCdnBaseUrl: 'https://cdn.ilike-app.com/images',
  );

  /// 当前使用的环境配置
  static EnvConfig get current {
    // 在Flutter web中，可以通过URL参数设置环境
    if (kIsWeb) {
      final String? envParam = Uri.base.queryParameters['env'];
      if (envParam == 'test') return EnvConfig.test;
      if (envParam == 'prod') return EnvConfig.prod;
      return EnvConfig.dev;
    }

    // 在debug模式下使用开发环境，release模式下使用生产环境
    if (kReleaseMode) {
      return EnvConfig.prod;
    } else if (kProfileMode) {
      return EnvConfig.test;
    } else {
      return EnvConfig.dev;
    }
  }
}

/// 环境管理类
/// 提供环境相关的工具方法
class EnvManager {
  /// 私有构造函数，防止实例化
  EnvManager._();

  /// 当前环境配置
  static late EnvConfig _config;

  /// 初始化环境配置
  static void init({EnvConfig? config}) {
    _config = config ?? EnvConfig.current;
    debugPrint('初始化环境: ${_config.type.name}, API地址: ${_config.serviceEndpoints.getServiceUrl(ServiceType.content)}');
  }

  /// 获取当前环境配置
  static EnvConfig get config => _config;

  /// 获取API基础URL
  static String get apiBaseUrl => _config.serviceEndpoints.getServiceUrl(ServiceType.content);

  /// 获取用户服务基础URL
  static String get userApiBaseUrl => _config.serviceEndpoints.getServiceUrl(ServiceType.user);

  /// 获取内容服务基础URL
  static String get contentApiBaseUrl => _config.serviceEndpoints.getServiceUrl(ServiceType.content);

  /// 获取OSS服务基础URL  
  static String get ossApiBaseUrl => _config.serviceEndpoints.getServiceUrl(ServiceType.oss);

  /// 获取图片CDN基础URL
  static String get imageCdnBaseUrl => _config.imageCdnBaseUrl;

  /// 构建完整的API URL (内容服务)
  static String buildApiUrl(String path) {
    final String baseUrl = _config.serviceEndpoints.getServiceUrl(ServiceType.content);
    if (baseUrl.endsWith('/') && path.startsWith('/')) {
      return baseUrl + path.substring(1);
    } else if (!baseUrl.endsWith('/') && !path.startsWith('/')) {
      return '$baseUrl/$path';
    } else {
      return baseUrl + path;
    }
  }

  /// 构建特定服务的API URL
  static String buildServiceApiUrl(ServiceType serviceType, String path) {
    final String baseUrl = _config.serviceEndpoints.getServiceUrl(serviceType);
    if (baseUrl.endsWith('/') && path.startsWith('/')) {
      return baseUrl + path.substring(1);
    } else if (!baseUrl.endsWith('/') && !path.startsWith('/')) {
      return '$baseUrl/$path';
    } else {
      return baseUrl + path;
    }
  }

  /// 构建用户服务API URL
  static String buildUserApiUrl(String path) {
    return buildServiceApiUrl(ServiceType.user, path);
  }

  /// 构建内容服务API URL
  static String buildContentApiUrl(String path) {
    return buildServiceApiUrl(ServiceType.content, path);
  }

  /// 构建完整的图片URL
  static String buildImageUrl(String path) {
    final String baseUrl = _config.imageCdnBaseUrl;
    if (baseUrl.endsWith('/') && path.startsWith('/')) {
      return baseUrl + path.substring(1);
    } else if (!baseUrl.endsWith('/') && !path.startsWith('/')) {
      return '$baseUrl/$path';
    } else {
      return baseUrl + path;
    }
  }

  /// 判断是否为开发环境
  static bool get isDev => _config.type == EnvType.dev;

  /// 判断是否为测试环境
  static bool get isTest => _config.type == EnvType.test;

  /// 判断是否为生产环境
  static bool get isProd => _config.type == EnvType.prod;
}
