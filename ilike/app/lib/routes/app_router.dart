import 'package:flutter/material.dart';
import 'package:ilike/screens/login/login_screen.dart';
import 'package:ilike/screens/register/register_screen.dart';
import 'package:ilike/screens/forgot_password_screen.dart';
import 'package:ilike/screens/home/<USER>';
import 'package:ilike/screens/create/create_screen.dart';
import 'package:ilike/screens/create/select_category_screen.dart';
import 'package:ilike/screens/create/record_form_screen.dart';
import 'package:ilike/screens/detail/record_detail_screen.dart';
import 'package:ilike/screens/profile/profile_screen.dart';
import 'package:ilike/screens/edit/edit_record_screen.dart';
import 'package:ilike/screens/search/search_screen.dart';
import 'package:ilike/screens/search/enhanced_search_screen.dart';

class AppRouter {
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String create = '/create';
  static const String selectCategory = '/create/select-category';
  static const String recordForm = '/create/record-form';
  static const String recordDetail = '/record/detail';
  static const String editRecord = '/record/edit';
  static const String profile = '/profile';
  static const String search = '/search';
  static const String enhancedSearch = '/enhanced-search';

  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case login:
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case register:
        return MaterialPageRoute(builder: (_) => const RegisterScreen());
      case forgotPassword:
        return MaterialPageRoute(builder: (_) => const ForgotPasswordScreen());
      case home:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case create:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => CreateScreen(
            selectedCategory: args?['selectedCategory'],
          ),
        );
      case selectCategory:
        return MaterialPageRoute(builder: (_) => const SelectCategoryScreen());
      case recordForm:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => RecordFormScreen(
            selectedCategory: args['selectedCategory'],
          ),
        );
      case recordDetail:
        final args = settings.arguments as Map<String, dynamic>;
        final recordId = args['recordId'];
        final intRecordId =
            recordId is int ? recordId : int.parse(recordId.toString());
        return MaterialPageRoute(
          builder: (_) => RecordDetailScreen(recordId: intRecordId),
        );
      case editRecord:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => EditRecordScreen(record: args['record']),
        );
      case profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case search:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => SearchScreen(
            initialQuery: args?['query'],
            category: args?['category'],
          ),
        );
      case enhancedSearch:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => EnhancedSearchScreen(
            initialQuery: args?['query'],
            category: args?['category'],
          ),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('未找到路由: ${settings.name}'),
            ),
          ),
        );
    }
  }
}
