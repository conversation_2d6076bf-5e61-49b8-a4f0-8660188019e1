package main

import (
	"context"
	"fmt"
	"log"
	"math"
	"sync"
	"sync/atomic"
	"time"
)

// AdvancedWorkerPool 高级线程池
type AdvancedWorkerPool struct {
	// 基础配置
	minWorkers    int
	maxWorkers    int
	queueSize     int
	keepAliveTime time.Duration

	// 运行时状态
	workers       int32
	activeWorkers int32
	taskQueue     chan Task
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup

	// 统计信息
	stats *PoolStats

	// 配置选项
	config *PoolConfig

	// 监控回调
	monitor PoolMonitor
}

// PoolConfig 线程池配置
type PoolConfig struct {
	MinWorkers      int           // 最小工作协程数
	MaxWorkers      int           // 最大工作协程数
	QueueSize       int           // 任务队列大小
	KeepAliveTime   time.Duration // 空闲协程存活时间
	ShutdownTimeout time.Duration // 关闭超时时间
	RetryAttempts   int           // 任务重试次数
	RetryDelay      time.Duration // 重试延迟
	EnableMetrics   bool          // 启用指标收集
	EnableAutoScale bool          // 启用自动扩缩容
	ScaleThreshold  float64       // 扩缩容阈值
}

// PoolStats 线程池统计信息
type PoolStats struct {
	mu             sync.RWMutex
	TotalTasks     int64
	CompletedTasks int64
	FailedTasks    int64
	RetriedTasks   int64
	QueueSize      int
	ActiveWorkers  int32
	TotalWorkers   int32
	AvgTaskTime    time.Duration
	LastTaskTime   time.Time
}

// PoolMonitor 监控接口
type PoolMonitor interface {
	OnTaskSubmitted(taskID string)
	OnTaskCompleted(taskID string, duration time.Duration)
	OnTaskFailed(taskID string, err error)
	OnWorkerStarted(workerID int)
	OnWorkerStopped(workerID int)
	OnPoolScaled(oldSize, newSize int)
}

// DefaultPoolConfig 默认配置
func DefaultPoolConfig() *PoolConfig {
	return &PoolConfig{
		MinWorkers:      2,
		MaxWorkers:      10,
		QueueSize:       100,
		KeepAliveTime:   60 * time.Second,
		ShutdownTimeout: 30 * time.Second,
		RetryAttempts:   3,
		RetryDelay:      1 * time.Second,
		EnableMetrics:   true,
		EnableAutoScale: true,
		ScaleThreshold:  0.8, // 80% 队列使用率触发扩容
	}
}

// NewAdvancedWorkerPool 创建高级线程池
func NewAdvancedWorkerPool(config *PoolConfig) *AdvancedWorkerPool {
	if config == nil {
		config = DefaultPoolConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	pool := &AdvancedWorkerPool{
		minWorkers:    config.MinWorkers,
		maxWorkers:    config.MaxWorkers,
		queueSize:     config.QueueSize,
		keepAliveTime: config.KeepAliveTime,
		taskQueue:     make(chan Task, config.QueueSize),
		ctx:           ctx,
		cancel:        cancel,
		stats:         &PoolStats{},
		config:        config,
	}

	// 启动监控协程
	if config.EnableMetrics {
		go pool.metricsCollector()
	}

	// 启动自动扩缩容协程
	if config.EnableAutoScale {
		go pool.autoScaler()
	}

	return pool
}

// Start 启动线程池
func (p *AdvancedWorkerPool) Start() {
	fmt.Printf("Starting advanced worker pool (min: %d, max: %d)\n", p.minWorkers, p.maxWorkers)

	// 启动最小数量的工作协程
	for i := 0; i < p.minWorkers; i++ {
		p.startWorker(i)
	}

	atomic.StoreInt32(&p.workers, int32(p.minWorkers))
}

// Stop 停止线程池
func (p *AdvancedWorkerPool) Stop() {
	fmt.Println("Stopping advanced worker pool...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), p.config.ShutdownTimeout)
	defer cancel()

	// 取消上下文
	p.cancel()

	// 等待所有工作协程完成
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		fmt.Println("All workers stopped gracefully")
	case <-ctx.Done():
		fmt.Println("Worker pool stopped with timeout")
	}

	// 输出最终统计
	p.printFinalStats()
}

// Submit 提交任务
func (p *AdvancedWorkerPool) Submit(task Task) error {
	select {
	case p.taskQueue <- task:
		atomic.AddInt64(&p.stats.TotalTasks, 1)
		if p.monitor != nil {
			p.monitor.OnTaskSubmitted(task.GetID())
		}
		return nil
	case <-p.ctx.Done():
		return fmt.Errorf("worker pool is stopped")
	default:
		return fmt.Errorf("task queue is full")
	}
}

// SubmitWithRetry 提交带重试的任务
func (p *AdvancedWorkerPool) SubmitWithRetry(task Task) error {
	return p.submitWithRetry(task, 0)
}

// submitWithRetry 内部重试逻辑
func (p *AdvancedWorkerPool) submitWithRetry(task Task, attempt int) error {
	err := p.Submit(task)
	if err != nil && attempt < p.config.RetryAttempts {
		atomic.AddInt64(&p.stats.RetriedTasks, 1)
		time.Sleep(p.config.RetryDelay * time.Duration(attempt+1))
		return p.submitWithRetry(task, attempt+1)
	}
	return err
}

// SetMonitor 设置监控回调
func (p *AdvancedWorkerPool) SetMonitor(monitor PoolMonitor) {
	p.monitor = monitor
}

// GetStats 获取统计信息
func (p *AdvancedWorkerPool) GetStats() PoolStats {
	p.stats.mu.RLock()
	defer p.stats.mu.RUnlock()

	stats := *p.stats
	stats.QueueSize = len(p.taskQueue)
	stats.ActiveWorkers = atomic.LoadInt32(&p.activeWorkers)
	stats.TotalWorkers = atomic.LoadInt32(&p.workers)

	return stats
}

// Scale 手动扩缩容
func (p *AdvancedWorkerPool) Scale(targetWorkers int) error {
	if targetWorkers < p.minWorkers || targetWorkers > p.maxWorkers {
		return fmt.Errorf("target workers %d out of range [%d, %d]",
			targetWorkers, p.minWorkers, p.maxWorkers)
	}

	current := int(atomic.LoadInt32(&p.workers))

	if targetWorkers > current {
		// 扩容
		for i := current; i < targetWorkers; i++ {
			p.startWorker(i)
		}
	} else if targetWorkers < current {
		// 缩容
		// 通过发送停止信号来减少工作协程
		for i := 0; i < current-targetWorkers; i++ {
			select {
			case p.taskQueue <- &StopSignal{}:
			default:
				// 如果队列满了，直接减少计数
			}
		}
	}

	if p.monitor != nil {
		p.monitor.OnPoolScaled(current, targetWorkers)
	}

	return nil
}

// startWorker 启动工作协程
func (p *AdvancedWorkerPool) startWorker(id int) {
	p.wg.Add(1)
	atomic.AddInt32(&p.workers, 1)

	if p.monitor != nil {
		p.monitor.OnWorkerStarted(id)
	}

	go p.worker(id)
}

// worker 工作协程实现
func (p *AdvancedWorkerPool) worker(id int) {
	defer func() {
		p.wg.Done()
		atomic.AddInt32(&p.workers, -1)

		if p.monitor != nil {
			p.monitor.OnWorkerStopped(id)
		}
	}()

	keepAliveTimer := time.NewTimer(p.keepAliveTime)
	defer keepAliveTimer.Stop()

	for {
		select {
		case task, ok := <-p.taskQueue:
			if !ok {
				return
			}

			// 检查是否是停止信号
			if _, isStopSignal := task.(*StopSignal); isStopSignal {
				return
			}

			// 重置保活定时器
			if !keepAliveTimer.Stop() {
				<-keepAliveTimer.C
			}
			keepAliveTimer.Reset(p.keepAliveTime)

			// 执行任务
			atomic.AddInt32(&p.activeWorkers, 1)
			startTime := time.Now()

			err := task.Execute()
			duration := time.Since(startTime)

			atomic.AddInt32(&p.activeWorkers, -1)

			// 更新统计信息
			p.stats.mu.Lock()
			if err != nil {
				atomic.AddInt64(&p.stats.FailedTasks, 1)
				if p.monitor != nil {
					p.monitor.OnTaskFailed(task.GetID(), err)
				}
			} else {
				atomic.AddInt64(&p.stats.CompletedTasks, 1)
				if p.monitor != nil {
					p.monitor.OnTaskCompleted(task.GetID(), duration)
				}
			}
			p.stats.AvgTaskTime = p.calculateAvgTaskTime(duration)
			p.stats.LastTaskTime = time.Now()
			p.stats.mu.Unlock()

		case <-keepAliveTimer.C:
			// 检查是否需要缩容
			currentWorkers := int(atomic.LoadInt32(&p.workers))
			if currentWorkers > p.minWorkers {
				// 空闲超时，退出工作协程
				return
			}
			keepAliveTimer.Reset(p.keepAliveTime)

		case <-p.ctx.Done():
			return
		}
	}
}

// StopSignal 停止信号
type StopSignal struct{}

func (s *StopSignal) Execute() error {
	return nil
}

func (s *StopSignal) GetID() string {
	return "stop-signal"
}

// calculateAvgTaskTime 计算平均任务执行时间
func (p *AdvancedWorkerPool) calculateAvgTaskTime(duration time.Duration) time.Duration {
	completed := atomic.LoadInt64(&p.stats.CompletedTasks)
	if completed == 0 {
		return duration
	}

	// 简单的移动平均
	avg := p.stats.AvgTaskTime
	if avg == 0 {
		return duration
	}

	// 使用指数移动平均
	alpha := 0.1
	newAvg := time.Duration(float64(avg)*(1-alpha) + float64(duration)*alpha)
	return newAvg
}

// metricsCollector 指标收集器
func (p *AdvancedWorkerPool) metricsCollector() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats := p.GetStats()
			p.logMetrics(stats)
		case <-p.ctx.Done():
			return
		}
	}
}

// autoScaler 自动扩缩容
func (p *AdvancedWorkerPool) autoScaler() {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.checkAndScale()
		case <-p.ctx.Done():
			return
		}
	}
}

// checkAndScale 检查并执行扩缩容
func (p *AdvancedWorkerPool) checkAndScale() {
	stats := p.GetStats()
	currentWorkers := int(atomic.LoadInt32(&p.workers))

	// 计算队列使用率
	queueUsage := float64(stats.QueueSize) / float64(p.queueSize)

	if queueUsage > p.config.ScaleThreshold && currentWorkers < p.maxWorkers {
		// 扩容
		newSize := int(math.Min(float64(p.maxWorkers),
			float64(currentWorkers)*1.5))
		p.Scale(newSize)
		fmt.Printf("Auto-scaling up: %d -> %d (queue usage: %.2f%%)\n",
			currentWorkers, newSize, queueUsage*100)
	} else if queueUsage < p.config.ScaleThreshold*0.5 && currentWorkers > p.minWorkers {
		// 缩容
		newSize := int(math.Max(float64(p.minWorkers),
			float64(currentWorkers)*0.8))
		p.Scale(newSize)
		fmt.Printf("Auto-scaling down: %d -> %d (queue usage: %.2f%%)\n",
			currentWorkers, newSize, queueUsage*100)
	}
}

// logMetrics 记录指标
func (p *AdvancedWorkerPool) logMetrics(stats PoolStats) {
	fmt.Printf("[Metrics] Workers: %d/%d, Active: %d, Queue: %d/%d, "+
		"Completed: %d, Failed: %d, AvgTime: %v\n",
		stats.ActiveWorkers, stats.TotalWorkers, stats.ActiveWorkers,
		stats.QueueSize, p.queueSize, stats.CompletedTasks,
		stats.FailedTasks, stats.AvgTaskTime)
}

// printFinalStats 打印最终统计
func (p *AdvancedWorkerPool) printFinalStats() {
	stats := p.GetStats()
	fmt.Printf("\n=== Final Pool Statistics ===\n")
	fmt.Printf("Total Tasks: %d\n", stats.TotalTasks)
	fmt.Printf("Completed: %d\n", stats.CompletedTasks)
	fmt.Printf("Failed: %d\n", stats.FailedTasks)
	fmt.Printf("Retried: %d\n", stats.RetriedTasks)
	fmt.Printf("Success Rate: %.2f%%\n",
		float64(stats.CompletedTasks)/float64(stats.TotalTasks)*100)
	fmt.Printf("Average Task Time: %v\n", stats.AvgTaskTime)
}

// 使用示例
func demoAdvancedWorkerPool() {
	fmt.Println("\n=== Advanced Worker Pool Demo ===")

	// 创建配置
	config := DefaultPoolConfig()
	config.MinWorkers = 2
	config.MaxWorkers = 8
	config.QueueSize = 20
	config.EnableAutoScale = true
	config.ScaleThreshold = 0.7

	// 创建线程池
	pool := NewAdvancedWorkerPool(config)

	// 设置监控
	pool.SetMonitor(&SimpleMonitor{})

	// 启动线程池
	pool.Start()

	// 提交任务
	for i := 0; i < 30; i++ {
		task := &SimpleTask{
			ID:       fmt.Sprintf("adv-task-%d", i),
			Duration: time.Duration(i%5+1) * time.Second,
			Data:     fmt.Sprintf("data-%d", i),
		}

		// 使用重试机制
		if err := pool.SubmitWithRetry(task); err != nil {
			log.Printf("Failed to submit task %s: %v", task.ID, err)
		}

		// 模拟任务提交间隔
		time.Sleep(200 * time.Millisecond)
	}

	// 等待任务执行
	time.Sleep(30 * time.Second)

	// 停止线程池
	pool.Stop()
}

// SimpleMonitor 简单监控实现
type SimpleMonitor struct{}

func (m *SimpleMonitor) OnTaskSubmitted(taskID string) {
	fmt.Printf("[Monitor] Task submitted: %s\n", taskID)
}

func (m *SimpleMonitor) OnTaskCompleted(taskID string, duration time.Duration) {
	fmt.Printf("[Monitor] Task completed: %s (duration: %v)\n", taskID, duration)
}

func (m *SimpleMonitor) OnTaskFailed(taskID string, err error) {
	fmt.Printf("[Monitor] Task failed: %s (error: %v)\n", taskID, err)
}

func (m *SimpleMonitor) OnWorkerStarted(workerID int) {
	fmt.Printf("[Monitor] Worker started: %d\n", workerID)
}

func (m *SimpleMonitor) OnWorkerStopped(workerID int) {
	fmt.Printf("[Monitor] Worker stopped: %d\n", workerID)
}

func (m *SimpleMonitor) OnPoolScaled(oldSize, newSize int) {
	fmt.Printf("[Monitor] Pool scaled: %d -> %d\n", oldSize, newSize)
}

// 在 main 函数中调用
func init() {
	// 注册高级线程池演示
	// demoAdvancedWorkerPool()
}
