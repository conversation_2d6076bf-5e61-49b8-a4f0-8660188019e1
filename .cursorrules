You are an expert in Go, microservices architecture, and clean backend development practices. Your role is to ensure code is idiomatic, modular, testable, and aligned with modern best practices and design patterns.

### General Responsibilities:
- Guide the development of idiomatic, maintainable, and high-performance Go code.
- Enforce modular design and separation of concerns through Clean Architecture.
- Promote test-driven development, robust observability, and scalable patterns across services.

### Architecture Patterns:
- Apply **Clean Architecture** by structuring code into handlers/controllers, services/use cases, repositories/data access, and domain models.
- Use **domain-driven design** principles where applicable.
- Prioritize **interface-driven development** with explicit dependency injection.
- Prefer **composition over inheritance**; favor small, purpose-specific interfaces.
- Ensure that all public functions interact with interfaces, not concrete types, to enhance flexibility and testability.

### Project Structure Guidelines:
- Use a consistent project layout:
  - cmd/: application entrypoints
  - internal/: core application logic (not exposed externally)
  - pkg/: shared utilities and packages
  - api/: gRPC/REST transport definitions and handlers
  - configs/: configuration schemas and loading
  - test/: test utilities, mocks, and integration tests
- Group code by feature when it improves clarity and cohesion.
- Keep logic decoupled from framework-specific code.
- Using DDD architecture
- Do not use the path parameter, and the http interface only provides get and post method
- Use the decorator pattern (function wrapper/higher-order function) to reduce procedural coding

### Development Best Practices:
- Write **short, focused functions** with a single responsibility.
- Always **check and handle errors explicitly**, using wrapped errors for traceability ('fmt.Errorf("context: %w", err)').
- Avoid **global state**; use constructor functions to inject dependencies.
- Leverage **Go's context propagation** for request-scoped values, deadlines, and cancellations.
- Use **goroutines safely**; guard shared state with channels or sync primitives.
- **Defer closing resources** and handle them carefully to avoid leaks.

### Security and Resilience:
- Apply **input validation and sanitization** rigorously, especially on inputs from external sources.
- Use secure defaults for **JWT, cookies**, and configuration settings.
- Isolate sensitive operations with clear **permission boundaries**.
- Implement **retries, exponential backoff, and timeouts** on all external calls.
- Use **circuit breakers and rate limiting** for service protection.
- Consider implementing **distributed rate-limiting** to prevent abuse across services (e.g., using Redis).

### Testing:
- Write **unit tests** using table-driven patterns and parallel execution.
- **Mock external interfaces** cleanly using generated or handwritten mocks.
- Separate **fast unit tests** from slower integration and E2E tests.
- Ensure **test coverage** for every exported function, with behavioral checks.
- Use tools like 'go test -cover' to ensure adequate test coverage.

### Documentation and Standards:
- Document public functions and packages with **GoDoc-style comments**.
- Provide concise **READMEs** for services and libraries.
- Maintain a 'CONTRIBUTING.md' and 'ARCHITECTURE.md' to guide team practices.
- Enforce naming consistency and formatting with 'go fmt', 'goimports', and 'golangci-lint'.

### Observability with OpenTelemetry:
- Use **OpenTelemetry** for distributed tracing, metrics, and structured logging.
- Start and propagate tracing **spans** across all service boundaries (HTTP, gRPC, DB, external APIs).
- Always attach 'context.Context' to spans, logs, and metric exports.
- Use **otel.Tracer** for creating spans and **otel.Meter** for collecting metrics.
- Record important attributes like request parameters, user ID, and error messages in spans.
- Use **log correlation** by injecting trace IDs into structured logs.
- Export data to **OpenTelemetry Collector**, **Jaeger**, or **Prometheus**.

### Tracing and Monitoring Best Practices:
- Trace all **incoming requests** and propagate context through internal and external calls.
- Use **middleware** to instrument HTTP and gRPC endpoints automatically.
- Annotate slow, critical, or error-prone paths with **custom spans**.
- Monitor application health via key metrics: **request latency, throughput, error rate, resource usage**.
- Define **SLIs** (e.g., request latency < 300ms) and track them with **Prometheus/Grafana** dashboards.
- Alert on key conditions (e.g., high 5xx rates, DB errors, Redis timeouts) using a robust alerting pipeline.
- Avoid excessive **cardinality** in labels and traces; keep observability overhead minimal.
- Use **log levels** appropriately (info, warn, error) and emit **JSON-formatted logs** for ingestion by observability tools.
- Include unique **request IDs** and trace context in all logs for correlation.

### Performance:
- Use **benchmarks** to track performance regressions and identify bottlenecks.
- Minimize **allocations** and avoid premature optimization; profile before tuning.
- Instrument key areas (DB, external calls, heavy computation) to monitor runtime behavior.

### Concurrency and Goroutines:
- Ensure safe use of **goroutines**, and guard shared state with channels or sync primitives.
- Implement **goroutine cancellation** using context propagation to avoid leaks and deadlocks.

### Tooling and Dependencies:
- Rely on **stable, minimal third-party libraries**; prefer the standard library where feasible.
- Use **Go modules** for dependency management and reproducibility.
- Version-lock dependencies for deterministic builds.
- Integrate **linting, testing, and security checks** in CI pipelines.

### Key Conventions:
1. Prioritize **readability, simplicity, and maintainability**.
2. Design for **change**: isolate business logic and minimize framework lock-in.
3. Emphasize clear **boundaries** and **dependency inversion**.
4. Ensure all behavior is **observable, testable, and documented**.
5. **Automate workflows** for testing, building, and deployment.

# REST API 统一响应结构规范

## 核心要求
- **所有REST API接口必须使用统一的返回结构**
- **不允许将系统级错误输出给用户**

## 统一响应结构
```go
type Response struct {
    Code    int         `json:"code"`              // 业务状态码
    Message string      `json:"message"`           // 响应消息
    Data    interface{} `json:"data,omitempty"`    // 响应数据
    Meta    *Meta       `json:"meta,omitempty"`    // 元数据信息
}

type Meta struct {
    RequestID  string                 `json:"request_id,omitempty"`  // 请求ID
    Timestamp  int64                  `json:"timestamp"`             // 时间戳
    Pagination *Pagination            `json:"pagination,omitempty"`  // 分页信息
    Extra      map[string]interface{} `json:"extra,omitempty"`       // 额外信息
}
```

## 错误处理规范

### 1. 系统级错误处理
- **严格禁止**将系统内部错误信息直接返回给用户
- 系统级错误（如数据库连接失败、内部服务异常等）必须转换为用户友好的错误消息
- 在开发环境中可以记录详细错误信息到日志，但响应中只返回通用错误消息

### 2. 错误响应示例
```go
// ❌ 错误做法 - 直接暴露系统错误
func GetUser(c *gin.Context) {
    user, err := userService.GetByID("123")
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()}) // 直接暴露错误信息
        return
    }
    c.JSON(200, user)
}

// ✅ 正确做法 - 使用统一响应结构，隐藏系统错误
func GetUser(c *gin.Context) {
    user, err := userService.GetByID("123")
    if err != nil {
        // 记录详细错误到日志
        logger.Error("failed to get user", "error", err, "user_id", "123")
        
        // 返回用户友好的错误消息
        response.InternalError(c, fmt.Errorf("internal server error"))
        return
    }
    response.Success(c, user)
}
```

### 3. 错误码分类
- **HTTP状态码 (200-599)**: 标准HTTP状态码
- **业务错误码 (1000-9999)**: 按模块分类的业务错误码
- **系统错误码**: 内部使用，不暴露给用户

### 4. 错误消息规范
- 用户可见的错误消息必须简洁明了
- 避免暴露系统内部信息（如文件路径、SQL语句、堆栈信息等）
- 提供有意义的错误提示，帮助用户理解问题

## 响应工具函数使用

### 成功响应
```go
// 基础成功响应
response.Success(c, data)

// 创建成功响应
response.Created(c, data)

// 更新成功响应
response.Updated(c, data)

// 删除成功响应
response.Deleted(c)

// 分页响应
response.Paginated(c, data, page, size, total)
```

### 错误响应
```go
// 验证错误
response.ValidationError(c, errors)

// 未授权
response.Unauthorized(c, "token is required")

// 禁止访问
response.Forbidden(c, "permission denied")

// 资源不存在
response.NotFound(c, "user")

// 限流
response.TooManyRequests(c, 60)

// 内部错误（隐藏系统详情）
response.InternalError(c, err)
```

## 中间件集成
- 使用统一的响应中间件确保所有API都遵循响应结构
- 在中间件中处理系统级错误，转换为用户友好的响应
- 记录详细的错误信息到日志系统，但不在响应中暴露

## 日志记录
- 系统级错误必须记录到日志系统
- 包含请求ID、错误详情、堆栈信息等调试信息
- 使用结构化日志格式，便于日志分析

# 领域驱动设计 (DDD) Cursor Rules

## 核心原则
- 以业务领域为核心，代码结构反映业务概念
- 使用统一语言，代码命名与业务术语一致
- 保持领域模型的纯净性，避免技术细节污染

## 项目结构
```
src/
├── domain/           # 领域层 - 核心业务逻辑
│   ├── entities/     # 实体
│   ├── value-objects/ # 值对象
│   ├── aggregates/   # 聚合根
│   └── repositories/ # 仓储接口
├── application/      # 应用层 - 用例和协调
│   └── services/     # 应用服务
├── infrastructure/   # 基础设施层 - 技术实现
│   ├── persistence/  # 数据持久化
│   └── external/     # 外部服务
└── interfaces/       # 接口层 - 用户界面和API
    ├── controllers/  # 控制器
    └── dto/         # 数据传输对象
```

## 编码规范

### 实体 (Entities)
- 具有唯一标识符
- 包含业务行为，不只是数据容器
- 保护内部状态，通过方法暴露行为

### 值对象 (Value Objects)
- 不可变，无标识符
- 基于属性值相等性
- 封装相关属性和行为

### 聚合根 (Aggregate Roots)
- 管理聚合内实体的一致性
- 作为外部访问聚合的唯一入口
- 确保业务规则和不变性

### 仓储 (Repositories)
- 提供聚合的持久化抽象
- 按聚合根组织，不是按实体
- 接口定义在领域层，实现在基础设施层

### 应用服务 (Application Services)
- 协调领域对象完成用例
- 处理事务边界
- 不包含业务逻辑，只协调

### 领域服务 (Domain Services)
- 处理跨聚合的业务逻辑
- 当业务规则不属于任何特定实体时使用

## 命名约定
- 实体：`User`, `Order`, `Product`
- 值对象：`Email`, `Money`, `Address`
- 聚合根：`OrderAggregate`, `UserAggregate`
- 仓储：`IUserRepository`, `IOrderRepository`
- 应用服务：`UserApplicationService`, `OrderApplicationService`
- 领域服务：`OrderCalculationService`, `UserValidationService`

## 设计原则
- 依赖倒置：高层模块不依赖低层模块
- 单一职责：每个类只有一个变化原因
- 开闭原则：对扩展开放，对修改关闭
- 里氏替换：子类可替换父类
- 接口隔离：客户端不依赖不需要的接口
- 依赖注入：通过构造函数注入依赖

## 代码生成指导
- 优先考虑业务概念，而非技术实现
- 使用领域事件进行解耦
- 保持领域模型的纯净性
- 通过接口定义契约
- 使用工厂模式创建复杂对象
- 实现领域事件和事件溯源模式

# 代码复用与重构约束

## 核心原则
- **优先复用现有代码**：实现新功能前必须检查现有代码库，寻找可复用的组件、模式或逻辑
- **重构优于重写**：优先考虑重构和扩展现有代码，而不是重新实现
- **统一设计模式**：保持一致的架构模式和代码风格

## 实施要求

### 代码审查流程
在实现新功能前必须：
1. 搜索现有相似功能
2. 检查可复用的工具函数
3. 评估现有代码的扩展性
4. 确定重构策略

### 重构优先原则
- **功能扩展**：通过参数化扩展现有功能
- **接口适配**：使用适配器模式适配现有接口
- **组合复用**：通过组合现有组件构建新功能
- **模板方法**：使用模板方法模式统一相似功能

### 重构检查清单
- [ ] 搜索现有代码库中的相似实现
- [ ] 评估现有代码的可扩展性
- [ ] 考虑通过参数化扩展现有功能
- [ ] 使用装饰器模式添加新行为
- [ ] 确保重构后的代码保持向后兼容

## 代码组织规范
- 使用一致的命名模式
- 避免创建功能重复的组件
- 优先扩展现有组件
- 定义清晰的接口契约
- 使用依赖注入

## 性能考虑
- 在抽象和性能之间找到平衡
- 复用现有的缓存机制
- 避免重复的数据库连接
- 使用事务管理

记住：**好的代码是重构出来的，不是一次性写出来的**。持续重构和改进是高质量代码的关键。

# 前端列表页面设计规范

## 核心原则
- **统一性**：所有列表页面必须保持一致的视觉设计和交互模式
- **简洁性**：移除冗余的描述性文字，突出核心功能
- **信息性**：通过统计信息快速了解数据整体状况
- **响应式**：支持不同屏幕尺寸和深色模式

## 页面结构规范

### 1. 统计信息区域（必须）
- 位置：页面顶部，标题区域
- 布局：4个统计卡片，水平排列
- 内容：总数量、启用数量、禁用数量、分类数量
- 样式：统一的图标、颜色、字体大小

```typescript
// 统计信息示例
const stats = [
  { title: '总数量', value: total, icon: <MainIcon />, color: '#1890ff' },
  { title: '启用数量', value: activeCount, icon: <CheckCircleOutlined />, color: '#52c41a' },
  { title: '禁用数量', value: inactiveCount, icon: <CloseCircleOutlined />, color: '#ff4d4f' },
  { title: '分类数量', value: categoryCount, icon: <AppstoreOutlined />, color: '#722ed1' },
];
```

### 2. 搜索筛选区域（必须）
- 位置：统计信息下方
- 样式：统一的Card样式，圆角、阴影、边框
- 布局：响应式Grid布局
- 功能：实时搜索，无需点击搜索按钮

```typescript
// 搜索区域样式
const cardStyle = {
  borderRadius: 12,
  borderStyle: 'none',
  background: isDarkMode ? '#1f1f1f' : '#ffffff',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
  border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
  marginBottom: 24,
};
```

### 3. 表格区域（必须）
- 位置：搜索区域下方
- 样式：与搜索区域相同的Card样式
- 功能：分页、排序、操作按钮
- 列设计：合并相关信息，避免冗余子标题

## 表格列设计规范

### 1. 信息列设计
- **主要信息列**：头像 + 名称 + 标识
- **详细信息列**：合并相关属性，使用标签展示
- **状态列**：使用颜色标签区分状态
- **时间列**：统一格式，简洁显示
- **操作列**：统一的按钮样式和tooltip

### 2. 列合并原则
- 避免创建过多独立列
- 将相关信息合并到一个列中
- 使用render函数自定义列内容
- 保持视觉层次清晰

```typescript
// 列设计示例
const columns = [
  {
    title: '主要信息',
    key: 'main_info',
    render: (record) => (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <Avatar size={32} icon={<MainIcon />} />
        <div>
          <div style={{ fontWeight: 500 }}>{record.display_name}</div>
          <div style={{ fontSize: 12, color: '#8c8c8c' }}>{record.name}</div>
        </div>
      </div>
    ),
  },
  {
    title: '详细信息',
    key: 'detail_info',
    render: (record) => (
      <div>
        <div style={{ marginBottom: 4 }}>
          <Tag color="blue">{record.category}</Tag>
        </div>
        <div>
          <Tag color="cyan">{record.type1}</Tag>
          <Tag color="magenta" style={{ marginLeft: 4 }}>{record.type2}</Tag>
        </div>
      </div>
    ),
  },
];
```

## 交互设计规范

### 1. 搜索交互
- 实时搜索：输入时立即触发搜索
- 自动重置分页：搜索时重置到第一页
- 清空功能：支持一键清空搜索条件

```typescript
// 搜索处理
const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setSearchKeyword(e.target.value);
  setCurrentPage(1); // 重置分页
};
```

### 2. 操作按钮
- 统一使用text类型按钮
- 添加tooltip提示
- 使用统一的颜色主题
- 支持深色模式

```typescript
// 操作按钮样式
const actionButtonStyle = {
  color: isDarkMode ? '#40a9ff' : '#1890ff',
  fontWeight: 500,
};

const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';
```

### 3. 分页设计
- 显示总数和当前范围
- 支持快速跳转
- 支持页面大小调整
- 响应式设计

## 主题支持规范

### 1. 深色模式
- 所有组件必须支持深色模式
- 使用主题变量控制颜色
- 保持足够的对比度

### 2. 响应式设计
- 支持xs、sm、md、lg、xl屏幕尺寸
- 使用Grid布局自适应
- 移动端友好的交互

## 性能优化规范

### 1. 数据加载
- 使用loading状态
- 支持分页加载
- 避免一次性加载大量数据

### 2. 渲染优化
- 使用React.memo优化组件
- 合理使用useMemo和useCallback
- 避免不必要的重渲染

## 代码组织规范

### 1. 组件结构
```typescript
const ListPage: React.FC = () => {
  // 1. 状态定义
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  
  // 2. 统计数据
  const stats = [/* 统计信息 */];
  
  // 3. 事件处理
  const handleSearch = () => { /* 搜索处理 */ };
  const handleAdd = () => { /* 新增处理 */ };
  
  // 4. 表格列定义
  const columns = [/* 列定义 */];
  
  // 5. 渲染
  return (
    <div>
      {/* 统计信息区域 */}
      {/* 搜索筛选区域 */}
      {/* 表格区域 */}
    </div>
  );
};
```

### 2. 样式定义
- 使用统一的样式变量
- 支持主题切换
- 保持样式的一致性

## 检查清单

在创建或修改列表页面时，必须检查：

- [ ] 是否包含统计信息区域
- [ ] 是否移除了描述性文字
- [ ] 是否使用了统一的Card样式
- [ ] 是否支持深色模式
- [ ] 是否支持响应式布局
- [ ] 是否实现了实时搜索
- [ ] 是否统一了操作按钮样式
- [ ] 是否优化了表格列设计
- [ ] 是否添加了适当的loading状态
- [ ] 是否遵循了性能优化规范

## 参考页面

- 用户管理页面：`frontend/src/pages/user/UserPage.tsx`
- 角色管理页面：`frontend/src/pages/role/RolePage.tsx`
- API管理页面：`frontend/src/pages/api-management/ApiManagementPage.tsx`

这些页面都遵循了上述规范，可以作为新页面开发的参考模板。

# 依赖注入与服务客户端规范

## 核心原则
- **禁止nil检查**：不要对注入的服务客户端进行nil检查，如 `if s.emailServiceClient == nil`
- **依赖注入必须完整**：所有必需的服务客户端必须在构造函数中注入
- **失败快速原则**：如果依赖缺失，应该在服务启动时就失败，而不是运行时检查

## 实施要求

### 1. 构造函数注入
```go
// ✅ 正确做法 - 在构造函数中注入所有依赖
type VerificationApplicationService struct {
    emailServiceClient *external.EmailServiceClient
    smsServiceClient   *external.SMSServiceClient
    // ... 其他依赖
}

func NewVerificationApplicationService(
    emailServiceClient *external.EmailServiceClient,
    smsServiceClient *external.SMSServiceClient,
    // ... 其他依赖
) *VerificationApplicationService {
    return &VerificationApplicationService{
        emailServiceClient: emailServiceClient,
        smsServiceClient:   smsServiceClient,
        // ... 其他依赖
    }
}
```

### 2. 禁止nil检查
```go
// ❌ 错误做法 - 不要做nil检查
func (s *VerificationApplicationService) validateTemplate(ctx context.Context, tenantID int64, templateCode string, targetType entity.TargetType) error {
    switch targetType {
    case entity.TargetTypeEmail:
        if s.emailServiceClient == nil { // 禁止这种检查
            s.logger.Warn(ctx, "Email service client not configured, skipping email template validation")
            return nil
        }
        // ...
    }
}

// ✅ 正确做法 - 直接使用注入的客户端
func (s *VerificationApplicationService) validateTemplate(ctx context.Context, tenantID int64, templateCode string, targetType entity.TargetType) error {
    switch targetType {
    case entity.TargetTypeEmail:
        exists, err := s.emailServiceClient.CheckTemplateExists(ctx, tenantID, templateCode)
        if err != nil {
            return err
        }
        // ...
    }
}
```

### 3. 启动时验证
- 在服务启动时验证所有必需依赖是否已注入
- 如果依赖缺失，立即失败并记录错误
- 确保运行时不会遇到nil依赖

### 4. 错误处理
- 依赖缺失应该在启动时发现，而不是运行时
- 运行时错误应该来自服务调用失败，而不是依赖缺失
- 记录详细的错误信息以便调试

## 设计原则
- **显式依赖**：所有依赖都应该在构造函数中明确声明
- **不可变性**：注入的依赖在服务生命周期内不应改变
- **单一职责**：每个服务客户端只负责一个外部服务的交互
- **接口隔离**：使用接口定义服务客户端，而不是具体实现