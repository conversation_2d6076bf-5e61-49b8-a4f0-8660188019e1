# API 代理配置说明

## 配置概述

本项目已配置了 API 代理，将前端请求代理到后端服务。

### 代理配置详情

- **代理前缀**: `/api/prompts`
- **目标服务**: `http://localhost:8082`
- **配置位置**: `vite.config.ts`

### 工作原理

1. 前端发起请求到 `/api/prompts/xxx`
2. Vite 开发服务器将请求代理到 `http://localhost:8082/xxx`
3. 代理会自动移除 `/api/prompts` 前缀

## 使用方法

### 1. 启动开发服务器

```bash
npm run dev
```

### 2. API 调用示例

```typescript
import { ApiService } from './src/services/api';

// 获取提示词列表
const getPromptList = async () => {
  try {
    const result = await ApiService.getPromptList({
      pageNum: 1,
      pageSize: 10
    });
    console.log('提示词列表:', result);
  } catch (error) {
    console.error('获取失败:', error);
  }
};

// 创建提示词
const createPrompt = async () => {
  try {
    const newPrompt = await ApiService.createPrompt({
      title: '测试提示词',
      content: '这是一个测试提示词',
      categoryId: 1,
      tags: ['测试', '示例']
    });
    console.log('创建成功:', newPrompt);
  } catch (error) {
    console.error('创建失败:', error);
  }
};
```

### 3. 直接使用 axios

```typescript
import axios from 'axios';

// 直接调用 API
const response = await axios.get('/api/prompts/list?pageNum=1&pageSize=10');
```

## 注意事项

1. **开发环境**: 代理配置仅在开发环境（`npm run dev`）下生效
2. **生产环境**: 生产环境需要配置 nginx 或其他反向代理
3. **端口要求**: 确保后端服务运行在 8082 端口
4. **CORS**: 代理配置解决了开发环境的跨域问题

## 生产环境配置

在生产环境中，需要在 nginx 或其他反向代理中配置类似的代理规则：

```nginx
location /api/prompts/ {
    proxy_pass http://localhost:8082/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 故障排除

1. **代理不生效**: 检查 vite.config.ts 配置是否正确
2. **后端连接失败**: 确认后端服务是否在 8082 端口运行
3. **请求路径错误**: 确认 API 调用路径是否以 `/api/prompts` 开头 