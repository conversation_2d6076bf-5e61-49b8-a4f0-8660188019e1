# 表单错误处理指南

## 概述

已经实现了完善的表单错误处理机制，当注册或登录失败时，会在当前页面显示具体的字段级错误，而不是直接跳转或只显示通用错误。

## 功能特性

### 1. 字段级错误映射
- 服务端返回的字段错误会自动映射到对应的表单字段
- 支持多种错误响应格式
- 自动处理常见的用户存在错误

### 2. 错误显示优先级
1. **字段级错误**: 显示在具体字段下方
2. **通用错误**: 显示在页面顶部的Alert组件中
3. **网络错误**: 显示友好的网络连接错误信息

### 3. 支持的错误格式

#### 格式1: 错误数组
```json
{
  "errors": [
    { "field": "username", "message": "用户名已存在" },
    { "field": "email", "message": "邮箱格式不正确" }
  ]
}
```

#### 格式2: 错误对象
```json
{
  "details": {
    "field_errors": {
      "username": ["用户名太短", "用户名包含非法字符"],
      "password": ["密码强度不足"]
    }
  }
}
```

#### 格式3: 通用消息
```json
{
  "message": "Username already exists"
}
```

## 实现细节

### 错误映射工具函数
```typescript
// src/utils/auth.ts
export const mapApiErrorsToFormFields = (error: any): Array<{ name: string; errors: string[] }> => {
  // 处理不同格式的API错误
  // 返回Ant Design表单字段错误格式
}
```

### 在组件中使用
```typescript
try {
  await register(userData);
  // 注册成功处理
} catch (error: any) {
  // 尝试映射字段错误
  const formFieldErrors = mapApiErrorsToFormFields(error);
  
  if (formFieldErrors.length > 0) {
    // 设置字段错误
    form.setFields(formFieldErrors);
    clearError(); // 清除全局错误
  } else {
    // 显示通用错误
  }
}
```

## 用户体验改进

### 1. 错误显示
- ✅ 字段错误显示在对应字段下方
- ✅ 错误信息本地化（中文）
- ✅ 清晰的错误图标和颜色
- ✅ 可关闭的全局错误提示

### 2. 交互体验
- ✅ 用户输入时自动清除字段错误
- ✅ 不会因为错误而跳转页面
- ✅ 保持用户已输入的表单数据
- ✅ 支持多个字段同时显示错误

### 3. 错误恢复
- ✅ 用户修改字段时自动清除该字段错误
- ✅ 重新提交时清除之前的错误
- ✅ 支持手动关闭全局错误提示

## 测试页面

访问 `/error-test` 页面可以测试不同类型的错误处理：

1. **字段错误数组**: 测试服务端返回的字段错误数组格式
2. **字段错误对象**: 测试服务端返回的字段错误对象格式
3. **用户存在错误**: 测试用户名已存在的错误处理
4. **邮箱存在错误**: 测试邮箱已存在的错误处理

## 常见错误场景

### 注册页面
- ✅ 用户名已存在 → 显示在用户名字段下
- ✅ 邮箱已被注册 → 显示在邮箱字段下
- ✅ 密码强度不足 → 显示在密码字段下
- ✅ 多个字段错误 → 同时显示在对应字段下

### 登录页面
- ✅ 用户名或密码错误 → 显示在对应字段下
- ✅ 账户被锁定 → 显示全局错误
- ✅ 网络连接失败 → 显示全局错误

## 开发指南

### 添加新的错误类型
1. 在 `mapApiErrorsToFormFields` 函数中添加新的错误格式处理
2. 在 `AUTH_ERROR_MESSAGES` 中添加对应的错误消息
3. 在组件中使用统一的错误处理模式

### 自定义错误处理
```typescript
// 在组件中自定义特定错误的处理
catch (error: any) {
  const formFieldErrors = mapApiErrorsToFormFields(error);
  
  if (formFieldErrors.length > 0) {
    form.setFields(formFieldErrors);
    clearError();
  } else if (error.message.includes('特定错误')) {
    // 自定义处理特定错误
    form.setFields([{
      name: 'specificField',
      errors: ['自定义错误消息']
    }]);
  } else {
    // 默认错误处理
  }
}
```

## 总结

新的错误处理机制提供了：
- 🎯 精确的字段级错误显示
- 🚫 避免不必要的页面跳转
- 💡 更好的用户体验
- 🔧 灵活的错误格式支持
- 🧪 完整的测试覆盖

用户现在可以在同一个页面上看到具体的错误信息，并且可以直接修正错误后重新提交，而不需要重新填写整个表单。