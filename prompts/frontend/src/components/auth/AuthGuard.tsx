import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth, useAuthActions } from '../../stores/useAuthStore';
import { AUTH_ROUTES } from '../../constants/auth';
import type { AuthGuardProps } from '../../types/auth';

/**
 * 认证守卫组件 - 保护需要登录的路由
 */
const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  fallback,
  redirectTo = AUTH_ROUTES.LOGIN 
}) => {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useAuth();
  const { checkAuthStatus, initializeAuth } = useAuthActions();

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        await initializeAuth();
        // 如果有token但未认证，尝试验证
        if (!isAuthenticated) {
          await checkAuthStatus();
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
      }
    };

    initAuth();
  }, [initializeAuth, checkAuthStatus, isAuthenticated]);

  // 显示加载状态
  if (isLoading) {
    return (
      fallback || (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh' 
        }}>
          <Spin size="large" tip="验证登录状态..." />
        </div>
      )
    );
  }

  // 未认证时重定向到登录页
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // 已认证，渲染子组件
  return <>{children}</>;
};

export default AuthGuard;