import React from 'react';
import { Dropdown, Avatar, Space, Typography, Button } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useAuth } from '../../stores/useAuthStore';
import { getUserDisplayName } from '../../utils/auth';

const { Text } = Typography;

const UserInfo: React.FC = () => {
  const { user, logout, isLoading } = useAuth();

  if (!user) return null;

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => {
        // 跳转到个人资料页面
        console.log('Navigate to profile');
      },
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => {
        // 跳转到设置页面
        console.log('Navigate to settings');
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown menu={{ items: menuItems }} placement="bottomRight" arrow>
      <Space style={{ cursor: 'pointer', padding: '8px 12px' }}>
        <Avatar 
          size="small" 
          src={user.avatar} 
          icon={<UserOutlined />}
        />
        <Text style={{ color: '#fff' }}>
          {getUserDisplayName(user)}
        </Text>
      </Space>
    </Dropdown>
  );
};

export default UserInfo;