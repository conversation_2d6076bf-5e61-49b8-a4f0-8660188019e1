import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth, useAuthActions } from '../../stores/useAuthStore';
import { AUTH_ROUTES } from '../../constants/auth';
import type { GuestGuardProps } from '../../types/auth';

/**
 * 访客守卫组件 - 已登录用户重定向到首页
 */
const GuestGuard: React.FC<GuestGuardProps> = ({ 
  children, 
  redirectTo = AUTH_ROUTES.HOME 
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const { checkAuthStatus, initializeAuth } = useAuthActions();

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        await initializeAuth();
        // 如果有token，尝试验证
        const token = localStorage.getItem('auth_access_token');
        if (token && !isAuthenticated) {
          await checkAuthStatus();
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
      }
    };

    initAuth();
  }, [initializeAuth, checkAuthStatus, isAuthenticated]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" tip="检查登录状态..." />
      </div>
    );
  }

  // 已认证时重定向到首页
  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }

  // 未认证，渲染子组件（登录/注册页面）
  return <>{children}</>;
};

export default GuestGuard;