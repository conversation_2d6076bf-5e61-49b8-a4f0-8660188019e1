/* 认证页面样式 */

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-content {
  width: 100%;
  max-width: 400px;
  position: relative;
}

.auth-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: none;
  overflow: hidden;
}

.auth-card .ant-card-body {
  padding: 40px 32px;
}

.register-card {
  max-width: 450px;
}

.register-card .ant-card-body {
  padding: 32px;
}

.auth-header {
  text-align: center;
  margin-bottom: 32px;
}

.auth-title {
  margin-bottom: 8px !important;
  color: #262626;
  font-weight: 600;
}

.auth-subtitle {
  font-size: 14px;
  color: #8c8c8c;
}

.auth-form {
  margin-top: 24px;
}

.auth-form .ant-form-item {
  margin-bottom: 20px;
}

.auth-form .ant-input-affix-wrapper,
.auth-form .ant-input {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.auth-form .ant-input-affix-wrapper:hover,
.auth-form .ant-input:hover {
  border-color: #40a9ff;
}

.auth-form .ant-input-affix-wrapper:focus,
.auth-form .ant-input-affix-wrapper-focused,
.auth-form .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.auth-form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}

.auth-submit-btn {
  height: 44px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  transition: all 0.3s;
}

.auth-submit-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.auth-submit-btn:active {
  transform: translateY(0);
}

.auth-link {
  color: #1890ff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.auth-link:hover {
  color: #40a9ff;
  text-decoration: none;
}

.auth-footer {
  text-align: center;
  margin-top: 16px;
}

.auth-error {
  margin-bottom: 20px;
  border-radius: 8px;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: -12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.password-strength-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.password-requirements {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.password-requirement {
  display: flex;
  align-items: center;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    padding: 16px;
  }
  
  .auth-content {
    max-width: 100%;
  }
  
  .auth-card .ant-card-body {
    padding: 24px 20px;
  }
  
  .register-card .ant-card-body {
    padding: 24px 20px;
  }
  
  .auth-header {
    margin-bottom: 24px;
  }
  
  .auth-title {
    font-size: 20px !important;
  }
  
  .password-requirements {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .auth-container {
    padding: 12px;
  }
  
  .auth-card .ant-card-body {
    padding: 20px 16px;
  }
  
  .register-card .ant-card-body {
    padding: 20px 16px;
  }
  
  .auth-title {
    font-size: 18px !important;
  }
  
  .auth-subtitle {
    font-size: 13px;
  }
}

/* 加载状态 */
.auth-form .ant-btn-loading {
  pointer-events: none;
}

/* 表单验证状态 */
.auth-form .ant-form-item-has-error .ant-input-affix-wrapper,
.auth-form .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f;
}

.auth-form .ant-form-item-has-error .ant-input-affix-wrapper:hover,
.auth-form .ant-form-item-has-error .ant-input:hover {
  border-color: #ff7875;
}

.auth-form .ant-form-item-has-success .ant-input-affix-wrapper,
.auth-form .ant-form-item-has-success .ant-input {
  border-color: #52c41a;
}

/* 分割线样式 */
.auth-card .ant-divider {
  margin: 20px 0 16px 0;
  color: #8c8c8c;
  font-size: 13px;
}

/* 复选框样式 */
.auth-form .ant-checkbox-wrapper {
  font-size: 14px;
  color: #595959;
}

.auth-form .ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #1890ff;
}

/* 进度条样式 */
.password-strength .ant-progress-line {
  margin-right: 0;
}

.password-strength .ant-progress-bg {
  border-radius: 2px;
}

/* 图标样式 */
.auth-form .anticon {
  color: #8c8c8c;
}

.auth-form .ant-input-affix-wrapper:focus .anticon,
.auth-form .ant-input-affix-wrapper-focused .anticon {
  color: #1890ff;
}

/* 动画效果 */
.auth-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 错误提示动画 */
.auth-error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}