import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Space, 
  Progress,
  message
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  LockOutlined, 
  EyeInvisibleOutlined, 
  EyeTwoTone,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../../stores/useAuthStore';
import { register } from '../../services/auth';
import type { RegisterFormData } from '../../types/auth';
import { handleRegisterError, applyAuthErrorResult } from '../../utils/authErrorHandler';
import { 
  checkPasswordStrength,
  getPasswordStrengthColor,
  getPasswordStrengthLabel
} from '../../utils/auth';
import { AUTH_ROUTES } from '../../constants/auth';
import './AuthPages.css';

const { Title, Text } = Typography;

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  const [form] = Form.useForm();
  const [passwordStrength, setPasswordStrength] = useState<any>(null);
  const [confirmPasswordStatus, setConfirmPasswordStatus] = useState<'success' | 'error' | ''>('');

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated) {
      navigate(AUTH_ROUTES.HOME, { replace: true });
    }
  }, [isAuthenticated, navigate]);

  const onSubmit = async (values: RegisterFormData) => {
    try {
      // 执行注册API调用
      const response = await register({
        username: values.username.trim(),
        email: values.email.trim(),
        password: values.password,
      });

      // 处理响应结果
      const errorResult = handleRegisterError(response, form);
      
      if (errorResult.shouldRedirect) {
        message.success('注册成功！请登录您的账户。');
        navigate(errorResult.redirectPath!, { 
          state: { 
            message: '注册成功，请使用您的账户信息登录',
            username: values.username 
          } 
        });
      } else {
        // 应用错误处理
        applyAuthErrorResult(errorResult, form);
      }
      
    } catch (error: any) {
      console.error('Register error:', error);
      message.error('注册失败，请稍后重试');
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    
    // 清除密码字段的错误
    form.setFields([{ name: 'password', errors: [] }]);
    
    // 检查密码强度
    if (password) {
      const strength = checkPasswordStrength(password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(null);
    }

    // 检查确认密码
    const confirmPassword = form.getFieldValue('confirmPassword');
    if (confirmPassword) {
      validateConfirmPassword(password, confirmPassword);
    }
  };

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const confirmPassword = e.target.value;
    const password = form.getFieldValue('password');
    
    // 清除确认密码字段的错误
    form.setFields([{ name: 'confirmPassword', errors: [] }]);
    
    validateConfirmPassword(password, confirmPassword);
  };

  const validateConfirmPassword = (password: string, confirmPassword: string) => {
    if (confirmPassword) {
      if (password === confirmPassword) {
        setConfirmPasswordStatus('success');
      } else {
        setConfirmPasswordStatus('error');
      }
    } else {
      setConfirmPasswordStatus('');
    }
  };

  const handleFieldChange = (field: string) => {
    // 清除对应字段的错误
    form.setFields([{ name: field, errors: [] }]);
  };

  const renderPasswordStrength = () => {
    if (!passwordStrength) return null;

    const { strength, score, feedback, requirements } = passwordStrength;
    const color = getPasswordStrengthColor(strength);
    const label = getPasswordStrengthLabel(strength);
    const percent = ((score + 1) / 4) * 100;

    return (
      <div className="password-strength">
        <div className="password-strength-bar">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            密码强度：
          </Text>
          <Progress
            percent={percent}
            strokeColor={color}
            showInfo={false}
            size="small"
            style={{ flex: 1, marginLeft: 8, marginRight: 8 }}
          />
          <Text style={{ color, fontSize: '12px', fontWeight: 500 }}>
            {label}
          </Text>
        </div>
        
        <div className="password-requirements">
          {Object.entries(requirements).map(([key, met]) => {
            const labels = {
              minLength: '至少8位字符',
              hasLetter: '包含字母',
              hasNumber: '包含数字',
              hasSpecialChar: '包含特殊字符',
            };
            
            return (
              <div key={key} className="password-requirement">
                {met ? (
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
                ) : (
                  <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '12px' }} />
                )}
                <Text 
                  style={{ 
                    fontSize: '12px', 
                    marginLeft: 4,
                    color: met ? '#52c41a' : '#ff4d4f' 
                  }}
                >
                  {labels[key as keyof typeof labels]}
                </Text>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="auth-container">
      <div className="auth-content">
        <Card className="auth-card register-card">
          <div className="auth-header">
            <Title level={2} className="auth-title">
              注册 Prompts 账户
            </Title>
            <Text type="secondary" className="auth-subtitle">
              创建您的账户，开始使用 Prompts
            </Text>
          </div>

          <Form
            form={form}
            name="register"
            onFinish={onSubmit}
            autoComplete="off"
            size="large"
            className="auth-form"
            scrollToFirstError
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3位字符' },
                { max: 20, message: '用户名最多20位字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名（3-20位，字母数字下划线）"
                onChange={() => handleFieldChange('username')}
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="邮箱地址"
                onChange={() => handleFieldChange('email')}
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 8, message: '密码至少8位字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码（至少8位，包含字母和数字）"
                onChange={handlePasswordChange}
                autoComplete="new-password"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            {renderPasswordStrength()}

            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="确认密码"
                onChange={handleConfirmPasswordChange}
                autoComplete="new-password"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                suffix={
                  confirmPasswordStatus === 'success' ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : confirmPasswordStatus === 'error' ? (
                    <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
                  ) : null
                }
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={false}
                block
                className="auth-submit-btn"
              >
                注册账户
              </Button>
            </Form.Item>
          </Form>

          <div className="auth-footer">
            <Space>
              <Text type="secondary">已有账户？</Text>
              <Link to={AUTH_ROUTES.LOGIN} className="auth-link">
                立即登录
              </Link>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RegisterPage;