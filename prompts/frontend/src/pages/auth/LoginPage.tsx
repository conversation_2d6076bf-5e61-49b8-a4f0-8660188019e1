import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { 
  Form, 
  Input, 
  Button, 
  Checkbox, 
  Card, 
  Typography, 
  Space, 
  message
} from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { useAuth, useAuthActions } from '../../stores/useAuthStore';
import { login } from '../../services/auth';
import type { LoginFormData } from '../../types/auth';
import { handleLoginError, applyAuthErrorResult } from '../../utils/authErrorHandler';
import { AUTH_ROUTES } from '../../constants/auth';
import './AuthPages.css';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { setError } = useAuthActions();
  
  const [form] = Form.useForm();
  const [rememberMe, setRememberMe] = useState(false);

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || AUTH_ROUTES.HOME;

  // 如果已经登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  const onSubmit = async (values: LoginFormData) => {
    try {
      // 执行登录API调用
      const response = await login({
        username: values.username.trim(),
        password: values.password,
      });

      // 处理响应结果
      const errorResult = handleLoginError(response, form);
      
      if (errorResult.shouldRedirect) {
        // 保存记住我状态
        if (values.remember) {
          localStorage.setItem('auth_remember_me', 'true');
        } else {
          localStorage.removeItem('auth_remember_me');
        }
        
        message.success('登录成功！');
        navigate(errorResult.redirectPath!, { replace: true });
      } else {
        // 应用错误处理
        applyAuthErrorResult(errorResult, form);
      }
      
    } catch (error: any) {
      console.error('Login error:', error);
      message.error('登录失败，请稍后重试');
    }
  };

  const handleFieldChange = (field: string) => {
    // 清除对应字段的错误
    form.setFields([{ name: field, errors: [] }]);
  };

  return (
    <div className="auth-container">
      <div className="auth-content">
        <Card className="auth-card">
          <div className="auth-header">
            <Title level={2} className="auth-title">
              登录到 Prompts
            </Title>
            <Text type="secondary" className="auth-subtitle">
              欢迎回来，请输入您的账户信息
            </Text>
          </div>

          <Form
            form={form}
            name="login"
            onFinish={onSubmit}
            autoComplete="off"
            size="large"
            className="auth-form"
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名或邮箱' },
                { min: 3, message: '用户名至少3位字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名或邮箱"
                onChange={() => handleFieldChange('username')}
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                onChange={() => handleFieldChange('password')}
                autoComplete="current-password"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item>
              <div className="auth-form-options">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox 
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  >
                    记住我
                  </Checkbox>
                </Form.Item>
                <Link to="/forgot-password" className="auth-link">
                  忘记密码？
                </Link>
              </div>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={false}
                block
                className="auth-submit-btn"
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          <div className="auth-footer">
            <Space>
              <Text type="secondary">没有账户？</Text>
              <Link to={AUTH_ROUTES.REGISTER} className="auth-link">
                立即注册
              </Link>
            </Space>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default LoginPage;