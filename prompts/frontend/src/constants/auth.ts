// Authentication related constants

// Local storage keys
export const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'auth_access_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  USER: 'auth_user',
  REMEMBER_ME: 'auth_remember_me',
} as const;

// API endpoints for users service
export const AUTH_ENDPOINTS = {
  LOGIN: '/api/user/auth/login',
  REGISTER: '/api/user/auth/register',
  LOGOUT: '/api/user/auth/logout',
  REFRESH: '/api/user/auth/refresh',
  ME: '/api/user/me/get',
  FORGOT_PASSWORD: '/api/user/auth/forgot-password',
  RESET_PASSWORD: '/api/user/auth/reset-password',
} as const;

// Form validation constants
export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/,
    MESSAGE: '用户名只能包含字母、数字和下划线，长度3-20位',
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MESSAGE: '请输入有效的邮箱地址',
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    PATTERN: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]/,
    MESSAGE: '密码至少8位，包含字母和数字',
  },
} as const;

// Password strength configuration
export const PASSWORD_STRENGTH = {
  WEAK: {
    score: 0,
    label: '弱',
    color: '#ff4d4f',
    requirements: 1,
  },
  MEDIUM: {
    score: 1,
    label: '中等',
    color: '#faad14',
    requirements: 2,
  },
  STRONG: {
    score: 2,
    label: '强',
    color: '#52c41a',
    requirements: 3,
  },
  VERY_STRONG: {
    score: 3,
    label: '很强',
    color: '#1890ff',
    requirements: 4,
  },
} as const;

// Authentication error messages
export const AUTH_ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  INVALID_CREDENTIALS: '用户名或密码错误',
  USER_EXISTS: '用户名或邮箱已存在',
  WEAK_PASSWORD: '密码强度不足，请使用更复杂的密码',
  PASSWORDS_NOT_MATCH: '两次输入的密码不一致',
  REQUIRED_FIELD: '此字段为必填项',
  INVALID_EMAIL: '邮箱格式不正确',
  INVALID_USERNAME: '用户名格式不正确',
  TOKEN_EXPIRED: '登录已过期，请重新登录',
  ACCOUNT_LOCKED: '账户已被锁定，请联系管理员',
  ACCOUNT_DISABLED: '账户已被禁用，请联系管理员',
  TOO_MANY_ATTEMPTS: '登录尝试次数过多，请稍后重试',
} as const;

// Route paths
export const AUTH_ROUTES = {
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',
  HOME: '/',
  DASHBOARD: '/dashboard',
} as const;

// Token configuration
export const TOKEN_CONFIG = {
  REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes before expiry
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// Session configuration
export const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  WARNING_TIMEOUT: 5 * 60 * 1000, // 5 minutes before idle timeout
  CHECK_INTERVAL: 60 * 1000, // Check every minute
} as const;

// Form field names
export const FORM_FIELDS = {
  USERNAME: 'username',
  EMAIL: 'email',
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
  REMEMBER_ME: 'remember',
} as const;

// Authentication events for analytics
export const AUTH_EVENTS = {
  LOGIN_ATTEMPT: 'auth_login_attempt',
  LOGIN_SUCCESS: 'auth_login_success',
  LOGIN_FAILURE: 'auth_login_failure',
  REGISTER_ATTEMPT: 'auth_register_attempt',
  REGISTER_SUCCESS: 'auth_register_success',
  REGISTER_FAILURE: 'auth_register_failure',
  LOGOUT: 'auth_logout',
  TOKEN_REFRESH: 'auth_token_refresh',
  SESSION_EXPIRED: 'auth_session_expired',
} as const;

// Default redirect paths after authentication
export const DEFAULT_REDIRECTS = {
  AFTER_LOGIN: '/',
  AFTER_LOGOUT: '/login',
  AFTER_REGISTER: '/login',
  UNAUTHORIZED: '/login',
} as const;

// User status constants
export const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  LOCKED: 'locked',
  DISABLED: 'disabled',
} as const;

// Third-party providers (for future use)
export const THIRD_PARTY_PROVIDERS = {
  GOOGLE: 'google',
  GITHUB: 'github',
  WECHAT: 'wechat',
  DINGTALK: 'dingtalk',
} as const;