import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import type { ApiResponse } from '../types/api';

// ==================== API配置 ====================

// API配置
export const API_CONFIG = {
  // 基础配置
  BASE_URL: '', // 开发环境下必须为空，所有请求都通过代理转发
  
  // API前缀配置
  PREFIXES: {
    // 全局API前缀
    GLOBAL: '/api/prompts',
    
    // 各模块API前缀
    AUTH: '/api/user/auth',
    PROMPTS: '/api/prompts',
    CATEGORIES: '/api/prompts/categories',
    TAGS: '/api/prompts/tags',
    FAVORITES: '/api/prompts/favorites',
    SHARE: '/api/prompts/share',
    ANALYTICS: '/api/prompts/analytics',
    VERSION: '/api/prompts/version',
  },
  
  // 超时配置
  TIMEOUT: 10000,
  
  // 重试配置
  RETRY: {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
  },
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
} as const;

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: `${API_CONFIG.PREFIXES.AUTH}/login`,
    REGISTER: `${API_CONFIG.PREFIXES.AUTH}/register`,
    REFRESH: `${API_CONFIG.PREFIXES.AUTH}/refresh`,
    LOGOUT: `${API_CONFIG.PREFIXES.AUTH}/logout`,
    ME: `${API_CONFIG.PREFIXES.AUTH}/me`,
  },
  
  // 提示词相关
  PROMPTS: {
    CREATE: `${API_CONFIG.PREFIXES.PROMPTS}/create`,
    UPDATE: `${API_CONFIG.PREFIXES.PROMPTS}/update`,
    DELETE: `${API_CONFIG.PREFIXES.PROMPTS}/delete`,
    GET: `${API_CONFIG.PREFIXES.PROMPTS}/get`,
    LIST: `${API_CONFIG.PREFIXES.PROMPTS}/list`,
    SEARCH: `${API_CONFIG.PREFIXES.PROMPTS}/search`,
    PUBLIC: `${API_CONFIG.PREFIXES.PROMPTS}/public`,
    USE: `${API_CONFIG.PREFIXES.PROMPTS}/use`,
    COPY: `${API_CONFIG.PREFIXES.PROMPTS}/copy`,
    DRAFT_SAVE: `${API_CONFIG.PREFIXES.PROMPTS}/draft/save`,
    DRAFT_UPDATE: `${API_CONFIG.PREFIXES.PROMPTS}/draft/update`,
    PUBLISH: `${API_CONFIG.PREFIXES.PROMPTS}/publish`,
  },
  
  // 分类相关
  CATEGORIES: {
    CREATE: `${API_CONFIG.PREFIXES.CATEGORIES}/create`,
    UPDATE: `${API_CONFIG.PREFIXES.CATEGORIES}/update`,
    DELETE: `${API_CONFIG.PREFIXES.CATEGORIES}/delete`,
    GET: `${API_CONFIG.PREFIXES.CATEGORIES}/get`,
    LIST: `${API_CONFIG.PREFIXES.CATEGORIES}/list`,
    TREE: `${API_CONFIG.PREFIXES.CATEGORIES}/tree`,
    GLOBAL: `${API_CONFIG.PREFIXES.CATEGORIES}/global`,
    SORT: `${API_CONFIG.PREFIXES.CATEGORIES}/sort`,
  },
  
  // 标签相关
  TAGS: {
    CREATE: `${API_CONFIG.PREFIXES.TAGS}/create`,
    UPDATE: `${API_CONFIG.PREFIXES.TAGS}/update`,
    DELETE: `${API_CONFIG.PREFIXES.TAGS}/delete`,
    GET: `${API_CONFIG.PREFIXES.TAGS}/get`,
    LIST: `${API_CONFIG.PREFIXES.TAGS}/list`,
    SEARCH: `${API_CONFIG.PREFIXES.TAGS}/search`,
    GLOBAL: `${API_CONFIG.PREFIXES.TAGS}/global`,
    POPULAR: `${API_CONFIG.PREFIXES.TAGS}/popular`,
  },
  
  // 收藏相关
  FAVORITES: {
    ADD: `${API_CONFIG.PREFIXES.FAVORITES}/add`,
    REMOVE: `${API_CONFIG.PREFIXES.FAVORITES}/remove`,
    LIST: `${API_CONFIG.PREFIXES.FAVORITES}/list`,
    FOLDER_CREATE: `${API_CONFIG.PREFIXES.FAVORITES}/folder/create`,
  },
  
  // 分享相关
  SHARE: {
    CREATE: `${API_CONFIG.PREFIXES.SHARE}/create`,
    DETAIL: `${API_CONFIG.PREFIXES.SHARE}/detail`,
  },
  
  // 统计分析
  ANALYTICS: {
    PERSONAL: `${API_CONFIG.PREFIXES.ANALYTICS}/personal`,
    TRENDING: `${API_CONFIG.PREFIXES.ANALYTICS}/trending`,
  },
  
  // 版本管理
  VERSION: {
    HISTORY: `${API_CONFIG.PREFIXES.VERSION}/history`,
    COMPARE: `${API_CONFIG.PREFIXES.VERSION}/compare`,
    ROLLBACK: `${API_CONFIG.PREFIXES.VERSION}/rollback`,
  },
} as const;

// 环境配置
export const ENV_CONFIG = {
  // 开发环境
  DEVELOPMENT: {
    BASE_URL: '',
    API_PREFIX: '/api/prompts',
  },
  
  // 生产环境
  PRODUCTION: {
    BASE_URL: 'https://api.example.com',
    API_PREFIX: '/api/prompts',
  },
  
  // 测试环境
  TEST: {
    BASE_URL: 'http://test-api.example.com',
    API_PREFIX: '/api/prompts',
  },
} as const;

// 获取当前环境配置
export const getCurrentEnvConfig = () => {
  // 在浏览器环境中，根据当前URL判断环境
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
  
  if (isProduction) {
    return ENV_CONFIG.PRODUCTION;
  } else if (window.location.hostname.includes('test')) {
    return ENV_CONFIG.TEST;
  } else {
    return ENV_CONFIG.DEVELOPMENT;
  }
};

// 构建完整API URL
export const buildApiUrl = (endpoint: string): string => {
  // 在开发环境下，直接返回endpoint，让setupProxy.js处理代理
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  if (isLocalhost) {
    return endpoint;
  }
  
  // 生产环境下构建完整URL
  const envConfig = getCurrentEnvConfig();
  return `${envConfig.BASE_URL}${endpoint}`;
};

// 导出类型
export type ApiEndpoint = typeof API_ENDPOINTS;
export type ApiConfig = typeof API_CONFIG;

// ==================== TOKEN管理 ====================

// 获取访问令牌（优先从sessionStorage，降级到localStorage）
export const getAccessToken = (): string | null => {
  try {
    // 优先从sessionStorage获取（更安全）
    let token = sessionStorage.getItem('access_token');
    if (token) {
      return token;
    }
    
    // 降级到localStorage
    token = localStorage.getItem('access_token');
    return token;
  } catch (error) {
    console.error('Failed to get access token:', error);
    return null;
  }
};

// 清除认证信息
export const clearAuth = (): void => {
  try {
    // 清除sessionStorage
    sessionStorage.removeItem('access_token');
    sessionStorage.removeItem('refresh_token');
    sessionStorage.removeItem('user_info');
    sessionStorage.removeItem('token_expiry');
    
    // 清除localStorage
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('tenant_id');
  } catch (error) {
    console.error('Failed to clear auth:', error);
  }
};

// 获取全局未授权处理器（延迟导入避免循环依赖）
let globalUnauthorizedHandler: (() => void) | null = null;

export const setGlobalUnauthorizedHandler = (handler: () => void) => {
  globalUnauthorizedHandler = handler;
};

export const getGlobalUnauthorizedHandler = () => {
  return globalUnauthorizedHandler;
};

// ==================== HTTP客户端配置 ====================

/**
 * 获取baseURL配置
 * 开发环境：使用空字符串，通过setupProxy.js代理
 * 生产环境：使用环境变量或默认URL
 */
const getBaseURL = (): string => {
    const envConfig = getCurrentEnvConfig();
    
    // 开发环境下强制使用空字符串，确保走setupProxy.js代理
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    if (isLocalhost) {
        return ''; // 开发环境必须为空，所有请求都通过setupProxy.js代理转发
    }
    
    // 生产环境使用配置的URL
    return envConfig.BASE_URL || 'http://localhost:8080';
};

/**
 * 请求配置
 */
const config: AxiosRequestConfig = {
    baseURL: getBaseURL(),
    timeout: API_CONFIG.TIMEOUT,
    headers: {
        ...API_CONFIG.HEADERS,
    },
};

/**
 * 创建axios实例
 */
const instance: AxiosInstance = axios.create(config);

/**
 * 请求拦截器
 */
instance.interceptors.request.use(
    (config) => {
        // 添加认证token
        const token = getAccessToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        // 添加租户代码 - 全局设置
        config.headers['X-Tenant-Code'] = 'prompts';

        // 添加租户ID（如果存在）
        const tenantId = localStorage.getItem('tenant_id');
        if (tenantId) {
            config.headers['X-Tenant-ID'] = tenantId;
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = generateRequestId();

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

/**
 * 响应拦截器
 */
instance.interceptors.response.use(
    (response: AxiosResponse) => {
        // 直接返回响应数据，保持原有的响应结构
        return response;
    },
    (error) => {
        // 处理401未授权错误和403权限不足错误
        if (error.response?.status === 401 || error.response?.status === 403) {
            const unauthorizedHandler = getGlobalUnauthorizedHandler();
            if (unauthorizedHandler) {
                // 使用React Router进行导航，保持路由状态
                unauthorizedHandler();
            } else {
                // 如果没有设置处理器，使用备用方案
                clearAuth();
                window.location.href = '/login';
            }
            return Promise.reject(error);
        }

        // 处理其他网络错误，但跳过404错误（由具体服务处理）
        if (error.response?.status !== 404) {
          handleNetworkError(error);
        }
        return Promise.reject(error);
    }
);

/**
 * 处理网络错误
 */
const handleNetworkError = (error: any) => {
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        message.error('请求超时，请稍后重试');
    } else if (error.response) {
        const {status} = error.response;
        switch (status) {
            case 401:
                message.error('登录已过期，请重新登录');
                clearAuth();
                window.location.href = '/login';
                break;
            case 403:
                message.error('没有权限访问');
                break;
            case 404:
                message.error('请求的资源不存在');
                break;
            case 500:
                message.error('服务器内部错误');
                break;
            default:
                message.error('网络错误，请稍后重试');
                break;
        }
    } else {
        message.error('网络连接失败，请检查网络设置');
    }
};

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// ==================== 导出BASIC HTTP方法 ====================

/**
 * 基础请求方法封装（兼容原有request对象）
 */
export const request = {
    get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.get(url, config);
    },

    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.post(url, data, config);
    },

    put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.put(url, data, config);
    },

    delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.delete(url, config);
    },

    patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.patch(url, data, config);
    },
};

// ==================== API服务方法 ====================

/**
 * 类型安全的 API 服务方法，自动解包 data 字段，返回业务数据，遇到 code!=0 自动抛出错误
 */
export const apiService = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.get<ApiResponse<T>>(url, config).then(res => res.data);
  },
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.post<ApiResponse<T>>(url, data, config).then(res => res.data);
  },
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.put<ApiResponse<T>>(url, data, config).then(res => res.data);
  },
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.delete<ApiResponse<T>>(url, config).then(res => res.data);
  },
  /**
   * 通用 request 方法，支持 method 选择
   */
  async request<T = any>(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST', data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const url = buildApiUrl(endpoint);
    let res: AxiosResponse<ApiResponse<T>>;
    switch (method) {
      case 'GET':
        res = await instance.get<ApiResponse<T>>(url, config);
        break;
      case 'POST':
        res = await instance.post<ApiResponse<T>>(url, data, config);
        break;
      case 'PUT':
        res = await instance.put<ApiResponse<T>>(url, data, config);
        break;
      case 'DELETE':
        res = await instance.delete<ApiResponse<T>>(url, config);
        break;
      default:
        res = await instance.post<ApiResponse<T>>(url, data, config);
    }
    return res.data;
  },
};

// ==================== 导出 ====================

// 导出axios实例（用于特殊情况）
export { instance as api };

// 导出类型
export type { AxiosInstance, AxiosRequestConfig, AxiosResponse };

// 默认导出
export default request; 