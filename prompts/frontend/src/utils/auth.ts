import { AUTH_ERROR_MESSAGES } from '../constants/auth';

// 密码强度类型定义
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong';

// 密码强度结果
export interface PasswordStrengthResult {
  strength: PasswordStrength;
  score: number;
  feedback: string;
  requirements: {
    minLength: boolean;
    hasLetter: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
}

// 字段错误类型
export interface FieldError {
  field: string;
  message: string;
  value?: any;
}

/**
 * Validate username format
 */
export const validateUsername = (username: string): string | null => {
  if (!username) {
    return AUTH_ERROR_MESSAGES.REQUIRED_FIELD;
  }
  
  if (username.length < 3) {
    return '用户名至少3位字符';
  }
  
  if (username.length > 20) {
    return '用户名最多20位字符';
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return '用户名只能包含字母、数字和下划线';
  }
  
  return null;
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): string | null => {
  if (!email) {
    return AUTH_ERROR_MESSAGES.REQUIRED_FIELD;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return '请输入有效的邮箱地址';
  }
  
  return null;
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): string | null => {
  if (!password) {
    return AUTH_ERROR_MESSAGES.REQUIRED_FIELD;
  }
  
  if (password.length < 8) {
    return '密码至少8位字符';
  }
  
  if (password.length > 32) {
    return '密码最多32位字符';
  }
  
  if (!/[a-zA-Z]/.test(password)) {
    return '密码必须包含字母';
  }
  
  if (!/\d/.test(password)) {
    return '密码必须包含数字';
  }
  
  return null;
};

/**
 * Validate password confirmation
 */
export const validatePasswordConfirm = (password: string, confirmPassword: string): string | null => {
  if (!confirmPassword) {
    return AUTH_ERROR_MESSAGES.REQUIRED_FIELD;
  }
  
  if (password !== confirmPassword) {
    return '两次输入的密码不一致';
  }
  
  return null;
};

/**
 * Check password strength
 */
export const checkPasswordStrength = (password: string): PasswordStrengthResult => {
  let score = 0;
  const requirements = {
    minLength: password.length >= 8,
    hasLetter: /[a-zA-Z]/.test(password),
    hasNumber: /\d/.test(password),
    hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };
  
  // 基础分数
  if (requirements.minLength) score++;
  if (requirements.hasLetter) score++;
  if (requirements.hasNumber) score++;
  if (requirements.hasSpecialChar) score++;
  
  // 额外分数
  if (password.length >= 12) score++;
  if (/[A-Z]/.test(password) && /[a-z]/.test(password)) score++;
  if (/\d{2,}/.test(password)) score++;
  
  // 确定强度等级
  let strength: PasswordStrength;
  let feedback: string;
  
  if (score <= 2) {
    strength = 'weak';
    feedback = '密码强度较弱，建议使用更复杂的密码';
  } else if (score <= 4) {
    strength = 'fair';
    feedback = '密码强度一般，建议增加特殊字符';
  } else if (score <= 6) {
    strength = 'good';
    feedback = '密码强度良好';
  } else {
    strength = 'strong';
    feedback = '密码强度很强';
  }
  
  return {
    strength,
    score,
    feedback,
    requirements,
  };
};

/**
 * Get password strength color
 */
export const getPasswordStrengthColor = (strength: PasswordStrength): string => {
  switch (strength) {
    case 'weak':
      return '#ff4d4f';
    case 'fair':
      return '#faad14';
    case 'good':
      return '#52c41a';
    case 'strong':
      return '#1890ff';
    default:
      return '#d9d9d9';
  }
};

/**
 * Get password strength label
 */
export const getPasswordStrengthLabel = (strength: PasswordStrength): string => {
  switch (strength) {
    case 'weak':
      return '弱';
    case 'fair':
      return '一般';
    case 'good':
      return '良好';
    case 'strong':
      return '强';
    default:
      return '未知';
  }
};

/**
 * Check if JWT token is expired
 */
export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch (error) {
    return true;
  }
};

/**
 * Get token expiration time
 */
export const getTokenExpiration = (token: string): number | null => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000;
  } catch (error) {
    return null;
  }
};

/**
 * Check if token needs refresh (within threshold)
 */
export const shouldRefreshToken = (token: string, thresholdMs: number = 5 * 60 * 1000): boolean => {
  const expiration = getTokenExpiration(token);
  if (!expiration) return true;
  
  const currentTime = Date.now();
  return (expiration - currentTime) <= thresholdMs;
};

/**
 * Get user display name
 */
export const getUserDisplayName = (user: { username: string; real_name?: string; email?: string }): string => {
  return user.real_name || user.username || user.email || '未知用户';
};

/**
 * Generate secure random string for state parameter
 */
export const generateSecureState = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Sanitize user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Debounce function for validation
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};