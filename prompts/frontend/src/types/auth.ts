// Authentication related types for users service integration

// User model from users service
export interface User {
  id: number;
  username: string;
  email: string;
  real_name?: string;
  avatar?: string;
  status: 'active' | 'inactive' | 'locked' | 'disabled';
  created_at: string;
  updated_at: string;
}

// Login request payload
export interface LoginRequest {
  username: string; // Can be username or email
  password: string;
}

// Registration request payload
export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  real_name?: string;
}

// Authentication response from users service
export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: User;
}

// Token refresh request
export interface RefreshTokenRequest {
  refresh_token: string;
}

// Logout request
export interface LogoutRequest {
  refresh_token?: string;
}

// Authentication state for Zustand store
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Authentication actions for Zustand store
export interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  setUser: (user: User) => void;
  setTokens: (accessToken: string, refreshToken: string) => void;
  clearAuth: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  initializeAuth: () => Promise<void>;
  scheduleTokenRefresh: () => void;
  checkAuthStatus: () => Promise<boolean>;
  getCurrentUser: () => Promise<User>;
}

// Combined auth store type
export interface AuthStore extends AuthState, AuthActions {}

// Form validation error types
export interface FieldError {
  field: string;
  message: string;
}

export interface ValidationErrors {
  [key: string]: string[];
}

// API error response structure
export interface AuthApiError {
  code: number;
  message: string;
  errors?: FieldError[];
  details?: {
    field_errors?: ValidationErrors;
    general_error?: string;
  };
}

// Form field validation rules
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => Promise<void> | void;
  message?: string;
}

export interface FormValidationRules {
  username: ValidationRule[];
  email: ValidationRule[];
  password: ValidationRule[];
  confirmPassword: ValidationRule[];
}

// Password strength levels
export type PasswordStrength = 'weak' | 'medium' | 'strong' | 'very-strong';

export interface PasswordStrengthResult {
  strength: PasswordStrength;
  score: number;
  feedback: string[];
  requirements: {
    minLength: boolean;
    hasLetter: boolean;
    hasNumber: boolean;
    hasSpecialChar: boolean;
  };
}

// Authentication form states
export interface LoginFormData {
  username: string;
  password: string;
  remember?: boolean;
}

export interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Route protection types
export interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export interface GuestGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

// Session management types
export interface SessionInfo {
  id: string;
  user_id: number;
  device: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  last_active: string;
  is_current: boolean;
}

// Third-party authentication (for future use)
export interface ThirdPartyProvider {
  name: string;
  display_name: string;
  icon: string;
  auth_url: string;
  client_id: string;
}

export interface ThirdPartyAuthRequest {
  provider: string;
  code: string;
  state?: string;
}

// MFA types (for future use)
export interface MFALoginRequest extends LoginRequest {
  mfa_code: string;
}

export interface MFASetupResponse {
  secret: string;
  qr_code: string;
  backup_codes: string[];
}

// Forgot password types (for future use)
export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

// Authentication event types for logging
export type AuthEventType = 
  | 'login_attempt'
  | 'login_success' 
  | 'login_failure'
  | 'register_attempt'
  | 'register_success'
  | 'register_failure'
  | 'logout'
  | 'token_refresh'
  | 'session_expired';

export interface AuthEvent {
  type: AuthEventType;
  timestamp: string;
  user_id?: number;
  ip_address?: string;
  user_agent?: string;
  details?: Record<string, any>;
}