import React from 'react';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  User, 
  LoginRequest, 
  RegisterRequest, 
  AuthState, 
  AuthActions,
  AuthStore 
} from '../types/auth';
import { login, register, logout, refreshToken, getCurrentUser, validateToken } from '../services/auth';
import { AUTH_STORAGE_KEYS, TOKEN_CONFIG } from '../constants/auth';
import { isTokenExpired, shouldRefreshToken } from '../utils/auth';
// 兼容旧的ApiError类型
interface ApiError extends Error {
  code?: number;
  response?: any;
  errors?: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  details?: {
    field_errors?: Record<string, string | string[]>;
  };
  isNetworkError?: boolean;
  isAuthError?: boolean;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        ...initialState,

        // 基础Actions
        setUser: (user: User) => {
          set({ user, isAuthenticated: true });
        },

        setTokens: (accessToken: string, refreshToken: string) => {
          set({ 
            accessToken, 
            refreshToken, 
            isAuthenticated: true,
            error: null 
          });
          
          // 存储到localStorage
          localStorage.setItem(AUTH_STORAGE_KEYS.ACCESS_TOKEN, accessToken);
          localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
        },

        clearAuth: () => {
          set(initialState);
          
          // 清除localStorage
          localStorage.removeItem(AUTH_STORAGE_KEYS.ACCESS_TOKEN);
          localStorage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
          localStorage.removeItem(AUTH_STORAGE_KEYS.USER);
          localStorage.removeItem(AUTH_STORAGE_KEYS.REMEMBER_ME);
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error: string | null) => {
          set({ error });
        },

        // 登录
        login: async (credentials: LoginRequest) => {
          try {
            set({ isLoading: true, error: null });
            
            console.log('Starting login process...');
            const authResponse = await login(credentials);
            
            // 更新状态
            set({
              user: authResponse.data.user,
              accessToken: authResponse.data.access_token,
              refreshToken: authResponse.data.refresh_token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            
            // 存储tokens
            localStorage.setItem(AUTH_STORAGE_KEYS.ACCESS_TOKEN, authResponse.data.access_token);
            localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, authResponse.data.refresh_token);
            localStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(authResponse.data.user));
            
            console.log('Login successful, user:', authResponse.data.user.username);
            
            // 设置自动刷新token
            get().scheduleTokenRefresh();
            
          } catch (error) {
            console.error('Login error:', error);
            const apiError = error as ApiError;
            const errorMessage = apiError.message || '登录失败';
            set({ 
              isLoading: false, 
              error: errorMessage,
              isAuthenticated: false 
            });
            throw error; // 重新抛出ApiError，让组件处理
          }
        },

        // 注册
        register: async (userData: RegisterRequest) => {
          try {
            set({ isLoading: true, error: null });
            
            console.log('Starting registration process...');
            await register(userData);
            
            set({ isLoading: false, error: null });
            console.log('Registration successful');
            
          } catch (error) {
            console.error('Registration error:', error);
            const apiError = error as ApiError;
            const errorMessage = apiError.message || '注册失败';
            set({ 
              isLoading: false, 
              error: errorMessage 
            });
            throw error; // 重新抛出ApiError，让组件处理
          }
        },

        // 登出
        logout: async () => {
          try {
            set({ isLoading: true });
            
            const { refreshToken } = get();
            
            // 调用服务端登出
            if (refreshToken) {
              await logout();
            }
            
            console.log('Logout successful');
            
          } catch (error) {
            console.error('Logout error:', error);
            // 即使服务端登出失败，也要清除本地状态
          } finally {
            // 清除认证状态
            get().clearAuth();
          }
        },

        // 刷新token
        refreshAuth: async () => {
          try {
            const { refreshToken: currentRefreshToken } = get();
            
            if (!currentRefreshToken) {
              throw new Error('No refresh token available');
            }
            
            console.log('Refreshing authentication token...');
            const authResponse = await refreshToken();
            
            // 更新状态
            set({
              user: authResponse.user,
              accessToken: authResponse.access_token,
              refreshToken: authResponse.refresh_token,
              isAuthenticated: true,
              error: null,
            });
            
            // 更新localStorage
            localStorage.setItem(AUTH_STORAGE_KEYS.ACCESS_TOKEN, authResponse.access_token);
            localStorage.setItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN, authResponse.refresh_token);
            localStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(authResponse.user));
            
            console.log('Token refresh successful');
            
            // 重新安排下次刷新
            get().scheduleTokenRefresh();
            
          } catch (error) {
            console.error('Token refresh failed:', error);
            // Token刷新失败，清除认证状态
            get().clearAuth();
            throw error;
          }
        },

        // 初始化认证状态
        initializeAuth: async () => {
          try {
            const accessToken = localStorage.getItem(AUTH_STORAGE_KEYS.ACCESS_TOKEN);
            const refreshToken = localStorage.getItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
            const userStr = localStorage.getItem(AUTH_STORAGE_KEYS.USER);
            
            if (!accessToken || !refreshToken) {
              console.log('No stored tokens found');
              return;
            }
            
            // 检查access token是否过期
            if (isTokenExpired(accessToken)) {
              console.log('Access token expired, attempting refresh...');
              await get().refreshAuth();
              return;
            }
            
            // 检查是否需要刷新token
            if (shouldRefreshToken(accessToken, TOKEN_CONFIG.REFRESH_THRESHOLD)) {
              console.log('Token needs refresh, refreshing...');
              await get().refreshAuth();
              return;
            }
            
            // Token有效，恢复用户状态
            if (userStr) {
              const user = JSON.parse(userStr);
              set({
                user,
                accessToken,
                refreshToken,
                isAuthenticated: true,
                error: null,
              });
              
              console.log('Authentication state restored for user:', user.username);
              
              // 安排token刷新
              get().scheduleTokenRefresh();
            }
            
          } catch (error) {
            console.error('Initialize auth failed:', error);
            get().clearAuth();
          }
        },

        // 安排token刷新
        scheduleTokenRefresh: () => {
          const { accessToken } = get();
          
          if (!accessToken) return;
          
          try {
            const payload = JSON.parse(atob(accessToken.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilRefresh = expirationTime - currentTime - TOKEN_CONFIG.REFRESH_THRESHOLD;
            
            if (timeUntilRefresh > 0) {
              console.log(`Token refresh scheduled in ${Math.round(timeUntilRefresh / 1000 / 60)} minutes`);
              
              setTimeout(() => {
                const state = get();
                if (state.isAuthenticated && state.refreshToken) {
                  state.refreshAuth().catch(console.error);
                }
              }, timeUntilRefresh);
            }
          } catch (error) {
            console.error('Failed to schedule token refresh:', error);
          }
        },

        // 检查认证状态
        checkAuthStatus: async () => {
          try {
            const { accessToken, isAuthenticated } = get();
            
            if (!isAuthenticated || !accessToken) {
              return false;
            }
            
            // 检查token是否过期
            if (isTokenExpired(accessToken)) {
              console.log('Token expired, attempting refresh...');
              await get().refreshAuth();
              return get().isAuthenticated;
            }
            
            return true;
          } catch (error) {
            console.error('Check auth status failed:', error);
            get().clearAuth();
            return false;
          }
        },

        // 获取当前用户信息
        getCurrentUser: async () => {
          try {
            const userResponse = await getCurrentUser();
            const user = userResponse.data;
            set({ user });
            localStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(user));
            return user;
          } catch (error) {
            console.error('Get current user failed:', error);
            throw error;
          }
        },
      }),
      {
        name: 'auth-store',
        // 只持久化基本的认证状态，不包括敏感的token信息
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// 导出便捷的hooks
export const useAuth = () => {
  const store = useAuthStore();
  const clearError = React.useCallback(() => store.setError(null), [store.setError]);
  
  return {
    user: store.user,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    register: store.register,
    logout: store.logout,
    clearError,
  };
};

export const useAuthActions = () => {
  const store = useAuthStore();
  return {
    login: store.login,
    register: store.register,
    logout: store.logout,
    refreshAuth: store.refreshAuth,
    initializeAuth: store.initializeAuth,
    checkAuthStatus: store.checkAuthStatus,
    getCurrentUser: store.getCurrentUser,
    clearAuth: store.clearAuth,
    setError: store.setError,
  };
};