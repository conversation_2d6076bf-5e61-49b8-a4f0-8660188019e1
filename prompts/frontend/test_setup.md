# 前端测试配置说明

## 1. 测试环境配置

### 1.1 依赖安装
```bash
# 安装测试依赖
npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event
npm install --save-dev jest jest-environment-jsdom
npm install --save-dev @types/jest
```

### 1.2 Jest 配置
在 `package.json` 中添加 Jest 配置：

```json
{
  "jest": {
    "testEnvironment": "jsdom",
    "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"],
    "moduleNameMapping": {
      "^@/(.*)$": "<rootDir>/src/$1",
      "\\.(css|less|scss|sass)$": "identity-obj-proxy"
    },
    "collectCoverageFrom": [
      "src/**/*.{ts,tsx}",
      "!src/**/*.d.ts",
      "!src/index.tsx",
      "!src/setupTests.ts"
    ],
    "coverageThreshold": {
      "global": {
        "branches": 80,
        "functions": 80,
        "lines": 80,
        "statements": 80
      }
    }
  }
}
```

### 1.3 测试脚本
在 `package.json` 中添加测试脚本：

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui"
  }
}
```

## 2. 测试文件结构

```
src/
├── components/
│   ├── PromptCard/
│   │   ├── PromptCard.tsx
│   │   └── __tests__/
│   │       └── PromptCard.test.tsx
│   └── TagInput/
│       ├── TagInput.tsx
│       └── __tests__/
│           └── TagInput.test.tsx
├── pages/
│   ├── PromptList/
│   │   ├── PromptList.tsx
│   │   └── __tests__/
│   │       └── PromptList.test.tsx
│   └── CreatePrompt/
│       ├── CreatePrompt.tsx
│       └── __tests__/
│           └── CreatePrompt.test.tsx
├── services/
│   ├── promptService.ts
│   └── __tests__/
│       └── promptService.test.ts
└── setupTests.ts
```

## 3. 测试工具配置

### 3.1 setupTests.ts
```typescript
import '@testing-library/jest-dom';

// 模拟 fetch API
global.fetch = jest.fn();

// 模拟 localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;
```

### 3.2 测试工具函数
```typescript
// src/test-utils/test-utils.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
```

## 4. 组件测试示例

### 4.1 PromptCard 组件测试
```typescript
// src/components/PromptCard/__tests__/PromptCard.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '../../../test-utils/test-utils';
import PromptCard from '../PromptCard';

const mockPrompt = {
  id: 1,
  title: '测试提示词',
  content: '这是一个测试提示词的内容',
  description: '测试描述',
  tags: ['测试', '示例'],
  category: { id: 1, name: '技术' },
  visibility: 'private' as const,
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z',
};

describe('PromptCard', () => {
  it('should render prompt information correctly', () => {
    render(<PromptCard prompt={mockPrompt} onEdit={jest.fn()} onDelete={jest.fn()} />);
    
    expect(screen.getByText('测试提示词')).toBeInTheDocument();
    expect(screen.getByText('这是一个测试提示词的内容')).toBeInTheDocument();
    expect(screen.getByText('测试')).toBeInTheDocument();
    expect(screen.getByText('示例')).toBeInTheDocument();
  });

  it('should call onEdit when edit button is clicked', () => {
    const onEdit = jest.fn();
    render(<PromptCard prompt={mockPrompt} onEdit={onEdit} onDelete={jest.fn()} />);
    
    fireEvent.click(screen.getByText('编辑'));
    expect(onEdit).toHaveBeenCalledWith(mockPrompt.id);
  });

  it('should call onDelete when delete button is clicked', () => {
    const onDelete = jest.fn();
    render(<PromptCard prompt={mockPrompt} onEdit={jest.fn()} onDelete={onDelete} />);
    
    fireEvent.click(screen.getByText('删除'));
    expect(onDelete).toHaveBeenCalledWith(mockPrompt.id);
  });

  it('should show loading state when isDeleting is true', () => {
    render(
      <PromptCard 
        prompt={mockPrompt} 
        onEdit={jest.fn()} 
        onDelete={jest.fn()} 
        isDeleting={true}
      />
    );
    
    expect(screen.getByText('删除中...')).toBeInTheDocument();
  });
});
```

### 4.2 TagInput 组件测试
```typescript
// src/components/TagInput/__tests__/TagInput.test.tsx
import React from 'react';
import { render, screen, fireEvent } from '../../../test-utils/test-utils';
import TagInput from '../TagInput';

describe('TagInput', () => {
  it('should add new tag when Enter is pressed', () => {
    const onChange = jest.fn();
    render(<TagInput value={[]} onChange={onChange} />);
    
    const input = screen.getByPlaceholderText('输入标签...');
    fireEvent.change(input, { target: { value: '新标签' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 13, charCode: 13 });
    
    expect(onChange).toHaveBeenCalledWith(['新标签']);
  });

  it('should remove tag when delete button is clicked', () => {
    const onChange = jest.fn();
    render(<TagInput value={['标签1', '标签2']} onChange={onChange} />);
    
    const deleteButtons = screen.getAllByText('×');
    fireEvent.click(deleteButtons[0]);
    
    expect(onChange).toHaveBeenCalledWith(['标签2']);
  });

  it('should not add empty tag', () => {
    const onChange = jest.fn();
    render(<TagInput value={[]} onChange={onChange} />);
    
    const input = screen.getByPlaceholderText('输入标签...');
    fireEvent.change(input, { target: { value: '   ' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 13, charCode: 13 });
    
    expect(onChange).not.toHaveBeenCalled();
  });

  it('should not add duplicate tag', () => {
    const onChange = jest.fn();
    render(<TagInput value={['已存在']} onChange={onChange} />);
    
    const input = screen.getByPlaceholderText('输入标签...');
    fireEvent.change(input, { target: { value: '已存在' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 13, charCode: 13 });
    
    expect(onChange).not.toHaveBeenCalled();
  });
});
```

## 5. 页面测试示例

### 5.1 PromptList 页面测试
```typescript
// src/pages/PromptList/__tests__/PromptList.test.tsx
import React from 'react';
import { render, screen, waitFor } from '../../../test-utils/test-utils';
import PromptList from '../PromptList';
import * as promptService from '../../../services/promptService';

// Mock the service
jest.mock('../../../services/promptService');

describe('PromptList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should load and display prompts', async () => {
    const mockPrompts = [
      {
        id: 1,
        title: '提示词1',
        content: '内容1',
        tags: ['标签1'],
        created_at: '2024-01-15T10:00:00Z',
      },
      {
        id: 2,
        title: '提示词2',
        content: '内容2',
        tags: ['标签2'],
        created_at: '2024-01-15T11:00:00Z',
      },
    ];

    (promptService.getPromptList as jest.Mock).mockResolvedValue({
      code: 0,
      data: {
        list: mockPrompts,
        total: 2,
        page: 1,
        size: 10,
      },
    });

    render(<PromptList />);

    await waitFor(() => {
      expect(screen.getByText('提示词1')).toBeInTheDocument();
      expect(screen.getByText('提示词2')).toBeInTheDocument();
    });
  });

  it('should show loading state while fetching data', () => {
    (promptService.getPromptList as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(<PromptList />);
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('should show error message when API fails', async () => {
    (promptService.getPromptList as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    render(<PromptList />);

    await waitFor(() => {
      expect(screen.getByText('加载失败，请重试')).toBeInTheDocument();
    });
  });
});
```

## 6. 服务测试示例

### 6.1 promptService 测试
```typescript
// src/services/__tests__/promptService.test.ts
import * as promptService from '../promptService';

// Mock fetch
global.fetch = jest.fn();

describe('promptService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPromptList', () => {
    it('should fetch prompt list successfully', async () => {
      const mockResponse = {
        code: 0,
        data: {
          list: [],
          total: 0,
          page: 1,
          size: 10,
        },
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await promptService.getPromptList({ page: 1, size: 10 });

      expect(result).toEqual(mockResponse);
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8080/api/prompts/list?page=1&size=10',
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-User-ID': '123',
          }),
        })
      );
    });

    it('should handle API error', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(promptService.getPromptList({ page: 1, size: 10 })).rejects.toThrow(
        'Network error'
      );
    });
  });

  describe('createPrompt', () => {
    it('should create prompt successfully', async () => {
      const mockPrompt = {
        title: '测试提示词',
        content: '测试内容',
        tags: ['测试'],
      };

      const mockResponse = {
        code: 0,
        data: { id: 1, ...mockPrompt },
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await promptService.createPrompt(mockPrompt);

      expect(result).toEqual(mockResponse);
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8080/api/prompts/create',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-User-ID': '123',
          }),
          body: JSON.stringify(mockPrompt),
        })
      );
    });
  });
});
```

## 7. 端到端测试配置

### 7.1 Playwright 配置
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### 7.2 端到端测试示例
```typescript
// e2e/prompt-management.spec.ts
import { test, expect } from '@playwright/test';

test.describe('提示词管理', () => {
  test.beforeEach(async ({ page }) => {
    // 设置用户认证
    await page.addInitScript(() => {
      localStorage.setItem('user_id', '123');
    });
  });

  test('应该能够创建新的提示词', async ({ page }) => {
    await page.goto('/prompts/create');

    // 填写表单
    await page.fill('[data-testid="title-input"]', '测试提示词');
    await page.fill('[data-testid="content-input"]', '这是一个测试提示词的内容');
    await page.click('[data-testid="add-tag-button"]');
    await page.fill('[data-testid="tag-input"]', '测试');
    await page.press('[data-testid="tag-input"]', 'Enter');

    // 提交表单
    await page.click('[data-testid="submit-button"]');

    // 验证跳转到列表页面
    await expect(page).toHaveURL('/prompts');
    await expect(page.locator('text=测试提示词')).toBeVisible();
  });

  test('应该能够搜索提示词', async ({ page }) => {
    await page.goto('/prompts');

    // 搜索提示词
    await page.fill('[data-testid="search-input"]', '测试');
    await page.press('[data-testid="search-input"]', 'Enter');

    // 验证搜索结果
    await expect(page.locator('[data-testid="prompt-card"]')).toHaveCount(1);
  });
});
```

## 8. 测试覆盖率配置

### 8.1 覆盖率阈值
```json
{
  "coverageThreshold": {
    "global": {
      "branches": 80,
      "functions": 80,
      "lines": 80,
      "statements": 80
    },
    "./src/components/": {
      "branches": 90,
      "functions": 90,
      "lines": 90,
      "statements": 90
    }
  }
}
```

### 8.2 覆盖率报告
```bash
# 生成覆盖率报告
npm run test:coverage

# 查看覆盖率报告
open coverage/lcov-report/index.html
```

## 9. 持续集成配置

### 9.1 GitHub Actions
```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

## 10. 测试最佳实践

### 10.1 测试原则
- 测试用户行为，而不是实现细节
- 使用有意义的测试描述
- 保持测试简单和可读
- 避免测试间的依赖
- 使用适当的测试数据

### 10.2 测试数据管理
```typescript
// src/test-utils/test-data.ts
export const mockPrompts = [
  {
    id: 1,
    title: 'JavaScript 基础',
    content: 'JavaScript 是一种编程语言...',
    tags: ['JavaScript', '编程'],
    created_at: '2024-01-15T10:00:00Z',
  },
  // ... 更多测试数据
];

export const mockCategories = [
  { id: 1, name: '技术', parent_id: 0 },
  { id: 2, name: '编程', parent_id: 1 },
  // ... 更多测试数据
];
```

### 10.3 测试工具函数
```typescript
// src/test-utils/test-helpers.ts
export const waitForLoadingToFinish = () =>
  waitFor(() => {
    expect(screen.queryByText('加载中...')).not.toBeInTheDocument();
  });

export const fillPromptForm = async (data: {
  title: string;
  content: string;
  tags?: string[];
}) => {
  await userEvent.type(screen.getByLabelText('标题'), data.title);
  await userEvent.type(screen.getByLabelText('内容'), data.content);
  
  if (data.tags) {
    for (const tag of data.tags) {
      await userEvent.type(screen.getByLabelText('标签'), tag);
      await userEvent.keyboard('{Enter}');
    }
  }
};
```

---

**文档版本**: v1.0  
**创建日期**: 2024-01-15  
**最后更新**: 2024-01-15  
**负责人**: 前端开发团队  
**审核人**: 项目经理 