#!/bin/bash

echo "🧪 Starting Prompts Platform Comprehensive Testing..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if services are running
check_services() {
    print_status "Checking if services are running..."
    
    # Check backend
    if curl -s http://localhost:8083/health > /dev/null; then
        print_success "Backend service is running on port 8083"
    else
        print_error "Backend service is not running. Please start it first."
        exit 1
    fi
    
    # Check frontend
    if curl -s http://localhost:5176 > /dev/null; then
        print_success "Frontend service is running on port 5176"
    else
        print_error "Frontend service is not running. Please start it first."
        exit 1
    fi
}

# Run API tests
run_api_tests() {
    print_status "Running API integration tests..."
    npx playwright test tests/prompts.spec.ts --project=chromium --grep="API Integration Tests" --reporter=line
    
    if [ $? -eq 0 ]; then
        print_success "API tests passed"
    else
        print_error "API tests failed"
        return 1
    fi
}

# Run UI tests
run_ui_tests() {
    print_status "Running UI tests..."
    npx playwright test tests/prompts.spec.ts --project=chromium --grep="Prompt Management - Real Tests" --reporter=line
    
    if [ $? -eq 0 ]; then
        print_success "UI tests passed"
    else
        print_error "UI tests failed"
        return 1
    fi
}

# Run comprehensive test suite
run_comprehensive_tests() {
    print_status "Running comprehensive test suite..."
    
    # Run all tests
    npx playwright test --project=chromium --reporter=line
    
    TEST_RESULT=$?
    
    if [ $TEST_RESULT -eq 0 ]; then
        print_success "All tests passed! 🎉"
    else
        print_error "Some tests failed. Check the logs above for details."
        return $TEST_RESULT
    fi
}

# Generate test report
generate_report() {
    print_status "Generating test report..."
    
    # Create results directory
    mkdir -p test-results
    
    # Get timestamp
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    
    # Generate HTML report
    npx playwright show-report --output test-results/report_$TIMESTAMP.html
    
    print_success "Test report generated: test-results/report_$TIMESTAMP.html"
}

# Database verification
verify_database() {
    print_status "Verifying database state..."
    
    # Test database connections
    curl -s -H "x-user-id: 1" -H "x-tenant-id: 1" "http://localhost:8083/api/prompts/prompts/list" | jq '.data.total' > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        TOTAL_PROMPTS=$(curl -s -H "x-user-id: 1" -H "x-tenant-id: 1" "http://localhost:8083/api/prompts/prompts/list" | jq '.data.total')
        print_success "Database accessible. Total prompts: $TOTAL_PROMPTS"
    else
        print_error "Database verification failed"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting comprehensive testing process..."
    
    # Check services
    check_services
    
    # Verify database
    verify_database
    
    # Run tests
    run_api_tests
    run_ui_tests
    run_comprehensive_tests
    
    # Generate report
    generate_report
    
    print_success "Testing completed successfully!"
    echo ""
    echo "📊 Test Summary:"
    echo "- Backend: http://localhost:8083 (running)"
    echo "- Frontend: http://localhost:5176 (running)"
    echo "- Report: test-results/report_$(date +"%Y%m%d_%H%M%S").html"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Review the test report for any failures"
    echo "2. Check the application manually at http://localhost:5176"
    echo "3. Run individual test files as needed"
    echo ""
}

# Run main function
main "$@"