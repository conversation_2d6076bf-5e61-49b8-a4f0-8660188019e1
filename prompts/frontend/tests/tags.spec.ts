import { test, expect } from '@playwright/test';

const API_BASE = 'http://localhost:8083';

test.describe('Tag Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should display tags', async ({ page }) => {
    await page.goto('/');
    
    // Check if tags are visible
    const tags = page.locator('.tags, .tag-list');
    await expect(tags).toBeVisible();
  });

  test('should create new tag', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to tags or create prompt with tags
    const createButton = page.locator('text=Create New Prompt');
    await createButton.click();
    
    // Find tag input
    const tagInput = page.locator('[data-testid="tag-input"]');
    await tagInput.fill('Test Tag');
    
    // Press Enter to create tag
    await tagInput.press('Enter');
    
    // Verify tag is added
    await expect(page.locator('text=Test Tag')).toBeVisible();
  });

  test('should search existing tags', async ({ page }) => {
    await page.goto('/');
    
    // Go to create prompt
    const createButton = page.locator('text=Create New Prompt');
    await createButton.click();
    
    // Find tag input
    const tagInput = page.locator('[data-testid="tag-input"]');
    await tagInput.fill('ai');
    
    // Wait for suggestions
    await page.waitForTimeout(500);
    
    // Check if suggestions appear
    const suggestions = page.locator('.ant-select-item-option');
    if (await suggestions.count() > 0) {
      await suggestions.first().click();
    }
  });

  test('should display popular tags', async ({ page }) => {
    await page.goto('/');
    
    // Check for popular tags section
    const popularTags = page.locator('text=Popular Tags');
    if (await popularTags.isVisible()) {
      await popularTags.click();
      
      // Verify tags are displayed
      const tags = page.locator('.popular-tag');
      await expect(tags).toBeVisible();
    }
  });

  test('should filter by tags', async ({ page }) => {
    await page.goto('/');
    
    // Find tag filter
    const tagFilter = page.locator('[data-testid="tag-filter"]');
    if (await tagFilter.isVisible()) {
      await tagFilter.click();
      
      // Select a tag
      const tagOptions = page.locator('.ant-select-item-option');
      if (await tagOptions.count() > 0) {
        await tagOptions.first().click();
        
        // Verify filtered results
        await expect(page.locator('.prompt-list')).toBeVisible();
      }
    }
  });

  test('should batch create tags', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to advanced tag management
    const tagsButton = page.locator('text=Tags');
    await tagsButton.click();
    
    // Look for batch create option
    const batchButton = page.locator('text=Batch Create');
    if (await batchButton.isVisible()) {
      await batchButton.click();
      
      // Fill batch form
      await page.fill('[data-testid="batch-tags"]', 'tag1, tag2, tag3');
      
      // Submit
      await page.click('text=Create Tags');
      
      // Verify success
      await expect(page.locator('.ant-message-success')).toBeVisible();
    }
  });
});

test.describe('Tag API', () => {
  test('should fetch tags', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/tags/list`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should create tag via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/tags/create`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        name: 'API Test Tag',
        description: 'Tag created via API',
        color: '#ff0000',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data.data).toHaveProperty('id');
  });

  test('should search tags via API', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/tags/search?keyword=test`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should get popular tags', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/tags/popular`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should batch create tags via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/tags/batch/create`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        tags: [
          { name: 'Batch Tag 1', description: 'First batch tag' },
          { name: 'Batch Tag 2', description: 'Second batch tag' },
        ],
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(Array.isArray(data.data)).toBe(true);
  });
});