import { test, expect } from '@playwright/test';

const API_BASE = 'http://localhost:8083';

test.describe('Category Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should display category tree', async ({ page }) => {
    await page.goto('/');
    
    // Check if categories are visible
    const categories = page.locator('.category-tree, .categories');
    await expect(categories).toBeVisible();
  });

  test('should create new category', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to category management
    const categoryButton = page.locator('text=Categories');
    await categoryButton.click();
    
    // Click create category
    const createButton = page.locator('text=Create Category');
    await createButton.click();
    
    // Fill category form
    await page.fill('[data-testid="category-name"]', 'Test Category');
    await page.fill('[data-testid="category-description"]', 'Test category description');
    
    // Submit form
    await page.click('text=Create Category');
    
    // Verify success
    await expect(page.locator('.ant-message-success')).toBeVisible();
  });

  test('should edit category', async ({ page }) => {
    await page.goto('/');
    
    // Navigate to categories
    await page.click('text=Categories');
    
    // Find edit button for first category
    const editButtons = page.locator('text=Edit');
    const count = await editButtons.count();
    
    if (count > 0) {
      await editButtons.first().click();
      
      // Update category name
      await page.fill('[data-testid="category-name"]', 'Updated Category Name');
      
      // Save changes
      await page.click('text=Update Category');
      
      // Verify success
      await expect(page.locator('.ant-message-success')).toBeVisible();
    }
  });

  test('should filter prompts by category', async ({ page }) => {
    await page.goto('/');
    
    // Check if category filter exists
    const categoryFilter = page.locator('[data-testid="category-filter"]');
    if (await categoryFilter.isVisible()) {
      await categoryFilter.click();
      
      // Select a category
      const options = page.locator('.ant-select-item-option');
      if (await options.count() > 0) {
        await options.first().click();
        
        // Verify filtered results
        await expect(page.locator('.prompt-list')).toBeVisible();
      }
    }
  });

  test('should display category tree structure', async ({ page }) => {
    await page.goto('/');
    
    // Check for tree structure
    const tree = page.locator('.ant-tree');
    await expect(tree).toBeVisible();
    
    // Check for expand/collapse buttons
    const expandButtons = page.locator('.ant-tree-switcher');
    if (await expandButtons.count() > 0) {
      await expandButtons.first().click();
    }
  });
});

test.describe('Category API', () => {
  test('should fetch categories', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/categories/tree`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should create category via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/categories/create`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        name: 'API Test Category',
        description: 'Category created via API',
        parent_id: 0,
        sort_order: 0,
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data.data).toHaveProperty('id');
  });
});