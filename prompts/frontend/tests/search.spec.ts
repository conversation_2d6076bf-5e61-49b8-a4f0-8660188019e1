import { test, expect } from '@playwright/test';

const API_BASE = 'http://localhost:8083';

test.describe('Search Functionality', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should search prompts by title', async ({ page }) => {
    await page.goto('/');
    
    // Find search input
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Enter search term
    await searchInput.fill('test title');
    
    // Press Enter or click search button
    await searchInput.press('Enter');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Verify search results are displayed
    const results = page.locator('.prompt-item, .search-result');
    await expect(results).toBeVisible();
  });

  test('should search prompts by content', async ({ page }) => {
    await page.goto('/');
    
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    await searchInput.fill('content search');
    await searchInput.press('Enter');
    
    await page.waitForLoadState('networkidle');
    
    const results = page.locator('.prompt-item, .search-result');
    await expect(results).toBeVisible();
  });

  test('should clear search', async ({ page }) => {
    await page.goto('/');
    
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Enter search term
    await searchInput.fill('test search');
    await searchInput.press('Enter');
    
    // Wait for results
    await page.waitForLoadState('networkidle');
    
    // Clear search
    const clearButton = page.locator('.ant-input-clear-icon, .search-clear');
    if (await clearButton.isVisible()) {
      await clearButton.click();
    } else {
      await searchInput.clear();
    }
    
    // Verify search is cleared
    await expect(searchInput).toHaveValue('');
  });

  test('should show search suggestions', async ({ page }) => {
    await page.goto('/');
    
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Type slowly to trigger suggestions
    await searchInput.type('ai', { delay: 100 });
    
    // Wait for suggestions
    const suggestions = page.locator('.search-suggestion, .ant-select-dropdown');
    await suggestions.waitFor({ state: 'visible', timeout: 5000 });
    
    if (await suggestions.isVisible()) {
      await expect(suggestions).toBeVisible();
    }
  });

  test('should search with filters', async ({ page }) => {
    await page.goto('/');
    
    // Apply category filter
    const categoryFilter = page.locator('[data-testid="category-filter"]');
    if (await categoryFilter.isVisible()) {
      await categoryFilter.click();
      const categoryOptions = page.locator('.ant-select-item-option');
      if (await categoryOptions.count() > 0) {
        await categoryOptions.first().click();
      }
    }
    
    // Apply tag filter
    const tagFilter = page.locator('[data-testid="tag-filter"]');
    if (await tagFilter.isVisible()) {
      await tagFilter.click();
      const tagOptions = page.locator('.ant-select-item-option');
      if (await tagOptions.count() > 0) {
        await tagOptions.first().click();
      }
    }
    
    // Enter search term
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    await searchInput.fill('search with filters');
    await searchInput.press('Enter');
    
    // Verify filtered results
    await page.waitForLoadState('networkidle');
    const results = page.locator('.prompt-item, .search-result');
    await expect(results).toBeVisible();
  });

  test('should handle no results', async ({ page }) => {
    await page.goto('/');
    
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Search for non-existent prompt
    await searchInput.fill('nonexistentprompt123456');
    await searchInput.press('Enter');
    
    // Wait for search results
    await page.waitForLoadState('networkidle');
    
    // Check for no results message
    const noResults = page.locator('text=No results found, text=No prompts found, .empty-state');
    await expect(noResults).toBeVisible();
  });

  test('should search case-insensitive', async ({ page }) => {
    await page.goto('/');
    
    const searchInput = page.locator('input[type="search"], input[placeholder*="Search"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Search with uppercase
    await searchInput.fill('TEST');
    await searchInput.press('Enter');
    
    await page.waitForLoadState('networkidle');
    
    // Search with lowercase
    await searchInput.clear();
    await searchInput.fill('test');
    await searchInput.press('Enter');
    
    await page.waitForLoadState('networkidle');
    
    const results = page.locator('.prompt-item, .search-result');
    await expect(results).toBeVisible();
  });
});

test.describe('Search API', () => {
  test('should search prompts via API', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/prompts/search?keyword=test`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should search with filters via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/prompts/list`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        keyword: 'test',
        category_id: 1,
        tag_ids: [1, 2],
        visibility: 'public',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should search public prompts', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/prompts/public?keyword=test`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });
});

test.describe('Advanced Search', () => {
  test('should search by date range', async ({ page }) => {
    await page.goto('/');
    
    // Open date picker
    const dateRange = page.locator('[data-testid="date-range"]');
    if (await dateRange.isVisible()) {
      await dateRange.click();
      
      // Select date range
      const today = page.locator('.ant-picker-cell-today');
      await today.click();
      
      // Apply filter
      await page.click('text=OK');
      
      // Verify filtered results
      const results = page.locator('.prompt-item');
      await expect(results).toBeVisible();
    }
  });

  test('should combine multiple filters', async ({ page }) => {
    await page.goto('/');
    
    // Apply category filter
    const categoryFilter = page.locator('[data-testid="category-filter"]');
    if (await categoryFilter.isVisible()) {
      await categoryFilter.click();
      const categoryOptions = page.locator('.ant-select-item-option');
      if (await categoryOptions.count() > 0) {
        await categoryOptions.first().click();
      }
    }
    
    // Apply visibility filter
    const visibilityFilter = page.locator('[data-testid="visibility-filter"]');
    if (await visibilityFilter.isVisible()) {
      await visibilityFilter.click();
      await page.click('text=Public');
    }
    
    // Apply search
    const searchInput = page.locator('input[type="search"]');
    await searchInput.fill('combined search');
    await searchInput.press('Enter');
    
    // Verify results with combined filters
    await page.waitForLoadState('networkidle');
    const results = page.locator('.prompt-item');
    await expect(results).toBeVisible();
  });

  test('should reset all filters', async ({ page }) => {
    await page.goto('/');
    
    // Apply some filters
    const searchInput = page.locator('input[type="search"]');
    await searchInput.fill('test filters');
    
    // Reset filters
    const resetButton = page.locator('text=Reset, text=Clear All');
    if (await resetButton.isVisible()) {
      await resetButton.click();
    }
    
    // Verify filters are reset
    await expect(searchInput).toHaveValue('');
  });
});