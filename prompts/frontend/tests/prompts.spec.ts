import { test, expect } from '@playwright/test';

const API_BASE = 'http://localhost:8083';

test.describe('Prompt Management - Real Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should display home page with correct title', async ({ page }) => {
    await page.goto('/');
    
    // Check for the actual title in Chinese
    await expect(page.locator('text=🧠 提示词管理')).toBeVisible();
    
    // Check navigation menu
    await expect(page.locator('text=首页')).toBeVisible();
    await expect(page.locator('text=工作台')).toBeVisible();
    await expect(page.locator('text=探索')).toBeVisible();
  });

  test('should navigate to create prompt page', async ({ page }) => {
    await page.goto('/');
    
    // Click create new prompt button
    const createButton = page.locator('text=创建新提示词');
    await createButton.waitFor({ state: 'visible' });
    await createButton.click();
    
    // Should navigate to editor page
    await expect(page).toHaveURL(/\/editor/);
    
    // Check for editor form elements
    await expect(page.locator('input[placeholder="请输入标题..."]')).toBeVisible();
  });

  test('should create a prompt with basic information', async ({ page }) => {
    await page.goto('/editor');
    
    // Fill title
    const titleInput = page.locator('input[placeholder="请输入标题..."]');
    await titleInput.fill('测试提示词 ' + Date.now());
    
    // Fill content in markdown editor
    const contentEditor = page.locator('.w-md-editor-text-input');
    await contentEditor.fill('# 测试内容\n\n这是一个测试提示词的内容。');
    
    // Add tags if tag input exists
    const tagInput = page.locator('input[placeholder*="标签"]');
    if (await tagInput.isVisible()) {
      await tagInput.fill('测试');
      await tagInput.press('Enter');
    }
    
    // Select category if exists
    const categorySelect = page.locator('.ant-tree-select-selector');
    if (await categorySelect.isVisible()) {
      await categorySelect.click();
      const categoryOptions = page.locator('.ant-tree-select-dropdown .ant-select-tree-node-content-wrapper');
      if (await categoryOptions.count() > 0) {
        await categoryOptions.first().click();
      }
    }
    
    // Save as draft
    const saveDraftButton = page.locator('text=保存草稿');
    if (await saveDraftButton.isVisible()) {
      await saveDraftButton.click();
      
      // Wait for success message
      await expect(page.locator('.ant-message-success')).toBeVisible({ timeout: 10000 });
    }
  });

  test('should display prompts list on home page', async ({ page }) => {
    await page.goto('/');
    
    // Wait for content to load
    await page.waitForSelector('.ant-card, .prompt-card', { timeout: 10000 });
    
    // Check if any prompts are displayed
    const promptCards = page.locator('.ant-card, .prompt-card');
    const cardCount = await promptCards.count();
    
    if (cardCount > 0) {
      // Verify first card has title
      const firstCard = promptCards.first();
      await expect(firstCard.locator('.ant-card-meta-title')).toBeVisible();
    } else {
      // Check for empty state
      await expect(page.locator('text=暂无数据, text=No data')).toBeVisible();
    }
  });

  test('should search prompts from home page', async ({ page }) => {
    await page.goto('/');
    
    // Find search input
    const searchInput = page.locator('input[placeholder*="搜索"]');
    await searchInput.waitFor({ state: 'visible' });
    
    // Enter search term
    await searchInput.fill('测试');
    
    // Press Enter or click search
    await searchInput.press('Enter');
    
    // Wait for search results or navigation
    await page.waitForLoadState('networkidle');
    
    // Check if we're on search page or results updated
    const url = page.url();
    if (url.includes('/search')) {
      await expect(page.locator('text=搜索结果')).toBeVisible();
    }
  });

  test('should navigate to workbench', async ({ page }) => {
    await page.goto('/');
    
    // Click workbench link
    const workbenchLink = page.locator('text=工作台');
    await workbenchLink.click();
    
    // Should navigate to workbench
    await expect(page).toHaveURL(/\/workbench/);
    
    // Check for workbench layout
    await expect(page.locator('text=我的提示词')).toBeVisible();
  });

  test('should view prompt details', async ({ page }) => {
    await page.goto('/');
    
    // Wait for prompts to load
    await page.waitForSelector('.ant-card', { timeout: 10000 });
    
    // Find first prompt card
    const promptCards = page.locator('.ant-card');
    const cardCount = await promptCards.count();
    
    if (cardCount > 0) {
      // Click on first prompt
      await promptCards.first().click();
      
      // Should navigate to detail page
      await expect(page).toHaveURL(/\/prompt\//);
      
      // Check for detail content
      await expect(page.locator('h1, .ant-typography-title')).toBeVisible();
    }
  });
});

test.describe('API Integration Tests', () => {
  test('should fetch prompts list via API', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/prompts/list?page=1&size=10`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
    });
    
    expect(response.status()).toBeOneOf([200, 201]);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should create prompt via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/prompts/create`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        title: 'API测试提示词 ' + Date.now(),
        content: '这是通过API创建的测试提示词内容',
        description: 'API测试描述',
        visibility: 'private',
        tags: ['测试', 'API'],
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data.data).toHaveProperty('id');
    
    // Store ID for cleanup
    return data.data.id;
  });

  test('should search prompts via API', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/prompts/search`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
      params: {
        keyword: '测试',
        page: 1,
        page_size: 10,
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should fetch public prompts', async ({ request }) => {
    const response = await request.get(`${API_BASE}/api/prompts/prompts/public`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
      },
      params: {
        page: 1,
        page_size: 10,
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data).toHaveProperty('data');
  });

  test('should save draft via API', async ({ request }) => {
    const response = await request.post(`${API_BASE}/api/prompts/prompts/draft/save`, {
      headers: {
        'x-user-id': '1',
        'x-tenant-id': '1',
        'Content-Type': 'application/json',
      },
      data: {
        title: '草稿测试 ' + Date.now(),
        content: '这是草稿内容',
        description: '草稿描述',
      },
    });
    
    expect(response.status()).toBe(200);
    const data = await response.json();
    expect(data).toHaveProperty('code', 0);
    expect(data.data).toHaveProperty('id');
  });
});