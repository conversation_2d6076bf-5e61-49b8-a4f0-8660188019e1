[server]
port = 8083
read_timeout = "60s"
write_timeout = "60s"
idle_timeout = "60s"
env = "dev"

[database.mysql]
host = "**************"
port = 3308
database = "platforms-prompts"
username = "root"
password = "Pu0cF6KVs]7AockCKVC"
charset = "utf8mb4"
parse_time = true
loc = "Local"
max_open_conns = 20
max_idle_conns = 10
conn_max_lifetime = "60m"
params = {}

[log.app]
level = "debug"
format = "json"
output = "stdout"
max_size = 100
max_backups = 7
max_age = 30
compress = false

[log.access]
level = "debug"
format = "json"
output = "stdout"
max_size = 100
max_backups = 7
max_age = 30
compress = false

[log.error]
level = "error"
format = "json"
output = "stderr"
max_size = 100
max_backups = 7
max_age = 30
compress = false


[redis]
host = "127.0.0.1"
port = 6379
password = ""
database = 0
pool_size = 10

[grpc]
port = 50051
service_name = "platforms-prompts"
group = "DEFAULT_GROUP"
namespace = "dev"
weight = 100.0
enable_register = true
metadata = {}

[otel]
endpoint = "http://localhost:4317"

[nacos]
host = "**************"
port = 8848
namespace = "dev"
group = "platforms"
username = "dev_account"
password = "ll3hotd60xhq3I4U3MghiLzSWGbZqIoYUPzLvFZL6A2vhnylEwWw55EPa6u1ZqPI"

[app]
serviceName = "platforms-prompts"
env = "dev"

[grpc_clients]
users_addr = "127.0.0.1:50051"

[[grpcSubscriptions]]
serviceName = "platforms-user"
group = "DEFAULT_GROUP"
namespace = ""
strategy = "weighted"
subscribe = true
retry = { enabled = true, maxRetries = 3, initialDelay = "1s", maxDelay = "10s", backoffFactor = 2.0 }
healthCheck = { enabled = true, interval = "30s", timeout = "5s" }
circuitBreaker = { enabled = true, failureThreshold = 5, successThreshold = 2, timeout = "60s" }
connTimeout = "10s"
maxConnections = 5
