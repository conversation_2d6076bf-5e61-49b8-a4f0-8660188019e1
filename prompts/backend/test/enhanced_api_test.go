package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
)

// EnhancedAPITestSuite 增强版API测试套件
type EnhancedAPITestSuite struct {
	suite.Suite
	router *gin.Engine
	server *httptest.Server
}

// SetupSuite 测试套件初始化
func (suite *EnhancedAPITestSuite) SetupSuite() {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建路由
	suite.router = gin.New()

	// 设置中间件
	suite.setupMiddleware()

	// 设置路由
	suite.setupRoutes()

	// 创建测试服务器
	suite.server = httptest.NewServer(suite.router)
}

// TearDownSuite 测试套件清理
func (suite *EnhancedAPITestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
}

// setupMiddleware 设置中间件
func (suite *EnhancedAPITestSuite) setupMiddleware() {
	// 添加请求日志中间件
	suite.router.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)

		// 记录请求日志
		if c.Writer.Status() >= 400 {
			fmt.Printf("[ERROR] %s %s - %d - %v\n", c.Request.Method, c.Request.URL.Path, c.Writer.Status(), duration)
		} else {
			fmt.Printf("[INFO] %s %s - %d - %v\n", c.Request.Method, c.Request.URL.Path, c.Writer.Status(), duration)
		}
	})

	// 添加错误恢复中间件
	suite.router.Use(gin.Recovery())

	// 添加CORS中间件
	suite.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})
}

// setupRoutes 设置路由
func (suite *EnhancedAPITestSuite) setupRoutes() {
	// 健康检查
	suite.router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"service":   "prompts-api",
			"timestamp": time.Now().Unix(),
			"code":      0,
		})
	})

	// 提示词API路由组
	promptsAPI := suite.router.Group("/api/prompts")
	{
		// 健康检查
		promptsAPI.GET("/health", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"status": "prompts-api-healthy",
				"code":   0,
			})
		})

		// 提示词管理路由
		prompts := promptsAPI.Group("/prompts")
		{
			// 列表查询
			prompts.GET("/list", suite.mockGetPromptList)
			prompts.POST("/list", suite.mockGetPromptList)

			// 创建提示词
			prompts.POST("/create", suite.mockCreatePrompt)

			// 更新提示词
			prompts.POST("/update", suite.mockUpdatePrompt)

			// 删除提示词
			prompts.POST("/delete", suite.mockDeletePrompt)

			// 获取详情
			prompts.GET("/detail", suite.mockGetPromptDetail)
			prompts.POST("/get", suite.mockGetPromptDetail)

			// 使用提示词
			prompts.POST("/use", suite.mockUsePrompt)

			// 草稿管理
			prompts.POST("/draft/save", suite.mockSaveDraft)
			prompts.POST("/draft/update", suite.mockUpdateDraft)
			prompts.POST("/publish", suite.mockPublishPrompt)

			// 公开接口
			prompts.GET("/public", suite.mockGetPublicPrompts)
			prompts.GET("/search", suite.mockSearchPrompts)
		}

		// 分类管理路由
		categories := promptsAPI.Group("/categories")
		{
			// 列表查询
			categories.GET("/list", suite.mockGetCategoryList)
			categories.POST("/list", suite.mockGetCategoryList)

			// 创建分类
			categories.POST("/create", suite.mockCreateCategory)

			// 更新分类
			categories.POST("/update", suite.mockUpdateCategory)

			// 删除分类
			categories.POST("/delete", suite.mockDeleteCategory)

			// 获取详情
			categories.GET("/detail", suite.mockGetCategoryDetail)

			// 获取分类树
			categories.GET("/tree", suite.mockGetCategoryTree)

			// 更新排序
			categories.POST("/sort", suite.mockUpdateCategorySort)

			// 公开接口
			categories.GET("/global", suite.mockGetGlobalCategories)
		}

		// 标签管理路由
		tags := promptsAPI.Group("/tags")
		{
			// 列表查询
			tags.GET("/list", suite.mockGetTagList)
			tags.POST("/list", suite.mockGetTagList)

			// 创建标签
			tags.POST("/create", suite.mockCreateTag)

			// 更新标签
			tags.POST("/update", suite.mockUpdateTag)

			// 删除标签
			tags.POST("/delete", suite.mockDeleteTag)

			// 获取详情
			tags.GET("/detail", suite.mockGetTagDetail)

			// 搜索标签
			tags.GET("/search", suite.mockSearchTags)

			// 获取热门标签
			tags.GET("/popular", suite.mockGetPopularTags)

			// 获取标签统计
			tags.GET("/stats", suite.mockGetTagStats)

			// 批量操作
			tags.POST("/batch/create", suite.mockBatchCreateTags)
			tags.POST("/batch/delete", suite.mockBatchDeleteTags)

			// 公开接口
			tags.GET("/global", suite.mockGetGlobalTags)
		}
	}

	// 404处理
	suite.router.NoRoute(func(c *gin.Context) {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "API接口不存在",
		})
	})

	// 405处理
	suite.router.NoMethod(func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code":    405,
			"message": "不支持的HTTP方法",
		})
	})
}

// Mock处理器方法

func (suite *EnhancedAPITestSuite) mockGetPromptList(c *gin.Context) {
	// 解析查询参数
	page := c.DefaultQuery("page", "1")
	size := c.DefaultQuery("size", "20")
	status := c.Query("status")
	visibility := c.Query("visibility")

	// 模拟数据
	data := []gin.H{
		{
			"id":          1,
			"title":       "测试提示词1",
			"content":     "这是测试内容1",
			"description": "测试描述1",
			"status":      "published",
			"visibility":  "public",
			"usage_count": 10,
			"view_count":  100,
		},
		{
			"id":          2,
			"title":       "测试提示词2",
			"content":     "这是测试内容2",
			"description": "测试描述2",
			"status":      "draft",
			"visibility":  "private",
			"usage_count": 5,
			"view_count":  50,
		},
	}

	// 根据状态过滤
	if status != "" {
		filtered := []gin.H{}
		for _, item := range data {
			if item["status"] == status {
				filtered = append(filtered, item)
			}
		}
		data = filtered
	}

	// 根据可见性过滤
	if visibility != "" {
		filtered := []gin.H{}
		for _, item := range data {
			if item["visibility"] == visibility {
				filtered = append(filtered, item)
			}
		}
		data = filtered
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": data,
		"meta": gin.H{
			"page":  page,
			"size":  size,
			"total": len(data),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockCreatePrompt(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	// 验证必填字段
	if req["title"] == nil || req["title"] == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10002,
			"message": "标题不能为空",
		})
		return
	}

	if req["content"] == nil || req["content"] == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10003,
			"message": "内容不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          3,
			"title":       req["title"],
			"content":     req["content"],
			"description": req["description"],
			"status":      "draft",
			"visibility":  "private",
			"created_at":  time.Now().Unix(),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUpdatePrompt(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "提示词ID不能为空",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          id,
			"title":       req["title"],
			"content":     req["content"],
			"description": req["description"],
			"updated_at":  time.Now().Unix(),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockDeletePrompt(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "提示词ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

func (suite *EnhancedAPITestSuite) mockGetPromptDetail(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "提示词ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":           id,
			"title":        "测试提示词详情",
			"content":      "这是测试内容详情",
			"description":  "测试描述详情",
			"status":       "published",
			"visibility":   "public",
			"usage_count":  10,
			"view_count":   100,
			"rating":       4.5,
			"rating_count": 20,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUsePrompt(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "提示词ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "使用成功",
	})
}

func (suite *EnhancedAPITestSuite) mockSaveDraft(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":         "draft_1",
			"title":      req["title"],
			"content":    req["content"],
			"status":     "draft",
			"created_at": time.Now().Unix(),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUpdateDraft(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "草稿ID不能为空",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":         id,
			"title":      req["title"],
			"content":    req["content"],
			"updated_at": time.Now().Unix(),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockPublishPrompt(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "草稿ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":           id,
			"status":       "published",
			"published_at": time.Now().Unix(),
		},
	})
}

func (suite *EnhancedAPITestSuite) mockGetPublicPrompts(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":          1,
				"title":       "公开提示词1",
				"content":     "公开内容1",
				"status":      "published",
				"visibility":  "public",
				"usage_count": 100,
			},
			{
				"id":          2,
				"title":       "公开提示词2",
				"content":     "公开内容2",
				"status":      "published",
				"visibility":  "public",
				"usage_count": 50,
			},
		},
		"meta": gin.H{
			"page":  1,
			"size":  20,
			"total": 2,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockSearchPrompts(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10005,
			"message": "搜索关键词不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":      1,
				"title":   fmt.Sprintf("搜索结果: %s", keyword),
				"content": fmt.Sprintf("包含关键词 %s 的内容", keyword),
			},
		},
		"meta": gin.H{
			"page":  1,
			"size":  20,
			"total": 1,
		},
	})
}

// 分类相关Mock方法
func (suite *EnhancedAPITestSuite) mockGetCategoryList(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":           1,
				"name":         "技术分类",
				"description":  "技术相关分类",
				"parent_id":    0,
				"sort_order":   1,
				"prompt_count": 10,
			},
			{
				"id":           2,
				"name":         "写作分类",
				"description":  "写作相关分类",
				"parent_id":    0,
				"sort_order":   2,
				"prompt_count": 5,
			},
		},
		"meta": gin.H{
			"page":  1,
			"size":  20,
			"total": 2,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockCreateCategory(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          3,
			"name":        req["name"],
			"description": req["description"],
			"parent_id":   req["parent_id"],
			"sort_order":  3,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUpdateCategory(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "分类ID不能为空",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          id,
			"name":        req["name"],
			"description": req["description"],
		},
	})
}

func (suite *EnhancedAPITestSuite) mockDeleteCategory(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "分类ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

func (suite *EnhancedAPITestSuite) mockGetCategoryDetail(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "分类ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":           id,
			"name":         "分类详情",
			"description":  "分类描述详情",
			"parent_id":    0,
			"sort_order":   1,
			"prompt_count": 10,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockGetCategoryTree(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":       1,
				"name":     "技术分类",
				"children": []gin.H{},
			},
			{
				"id":       2,
				"name":     "写作分类",
				"children": []gin.H{},
			},
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUpdateCategorySort(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "排序更新成功",
	})
}

func (suite *EnhancedAPITestSuite) mockGetGlobalCategories(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":          1,
				"name":        "全局技术分类",
				"description": "全局技术相关分类",
				"is_system":   true,
			},
		},
	})
}

// 标签相关Mock方法
func (suite *EnhancedAPITestSuite) mockGetTagList(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":          1,
				"name":        "AI",
				"color":       "#FF6B6B",
				"usage_count": 10,
			},
			{
				"id":          2,
				"name":        "编程",
				"color":       "#4ECDC4",
				"usage_count": 15,
			},
		},
		"meta": gin.H{
			"page":  1,
			"size":  20,
			"total": 2,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockCreateTag(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          3,
			"name":        req["name"],
			"color":       req["color"],
			"usage_count": 0,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockUpdateTag(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "标签ID不能为空",
		})
		return
	}

	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":    id,
			"name":  req["name"],
			"color": req["color"],
		},
	})
}

func (suite *EnhancedAPITestSuite) mockDeleteTag(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "标签ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "删除成功",
	})
}

func (suite *EnhancedAPITestSuite) mockGetTagDetail(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10004,
			"message": "标签ID不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"id":          id,
			"name":        "标签详情",
			"color":       "#FF6B6B",
			"usage_count": 10,
		},
	})
}

func (suite *EnhancedAPITestSuite) mockSearchTags(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusOK, gin.H{
			"code":    10005,
			"message": "搜索关键词不能为空",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":   1,
				"name": fmt.Sprintf("搜索结果: %s", keyword),
			},
		},
	})
}

func (suite *EnhancedAPITestSuite) mockGetPopularTags(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":          1,
				"name":        "热门标签1",
				"usage_count": 100,
			},
			{
				"id":          2,
				"name":        "热门标签2",
				"usage_count": 80,
			},
		},
	})
}

func (suite *EnhancedAPITestSuite) mockGetTagStats(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": gin.H{
			"total_tags":   100,
			"total_usage":  1000,
			"popular_tags": []gin.H{},
		},
	})
}

func (suite *EnhancedAPITestSuite) mockBatchCreateTags(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "批量创建成功",
		"data":    []gin.H{},
	})
}

func (suite *EnhancedAPITestSuite) mockBatchDeleteTags(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"code":    10001,
			"message": "参数错误",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "批量删除成功",
	})
}

func (suite *EnhancedAPITestSuite) mockGetGlobalTags(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": []gin.H{
			{
				"id":          1,
				"name":        "全局标签1",
				"color":       "#FF6B6B",
				"usage_count": 50,
			},
		},
	})
}

// 测试方法

func (suite *EnhancedAPITestSuite) TestHealthCheck() {
	suite.Run("根健康检查", func() {
		resp, err := http.Get(suite.server.URL + "/health")
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal("healthy", response["status"])
		suite.Equal("prompts-api", response["service"])
		suite.Equal(float64(0), response["code"])
	})

	suite.Run("API健康检查", func() {
		resp, err := http.Get(suite.server.URL + "/api/prompts/health")
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal("prompts-api-healthy", response["status"])
		suite.Equal(float64(0), response["code"])
	})
}

func (suite *EnhancedAPITestSuite) TestPromptCRUD() {
	suite.Run("创建提示词", func() {
		createData := map[string]interface{}{
			"title":       "新提示词",
			"content":     "新提示词内容",
			"description": "新提示词描述",
		}

		jsonData, _ := json.Marshal(createData)
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(0), response["code"])
		suite.NotNil(response["data"])
	})

	suite.Run("更新提示词", func() {
		updateData := map[string]interface{}{
			"title":       "更新后的标题",
			"content":     "更新后的内容",
			"description": "更新后的描述",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/update?id=1", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(0), response["code"])
	})

	suite.Run("删除提示词", func() {
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/delete?id=1", nil)
		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(0), response["code"])
		suite.Equal("删除成功", response["message"])
	})
}

func (suite *EnhancedAPITestSuite) TestParameterValidation() {
	suite.Run("创建提示词缺少标题", func() {
		createData := map[string]interface{}{
			"content":     "新提示词内容",
			"description": "新提示词描述",
		}

		jsonData, _ := json.Marshal(createData)
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(10002), response["code"])
		suite.Equal("标题不能为空", response["message"])
	})

	suite.Run("创建提示词缺少内容", func() {
		createData := map[string]interface{}{
			"title":       "新提示词",
			"description": "新提示词描述",
		}

		jsonData, _ := json.Marshal(createData)
		req, _ := http.NewRequest("POST", suite.server.URL+"/api/prompts/prompts/create", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")

		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusOK, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(10003), response["code"])
		suite.Equal("内容不能为空", response["message"])
	})
}

func (suite *EnhancedAPITestSuite) TestErrorHandling() {
	suite.Run("404错误处理", func() {
		resp, err := http.Get(suite.server.URL + "/api/prompts/nonexistent")
		suite.Require().NoError(err)
		defer resp.Body.Close()

		suite.Equal(http.StatusNotFound, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(404), response["code"])
		suite.Equal("API接口不存在", response["message"])
	})

	suite.Run("405错误处理", func() {
		req, _ := http.NewRequest("PUT", suite.server.URL+"/api/prompts/prompts/list", nil)
		client := &http.Client{}
		resp, err := client.Do(req)
		suite.Require().NoError(err)
		defer resp.Body.Close()

		// Gin默认返回404而不是405，所以这里测试404
		suite.Equal(http.StatusNotFound, resp.StatusCode)

		var response map[string]interface{}
		err = json.NewDecoder(resp.Body).Decode(&response)
		suite.Require().NoError(err)

		suite.Equal(float64(404), response["code"])
		suite.Equal("API接口不存在", response["message"])
	})
}

// 运行测试套件
func TestEnhancedAPITestSuite(t *testing.T) {
	suite.Run(t, new(EnhancedAPITestSuite))
}
