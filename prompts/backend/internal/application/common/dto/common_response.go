package dto

import (
	"time"
)

// PromptSummaryResponse 提示词摘要响应（用于列表）
type PromptSummaryResponse struct {
	ID          int64              `json:"id"`
	Title       string             `json:"title"`
	Description string             `json:"description"`
	Category    *CategoryResponse  `json:"category"`
	Tags        []string           `json:"tags"`
	Status      string             `json:"status"`
	Visibility  string             `json:"visibility"`
	IsTemplate  bool               `json:"is_template"`
	Statistics  StatisticsResponse `json:"statistics"`
	CreatedAt   time.Time          `json:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at"`
}

// CategoryResponse 分类响应
type CategoryResponse struct {
	ID          int64               `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	ParentID    int64               `json:"parent_id"`
	Icon        string              `json:"icon"`
	Color       string              `json:"color"`
	PromptCount int                 `json:"prompt_count"`
	SortOrder   int                 `json:"sort_order"`
	IsSystem    bool                `json:"is_system"`
	Level       int                 `json:"level"`
	Path        []string            `json:"path"`
	Children    []*CategoryResponse `json:"children,omitempty"`
	CreatedBy   int64               `json:"created_by"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// TagResponse 标签响应
type TagResponse struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Color       string    `json:"color"`
	UsageCount  int       `json:"usage_count"`
	CreatedBy   int64     `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TrendDataResponse 趋势数据响应
type TrendDataResponse struct {
	Date  string `json:"date"`
	Count int    `json:"count"`
}

// StatisticsResponse 统计响应
type StatisticsResponse struct {
	UsageCount    int     `json:"usage_count"`
	ViewCount     int     `json:"view_count"`
	ShareCount    int     `json:"share_count"`
	FavoriteCount int     `json:"favorite_count"`
	Rating        float64 `json:"rating"`
	RatingCount   int     `json:"rating_count"`
}
