package routes

import (
	"context"
	"platforms-pkg/grpcregistry"
	"platforms-pkg/httpmiddleware"
	"platforms-pkg/logiface"
	"platforms-user/api/userpb"

	"google.golang.org/grpc"
)

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct{}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string) *httpmiddleware.AuthedUser {
	logger := logiface.GetLogger()

	// 使用gRPC客户端获取用户信息
	client, err := grpcregistry.GetClientGlobal(ctx, "platforms-user", func(conn *grpc.ClientConn) interface{} {
		return userpb.NewUserServiceClient(conn)
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user service client", logiface.Error(err))
		return nil
	}

	userClient, ok := client.(userpb.UserServiceClient)
	if !ok {
		logger.Error(ctx, "Failed to cast to UserServiceClient")
		return nil
	}

	resp, err := userClient.GetUserInfoByToken(ctx, &userpb.GetUserInfoByTokenRequest{
		Token: token,
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user info by token", logiface.Error(err))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid or expired token")
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   resp.Data.UserId,
		Username: resp.Data.Username,
		RealName: resp.Data.RealName,
		Email:    resp.Data.Email,
		TenantId: resp.Data.TenantId,
	}
}

// TenantInfoProviderAdapter 租户信息提供者适配器
type TenantInfoProviderAdapter struct{}

// GetTenantInfo 实现 TenantInfoProvider 接口
func (a *TenantInfoProviderAdapter) GetTenantInfo(ctx context.Context, tenantCode string) *httpmiddleware.TenantInfo {
	logger := logiface.GetLogger()

	// 使用gRPC客户端获取租户信息
	client, err := grpcregistry.GetClientGlobal(ctx, "platforms-user", func(conn *grpc.ClientConn) interface{} {
		return userpb.NewUserServiceClient(conn)
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user service client", logiface.Error(err))
		return nil
	}

	userClient, ok := client.(userpb.UserServiceClient)
	if !ok {
		logger.Error(ctx, "Failed to cast to UserServiceClient")
		return nil
	}

	resp, err := userClient.GetTenantInfoByCode(ctx, &userpb.GetTenantInfoByCodeRequest{
		TenantCode: tenantCode,
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get tenant info by code", logiface.Error(err))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid tenant code")
		return nil
	}

	return &httpmiddleware.TenantInfo{
		TenantId:   resp.Data.TenantId,
		TenantCode: resp.Data.TenantCode,
		TenantName: resp.Data.TenantName,
	}
}
