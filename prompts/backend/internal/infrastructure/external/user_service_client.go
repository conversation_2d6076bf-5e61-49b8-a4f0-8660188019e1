package external

import (
	"context"
	"platforms-pkg/logiface"
)

// UserServiceClient 用户服务gRPC客户端
type UserServiceClient struct {
	logger logiface.Logger
}

// NewUserServiceClient 创建用户服务客户端
func NewUserServiceClient(logger logiface.Logger) *UserServiceClient {
	return &UserServiceClient{
		logger: logger,
	}
}

// GetUserInfoByToken 通过token获取用户信息
func (c *UserServiceClient) GetUserInfoByToken(ctx context.Context, token string) (*UserInfoResponse, error) {
	// 这里应该调用实际的gRPC服务
	// 暂时返回模拟数据
	return &UserInfoResponse{
		Code: 0,
		Data: &UserInfo{
			UserId:   "user123",
			Username: "testuser",
			RealName: "Test User",
			Email:    "<EMAIL>",
			TenantId: "tenant123",
		},
	}, nil
}

// GetTenantInfoByCode 通过租户代码获取租户信息
func (c *UserServiceClient) GetTenantInfoByCode(ctx context.Context, tenantCode string) (*TenantInfoResponse, error) {
	// 这里应该调用实际的gRPC服务
	// 暂时返回模拟数据
	return &TenantInfoResponse{
		Code: 0,
		Data: &TenantInfo{
			TenantId:   "tenant123",
			TenantCode: tenantCode,
			TenantName: "Test Tenant",
		},
	}, nil
}

// Health 健康检查
func (c *UserServiceClient) Health(ctx context.Context) error {
	// 这里应该检查gRPC连接状态
	return nil
}

// UserInfoResponse 用户信息响应
type UserInfoResponse struct {
	Code int       `json:"code"`
	Data *UserInfo `json:"data"`
}

// UserInfo 用户信息
type UserInfo struct {
	UserId   string `json:"user_id"`
	Username string `json:"username"`
	RealName string `json:"real_name"`
	Email    string `json:"email"`
	TenantId string `json:"tenant_id"`
}

// TenantInfoResponse 租户信息响应
type TenantInfoResponse struct {
	Code int         `json:"code"`
	Data *TenantInfo `json:"data"`
}

// TenantInfo 租户信息
type TenantInfo struct {
	TenantId   string `json:"tenant_id"`
	TenantCode string `json:"tenant_code"`
	TenantName string `json:"tenant_name"`
}
