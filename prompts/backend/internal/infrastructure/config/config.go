package config

import (
	"fmt"
	"os"
	"path/filepath"
	common_database "platforms-pkg/db"
	"strings"
	"sync"
	"time"

	"platforms-pkg/nacosconfig"

	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	ServiceName           string                   `mapstructure:"serviceName"`
	Server                ServerConfig             `mapstructure:"server"`
	Database              DatabaseConfig           `mapstructure:"database"`
	Log                   map[string]LogConfig     `mapstructure:"log"`
	Redis                 RedisConfig              `mapstructure:"redis"`
	GRPC                  GRPCConfig               `mapstructure:"grpc"`
	Otel                  OtelConfig               `mapstructure:"otel"`
	Nacos                 NacosConfig              `mapstructure:"nacos"`
	App                   AppConfig                `mapstructure:"app"`
	GRPCClients           GRPCClientsConfig        `mapstructure:"grpc_clients"`
	GRPCSubscriptions     []GRPCSubscriptionConfig `mapstructure:"grpcSubscriptions"`
	configChangeCallbacks []func(*Config)
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int           `mapstructure:"port"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
	IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
	Env          string        `mapstructure:"env"`
	Mode         string        `mapstructure:"mode"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL common_database.MySQLConfig `mapstructure:"mysql"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level         string                 `mapstructure:"level"`
	Format        string                 `mapstructure:"format"`
	Output        string                 `mapstructure:"output"`
	MaxSize       int                    `mapstructure:"max_size"`
	MaxBackups    int                    `mapstructure:"max_backups"`
	MaxAge        int                    `mapstructure:"max_age"`
	Compress      bool                   `mapstructure:"compress"`
	InitialFields map[string]interface{} `mapstructure:"initial_fields"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	Database int    `mapstructure:"database"`
	PoolSize int    `mapstructure:"pool_size"`
}

// GRPCConfig gRPC配置
type GRPCConfig struct {
	Port           int               `mapstructure:"port"`
	ServiceName    string            `mapstructure:"service_name"`
	Group          string            `mapstructure:"group"`
	Namespace      string            `mapstructure:"namespace"`
	Weight         float64           `mapstructure:"weight"`
	EnableRegister bool              `mapstructure:"enable_register"`
	Metadata       map[string]string `mapstructure:"metadata"`
}

// OtelConfig OpenTelemetry配置
type OtelConfig struct {
	Endpoint string `mapstructure:"endpoint"`
}

// NacosConfig Nacos配置
type NacosConfig struct {
	Host      string `mapstructure:"host"`
	Port      int    `mapstructure:"port"`
	Namespace string `mapstructure:"namespace"`
	Group     string `mapstructure:"group"`
	Username  string `mapstructure:"username"`
	Password  string `mapstructure:"password"`
}

// AppConfig 应用配置
type AppConfig struct {
	ServiceName string `mapstructure:"serviceName"`
	Env         string `mapstructure:"env"`
}

// GRPCClientsConfig gRPC客户端配置
type GRPCClientsConfig struct {
	UsersAddr string `mapstructure:"users_addr"`
}

// GRPCSubscriptionConfig gRPC订阅配置
type GRPCSubscriptionConfig struct {
	ServiceName    string `mapstructure:"serviceName"`
	Group          string `mapstructure:"group"`
	Namespace      string `mapstructure:"namespace"`
	Strategy       string `mapstructure:"strategy"`
	Subscribe      bool   `mapstructure:"subscribe"`
	ConnTimeout    string `mapstructure:"connTimeout"`
	MaxConnections int    `mapstructure:"maxConnections"`
}

// 全局配置实例
var globalConfig *Config
var configMutex sync.RWMutex

// GetConfig 获取全局配置
func GetConfig() *Config {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return globalConfig
}

// SetConfig 设置全局配置
func SetConfig(config *Config) {
	configMutex.Lock()
	defer configMutex.Unlock()
	globalConfig = config
}

// LoadConfig 加载配置
func LoadConfig() (*Config, error) {
	// 检查是否使用 Nacos（从环境变量）
	useNacos := getEnvOrDefault("USE_NACOS", "true") == "true"

	if !useNacos {
		// 从本地文件加载配置
		config, err := loadConfigFromFile("")
		if err != nil {
			return nil, fmt.Errorf("failed to load config from file: %w", err)
		}
		setDefaults(config)
		SetConfig(config)
		fmt.Println("Successfully loaded config from local file")
		return config, nil
	}

	// 使用 Nacos 加载配置
	config, err := loadConfigFromNacos()
	if err != nil {
		fmt.Printf("Failed to load from Nacos, falling back to local file: %v\n", err)
		// 回退到本地文件
		config, err = loadConfigFromFile("")
		if err != nil {
			return nil, fmt.Errorf("failed to load config from both Nacos and local file: %w", err)
		}
	}

	setDefaults(config)
	SetConfig(config)
	fmt.Printf("Successfully loaded config, server_port=%d, mysql_host=%s\n",
		config.Server.Port, config.Database.MySQL.Host)

	return config, nil
}

// loadConfigFromNacos 从 Nacos 加载配置
func loadConfigFromNacos() (*Config, error) {
	serviceName := getEnvOrDefault("SERVICE_NAME", "platforms-prompts")
	// 使用公共的 NewNacosConfigFromEnv 函数创建 Nacos 配置
	dataId := getEnvOrDefault("NACOS_DATA_ID", serviceName)
	nacosCfg := nacosconfig.NewNacosConfigFromEnv(dataId)

	// 创建 Nacos 客户端
	nacosClient, err := nacosconfig.NewNacosClient(nacosCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create nacos client: %w", err)
	}
	defer nacosClient.Close()

	// 获取配置内容
	content, err := nacosClient.GetConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get config from nacos: %w", err)
	}

	// 解析配置
	config := &Config{}
	viper.SetConfigType("toml")
	if err := viper.ReadConfig(strings.NewReader(content)); err != nil {
		return nil, fmt.Errorf("failed to read config content: %w", err)
	}

	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 设置 Nacos 连接信息
	host, port := parseAddress(nacosCfg.Address)
	config.Nacos = NacosConfig{
		Host:      host,
		Port:      int(port),
		Namespace: nacosCfg.Namespace,
		Group:     nacosCfg.Group,
		Username:  nacosCfg.User,
		Password:  nacosCfg.Password,
	}
	config.ServiceName = serviceName
	return config, nil
}

// loadConfigFromFile 从本地文件加载配置
func loadConfigFromFile(configPath string) (*Config, error) {
	// 设置配置文件路径
	if configPath == "" {
		configPath = "./configs"
	}
	configName := "app"
	configType := "toml"

	// 检查环境变量
	if envConfigPath := os.Getenv("CONFIG_PATH"); envConfigPath != "" {
		configPath = envConfigPath
	}

	// 设置viper配置
	viper.SetConfigName(configName)
	viper.SetConfigType(configType)
	viper.AddConfigPath(configPath)

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析配置
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// LoadConfigFromFile 从指定文件加载配置
func LoadConfigFromFile(filePath string) (*Config, error) {
	// 获取文件目录和文件名
	dir := filepath.Dir(filePath)
	fileName := filepath.Base(filePath)
	ext := filepath.Ext(fileName)
	name := fileName[:len(fileName)-len(ext)]

	// 设置viper配置
	viper.SetConfigName(name)
	viper.SetConfigType(ext[1:]) // 去掉点号
	viper.AddConfigPath(dir)

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析配置
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 设置默认值
	setDefaults(&config)

	return &config, nil
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 60 * time.Second
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 60 * time.Second
	}
	if config.Server.IdleTimeout == 0 {
		config.Server.IdleTimeout = 60 * time.Second
	}
	if config.Server.Env == "" {
		config.Server.Env = "dev"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "release"
	}

	// 设置日志默认值
	if config.Log == nil {
		config.Log = make(map[string]LogConfig)
	}
	if _, exists := config.Log["app"]; !exists {
		config.Log["app"] = LogConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		}
	}
	if _, exists := config.Log["access"]; !exists {
		config.Log["access"] = LogConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
		}
	}
	if _, exists := config.Log["error"]; !exists {
		config.Log["error"] = LogConfig{
			Level:  "error",
			Format: "json",
			Output: "stderr",
		}
	}

	// 设置Redis默认值
	if config.Redis.Host == "" {
		config.Redis.Host = "127.0.0.1"
	}
	if config.Redis.Port == 0 {
		config.Redis.Port = 6379
	}
	if config.Redis.PoolSize == 0 {
		config.Redis.PoolSize = 10
	}

	// 设置MySQL默认值
	if config.Database.MySQL.Host == "" {
		config.Database.MySQL.Host = "127.0.0.1"
	}
	if config.Database.MySQL.Port == 0 {
		config.Database.MySQL.Port = 3306
	}
	if config.Database.MySQL.Charset == "" {
		config.Database.MySQL.Charset = "utf8mb4"
	}
	if config.Database.MySQL.Loc == "" {
		config.Database.MySQL.Loc = "Local"
	}
	if config.Database.MySQL.MaxOpenConns == 0 {
		config.Database.MySQL.MaxOpenConns = 100
	}
	if config.Database.MySQL.MaxIdleConns == 0 {
		config.Database.MySQL.MaxIdleConns = 10
	}
	if config.Database.MySQL.ConnMaxLifetime == "" {
		config.Database.MySQL.ConnMaxLifetime = "300s"
	}

	// 设置gRPC默认值
	if config.GRPC.Port == 0 {
		config.GRPC.Port = 9090
	}
	if config.GRPC.Group == "" {
		config.GRPC.Group = "DEFAULT_GROUP"
	}
	if config.GRPC.Namespace == "" {
		config.GRPC.Namespace = "public"
	}
	if config.GRPC.Weight == 0 {
		config.GRPC.Weight = 1.0
	}

	// 设置Nacos默认值
	if config.Nacos.Host == "" {
		config.Nacos.Host = "127.0.0.1"
	}
	if config.Nacos.Port == 0 {
		config.Nacos.Port = 8848
	}
	if config.Nacos.Group == "" {
		config.Nacos.Group = "DEFAULT_GROUP"
	}
	if config.Nacos.Namespace == "" {
		config.Nacos.Namespace = "public"
	}

	// 设置OpenTelemetry默认值
	if config.Otel.Endpoint == "" {
		config.Otel.Endpoint = "http://localhost:4317"
	}
}

// getEnvOrDefault 获取环境变量或默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// GetServerReadTimeout 获取服务器读取超时时间
func (c *Config) GetServerReadTimeout() time.Duration {
	return c.Server.ReadTimeout
}

// GetServerWriteTimeout 获取服务器写入超时时间
func (c *Config) GetServerWriteTimeout() time.Duration {
	return c.Server.WriteTimeout
}

// GetServerIdleTimeout 获取服务器空闲超时时间
func (c *Config) GetServerIdleTimeout() time.Duration {
	return c.Server.IdleTimeout
}

// ListenNacosConfigChange 监听 Nacos 配置变化
func ListenNacosConfigChange(serviceName string) error {
	// 使用公共的 NewNacosConfigFromEnv 函数创建 Nacos 配置
	dataId := getEnvOrDefault("NACOS_DATA_ID", serviceName)
	nacosCfg := nacosconfig.NewNacosConfigFromEnv(dataId)

	// 创建 Nacos 客户端
	nacosClient, err := nacosconfig.NewNacosClient(nacosCfg)
	if err != nil {
		return fmt.Errorf("failed to create nacos client: %w", err)
	}

	// 启动配置监听
	go func() {
		if err := nacosClient.ListenConfig(func(content string) {
			// 解析新配置
			newConfig, err := parseConfigContent(content)
			if err != nil {
				fmt.Printf("Failed to parse config content: %v\n", err)
				return
			}

			// 设置默认值
			setDefaults(newConfig)

			// 更新全局配置
			SetConfig(newConfig)

			// 触发配置变更回调
			triggerConfigChangeCallbacks(newConfig)

			fmt.Printf("Config updated from Nacos: server_port=%d, mysql_host=%s\n",
				newConfig.Server.Port, newConfig.Database.MySQL.Host)
		}); err != nil {
			fmt.Printf("Failed to listen config changes: %v\n", err)
		}
	}()

	return nil
}

// parseAddress 解析地址字符串
func parseAddress(addr string) (string, uint64) {
	parts := strings.Split(addr, ":")
	if len(parts) != 2 {
		return "127.0.0.1", 8848
	}
	host := parts[0]
	port := uint64(8848)
	if p, err := parseUint64(parts[1]); err == nil {
		port = p
	}
	return host, port
}

// parseUint64 解析无符号整数
func parseUint64(s string) (uint64, error) {
	var result uint64
	_, err := fmt.Sscanf(s, "%d", &result)
	return result, err
}

// parseConfigContent 解析配置内容
func parseConfigContent(content string) (*Config, error) {
	// 使用 viper 解析配置内容
	viper.SetConfigType("toml")
	if err := viper.ReadConfig(strings.NewReader(content)); err != nil {
		return nil, fmt.Errorf("failed to read config content: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// RegisterConfigChangeCallback 注册配置变更回调
func RegisterConfigChangeCallback(callback func(*Config)) {
	configMutex.Lock()
	defer configMutex.Unlock()

	if globalConfig != nil {
		globalConfig.configChangeCallbacks = append(globalConfig.configChangeCallbacks, callback)
	}
}

// triggerConfigChangeCallbacks 触发配置变更回调
func triggerConfigChangeCallbacks(config *Config) {
	configMutex.RLock()
	callbacks := make([]func(*Config), len(config.configChangeCallbacks))
	copy(callbacks, config.configChangeCallbacks)
	configMutex.RUnlock()

	for _, callback := range callbacks {
		go callback(config)
	}
}
