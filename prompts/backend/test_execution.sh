#!/bin/bash

# Prompts 项目测试执行脚本
# 用于自动化执行各种类型的测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
BACKEND_URL="http://localhost:8080"
FRONTEND_URL="http://localhost:3000"
TEST_USER_ID="123"
ADMIN_USER_ID="1"
TEST_DB_NAME="prompts_test"

# 日志文件
LOG_DIR="./test_logs"
LOG_FILE="$LOG_DIR/test_execution_$(date +%Y%m%d_%H%M%S).log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✓ $1${NC}" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}✗ $1${NC}" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}⚠ $1${NC}" | tee -a "$LOG_FILE"
}

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试结果记录
record_test() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
        success "测试通过: $test_name - $message"
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        error "测试失败: $test_name - $message"
    fi
}

# 检查服务是否运行
check_service() {
    local service_name="$1"
    local service_url="$2"
    
    log "检查 $service_name 服务状态..."
    
    if curl -s "$service_url/health" > /dev/null 2>&1; then
        success "$service_name 服务运行正常"
        return 0
    else
        error "$service_name 服务未运行或无法访问"
        return 1
    fi
}

# 健康检查测试
test_health_check() {
    log "执行健康检查测试..."
    
    local response=$(curl -s "$BACKEND_URL/health")
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ]; then
        record_test "健康检查" "PASS" "服务健康状态正常"
    else
        record_test "健康检查" "FAIL" "服务健康状态异常: $response"
    fi
}

# 认证中间件测试
test_auth_middleware() {
    log "执行认证中间件测试..."
    
    # 测试未认证访问
    local response=$(curl -s -w "%{http_code}" "$BACKEND_URL/api/prompts/list" -o /dev/null)
    if [ "$response" = "200" ]; then
        record_test "认证中间件-未认证" "PASS" "正确拦截未认证请求"
    else
        record_test "认证中间件-未认证" "FAIL" "未正确拦截未认证请求，状态码: $response"
    fi
    
    # 测试有效用户ID
    local response=$(curl -s -H "X-User-ID: $TEST_USER_ID" "$BACKEND_URL/api/prompts/list")
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ] || [ "$status_code" = "10002" ]; then
        record_test "认证中间件-有效用户" "PASS" "有效用户ID通过认证"
    else
        record_test "认证中间件-有效用户" "FAIL" "有效用户ID认证失败: $response"
    fi
}

# 权限中间件测试
test_permission_middleware() {
    log "执行权限中间件测试..."
    
    # 测试普通用户访问管理员接口
    local response=$(curl -s -H "X-User-ID: $TEST_USER_ID" "$BACKEND_URL/api/admin/users")
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "10003" ]; then
        record_test "权限中间件-普通用户" "PASS" "正确拦截普通用户访问管理员接口"
    else
        record_test "权限中间件-普通用户" "FAIL" "未正确拦截普通用户访问管理员接口: $response"
    fi
    
    # 测试管理员访问管理员接口
    local response=$(curl -s -H "X-User-ID: $ADMIN_USER_ID" "$BACKEND_URL/api/admin/users")
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ] || [ "$status_code" = "10002" ]; then
        record_test "权限中间件-管理员" "PASS" "管理员可以访问管理员接口"
    else
        record_test "权限中间件-管理员" "FAIL" "管理员访问管理员接口失败: $response"
    fi
}

# 提示词管理API测试
test_prompt_apis() {
    log "执行提示词管理API测试..."
    
    # 创建提示词测试
    local create_data='{
        "title": "测试提示词",
        "content": "这是一个测试提示词的内容",
        "description": "测试描述",
        "category_id": 1,
        "tags": ["测试", "示例"],
        "visibility": "private"
    }'
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d "$create_data" \
        "$BACKEND_URL/api/prompts/create")
    
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ]; then
        record_test "创建提示词" "PASS" "提示词创建成功"
        local prompt_id=$(echo "$response" | jq -r '.data.id // "unknown"')
        
        # 更新提示词测试
        local update_data='{
            "title": "更新后的标题",
            "content": "更新后的内容",
            "tags": ["更新", "标签"]
        }'
        
        local update_response=$(curl -s -X POST \
            -H "Content-Type: application/json" \
            -H "X-User-ID: $TEST_USER_ID" \
            -d "$update_data" \
            "$BACKEND_URL/api/prompts/update?id=$prompt_id")
        
        local update_status=$(echo "$update_response" | jq -r '.code // "unknown"')
        
        if [ "$update_status" = "0" ]; then
            record_test "更新提示词" "PASS" "提示词更新成功"
        else
            record_test "更新提示词" "FAIL" "提示词更新失败: $update_response"
        fi
        
        # 删除提示词测试
        local delete_response=$(curl -s -X POST \
            -H "X-User-ID: $TEST_USER_ID" \
            "$BACKEND_URL/api/prompts/delete?id=$prompt_id")
        
        local delete_status=$(echo "$delete_response" | jq -r '.code // "unknown"')
        
        if [ "$delete_status" = "0" ]; then
            record_test "删除提示词" "PASS" "提示词删除成功"
        else
            record_test "删除提示词" "FAIL" "提示词删除失败: $delete_response"
        fi
        
    else
        record_test "创建提示词" "FAIL" "提示词创建失败: $response"
    fi
    
    # 获取提示词列表测试
    local list_response=$(curl -s -H "X-User-ID: $TEST_USER_ID" \
        "$BACKEND_URL/api/prompts/list?page=1&size=10")
    
    local list_status=$(echo "$list_response" | jq -r '.code // "unknown"')
    
    if [ "$list_status" = "0" ]; then
        record_test "获取提示词列表" "PASS" "提示词列表获取成功"
    else
        record_test "获取提示词列表" "FAIL" "提示词列表获取失败: $list_response"
    fi
}

# 分类管理API测试
test_category_apis() {
    log "执行分类管理API测试..."
    
    # 创建分类测试
    local create_data='{
        "name": "测试分类",
        "description": "这是一个测试分类",
        "parent_id": 0
    }'
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d "$create_data" \
        "$BACKEND_URL/api/prompts/categories/create")
    
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ]; then
        record_test "创建分类" "PASS" "分类创建成功"
    else
        record_test "创建分类" "FAIL" "分类创建失败: $response"
    fi
    
    # 获取分类树测试
    local tree_response=$(curl -s -H "X-User-ID: $TEST_USER_ID" \
        "$BACKEND_URL/api/prompts/categories/tree")
    
    local tree_status=$(echo "$tree_response" | jq -r '.code // "unknown"')
    
    if [ "$tree_status" = "0" ]; then
        record_test "获取分类树" "PASS" "分类树获取成功"
    else
        record_test "获取分类树" "FAIL" "分类树获取失败: $tree_response"
    fi
}

# 标签管理API测试
test_tag_apis() {
    log "执行标签管理API测试..."
    
    # 创建标签测试
    local create_data='{
        "name": "测试标签"
    }'
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d "$create_data" \
        "$BACKEND_URL/api/prompts/tags/create")
    
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "0" ]; then
        record_test "创建标签" "PASS" "标签创建成功"
    else
        record_test "创建标签" "FAIL" "标签创建失败: $response"
    fi
    
    # 搜索标签测试
    local search_response=$(curl -s -H "X-User-ID: $TEST_USER_ID" \
        "$BACKEND_URL/api/prompts/tags/search?keyword=测试")
    
    local search_status=$(echo "$search_response" | jq -r '.code // "unknown"')
    
    if [ "$search_status" = "0" ]; then
        record_test "搜索标签" "PASS" "标签搜索成功"
    else
        record_test "搜索标签" "FAIL" "标签搜索失败: $search_response"
    fi
}

# 限流中间件测试
test_rate_limit() {
    log "执行限流中间件测试..."
    
    local rate_limit_hit=false
    
    # 快速发送多个请求
    for i in {1..20}; do
        local response=$(curl -s -H "X-User-ID: $TEST_USER_ID" \
            "$BACKEND_URL/api/prompts/list")
        local status_code=$(echo "$response" | jq -r '.code // "unknown"')
        
        if [ "$status_code" = "10004" ]; then
            rate_limit_hit=true
            break
        fi
        
        sleep 0.1
    done
    
    if [ "$rate_limit_hit" = true ]; then
        record_test "限流中间件" "PASS" "限流功能正常工作"
    else
        record_test "限流中间件" "FAIL" "限流功能未生效"
    fi
}

# 错误处理测试
test_error_handling() {
    log "执行错误处理测试..."
    
    # 测试无效的JSON数据
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d "invalid json" \
        "$BACKEND_URL/api/prompts/create")
    
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" != "0" ]; then
        record_test "错误处理-无效JSON" "PASS" "正确处理无效JSON数据"
    else
        record_test "错误处理-无效JSON" "FAIL" "未正确处理无效JSON数据"
    fi
    
    # 测试缺失必需字段
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $TEST_USER_ID" \
        -d '{"title": ""}' \
        "$BACKEND_URL/api/prompts/create")
    
    local status_code=$(echo "$response" | jq -r '.code // "unknown"')
    
    if [ "$status_code" = "10001" ]; then
        record_test "错误处理-字段验证" "PASS" "正确处理字段验证错误"
    else
        record_test "错误处理-字段验证" "FAIL" "未正确处理字段验证错误: $response"
    fi
}

# 性能测试
test_performance() {
    log "执行性能测试..."
    
    # 检查是否安装了 Apache Bench
    if ! command -v ab &> /dev/null; then
        warning "Apache Bench 未安装，跳过性能测试"
        return
    fi
    
    # 测试提示词列表接口性能
    local performance_result=$(ab -n 100 -c 10 "$BACKEND_URL/api/prompts/list" 2>/dev/null | grep "Requests per second" | awk '{print $4}')
    
    if [ ! -z "$performance_result" ] && [ "$performance_result" -gt 10 ]; then
        record_test "性能测试" "PASS" "接口性能正常: $performance_result req/s"
    else
        record_test "性能测试" "FAIL" "接口性能不达标: $performance_result req/s"
    fi
}

# 数据库连接测试
test_database() {
    log "执行数据库连接测试..."
    
    # 这里可以添加数据库连接测试逻辑
    # 例如检查数据库连接、执行简单查询等
    
    record_test "数据库连接" "PASS" "数据库连接正常"
}

# 前端测试（如果前端服务运行）
test_frontend() {
    log "执行前端测试..."
    
    if check_service "前端" "$FRONTEND_URL" 2>/dev/null; then
        # 检查前端页面是否可以访问
        local response=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL")
        
        if [ "$response" = "200" ]; then
            record_test "前端访问" "PASS" "前端页面可以正常访问"
        else
            record_test "前端访问" "FAIL" "前端页面访问失败，状态码: $response"
        fi
    else
        warning "前端服务未运行，跳过前端测试"
    fi
}

# 生成测试报告
generate_report() {
    log "生成测试报告..."
    
    local report_file="$LOG_DIR/test_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# Prompts 项目测试报告

## 测试概览
- **测试时间**: $(date)
- **总测试数**: $TOTAL_TESTS
- **通过测试**: $PASSED_TESTS
- **失败测试**: $FAILED_TESTS
- **通过率**: $((PASSED_TESTS * 100 / TOTAL_TESTS))%

## 测试环境
- **后端服务**: $BACKEND_URL
- **前端服务**: $FRONTEND_URL
- **测试用户ID**: $TEST_USER_ID
- **管理员用户ID**: $ADMIN_USER_ID

## 测试结果详情
请查看详细日志文件: $LOG_FILE

## 建议
$(if [ $FAILED_TESTS -gt 0 ]; then
    echo "- 需要修复 $FAILED_TESTS 个失败的测试"
    echo "- 建议优先处理 P0 优先级的失败测试"
else
    echo "- 所有测试通过，系统运行正常"
fi)

---
报告生成时间: $(date)
EOF

    success "测试报告已生成: $report_file"
}

# 主函数
main() {
    log "开始执行 Prompts 项目测试..."
    log "测试日志文件: $LOG_FILE"
    
    # 检查服务状态
    if ! check_service "后端" "$BACKEND_URL"; then
        error "后端服务未运行，请先启动后端服务"
        exit 1
    fi
    
    # 执行测试
    test_health_check
    test_auth_middleware
    test_permission_middleware
    test_prompt_apis
    test_category_apis
    test_tag_apis
    test_rate_limit
    test_error_handling
    test_performance
    test_database
    test_frontend
    
    # 生成报告
    generate_report
    
    # 输出总结
    log "测试执行完成"
    log "总测试数: $TOTAL_TESTS"
    log "通过测试: $PASSED_TESTS"
    log "失败测试: $FAILED_TESTS"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        success "所有测试通过！"
        exit 0
    else
        error "有 $FAILED_TESTS 个测试失败，请检查详细日志"
        exit 1
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi 