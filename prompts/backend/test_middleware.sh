#!/bin/bash

# Prompts 中间件测试脚本
# 用于测试各种中间件功能

BASE_URL="http://localhost:8080"
API_PREFIX="/api/prompts"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_request() {
    local method=$1
    local url=$2
    local headers=$3
    local data=$4
    local description=$5

    log_info "测试: $description"
    log_info "请求: $method $url"

    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -H "$headers" "$BASE_URL$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "$headers" -d "$data" "$BASE_URL$url")
    fi

    # 分离响应体和状态码
    body=$(echo "$response" | head -n -1)
    status_code=$(echo "$response" | tail -n 1)

    echo "状态码: $status_code"
    echo "响应体: $body"
    echo "---"
}

# 主测试函数
main() {
    log_info "开始测试 Prompts 中间件功能"
    echo

    # 1. 测试健康检查（不需要认证）
    log_info "=== 1. 健康检查测试 ==="
    test_request "GET" "/health" "" "" "健康检查接口"

    # 2. 测试认证中间件
    log_info "=== 2. 认证中间件测试 ==="
    
    # 2.1 无用户ID的请求
    test_request "GET" "$API_PREFIX/tags/list" "" "" "无用户ID的标签列表请求"
    
    # 2.2 有用户ID的请求
    test_request "GET" "$API_PREFIX/tags/list" "X-User-ID: 123" "" "有用户ID的标签列表请求"
    
    # 2.3 无效用户ID的请求
    test_request "GET" "$API_PREFIX/tags/list" "X-User-ID: invalid" "" "无效用户ID的标签列表请求"

    # 3. 测试权限中间件
    log_info "=== 3. 权限中间件测试 ==="
    
    # 3.1 普通用户权限
    test_request "POST" "$API_PREFIX/tags/create" "X-User-ID: 123" '{"name":"测试标签"}' "普通用户创建标签"
    
    # 3.2 管理员权限
    test_request "POST" "$API_PREFIX/tags/create" "X-User-ID: 1" '{"name":"管理员标签"}' "管理员创建标签"

    # 4. 测试公开接口
    log_info "=== 4. 公开接口测试 ==="
    
    # 4.1 无认证的公开接口
    test_request "GET" "$API_PREFIX/tags/global" "" "" "无认证访问公开标签"
    
    # 4.2 有认证的公开接口
    test_request "GET" "$API_PREFIX/tags/global" "X-User-ID: 123" "" "有认证访问公开标签"

    # 5. 测试参数验证中间件
    log_info "=== 5. 参数验证中间件测试 ==="
    
    # 5.1 正常请求
    test_request "GET" "$API_PREFIX/tags/list?page=1&size=10" "X-User-ID: 123" "" "正常参数的分页请求"
    
    # 5.2 缺少必需参数
    test_request "GET" "$API_PREFIX/tags/detail" "X-User-ID: 123" "" "缺少ID参数的详情请求"

    # 6. 测试错误处理中间件
    log_info "=== 6. 错误处理中间件测试 ==="
    
    # 6.1 404错误
    test_request "GET" "/api/nonexistent" "X-User-ID: 123" "" "访问不存在的接口"
    
    # 6.2 405错误
    test_request "PUT" "$API_PREFIX/tags/list" "X-User-ID: 123" "" "使用不支持的HTTP方法"

    # 7. 测试CORS中间件
    log_info "=== 7. CORS中间件测试 ==="
    
    # 7.1 预检请求
    test_request "OPTIONS" "$API_PREFIX/tags/list" "Origin: http://localhost:3000" "" "CORS预检请求"
    
    # 7.2 跨域请求
    test_request "GET" "$API_PREFIX/tags/list" "X-User-ID: 123\nOrigin: http://localhost:3000" "" "跨域请求"

    # 8. 测试限流中间件
    log_info "=== 8. 限流中间件测试 ==="
    
    # 8.1 多次请求测试限流
    for i in {1..6}; do
        log_info "限流测试请求 $i/6"
        test_request "GET" "$API_PREFIX/tags/list" "X-User-ID: 123" "" "限流测试请求 $i"
        sleep 0.1
    done

    # 9. 测试提示词相关接口
    log_info "=== 9. 提示词接口测试 ==="
    
    # 9.1 创建提示词
    test_request "POST" "$API_PREFIX/prompts/create" "X-User-ID: 123" '{
        "title": "测试提示词",
        "content": "这是一个测试提示词的内容",
        "description": "测试描述",
        "tags": ["测试", "示例"],
        "visibility": "private"
    }' "创建提示词"
    
    # 9.2 获取提示词列表
    test_request "GET" "$API_PREFIX/prompts/list?page=1&size=10" "X-User-ID: 123" "" "获取提示词列表"
    
    # 9.3 搜索提示词
    test_request "GET" "$API_PREFIX/prompts/search?keyword=测试" "X-User-ID: 123" "" "搜索提示词"

    # 10. 测试分类相关接口
    log_info "=== 10. 分类接口测试 ==="
    
    # 10.1 创建分类
    test_request "POST" "$API_PREFIX/categories/create" "X-User-ID: 123" '{
        "name": "测试分类",
        "description": "这是一个测试分类",
        "parent_id": 0
    }' "创建分类"
    
    # 10.2 获取分类树
    test_request "GET" "$API_PREFIX/categories/tree" "X-User-ID: 123" "" "获取分类树"

    log_info "中间件测试完成"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    
    if curl -s "$BASE_URL/health" > /dev/null; then
        log_success "服务正在运行"
        return 0
    else
        log_error "服务未运行，请先启动服务"
        log_info "启动命令: cd prompts/backend && go run cmd/main.go"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "Prompts 中间件测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -u, --url      指定基础URL (默认: http://localhost:8080)"
    echo "  -c, --check    仅检查服务状态"
    echo
    echo "示例:"
    echo "  $0                    # 运行完整测试"
    echo "  $0 -u http://localhost:8083  # 使用自定义URL"
    echo "  $0 -c                 # 仅检查服务状态"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -c|--check)
            check_service
            exit $?
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 主程序
if check_service; then
    main
else
    exit 1
fi 