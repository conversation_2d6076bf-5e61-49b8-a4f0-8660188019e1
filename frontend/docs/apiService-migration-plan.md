# apiService 全量替换方案

## 一、背景

当前前端项目中，API 调用主要通过 axios 实例（api）进行，分布在 services 层和部分页面组件。为提升类型安全、统一响应结构、便于维护和对接后端规范，需将所有 api 的使用场景统一替换为 apiService。

---

## 二、全量替换范围

### 1. 主要涉及文件
- `frontend/src/services/` 目录下所有 service 文件（如 user.ts、role.ts、permission.ts、tenant.ts、resource.ts、department.ts、position.ts、emailAccount.ts、idgenerator.ts 等）
- 业务相关页面组件中如有直接使用 api 的地方
- 其他自定义 hooks、工具函数等

### 2. 典型调用方式
- `import { api } from '../utils/request'` 或 `import { api } from '@/utils/request'`
- `api.get/post/put/delete(...)`

---

## 三、替换原则

1. **统一导入**：全部替换为 `import { apiService } from '../utils/request'`
2. **方法替换**：所有 `api.get/post/put/delete` 替换为 `apiService.get/post/put/delete`
3. **类型安全**：返回类型统一为 `Promise<ApiResponse<T>>`，与后端统一响应结构保持一致
4. **业务处理**：业务代码需根据 `res.code` 判断是否成功，数据取 `res.data`
5. **兼容性**：如有特殊场景（如文件流、下载等），可单独处理

---

## 四、替换步骤

1. 全局搜索 `import { api`，批量替换为 `import { apiService`
2. 全局搜索 `api.get`、`api.post`、`api.put`、`api.delete`，批量替换为对应的 `apiService` 方法
3. 检查所有 service 层和页面组件，调整返回类型为 `ApiResponse<T>`，并根据需要调整业务逻辑
4. 检查所有直接使用 `res.data` 的地方，确保兼容新的响应结构（如需判断 code、message 等）
5. 补充/完善类型定义，确保类型安全
6. 重点关注测试覆盖，避免批量替换带来遗漏

---

## 五、注意事项

- 替换后所有 API 响应均为统一结构 `{ code, message, data, meta }`
- 业务层需根据 code 判断是否成功，避免直接假定 data 一定有值
- 如有全局错误处理、拦截器等逻辑，需同步适配 apiService
- 建议分模块、分阶段替换，逐步推进

---

## 六、后续建议

- 替换完成后，建议统一梳理和补充接口类型定义
- 可考虑自动化脚本辅助批量替换和检查
- 替换期间建议全量回归测试，确保功能无回归

---

如需具体文件/模块的替换清单或批量脚本示例，请联系前端负责人。 

## 七、api 与 apiService 返回值差异与迁移对照

### 1. 返回值结构对比

| 调用方式         | 返回值类型                      | 获取业务数据的方式           |
|------------------|-------------------------------|-----------------------------|
| `api.post(...)`  | `Promise<AxiosResponse<T>>`   | `res.data`                  |
| `apiService.post(...)` | `Promise<ApiResponse<T>>` | `res.data`                  |

- **api（axios 实例）**
  - 返回的是 AxiosResponse 对象，结构为：
    ```ts
    {
      data: ApiResponse<T>, // 这里的 data 才是后端的统一响应结构
      status: number,
      headers: any,
      ...
    }
    ```
  - 业务代码通常写法：
    ```ts
    const res = await api.post(...);
    // 需要 res.data.code、res.data.data
    ```
- **apiService**
  - 直接返回后端的统一响应结构 `ApiResponse<T>`，即
    ```ts
    {
      code: number;
      message: string;
      data: T;
      meta?: any;
    }
    ```
  - 业务代码写法：
    ```ts
    const res = await apiService.post(...);
    // 直接用 res.code、res.data
    ```

---

### 2. 典型迁移场景对照

#### 场景一：只关心业务数据

**原写法（api）：**
```ts
const res = await api.post(API_ENDPOINTS.USER.LIST, params);
return res.data.data; // 取业务数据
```
**迁移后（apiService）：**
```ts
const res = await apiService.post(API_ENDPOINTS.USER.LIST, params);
return res.data; // 直接取业务数据
```

#### 场景二：需要判断业务状态码

**原写法（api）：**
```ts
const res = await api.post(API_ENDPOINTS.USER.LIST, params);
if (res.data.code === 0) {
  return res.data.data;
} else {
  throw new Error(res.data.message);
}
```
**迁移后（apiService）：**
```ts
const res = await apiService.post(API_ENDPOINTS.USER.LIST, params);
if (res.code === 0) {
  return res.data;
} else {
  throw new Error(res.message);
}
```

#### 场景三：直接返回完整响应

**原写法（api）：**
```ts
const res = await api.post(API_ENDPOINTS.USER.LIST, params);
return res.data; // 返回 ApiResponse<T>
```
**迁移后（apiService）：**
```ts
const res = await apiService.post(API_ENDPOINTS.USER.LIST, params);
return res; // 直接就是 ApiResponse<T>
```

#### 场景四：链式处理

**原写法（api）：**
```ts
api.post(API_ENDPOINTS.USER.LIST, params).then(res => {
  setData(res.data.data);
});
```
**迁移后（apiService）：**
```ts
apiService.post(API_ENDPOINTS.USER.LIST, params).then(res => {
  setData(res.data);
});
```

---

### 3. 迁移注意点

- api 返回的是 AxiosResponse，需 res.data 才是后端响应结构。
- apiService 直接返回后端响应结构，无需再 .data。
- 迁移时需检查所有 res.data.data、res.data.code、res.data.message 等用法，判断是否需要去掉一层 `.data`。
- 类型定义：apiService 返回值类型应为 `ApiResponse<T>`，而不是 `AxiosResponse<T>`。

---

### 4. 建议迁移步骤

1. 全局搜索 `api.` 的调用，定位所有需要迁移的点。
2. 检查每一处 `res.data` 的用法：
   - 如果是 `res.data.data`，迁移后应为 `res.data`
   - 如果是 `res.data.code`，迁移后应为 `res.code`
   - 如果是 `res.data` 直接返回，迁移后直接返回 `res`
3. 检查类型定义，确保 Promise 返回值为 `ApiResponse<T>`。
4. 补充/调整单元测试，确保数据获取逻辑无误。 

## 八、进一步迁移规范与最佳实践

### 1. service 层返回值简化规范

- **原写法**：
  ```ts
  const res = await api.post(...);
  return res.data;
  ```
- **迁移后**：
  ```ts
  return await apiService.post(...);
  ```
- **类型声明**：
  - 明确 service 层返回类型为 `Promise<ApiResponse<T>>`，如：
    ```ts
    export async function getRoles(...): Promise<ApiResponse<Role[]>> {
      return await apiService.post<Role[]>(...);
    }
    ```

### 2. 典型场景举例

#### 角色管理（role.ts）
- 原：
  ```ts
  export async function getRoles(params?: ListRoleRequest): Promise<ListRoleResponse> {
    const res = await api.post(API_ENDPOINTS.ROLE.LIST, params || {});
    return res.data;
  }
  ```
- 迁移后：
  ```ts
  export async function getRoles(params?: ListRoleRequest): Promise<ApiResponse<Role[]>> {
    return await apiService.post<Role[]>(API_ENDPOINTS.ROLE.LIST, params || {});
  }
  ```

#### 资源管理（resource.ts）
- 原：
  ```ts
  export async function getResources(params?: ListResourcesRequest): Promise<ListResourcesResponse> {
    const res = await api.post(API_ENDPOINTS.RESOURCE.LIST, params || {});
    return res.data;
  }
  ```
- 迁移后：
  ```ts
  export async function getResources(params?: ListResourcesRequest): Promise<ApiResponse<Resource[]>> {
    return await apiService.post<Resource[]>(API_ENDPOINTS.RESOURCE.LIST, params || {});
  }
  ```

### 3. 迁移注意点
- 只要原来是 `return res.data;`，迁移后都可以直接 `return await apiService.xxx(...)`，类型声明为 `Promise<ApiResponse<T>>`。
- 如果原来是 `return res.data.data;`，则应改为 `return (await apiService.xxx(...)).data`。
- 这样可减少一层变量和类型歧义，代码更简洁。 

### 4. 统一 ApiResponse 类型规范

- 如果原 service 返回值不是 `Promise<ApiResponse<T>>`，而是自定义了类似 ApiResponse 的结构（如包含 code、message、data、meta 等字段），
  - 必须统一修正为 `Promise<ApiResponse<T>>`，直接使用 `types/api.ts` 中的 ApiResponse 类型。
  - 不允许在 service 层自定义“伪 ApiResponse”类型或结构，避免类型分裂和维护混乱。
- 迁移步骤：
  1. 检查所有 service 层导出的返回类型。
  2. 如发现自定义的响应结构（如 ListRoleResponse、ListResourcesResponse 等），应删除这些类型，直接用 ApiResponse<T> 替代。
  3. 业务代码如需用到 meta 字段，直接用 ApiResponse 的 meta 属性。
- 这样可确保全项目 API 响应结构和类型定义高度一致，便于维护和类型推断。 

### 5. 检查并修正嵌套自定义响应结构

- 如果 service 返回类型为 `Promise<ApiResponse<SomeCustomResponse>>`，需检查 `SomeCustomResponse` 是否为自定义的响应结构（如包含 code/message/data/meta）。
  - 如果是，则应直接用 `ApiResponse<T>` 替代，T 为实际业务数据类型（如 `Resource[]`、`Role[]`、`Position[]` 等）。
  - 不允许出现 `ApiResponse<ListPositionResponse>` 这类嵌套结构，避免类型冗余和歧义。
- 迁移步骤：
  1. 检查所有 service 层的返回类型。
  2. 如发现 `ListPositionResponse`、`ListRoleResponse` 等自定义响应类型，且其结构本质为 ApiResponse<T>，应删除该类型，直接用 `ApiResponse<T>` 替代。
  3. 例如：
     - 原：
       ```ts
       export async function getPositions(params?: ListPositionRequest): Promise<ApiResponse<ListPositionResponse>>
       ```
     - 迁移后：
       ```ts
       export async function getPositions(params?: ListPositionRequest): Promise<ApiResponse<Position[]>>
       ```
- 这样可确保所有 service 层返回值类型扁平、清晰，便于类型推断和维护。 