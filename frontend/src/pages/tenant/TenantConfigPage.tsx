import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  InputNumber,
  Button,
  message,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Tag,
  Alert,
  Spin,
  Tooltip,
  Select,
  Checkbox,
} from 'antd';
import {
  LockOutlined,
  UserAddOutlined,
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  BankOutlined,
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import { TenantConfigService, PasswordPolicy, RegistrationMethods, TenantInfo } from '../../services/tenantConfig';
import { TenantService } from '../../services/tenant';
import { showAPIError } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';
import { Tenant } from '../../types';
import { useApiCallWithKey } from '../../hooks/useApiCall';

const { Title, Text } = Typography;
const { Option } = Select;

const TenantConfigPage: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [passwordPolicy, setPasswordPolicy] = useState<PasswordPolicy | null>(null);
  const [registrationMethods, setRegistrationMethods] = useState<RegistrationMethods | null>(null);
  const [tenantInfoConfig, setTenantInfoConfig] = useState<TenantInfo | null>(null);
  const [tenantInfo, setTenantInfo] = useState<Tenant | null>(null);
  const [passwordForm] = Form.useForm();
  const [registrationForm] = Form.useForm();
  const [tenantInfoForm] = Form.useForm();

  // 使用自定义hook防止重复API调用
  const loadConfigsWithKey = useApiCallWithKey(
    async (tenantId: string) => {
      setLoading(true);
      try {
        const [passwordRes, registrationRes, tenantInfoRes] = await Promise.all([
          TenantConfigService.getPasswordPolicy(parseInt(tenantId)),
          TenantConfigService.getRegistrationMethods(parseInt(tenantId)),
          TenantConfigService.getTenantInfo(parseInt(tenantId)),
        ]);

        if (passwordRes.code === SUCCESS && passwordRes.data) {
          setPasswordPolicy(passwordRes.data.policy);
          passwordForm.setFieldsValue(passwordRes.data.policy);
        }

        if (registrationRes.code === SUCCESS && registrationRes.data) {
          setRegistrationMethods(registrationRes.data.methods);
          registrationForm.setFieldsValue(registrationRes.data.methods);
        }

        if (tenantInfoRes.code === SUCCESS && tenantInfoRes.data) {
          setTenantInfoConfig(tenantInfoRes.data.info);
          tenantInfoForm.setFieldsValue(tenantInfoRes.data.info);
        }
      } catch (error) {
        showAPIError(error);
      } finally {
        setLoading(false);
      }
    },
    `loadConfigs_${tenantId}`
  );

  const loadTenantInfoWithKey = useApiCallWithKey(
    async (tenantId: string) => {
      try {
        const response = await TenantService.getTenant({ id: parseInt(tenantId) });
        if (response.code === SUCCESS && response.data) {
          setTenantInfo(response.data);
        }
      } catch (error) {
        showAPIError(error);
      }
    },
    `loadTenantInfo_${tenantId}`
  );

  // 加载配置数据
  useEffect(() => {
    if (tenantId) {
      loadConfigsWithKey(tenantId);
      loadTenantInfoWithKey(tenantId);
    }
  }, [tenantId, loadConfigsWithKey, loadTenantInfoWithKey]);

  // 保存密码策略
  const handleSavePasswordPolicy = async (values: PasswordPolicy) => {
    if (!tenantId) return;
    
    setSaving(true);
    try {
      const response = await TenantConfigService.updatePasswordPolicy({
        tenant_id: parseInt(tenantId),
        policy: values,
      });

      if (response.code === SUCCESS) {
        message.success('密码策略保存成功');
        setPasswordPolicy(values);
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  };

  // 保存注册方式配置
  const handleSaveRegistrationMethods = async (values: RegistrationMethods) => {
    if (!tenantId) return;
    
    setSaving(true);
    try {
      const response = await TenantConfigService.updateRegistrationMethods({
        tenant_id: parseInt(tenantId),
        methods: values,
      });

      if (response.code === SUCCESS) {
        message.success('注册方式配置保存成功');
        setRegistrationMethods(values);
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  };

  // 保存租户信息配置
  const handleSaveTenantInfo = async (values: TenantInfo) => {
    if (!tenantId) return;
    
    setSaving(true);
    try {
      const response = await TenantConfigService.updateTenantInfo({
        tenant_id: parseInt(tenantId),
        info: values,
      });

      if (response.code === SUCCESS) {
        message.success('租户信息保存成功');
        setTenantInfoConfig(values);
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  };

  // 复制系统配置
  const handleCopySystemConfigs = async () => {
    if (!tenantId) return;
    
    try {
      const response = await TenantConfigService.copySystemConfigs(parseInt(tenantId));
      if (response.code === SUCCESS) {
        message.success('系统配置复制成功');
        loadConfigsWithKey(tenantId); // 重新加载配置
      } else {
        message.error(response.message || '复制失败');
      }
    } catch (error) {
      showAPIError(error);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载配置中...</div>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space align="center">
            <Tag color="blue">租户ID: {tenantId}</Tag>
            {tenantInfo && (
              <Tag color="green">租户名称: {tenantInfo.tenant_name}</Tag>
            )}
            <Text type="secondary" style={{ fontSize: '12px' }}>
              租户配置会覆盖系统默认配置。如果租户未配置某项设置，将使用系统默认值。
            </Text>
          </Space>
        </div>

        <Tabs 
          defaultActiveKey="password" 
          type="card"
          items={[
            {
              key: 'password',
              label: (
                <span>
                  <LockOutlined />
                  密码策略
                </span>
              ),
              children: (
            <Form
              form={passwordForm}
              layout="vertical"
              onFinish={handleSavePasswordPolicy}
              initialValues={{
                min_length: 8,
                max_length: 32,
                require_uppercase: true,
                require_lowercase: true,
                require_digits: true,
                require_special_chars: false,
                forbidden_patterns: ['123456', 'password', 'admin'],
                password_history_count: 5,
                expire_days: 90,
              }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="密码长度"
                    required
                  >
                    <Space>
                      <Form.Item
                        name="min_length"
                        noStyle
                        rules={[{ required: true, message: '请输入最小长度' }]}
                      >
                        <InputNumber
                          min={6}
                          max={128}
                          placeholder="最小长度"
                          style={{ width: 120 }}
                        />
                      </Form.Item>
                      <Text>至</Text>
                      <Form.Item
                        name="max_length"
                        noStyle
                        rules={[{ required: true, message: '请输入最大长度' }]}
                      >
                        <InputNumber
                          min={6}
                          max={128}
                          placeholder="最大长度"
                          style={{ width: 120 }}
                        />
                      </Form.Item>
                    </Space>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="密码过期天数"
                    name="expire_days"
                    rules={[{ required: true, message: '请输入过期天数' }]}
                  >
                    <InputNumber
                      min={0}
                      max={365}
                      placeholder="过期天数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>


              
              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    label="必须包含大写字母"
                    name="require_uppercase"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="必须包含小写字母"
                    name="require_lowercase"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="必须包含数字"
                    name="require_digits"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    label="必须包含特殊字符"
                    name="require_special_chars"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label="密码历史记录数"
                    name="password_history_count"
                    rules={[{ required: true, message: '请输入历史记录数' }]}
                  >
                    <InputNumber
                      min={0}
                      max={20}
                      placeholder="历史记录数"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="禁止使用的密码模式"
                    name="forbidden_patterns"
                  >
                    <Select
                      mode="tags"
                      placeholder="输入禁止的密码模式"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存密码策略
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => passwordForm.resetFields()}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
              )
            },
            {
              key: 'registration',
              label: (
                <span>
                  <UserAddOutlined />
                  注册方式
                </span>
              ),
              children: (
            <Form
              form={registrationForm}
              layout="vertical"
              onFinish={handleSaveRegistrationMethods}
              initialValues={{
                email: {
                  enabled: true,
                  require_verification: true,
                  manual_activation: false,
                },
                phone: {
                  enabled: true,
                  require_verification: true,
                  manual_activation: false,
                },
                oauth: {
                  enabled: true,
                  manual_activation: false,
                },
                admin_creation: {
                  enabled: true,
                  require_approval: false,
                },
              }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Card title="邮箱注册" size="small">
                    <Form.Item
                      label="启用邮箱注册"
                      name={['email', 'enabled']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      label="需要邮箱验证"
                      name={['email', 'require_verification']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, curValues) => 
                        prevValues.email?.enabled !== curValues.email?.enabled || 
                        prevValues.email?.require_verification !== curValues.email?.require_verification ||
                        prevValues.email?.manual_activation !== curValues.email?.manual_activation
                      }
                    >
                      {({ getFieldValue }) => {
                        const emailEnabled = getFieldValue(['email', 'enabled']);
                        const emailRequireVerification = getFieldValue(['email', 'require_verification']);
                        const emailManualActivation = getFieldValue(['email', 'manual_activation']);
                        
                        return (
                          <Form.Item
                            label="邮箱验证模板代码"
                            name={['email', 'verification_template_code']}
                            rules={[
                              ...(emailEnabled && emailRequireVerification && !emailManualActivation
                                ? [{ required: true, message: '请输入邮箱验证模板代码' }] 
                                : [])
                            ]}
                            tooltip="用于发送邮箱验证邮件的模板代码"
                          >
                            <Input 
                              placeholder="例如: EMAIL_VERIFICATION_CODE" 
                              disabled={!emailEnabled || !emailRequireVerification || emailManualActivation}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                    <Form.Item
                      label="手动激活"
                      name={['email', 'manual_activation']}
                      valuePropName="checked"
                      tooltip="开启后用户注册后需要管理员手动激活，不发送验证邮件"
                    >
                      <Switch />
                    </Form.Item>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="手机号注册" size="small">
                    <Form.Item
                      label="启用手机号注册"
                      name={['phone', 'enabled']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      label="需要短信验证"
                      name={['phone', 'require_verification']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, curValues) => 
                        prevValues.phone?.enabled !== curValues.phone?.enabled || 
                        prevValues.phone?.require_verification !== curValues.phone?.require_verification ||
                        prevValues.phone?.manual_activation !== curValues.phone?.manual_activation
                      }
                    >
                      {({ getFieldValue }) => {
                        const phoneEnabled = getFieldValue(['phone', 'enabled']);
                        const phoneRequireVerification = getFieldValue(['phone', 'require_verification']);
                        const phoneManualActivation = getFieldValue(['phone', 'manual_activation']);
                        
                        return (
                          <Form.Item
                            label="短信验证模板代码"
                            name={['phone', 'verification_template_code']}
                            rules={[
                              ...(phoneEnabled && phoneRequireVerification && !phoneManualActivation
                                ? [{ required: true, message: '请输入短信验证模板代码' }] 
                                : [])
                            ]}
                            tooltip="用于发送短信验证码的模板代码"
                          >
                            <Input 
                              placeholder="例如: SMS_VERIFICATION_CODE" 
                              disabled={!phoneEnabled || !phoneRequireVerification || phoneManualActivation}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                    <Form.Item
                      label="手动激活"
                      name={['phone', 'manual_activation']}
                      valuePropName="checked"
                      tooltip="开启后用户注册后需要管理员手动激活，不发送验证短信"
                    >
                      <Switch />
                    </Form.Item>
                  </Card>
                </Col>
              </Row>

              <Row gutter={24} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Card title="三方登录注册" size="small">
                    <Form.Item
                      label="启用三方登录"
                      name={['oauth', 'enabled']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      label="手动激活"
                      name={['oauth', 'manual_activation']}
                      valuePropName="checked"
                      tooltip="开启后用户通过三方登录注册后需要管理员手动激活"
                    >
                      <Switch />
                    </Form.Item>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="管理员创建用户" size="small">
                    <Form.Item
                      label="启用管理员创建"
                      name={['admin_creation', 'enabled']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      label="需要审批"
                      name={['admin_creation', 'require_approval']}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Card>
                </Col>
              </Row>



              <Form.Item style={{ marginTop: 24, textAlign: 'center' }}>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={saving}
                  >
                    保存注册方式配置
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={() => registrationForm.resetFields()}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
              )
            },
            {
              key: 'tenant-info',
              label: (
                <span>
                  <BankOutlined />
                  租户信息
                </span>
              ),
              children: (
                <Form
                  form={tenantInfoForm}
                  layout="vertical"
                  onFinish={handleSaveTenantInfo}
                  initialValues={{
                    type: 'personal',
                    system_name: '我的系统',
                    service_email: '',
                    address: '',
                    contact_person: '',
                    contact_phone: '',
                    website: '',
                    logo: '',
                    description: '',
                    business_license: '',
                  }}
                >
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="租户类型"
                        name="type"
                        rules={[{ required: true, message: '请选择租户类型' }]}
                      >
                        <Select placeholder="请选择租户类型">
                          <Option value="personal">个人</Option>
                          <Option value="enterprise">企业</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="系统名称"
                        name="system_name"
                        rules={[
                          { required: true, message: '请输入系统名称' },
                          { max: 100, message: '系统名称长度不能超过100个字符' }
                        ]}
                      >
                        <Input placeholder="请输入系统名称" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="客服邮箱"
                        name="service_email"
                        rules={[
                          { type: 'email', message: '请输入有效的邮箱地址' },
                          { max: 100, message: '邮箱长度不能超过100个字符' }
                        ]}
                      >
                        <Input placeholder="请输入客服邮箱" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="联系人"
                        name="contact_person"
                        rules={[
                          { max: 50, message: '联系人长度不能超过50个字符' }
                        ]}
                      >
                        <Input placeholder="请输入联系人姓名" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="联系电话"
                        name="contact_phone"
                        rules={[
                          { max: 20, message: '联系电话长度不能超过20个字符' }
                        ]}
                      >
                        <Input placeholder="请输入联系电话" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="官网地址"
                        name="website"
                        rules={[
                          { type: 'url', message: '请输入有效的网址' },
                          { max: 200, message: '网址长度不能超过200个字符' }
                        ]}
                      >
                        <Input placeholder="https://example.com" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label="地址"
                        name="address"
                        rules={[
                          { max: 500, message: '地址长度不能超过500个字符' }
                        ]}
                      >
                        <Input placeholder="请输入详细地址" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) => 
                      prevValues.type !== curValues.type
                    }
                  >
                    {({ getFieldValue }) => {
                      const tenantType = getFieldValue('type');
                      
                      return tenantType === 'enterprise' ? (
                        <Row gutter={24}>
                          <Col span={24}>
                            <Form.Item
                              label="营业执照"
                              name="business_license"
                              tooltip="企业类型租户的营业执照信息"
                            >
                              <Input placeholder="请输入营业执照编号或上传文件路径" />
                            </Form.Item>
                          </Col>
                        </Row>
                      ) : null;
                    }}
                  </Form.Item>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="Logo图片"
                        name="logo"
                        tooltip="企业或个人标志图片的URL或文件路径"
                      >
                        <Input placeholder="请输入Logo图片地址" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="描述"
                        name="description"
                        rules={[
                          { max: 1000, message: '描述长度不能超过1000个字符' }
                        ]}
                      >
                        <Input.TextArea 
                          placeholder="请输入描述信息" 
                          rows={4}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item style={{ marginTop: 24, textAlign: 'center' }}>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SaveOutlined />}
                        loading={saving}
                      >
                        保存租户信息
                      </Button>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => tenantInfoForm.resetFields()}
                      >
                        重置
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              )
            }
          ]}
        />

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            <Button
              type="default"
              icon={<ReloadOutlined />}
              onClick={() => tenantId && loadConfigsWithKey(tenantId)}
            >
              刷新配置
            </Button>
            {/* 暂时隐藏复制系统配置功能 */}
            {/* <Button
              type="dashed"
              icon={<SettingOutlined />}
              onClick={handleCopySystemConfigs}
            >
              复制系统配置
            </Button> */}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default TenantConfigPage; 