import React from 'react';
import { Card, Typography, Space, Tag } from 'antd';
import { useSimpleMenu } from '../../hooks/useSimpleMenu';
import SimpleMenu from './SimpleMenu';
import SimpleBreadcrumb from '../Breadcrumb/SimpleBreadcrumb';

const { Title, Paragraph, Text } = Typography;

const MenuTest: React.FC = () => {
  const {
    menuItems,
    selectedKeys,
    openKeys,
    breadcrumbs,
    currentPageTitle,
    handleMenuClick,
    handleBreadcrumbClick,
  } = useSimpleMenu();

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>菜单组件测试页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 当前状态信息 */}
        <Card title="当前状态" size="small">
          <Space direction="vertical">
            <div>
              <Text strong>当前页面标题：</Text>
              <Tag color="blue">{currentPageTitle}</Tag>
            </div>
            <div>
              <Text strong>选中的菜单：</Text>
              {selectedKeys.map(key => (
                <Tag key={key} color="green">{key}</Tag>
              ))}
            </div>
            <div>
              <Text strong>展开的菜单：</Text>
              {openKeys.map(key => (
                <Tag key={key} color="orange">{key}</Tag>
              ))}
            </div>
          </Space>
        </Card>

        {/* 面包屑测试 */}
        <Card title="面包屑组件" size="small">
          <SimpleBreadcrumb
            items={breadcrumbs}
            onItemClick={handleBreadcrumbClick}
            style={{ marginBottom: 16 }}
          />
          <Paragraph>
            <Text type="secondary">
              点击面包屑项目会在控制台输出相关信息，并触发导航。
            </Text>
          </Paragraph>
        </Card>

        {/* 菜单测试 */}
        <Card title="菜单组件" size="small">
          <div style={{ width: 300, border: '1px solid #f0f0f0', borderRadius: 6 }}>
            <SimpleMenu
              menuItems={menuItems}
              selectedKeys={selectedKeys}
              openKeys={openKeys}
              onMenuClick={handleMenuClick}
              theme="light"
              mode="inline"
            />
          </div>
          <Paragraph style={{ marginTop: 16 }}>
            <Text type="secondary">
              点击菜单项会自动导航到对应页面，并更新面包屑和选中状态。
            </Text>
          </Paragraph>
        </Card>

        {/* 菜单数据结构 */}
        <Card title="菜单数据结构" size="small">
          <pre style={{ 
            background: '#f5f5f5', 
            padding: 16, 
            borderRadius: 6,
            fontSize: 12,
            overflow: 'auto',
            maxHeight: 300
          }}>
            {JSON.stringify(menuItems, null, 2)}
          </pre>
        </Card>
      </Space>
    </div>
  );
};

export default MenuTest;
