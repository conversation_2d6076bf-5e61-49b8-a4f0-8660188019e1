import { useState, useCallback } from 'react';
import type { EmailTemplate } from '../../../types/email';

interface TemplateState {
  template: EmailTemplate | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
}

interface UseTemplateStateReturn extends TemplateState {
  setTemplate: (template: EmailTemplate | ((prev: EmailTemplate | null) => EmailTemplate)) => void;
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setError: (error: string | null) => void;
  resetTemplate: () => void;
  updateTemplateField: <K extends keyof EmailTemplate>(field: K, value: EmailTemplate[K]) => void;
}

const initialState: TemplateState = {
  template: null,
  loading: false,
  saving: false,
  error: null,
};

const createEmptyTemplate = (): EmailTemplate => ({
  id: 0,
  tenant_id: 0,
  template_code: '',
  account_id: 0,
  name: '',
  description: '',
  type: 1,
  subject: '',
  html_content: '',
  plain_text_content: '',
  variables: {},
  rate_limit_per_minute: 0,
  rate_limit_per_hour: 0,
  rate_limit_per_day: 0,
  is_responsive: true,
  status: 1,
  created_at: '',
  updated_at: '',
  created_by: 0,
  updated_by: 0,
  version: 1,
  is_system: false,
});

export const useTemplateState = (): UseTemplateStateReturn => {
  const [state, setState] = useState<TemplateState>(initialState);

  const setTemplate = useCallback((template: EmailTemplate | ((prev: EmailTemplate | null) => EmailTemplate)) => {
    setState(prev => ({
      ...prev,
      template: typeof template === 'function' ? template(prev.template) : template,
    }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  const setSaving = useCallback((saving: boolean) => {
    setState(prev => ({ ...prev, saving }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const resetTemplate = useCallback(() => {
    setState(prev => ({
      ...prev,
      template: createEmptyTemplate(),
      error: null,
    }));
  }, []);

  const updateTemplateField = useCallback(<K extends keyof EmailTemplate>(
    field: K,
    value: EmailTemplate[K]
  ) => {
    setState(prev => ({
      ...prev,
      template: prev.template ? { ...prev.template, [field]: value } : null,
    }));
  }, []);

  return {
    ...state,
    setTemplate,
    setLoading,
    setSaving,
    setError,
    resetTemplate,
    updateTemplateField,
  };
};
