import { useCallback, useMemo } from 'react';
import { Form, message } from 'antd';
import { emailTemplateService } from '../../../services/email';
import type { Rule } from 'antd/es/form';

interface FieldError {
  field: string;
  message: string;
}

interface UseFormValidatorReturn {
  form: any;
  validateForm: () => Promise<any>;
  applyFieldErrors: (errors: FieldError[]) => void;
  clearFieldErrors: () => void;
  getValidationRules: () => Record<string, Rule[]>;
}

export const useFormValidator = (): UseFormValidatorReturn => {
  const [form] = Form.useForm();

  // 验证表单
  const validateForm = useCallback(async () => {
    try {
      const values = await form.validateFields();
      return values;
    } catch (errorInfo) {
      // 滚动到第一个错误字段
      const firstErrorField = errorInfo.errorFields?.[0]?.name?.[0];
      if (firstErrorField) {
        form.scrollToField(firstErrorField);
      }
      return null;
    }
  }, [form]);

  // 应用字段错误
  const applyFieldErrors = useCallback((errors: FieldError[]) => {
    const fieldErrors = errors.map(error => ({
      name: error.field,
      errors: [error.message],
    }));
    form.setFields(fieldErrors);
  }, [form]);

  // 清除字段错误
  const clearFieldErrors = useCallback(() => {
    form.setFields(
      form.getFieldsError().map(({ name }) => ({
        name,
        errors: [],
      }))
    );
  }, [form]);

  // 异步验证模板名称唯一性
  const validateTemplateName = useCallback(async (rule: any, value: string) => {
    if (!value || !value.trim()) return;

    try {
      const response = await emailTemplateService.checkTemplateNameUnique({
        name: value.trim(),
        exclude_id: form.getFieldValue('id'),
      });
      
      if (!response.data.is_unique) {
        throw new Error('模板名称已存在，请使用其他名称');
      }
    } catch (error: any) {
      if (error.message === '模板名称已存在，请使用其他名称') {
        throw error;
      }
      // 网络错误等其他错误不阻止提交
      console.warn('Template name validation failed:', error);
    }
  }, [form]);

  // 异步验证场景代码唯一性
  const validateScenarioCode = useCallback(async (rule: any, value: string) => {
    if (!value || !value.trim()) return;

    try {
      const response = await emailTemplateService.checkScenarioCodeUnique({
        scenario_code: value.trim(),
        exclude_id: form.getFieldValue('id'),
      });
      
      if (!response.data.is_unique) {
        throw new Error('场景代码已存在，请使用其他代码');
      }
    } catch (error: any) {
      if (error.message === '场景代码已存在，请使用其他代码') {
        throw error;
      }
      console.warn('Scenario code validation failed:', error);
    }
  }, [form]);

  // 获取验证规则
  const getValidationRules = useCallback((): Record<string, Rule[]> => ({
    template_name: [
      { required: true, message: '请输入模板名称' },
      { min: 2, max: 100, message: '模板名称长度应在2-100字符之间' },
      { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_\-\s]+$/, message: '模板名称只能包含中英文、数字、下划线、连字符和空格' },
      { validator: validateTemplateName },
    ],
    scenario_code: [
      { required: true, message: '请输入场景代码' },
      { min: 2, max: 50, message: '场景代码长度应在2-50字符之间' },
      { pattern: /^[a-z][a-z0-9_]*$/, message: '场景代码只能包含小写字母、数字和下划线，且必须以字母开头' },
      { validator: validateScenarioCode },
    ],
    subject: [
      { required: true, message: '请输入邮件主题' },
      { min: 1, max: 200, message: '邮件主题长度应在1-200字符之间' },
    ],
    account_id: [
      { required: true, message: '请选择发件账户' },
    ],
    description: [
      { max: 500, message: '模板描述长度不能超过500字符' },
    ],
  }), [validateTemplateName, validateScenarioCode]);

  return {
    form,
    validateForm,
    applyFieldErrors,
    clearFieldErrors,
    getValidationRules,
  };
};
