import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Button, 
  Space, 
  message,
  Row,
  Col,
  Divider,
  Spin,
  Alert,
  Typography,
  Tabs,
  Modal
} from 'antd';
import { SaveOutlined, ArrowLeftOutlined, EyeOutlined, EditOutlined, FileTextOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import TemplateContentEditor from './components/TemplateContentEditor';
import { emailTemplateService, emailAccountService } from '../../services/email';
import { EmailTemplate, UpdateTemplateRequest, EmailAccount } from '../../types/email';
import { useTheme } from '../../contexts/ThemeContext';
import { showAPIError, applyFieldErrorsToForm } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';
import CKEditorEmailTemplate from './components/CKEditorEmailTemplate';

const { Option } = Select;
const { Title, Text } = Typography;

const TemplateEdit: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState<EmailTemplate | null>(null);
  const [htmlContent, setHtmlContent] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewVariables, setPreviewVariables] = useState<Record<string, any>>({});
  const [activeTab, setActiveTab] = useState('basic');
  
  // 添加内容编辑器的ref
  const contentEditorRef = useRef<any>(null);

  // 邮件账户相关状态
  const [emailAccounts, setEmailAccounts] = useState<EmailAccount[]>([]);
  const [accountsLoading, setAccountsLoading] = useState(false);
  const [accountSearchText, setAccountSearchText] = useState('');

  // 获取模板详情
  const fetchTemplate = async () => {
    if (!id) {
      message.error('模板ID缺失');
      navigate('/email/templates');
      return;
    }

    setLoading(true);
    try {
      const response = await emailTemplateService.getTemplate(id);
      
      const templateData = response.data;
      
      setTemplate(templateData);
      
      // 根据实际API响应结构设置HTML内容
      // API返回的是 html_content 字段，不是 body
      setHtmlContent(templateData.html_content || '');
      
      // 设置表单初始值 - 根据实际API字段映射
      const formData = {
        template_name: templateData.name, // API返回的是 name，不是 template_name
        scenario_code: templateData.template_code, // API返回的是 template_code
        description: templateData.description, // 模板描述
        scenario_name: templateData.name, // 使用name作为场景名称
        template_type: templateData.type === 1 ? 'html' : 'text', // API返回的是数字类型
        subject: templateData.subject,
        status: templateData.status,
        account_id: templateData.account_id, // 添加发件账户ID
      };
      
      form.setFieldsValue(formData);
      
      // 初始化预览变量 - 根据API返回的variables字段
      if (templateData.variables) {
        const previewVars: Record<string, string> = {};
        Object.keys(templateData.variables).forEach(key => {
          const varInfo = templateData.variables[key];
          // 根据变量类型设置默认值
          if (varInfo.type === 'string') {
            previewVars[key] = `示例${varInfo.description || key}`;
          } else if (varInfo.type === 'number') {
            previewVars[key] = '123';
          } else {
            previewVars[key] = '示例值';
          }
        });
        setPreviewVariables(previewVars);
      }
      
    } catch (error) {
      console.error('获取模板失败:', error);
      message.error('获取模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取邮件账户列表（支持远程搜索）
  const fetchEmailAccounts = async (searchText = '') => {
    setAccountsLoading(true);
    try {
      const response = await emailAccountService.getEmailAccounts({
        page: 1,
        page_size: 50,
        is_active: true, // 只获取激活的账户
        keyword: searchText // 使用keyword字段进行搜索
      });
      
      // API返回 ApiResponse<EmailAccount[]>，所以accounts直接在response.data中
      if (response.data && Array.isArray(response.data)) {
        setEmailAccounts(response.data);
      } else {
        setEmailAccounts([]);
      }
    } catch (error) {
      console.error('Failed to fetch email accounts:', error);
      showAPIError(error);
      setEmailAccounts([]); // 出错时设置为空数组
    } finally {
      setAccountsLoading(false);
    }
  };

  // 处理账户搜索
  const handleAccountSearch = (value: string) => {
    setAccountSearchText(value);
    if (value.length >= 2 || value.length === 0) {
      fetchEmailAccounts(value);
    }
  };

  // 保存基本信息
  const handleSaveBasic = async (values: any) => {
    if (!template) {
      message.error('模板数据不存在');
      return;
    }

    setSaving(true);
    try {
      // 构建更新请求数据
      const updateData: UpdateTemplateRequest = {
        id: template.id,
        name: values.template_name,
        template_code: values.scenario_code,
        description: values.description,
        type: values.template_type === 'html' ? 1 : 2,
        subject: values.subject,
        status: values.status,
        account_id: values.account_id, // 添加发件账户ID
      };

      const response = await emailTemplateService.updateTemplate(updateData);
      
      if (response.code === SUCCESS) {
        message.success('基本信息保存成功');
        // 更新本地模板数据
        setTemplate(prev => prev ? { ...prev, ...updateData } : null);
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存基本信息失败:', error);
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  };

  // 保存模板内容
  const handleSaveContent = async () => {
    if (!id || !template) return;

    setSaving(true);
    try {
      // 获取编辑器中的自定义变量
      let customVariables: Record<string, any> = {};
      if (contentEditorRef.current) {
        customVariables = contentEditorRef.current.getCustomVariables();
      }
      
      // 合并模板原有变量和编辑器中的自定义变量
      // 转换变量格式以匹配API期望的格式
      const allVariables: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
      
      // 处理模板原有变量
      if (template.variables) {
        Object.entries(template.variables).forEach(([key, value]) => {
          allVariables[key] = {
            label: key, // 使用key作为label，因为template.variables中没有label字段
            type: value.type || 'string',
            required: value.required || false,
            description: value.description || '',
          };
        });
      }
      
      // 处理编辑器中的自定义变量
      Object.entries(customVariables).forEach(([key, value]) => {
        allVariables[key] = {
          label: value.label || key,
          type: value.type || 'string',
          required: value.required || false,
          description: value.description || '',
        };
      });

      // 使用普通的 updateTemplate 方法，不使用 variables_only 模式
      const updateData = {
        id: parseInt(id),
        html_content: htmlContent,
        variables: allVariables,
      };

      const response = await emailTemplateService.updateTemplate(updateData);
      
      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        message.error(response.message || '模板内容保存失败');
        return;
      }

      // 处理成功响应
      message.success('模板内容保存成功！');
    } catch (error) {
      message.error('模板内容保存失败');
      console.error('Update template error:', error);
    } finally {
      setSaving(false);
    }
  };

  // 预览模板
  const handlePreview = () => {
    setPreviewVisible(true);
  };

  // 处理编辑器预览
  const handleEditorPreview = (html: string, variables: Record<string, any>) => {
    setHtmlContent(html);
    setPreviewVariables(variables);
    setPreviewVisible(true);
  };

  // 返回列表页
  const handleBack = () => {
    navigate('/email/templates');
  };

  // HTML内容变化
  const handleHtmlChange = (html: string) => {
    setHtmlContent(html);
  };

  // 生成预览内容
  const generatePreviewContent = () => {
    let previewContent = htmlContent;
    
    // 替换变量
    Object.entries(previewVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      previewContent = previewContent.replace(regex, value || `{{${key}}}`);
    });
    
    return previewContent;
  };

  // 初始化数据
  useEffect(() => {
    fetchTemplate();
    fetchEmailAccounts(); // 获取邮件账户列表
  }, [id]);

  if (loading) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载模板数据中...</div>
      </div>
    );
  }

  if (!template) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Alert
          message="模板不存在"
          description="请检查模板ID是否正确"
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        bodyStyle={{ padding: 0 }}
      >
        <div style={{ padding: '24px' }}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            tabBarExtraContent={
              <Space>
                <Button 
                  type="text" 
                  icon={<ArrowLeftOutlined />} 
                  onClick={handleBack}
                >
                  返回
                </Button>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  loading={saving}
                  onClick={() => {
                    if (activeTab === 'basic') {
                      form.submit();
                    } else {
                      handleSaveContent();
                    }
                  }}
                >
                  保存{activeTab === 'basic' ? '基本信息' : '模板内容'}
                </Button>
              </Space>
            }
            items={[
            {
              key: 'basic',
              label: (
                <Space>
                  <EditOutlined />
                  基本信息
                </Space>
              ),
              children: (
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSaveBasic}
                >
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="模板名称"
                        name="template_name"
                        rules={[
                          { required: true, message: '请输入模板名称' },
                          { min: 2, max: 100, message: '模板名称长度应在2-100字符之间' }
                        ]}
                      >
                        <Input placeholder="请输入模板名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="场景代码"
                        name="scenario_code"
                        rules={[
                          { required: true, message: '请输入场景代码' },
                          { 
                            pattern: /^[a-z_][a-z0-9_]*$/, 
                            message: '场景代码只能包含小写字母、数字和下划线，且以字母或下划线开头' 
                          }
                        ]}
                      >
                        <Input placeholder="如：user_register, password_reset" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        label="场景名称"
                        name="scenario_name"
                        rules={[
                          { required: true, message: '请输入场景名称' },
                          { min: 2, max: 50, message: '场景名称长度应在2-50字符之间' }
                        ]}
                      >
                        <Input placeholder="如：用户注册、密码重置" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="模板类型"
                        name="template_type"
                        rules={[{ required: true, message: '请选择模板类型' }]}
                      >
                        <Select placeholder="选择模板类型">
                          <Option value="html">HTML富文本</Option>
                          <Option value="text">纯文本</Option>
                          <Option value="mixed">混合模式</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={18}>
                      <Form.Item
                        label="邮件主题"
                        name="subject"
                        rules={[
                          { required: true, message: '请输入邮件主题' },
                          { min: 5, max: 200, message: '邮件主题长度应在5-200字符之间' }
                        ]}
                      >
                        <Input placeholder="请输入邮件主题，支持变量：{{user_name}}，{{company_name}} 等" />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="模板状态"
                        name="status"
                      >
                        <Select placeholder="选择模板状态">
                          <Select.Option value={1}>草稿</Select.Option>
                          <Select.Option value={2}>已发布</Select.Option>
                          <Select.Option value={3}>已停用</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={18}>
                      <Form.Item
                        label="发件账户"
                        name="account_id"
                        rules={[{ required: true, message: '请选择发件账户' }]}
                      >
                        <Select 
                          placeholder="选择发件账户"
                          loading={accountsLoading}
                          showSearch
                          onSearch={handleAccountSearch}
                          searchValue={accountSearchText}
                          filterOption={false}
                          optionLabelProp="label"
                          notFoundContent={accountsLoading ? "加载中..." : "暂无数据"}
                        >
                          {emailAccounts.map((account) => (
                            <Option 
                              key={account.id} 
                              value={account.id}
                              label={`${account.name} (${account.from_address})`}
                            >
                              <div>
                                <div style={{ fontWeight: 500 }}>{account.name}</div>
                                <div style={{ fontSize: '12px', color: '#666' }}>
                                  {account.from_address} • {account.provider}
                                </div>
                              </div>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label="模板描述"
                        name="description"
                        rules={[
                          { max: 255, message: '模板描述长度不能超过255字符' }
                        ]}
                      >
                        <Input.TextArea 
                          placeholder="请输入模板描述，用于说明模板的用途和使用场景" 
                          rows={3}
                          showCount
                          maxLength={255}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              ),
            },
            {
              key: 'content',
              label: (
                <Space>
                  <FileTextOutlined />
                  模板内容
                </Space>
              ),
              children: (
                <Card title="模板内容" style={{ marginBottom: 24 }}>
                  <CKEditorEmailTemplate
                    value={htmlContent}
                    onChange={handleHtmlChange}
                    loading={loading}
                    showSaveButton={false}
                    showHelpCards={true}
                    templateId={template?.id} // 添加模板ID，用于获取服务端变量
                    onPreview={handlePreview}
                    ref={contentEditorRef} // 添加ref
                  />
                </Card>
              ),
            },
          ]}
          size="large"
        />
        </div>
      </Card>

      {/* 预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: '16px' }}>
          <Title level={5}>预览变量设置</Title>
          <Row gutter={16}>
            {Object.entries(previewVariables).map(([key, value]) => (
              <Col span={8} key={key}>
                <Form.Item label={key}>
                  <Input
                    value={value || ''}
                    onChange={(e) => setPreviewVariables(prev => ({
                      ...prev,
                      [key]: e.target.value
                    }))}
                    placeholder={`请输入${key}的值`}
                  />
                </Form.Item>
              </Col>
            ))}
          </Row>
        </div>
        
        <div style={{ 
          border: '1px solid #d9d9d9', 
          padding: '20px', 
          backgroundColor: '#f5f5f5',
          borderRadius: '6px'
        }}>
          <div 
            style={{ 
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '4px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              minHeight: '200px'
            }}
            dangerouslySetInnerHTML={{ __html: generatePreviewContent() }}
          />
        </div>
      </Modal>
    </div>
  );
};

export default TemplateEdit; 