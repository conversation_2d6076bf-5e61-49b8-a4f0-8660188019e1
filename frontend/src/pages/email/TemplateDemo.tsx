import React, { useState } from 'react';
import { Card, Button, Space, Typography, Row, Col, Alert, Divider } from 'antd';
import { 
  EyeOutlined, 
  EditOutlined, 
  PlusOutlined, 
  CheckCircleOutlined,
  ArrowRightOutlined 
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;

const TemplateDemo: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);

  const features = [
    {
      title: '模板列表管理',
      description: '查看所有邮件模板，支持搜索、筛选、分页',
      icon: <EyeOutlined />,
      path: '/email/templates',
      features: [
        '支持按模板名称、场景代码搜索',
        '支持按模板类型、租户类型筛选',
        '支持启用/禁用模板',
        '支持删除模板',
        '分页显示，性能优化'
      ]
    },
    {
      title: '创建新模板',
      description: '两步式创建流程，简单易用',
      icon: <PlusOutlined />,
      path: '/email/templates/create',
      features: [
        '第一步：填写基本信息',
        '第二步：编辑邮件内容',
        '支持富文本编辑和源码编辑',
        '实时预览功能',
        '模板变量支持'
      ]
    },
    {
      title: '编辑现有模板',
      description: '修改模板内容和配置',
      icon: <EditOutlined />,
      path: '/email/templates/1/edit',
      features: [
        '编辑所有模板字段',
        '支持富文本编辑',
        '支持源码编辑',
        '实时预览功能',
        '自动保存提示'
      ]
    },
    {
      title: '查看模板详情',
      description: '查看模板完整信息和预览效果',
      icon: <EyeOutlined />,
      path: '/email/templates/1',
      features: [
        '查看模板基本信息',
        '预览邮件效果',
        '查看变量配置',
        '支持克隆模板',
        '支持删除模板'
      ]
    }
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  const handleNextStep = () => {
    if (currentStep < features.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>邮件模板功能演示</Title>
        <Text type="secondary">
          完整的邮件模板管理系统，支持创建、编辑、查看和删除模板
        </Text>

        <Divider />

        {/* 功能概览 */}
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} lg={12} key={index}>
              <Card
                title={
                  <Space>
                    {feature.icon}
                    <span>{feature.title}</span>
                  </Space>
                }
                extra={
                  <Button 
                    type="primary" 
                    size="small"
                    onClick={() => handleNavigate(feature.path)}
                  >
                    体验功能
                  </Button>
                }
                style={{ 
                  height: '100%',
                  border: currentStep === index ? '2px solid #1890ff' : '1px solid #d9d9d9'
                }}
              >
                <Paragraph>{feature.description}</Paragraph>
                <ul style={{ paddingLeft: '20px' }}>
                  {feature.features.map((item, idx) => (
                    <li key={idx}>
                      <Text>{item}</Text>
                    </li>
                  ))}
                </ul>
              </Card>
            </Col>
          ))}
        </Row>

        <Divider />

        {/* 操作指南 */}
        <Card title="操作指南" style={{ marginBottom: '24px' }}>
          <Row gutter={[24, 16]}>
            <Col xs={24} md={12}>
              <Title level={4}>快速开始</Title>
              <ol style={{ paddingLeft: '20px' }}>
                <li>点击"模板列表管理"查看现有模板</li>
                <li>点击"创建新模板"创建您的第一个模板</li>
                <li>在列表中选择模板进行编辑或查看</li>
                <li>使用预览功能测试模板效果</li>
              </ol>
            </Col>
            <Col xs={24} md={12}>
              <Title level={4}>功能特点</Title>
              <ul style={{ paddingLeft: '20px' }}>
                <li>支持富文本和源码两种编辑模式</li>
                <li>实时预览邮件效果</li>
                <li>支持模板变量插入</li>
                <li>完整的权限控制</li>
                <li>响应式设计，支持移动端</li>
              </ul>
            </Col>
          </Row>
        </Card>

        {/* 技术特性 */}
        <Card title="技术特性">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card size="small" title="前端技术">
                <ul style={{ paddingLeft: '20px', margin: 0 }}>
                  <li>React 18 + TypeScript</li>
                  <li>Ant Design 组件库</li>
                  <li>React Quill 富文本编辑器</li>
                  <li>Monaco Editor 源码编辑器</li>
                </ul>
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card size="small" title="后端技术">
                <ul style={{ paddingLeft: '20px', margin: 0 }}>
                  <li>Go + Gin 框架</li>
                  <li>DDD 架构设计</li>
                  <li>RESTful API</li>
                  <li>统一响应格式</li>
                </ul>
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card size="small" title="功能特性">
                <ul style={{ paddingLeft: '20px', margin: 0 }}>
                  <li>完整的 CRUD 操作</li>
                  <li>实时预览功能</li>
                  <li>模板变量支持</li>
                  <li>权限控制</li>
                </ul>
              </Card>
            </Col>
          </Row>
        </Card>

        {/* 导航按钮 */}
        <div style={{ textAlign: 'center', marginTop: '32px' }}>
          <Space size="large">
            <Button 
              onClick={handlePrevStep} 
              disabled={currentStep === 0}
              icon={<ArrowRightOutlined style={{ transform: 'rotate(180deg)' }} />}
            >
              上一步
            </Button>
            <Button 
              type="primary" 
              onClick={handleNextStep}
              disabled={currentStep === features.length - 1}
              icon={<ArrowRightOutlined />}
            >
              下一步
            </Button>
          </Space>
        </div>

        {/* 当前步骤提示 */}
        {currentStep < features.length && (
          <Alert
            message={`当前演示：${features[currentStep].title}`}
            description={features[currentStep].description}
            type="info"
            showIcon
            style={{ marginTop: '16px' }}
            action={
              <Button 
                type="primary" 
                size="small"
                onClick={() => handleNavigate(features[currentStep].path)}
              >
                立即体验
              </Button>
            }
          />
        )}
      </Card>
    </div>
  );
};

export default TemplateDemo; 