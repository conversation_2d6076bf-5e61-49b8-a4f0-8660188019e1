import React, { useState } from 'react';
import { Card, Button, Space, message, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import CKEditorEmailTemplate from './components/CKEditorEmailTemplate';

const { Title } = Typography;

const TemplateTest: React.FC = () => {
  const navigate = useNavigate();
  const [htmlContent, setHtmlContent] = useState('<p>这是一个测试邮件模板</p><p>欢迎使用 <strong>{{user_name}}</strong>！</p>');

  // 处理内容变化
  const handleContentChange = (html: string) => {
    setHtmlContent(html);
    console.log('内容已更新:', html);
  };

  // 处理保存
  const handleSave = (html: string) => {
    console.log('保存模板:', html);
    message.success('模板保存成功！');
  };

  // 处理预览
  const handlePreview = (html: string, variables: Record<string, any>) => {
    console.log('预览模板:', html);
    console.log('预览变量:', variables);
    message.info('预览功能正常工作');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={() => navigate('/email/templates')}
            >
              返回
            </Button>
            <span>CKEditor 邮件模板编辑器测试</span>
          </Space>
        }
      >
        <div style={{ marginBottom: '16px' }}>
          <Title level={5}>功能测试说明</Title>
          <p>此页面用于测试新的 CKEditor 邮件模板编辑器的各项功能：</p>
          <ul>
            <li>可视化编辑模式</li>
            <li>源码编辑模式</li>
            <li>实时预览功能</li>
            <li>变量插入功能</li>
            <li>自定义变量管理</li>
            <li>保存功能</li>
          </ul>
        </div>

        <CKEditorEmailTemplate
          value={htmlContent}
          onChange={handleContentChange}
          onSave={handleSave}
          showSaveButton={true}
          showHelpCards={true}
          onPreview={handlePreview}
        />

        <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
          <Title level={5}>当前内容预览</Title>
          <div 
            style={{ 
              backgroundColor: 'white', 
              padding: '16px', 
              borderRadius: '4px',
              border: '1px solid #d9d9d9'
            }}
            dangerouslySetInnerHTML={{ __html: htmlContent }}
          />
        </div>
      </Card>
    </div>
  );
};

export default TemplateTest; 