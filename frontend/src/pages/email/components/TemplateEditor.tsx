import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, Tabs, Button, Space, message, Spin } from 'antd';
import { ArrowLeftOutlined, SaveOutlined, EyeOutlined } from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { useTemplateState } from '../hooks/useTemplateState';
import { useFormValidator } from '../hooks/useFormValidator';
import { useVariableManager } from '../hooks/useVariableManager';
import { TemplateForm } from './TemplateForm';
import { TemplateContentEditor } from './TemplateContentEditor';
import { VariableManager } from './VariableManager';
import { TemplatePreview } from './TemplatePreview';
import { emailTemplateService } from '../../../services/email';
import { showAPIError } from '../../../utils/errorHandler';
import { SUCCESS } from '../../../constants/errorCodes';
import type { EmailTemplate, CreateTemplateRequest, UpdateTemplateRequest } from '../../../types/email';

interface TemplateEditorProps {
  mode: 'create' | 'edit';
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = mode === 'edit' && id;

  // 状态管理
  const {
    template,
    loading,
    saving,
    setTemplate,
    setLoading,
    setSaving,
    resetTemplate
  } = useTemplateState();

  // 表单验证
  const {
    form,
    validateForm,
    applyFieldErrors,
    clearFieldErrors
  } = useFormValidator();

  // 变量管理
  const {
    variables,
    localVariables,
    serverVariables,
    addVariable,
    updateVariable,
    deleteVariable,
    syncVariables,
    validateVariableUniqueness
  } = useVariableManager(isEditMode ? id : undefined);

  // 预览相关
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  // 当前激活的标签页
  const [activeTab, setActiveTab] = useState('basic');

  // 初始化数据
  useEffect(() => {
    if (isEditMode) {
      loadTemplate();
    } else {
      resetTemplate();
    }
  }, [isEditMode, id]);

  // 加载模板数据
  const loadTemplate = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await emailTemplateService.getTemplate(id);
      if (response.code === SUCCESS) {
        setTemplate(response.data);
        // 设置表单初始值
        form.setFieldsValue({
          template_name: response.data.name,
          scenario_code: response.data.template_code,
          subject: response.data.subject,
          account_id: response.data.account_id,
          description: response.data.description,
        });
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  }, [id, setTemplate, setLoading, form]);

  // 保存模板
  const handleSave = useCallback(async () => {
    try {
      // 清除之前的错误
      clearFieldErrors();

      // 表单验证
      const formValues = await validateForm();
      if (!formValues) return;

      // 内容验证
      if (!template?.html_content?.trim()) {
        message.error('请输入邮件内容');
        setActiveTab('content');
        return;
      }

      // 变量验证
      const variableValidation = validateTemplateVariables();
      if (!variableValidation.isValid) {
        message.error(`模板中使用了未定义的变量：${variableValidation.undefinedVariables.join(', ')}`);
        setActiveTab('variables');
        return;
      }

      setSaving(true);

      if (isEditMode) {
        await updateTemplate(formValues);
      } else {
        await createTemplate(formValues);
      }
    } catch (error) {
      showAPIError(error);
    } finally {
      setSaving(false);
    }
  }, [template, isEditMode, validateForm, clearFieldErrors]);

  // 创建模板
  const createTemplate = useCallback(async (formValues: any) => {
    const templateData: CreateTemplateRequest = {
      template_name: formValues.template_name,
      scenario_code: formValues.scenario_code,
      subject: formValues.subject,
      account_id: formValues.account_id,
      description: formValues.description,
      html_content: template!.html_content,
      variables: combineVariables(),
      type: 1, // HTML类型
    };

    const response = await emailTemplateService.createTemplate(templateData);
    
    if (response.errors?.length) {
      applyFieldErrors(response.errors);
      return;
    }

    if (response.code !== SUCCESS) {
      message.error(response.message || '模板创建失败');
      return;
    }

    message.success('模板创建成功！');
    navigate('/email/templates');
  }, [template, combineVariables, applyFieldErrors, navigate]);

  // 更新模板
  const updateTemplate = useCallback(async (formValues: any) => {
    const updateData: UpdateTemplateRequest = {
      id: parseInt(id!),
      template_name: formValues.template_name,
      scenario_code: formValues.scenario_code,
      subject: formValues.subject,
      account_id: formValues.account_id,
      description: formValues.description,
      html_content: template!.html_content,
      variables: combineVariables(),
    };

    const response = await emailTemplateService.updateTemplate(updateData);
    
    if (response.code !== SUCCESS) {
      message.error(response.message || '模板更新失败');
      return;
    }

    message.success('模板更新成功！');
    navigate('/email/templates');
  }, [id, template, combineVariables, navigate]);

  // 合并所有变量
  const combineVariables = useCallback(() => {
    const allVariables: Record<string, any> = {};
    
    // 添加服务端变量
    serverVariables.forEach(variable => {
      allVariables[variable.name] = {
        label: variable.label,
        type: variable.type,
        required: variable.required,
        description: variable.description,
        default_value: variable.default_value,
      };
    });

    // 添加本地变量
    localVariables.forEach(variable => {
      allVariables[variable.name] = {
        label: variable.label,
        type: variable.type,
        required: variable.required,
        description: variable.description,
        default_value: variable.defaultValue,
      };
    });

    return allVariables;
  }, [serverVariables, localVariables]);

  // 验证模板变量
  const validateTemplateVariables = useCallback(() => {
    if (!template?.html_content) return { isValid: true, undefinedVariables: [] };

    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = Array.from(template.html_content.matchAll(variableRegex));
    const usedVariables = [...new Set(matches.map(match => match[1].trim()))];
    
    const definedVariables = [...serverVariables, ...localVariables].map(v => v.name);
    const undefinedVariables = usedVariables.filter(variable => 
      !definedVariables.includes(variable)
    );

    return {
      isValid: undefinedVariables.length === 0,
      undefinedVariables,
      usedVariables
    };
  }, [template?.html_content, serverVariables, localVariables]);

  // 预览模板
  const handlePreview = useCallback(async () => {
    if (!template?.html_content) {
      message.warning('请先输入模板内容');
      return;
    }

    try {
      const previewVariables: Record<string, any> = {};
      [...serverVariables, ...localVariables].forEach(variable => {
        previewVariables[variable.name] = variable.defaultValue || variable.default_value || '';
      });

      const response = await emailTemplateService.previewTemplate({
        template_id: isEditMode ? parseInt(id!) : undefined,
        html_content: template.html_content,
        subject: form.getFieldValue('subject') || '',
        variables: previewVariables,
      });

      if (response.code === SUCCESS) {
        setPreviewData(response.data);
        setPreviewVisible(true);
      }
    } catch (error) {
      showAPIError(error);
    }
  }, [template, serverVariables, localVariables, isEditMode, id, form]);

  // 返回列表
  const handleBack = useCallback(() => {
    navigate('/email/templates');
  }, [navigate]);

  // 标签页配置
  const tabItems = useMemo(() => [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <TemplateForm
          form={form}
          loading={loading}
          mode={mode}
        />
      ),
    },
    {
      key: 'content',
      label: '模板内容',
      children: (
        <TemplateContentEditor
          value={template?.html_content || ''}
          onChange={(content) => setTemplate(prev => ({ ...prev!, html_content: content }))}
          variables={variables}
          loading={saving}
        />
      ),
    },
    {
      key: 'variables',
      label: '变量管理',
      children: (
        <VariableManager
          variables={variables}
          localVariables={localVariables}
          serverVariables={serverVariables}
          onAddVariable={addVariable}
          onUpdateVariable={updateVariable}
          onDeleteVariable={deleteVariable}
          onSyncVariables={syncVariables}
          validateUniqueness={validateVariableUniqueness}
          isEditMode={isEditMode}
        />
      ),
    },
  ], [form, loading, mode, template, variables, localVariables, serverVariables, saving, isEditMode]);

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title={
          <Space>
            <Button 
              type="text" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            >
              返回
            </Button>
            <span>{isEditMode ? '编辑模板' : '创建模板'}</span>
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<EyeOutlined />}
              onClick={handlePreview}
              disabled={!template?.html_content}
            >
              预览
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              loading={saving}
              onClick={handleSave}
            >
              {isEditMode ? '更新模板' : '创建模板'}
            </Button>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>

      {/* 预览模态框 */}
      <TemplatePreview
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        data={previewData}
      />
    </div>
  );
};

export default TemplateEditor;
