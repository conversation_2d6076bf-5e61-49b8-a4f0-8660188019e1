/* CKEditor Email Template 样式文件 */

.ckeditor-email-template {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 预览工具栏 */
.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f0f8ff;
  border: 1px solid #1890ff;
  border-radius: 6px 6px 0 0;
  border-bottom: none;
}

/* CKEditor 容器 */
.ckeditor-container {
  position: relative;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  overflow: hidden;
  background: #fff;
}

/* CKEditor 工具栏容器 */
.ckeditor-toolbar {
  border-bottom: 1px solid #e8e8e8;
  background: #fff;
  padding: 8px 12px;
}

/* CKEditor 编辑器主体 */
.ckeditor-container .ck-editor__editable {
  min-height: 400px !important;
  max-height: 600px !important;
  padding: 16px !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  overflow-y: auto !important;
  border: none !important;
  outline: none !important;
}

/* CKEditor 占位符 */
.ckeditor-container .ck-editor__editable.ck-blank::before {
  color: #ccc !important;
  font-style: normal !important;
  content: '开始输入邮件内容...' !important;
}

/* 代码编辑器 */
.code-editor-container {
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  overflow: hidden;
  background: #fff;
}

.code-editor {
  width: 100%;
  min-height: 400px;
  max-height: 600px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: none;
  outline: none;
  resize: vertical;
  background: #f8f9fa;
  color: #333;
}

.code-editor:focus {
  background: #fff;
}

.code-editor::placeholder {
  color: #ccc;
}

/* 预览容器 */
.email-preview-container {
  min-height: 400px;
  padding: 24px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
}

/* 预览模式下的预览容器 */
.ckeditor-email-template .preview-toolbar + .email-preview-container {
  border-top: 1px solid #1890ff;
}

.email-preview-content {
  background-color: #ffffff;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-height: 350px;
  max-height: 600px;
  overflow-y: auto;
}

.email-preview-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 16px;
}

/* 帮助卡片 */
.help-cards {
  margin-top: 24px;
}

.help-cards .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.help-cards .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 16px;
}

.help-cards .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

.help-cards .ant-card-body {
  padding: 12px 16px;
}

.help-cards code {
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
  color: #d63384;
}

/* CKEditor 工具栏样式优化 */
.ckeditor-container .ck-toolbar {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

.ckeditor-container .ck-toolbar__items {
  flex-wrap: wrap !important;
  gap: 4px !important;
}

.ckeditor-container .ck-button {
  border: 1px solid #d9d9d9 !important;
  border-radius: 3px !important;
  background: #fff !important;
  margin: 0 1px !important;
  transition: all 0.2s ease !important;
}

.ckeditor-container .ck-button:hover {
  background: #f0f8ff !important;
  border-color: #1890ff !important;
}

.ckeditor-container .ck-button.ck-on {
  background: #e6f7ff !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

.ckeditor-container .ck-dropdown__panel {
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.ckeditor-container .ck-dropdown__panel .ck-button {
  border: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.ckeditor-container .ck-dropdown__panel .ck-button:hover {
  background: #f5f5f5 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .ckeditor-container .ck-editor__editable {
    min-height: 300px !important;
  }
  
  .code-editor {
    min-height: 300px;
  }
  
  .email-preview-container {
    padding: 16px;
  }
  
  .email-preview-content {
    padding: 16px;
  }
}

/* 变量选择器样式 */
.ckeditor-email-template .ant-select {
  min-width: 140px;
}

/* 模态框样式优化 */
.ckeditor-email-template .ant-modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 预览变量设置区域 */
.ckeditor-email-template .preview-variables {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

/* 邮件预览样式 */
.email-preview-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

.email-preview-content table td,
.email-preview-content table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.email-preview-content table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.email-preview-content img {
  max-width: 100%;
  height: auto;
}

.email-preview-content a {
  color: #1890ff;
  text-decoration: none;
}

.email-preview-content a:hover {
  text-decoration: underline;
}

/* 加载状态 */
.ckeditor-email-template .ant-spin {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 错误状态 */
.ckeditor-email-template .ant-alert {
  margin-bottom: 16px;
}

/* 成功消息 */
.ckeditor-email-template .ant-message {
  z-index: 1001;
} 

/* 变量管理模态框样式 */
.variable-list-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;
}

.variable-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.variable-list-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.variable-list-item-system {
  border: 1px solid #52c41a;
  background-color: #f6ffed;
}

.variable-list-item-template {
  border: 1px solid #1890ff;
  background-color: #e6f7ff;
}

.variable-list-item-custom {
  border: 1px solid #722ed1;
  background-color: #f9f0ff;
}

.variable-info {
  flex: 1;
}

.variable-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.variable-name {
  font-weight: 500;
  margin-right: 8px;
}

.variable-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.variable-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.variable-meta {
  font-size: 12px;
  color: #999;
}

.variable-delete-btn {
  margin-left: 8px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.variable-delete-btn:hover {
  opacity: 1;
}

/* 变量分类标签样式 */
.variable-category-tag {
  margin-right: 8px;
}

/* 表单样式优化 */
.variable-form-section {
  margin-top: 16px;
}

.variable-form-title {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 500;
} 