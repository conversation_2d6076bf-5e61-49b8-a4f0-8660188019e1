import React, { useState, useCallback, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { Card, Row, Col, Space, Button, message } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import CKEditorEmailTemplate from './CKEditorEmailTemplate';

interface TemplateContentEditorProps {
  value?: string;
  onChange?: (html: string) => void;
  onSave?: (html: string) => void;
  onSaveWithVariables?: (html: string, variables: Record<string, any>) => void; // 新增：保存时包含变量
  loading?: boolean;
  showSaveButton?: boolean;
  saveButtonText?: string;
  showHelpCards?: boolean;
  variables?: Array<{
    name: string;
    label: string;
    description?: string;
    defaultValue?: string;
  }>;
  templateId?: number; // 新增：模板ID
  onPreview?: (html: string, variables: Record<string, any>) => void;
}

interface TemplateContentEditorRef {
  getCustomVariables: () => Record<string, any>;
}

const TemplateContentEditor = forwardRef<TemplateContentEditorRef, TemplateContentEditorProps>(({
  value = '',
  onChange,
  onSave,
  onSaveWithVariables,
  loading = false,
  showSaveButton = true,
  saveButtonText = '保存模板内容',
  showHelpCards = true,
  variables = [],
  templateId,
  onPreview,
}, ref) => {
  const [htmlContent, setHtmlContent] = useState(value);
  
  // 新增：CKEditorEmailTemplate的ref
  const editorRef = useRef<any>(null);

  // 同步外部value变化
  useEffect(() => {
    setHtmlContent(value);
  }, [value]);

  // 处理内容变化
  const handleContentChange = useCallback((content: string) => {
    setHtmlContent(content);
    onChange?.(content);
  }, [onChange]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onSaveWithVariables) {
      // 这里需要从CKEditorEmailTemplate获取自定义变量
      // 暂时先调用onSave，后续可以通过ref获取变量
      onSave?.(htmlContent);
    } else {
      onSave?.(htmlContent);
    }
    message.success('模板内容保存成功');
  }, [htmlContent, onSave, onSaveWithVariables]);

  // 处理预览
  const handlePreview = useCallback((html: string, variables: Record<string, any>) => {
    if (onPreview) {
      onPreview(html, variables);
    }
  }, [onPreview]);

  // 暴露给父组件的实例方法
  useImperativeHandle(ref, () => ({
    getCustomVariables: () => {
      if (editorRef.current) {
        return editorRef.current.getCustomVariables();
      }
      return {};
    },
  }));

  return (
    <div>
      <CKEditorEmailTemplate
        ref={editorRef}
        value={htmlContent}
        onChange={handleContentChange}
        onSave={handleSave}
        onSaveWithVariables={onSaveWithVariables}
        loading={loading}
        showSaveButton={false}
        showHelpCards={showHelpCards}
        variables={variables}
        templateId={templateId}
        onPreview={handlePreview}
      />
      
      {showSaveButton && (
        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={handleSave}
            size="large"
          >
            {saveButtonText}
          </Button>
        </div>
      )}
    </div>
  );
});

export default TemplateContentEditor; 