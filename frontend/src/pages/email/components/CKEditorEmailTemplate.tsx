import React, { useState, useCallback, useEffect, useRef, useMemo, forwardRef, useImperativeHandle } from 'react';
import { Card, Button, Space, message, Select, Divider, Modal, Form, Input, Row, Col, Typography, Tag, Switch } from 'antd';
import GlobalTooltip from '../../../components/GlobalTooltip';
import { 
  EyeOutlined, 
  CodeOutlined, 
  SaveOutlined, 
  EyeInvisibleOutlined, 
  SettingOutlined,
  TableOutlined,
  PlayCircleOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import DecoupledEditor from '@ckeditor/ckeditor5-build-decoupled-document';
import { emailTemplateService } from '../../../services/email';
import { TemplateVariableInfo } from '../../../types/email';
import './CKEditorEmailTemplate.css';

const { Title, Text } = Typography;
const { Option } = Select;

interface EmailVariable {
  name: string;
  label: string;
  description?: string;
  defaultValue?: string;
  category?: 'system' | 'template' | 'custom';
  type?: 'string' | 'number' | 'boolean' | 'date';
  required?: boolean;
  default_value?: string;
}

interface CKEditorEmailTemplateProps {
  value?: string;
  onChange?: (html: string) => void;
  onSave?: (html: string) => void;
  onSaveWithVariables?: (html: string, variables: Record<string, any>) => void; // 新增：保存时包含变量
  loading?: boolean;
  showSaveButton?: boolean;
  showHelpCards?: boolean;
  variables?: EmailVariable[];
  templateId?: number; // 新增：模板ID，用于获取服务端变量
  onPreview?: (html: string, variables: Record<string, any>) => void;
}

interface CKEditorEmailTemplateRef {
  getCustomVariables: () => Record<string, any>;
}

const CKEditorEmailTemplate = forwardRef<CKEditorEmailTemplateRef, CKEditorEmailTemplateProps>(({
  value = '',
  onChange,
  onSave,
  onSaveWithVariables,
  loading = false,
  showSaveButton = true,
  showHelpCards = true,
  variables = [],
  templateId,
  onPreview,
}, ref) => {
  const [htmlContent, setHtmlContent] = useState(value || '');
  const [editMode, setEditMode] = useState<'visual' | 'code'>('visual');
  const [showPreview, setShowPreview] = useState(false);
  const [previewVariables, setPreviewVariables] = useState<Record<string, any>>({});
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [customVariables, setCustomVariables] = useState<EmailVariable[]>(variables);
  const [variablesModalVisible, setVariablesModalVisible] = useState(false);
  const [variablesForm] = Form.useForm();
  
  // 新增：服务端变量状态
  const [serverVariables, setServerVariables] = useState<TemplateVariableInfo[]>([]);
  const [variablesLoading, setVariablesLoading] = useState(false);
  
  // 新增：删除变量时的加载状态
  const [deletingVariables, setDeletingVariables] = useState<Set<string>>(new Set());
  
  // 新增：添加变量时的加载状态
  const [addingVariable, setAddingVariable] = useState(false);
  
  // 编辑器状态管理
  const [editorState, setEditorState] = useState<'initializing' | 'ready' | 'destroyed'>('initializing');

  const editorRef = useRef<any>(null);
  const toolbarRef = useRef<HTMLDivElement>(null);

  // 初始化customVariables，避免外部variables变化时的不必要更新
  useEffect(() => {
    setCustomVariables(variables);
  }, [variables]);

  // 监听外部value变化，同步到内部状态
  useEffect(() => {
    setHtmlContent(value || '');
  }, [value]);

  // 新增：从服务端获取变量列表
  useEffect(() => {
    if (templateId) {
      fetchTemplateVariables();
    }
  }, [templateId]);

  // 获取模板变量
  const fetchTemplateVariables = async () => {
    // 如果没有templateId，传递0表示只获取系统变量
    const templateIdToUse = templateId || 0;
    
    setVariablesLoading(true);
    try {
      const response = await emailTemplateService.getTemplateVariables({ template_id: templateIdToUse });
      if (response.data) {
        setServerVariables(response.data);
        
        // 初始化预览变量
        const initialVariables: Record<string, any> = {};
        response.data.forEach(variable => {
          initialVariables[variable.name] = variable.default_value || '';
        });
        setPreviewVariables(prev => {
          const hasChanges = JSON.stringify(prev) !== JSON.stringify(initialVariables);
          return hasChanges ? initialVariables : prev;
        });
      }
    } catch (error) {
      console.error('Failed to fetch template variables:', error);
      message.error('获取模板变量失败');
    } finally {
      setVariablesLoading(false);
    }
  };

  // 默认变量列表（当没有templateId时使用）
  const defaultVariables: EmailVariable[] = [
    { name: 'user_name', label: '用户姓名', description: '当前用户的姓名', defaultValue: '张三', category: 'system' },
    { name: 'user_email', label: '用户邮箱', description: '当前用户的邮箱地址', defaultValue: '<EMAIL>', category: 'system' },
    { name: 'company_name', label: '公司名称', description: '公司或组织名称', defaultValue: '示例公司', category: 'system' },
    { name: 'verification_code', label: '验证码', description: '验证码或激活码', defaultValue: '123456', category: 'system' },
    { name: 'current_date', label: '当前日期', description: '当前日期（YYYY-MM-DD格式）', defaultValue: '2024-01-01', category: 'system' },
    { name: 'current_time', label: '当前时间', description: '当前时间（HH:MM:SS格式）', defaultValue: '12:00:00', category: 'system' },
    { name: 'unsubscribe_url', label: '退订链接', description: '邮件退订链接', defaultValue: 'https://example.com/unsubscribe', category: 'system' },
    { name: 'support_email', label: '客服邮箱', description: '客服联系邮箱', defaultValue: '<EMAIL>', category: 'system' },
  ];

  // 合并所有变量：服务端变量 + 自定义变量
  const allVariables = useMemo(() => {
    const variables: EmailVariable[] = [];
    
    // 1. 添加服务端变量（如果有templateId）
    if (templateId && serverVariables.length > 0) {
      serverVariables.forEach(variable => {
        variables.push({
          name: variable.name,
          label: variable.label,
          description: variable.description,
          defaultValue: variable.default_value,
          category: variable.category as 'system' | 'template' | 'custom',
          type: variable.type as 'string' | 'number' | 'boolean' | 'date',
          required: variable.required,
          default_value: variable.default_value,
        });
      });
    } else {
      // 2. 添加默认变量（当没有templateId时）
      variables.push(...defaultVariables);
    }
    
    // 3. 添加自定义变量
    variables.push(...customVariables);
    
    return variables;
  }, [templateId, serverVariables, customVariables, defaultVariables]);

  // 插入变量到编辑器
  const insertVariable = useCallback((variableName: string) => {
    if (editorRef.current) {
      const editor = editorRef.current;
      const insertText = `{{${variableName}}}`;
      
      if (editMode === 'visual') {
        // 可视化模式：插入到光标位置
        editor.model.change((writer: any) => {
          const insertPosition = editor.model.document.selection.getFirstPosition();
          writer.insertText(insertText, insertPosition);
        });
      } else {
        // 代码模式：直接插入到内容中
        const currentContent = editor.getData();
        const newContent = currentContent + insertText;
        editor.setData(newContent);
      }
      
      editor.focus();
    }
  }, [editMode]);

  // 处理编辑器内容变化
  const handleEditorChange = useCallback((event: any, editor: any) => {
    const data = editor.getData();
    setHtmlContent(data);
    onChange?.(data);
  }, [onChange]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (onSaveWithVariables) {
      // 构建变量对象，过滤掉系统变量
      const variablesObj: Record<string, any> = {};
      customVariables.forEach(variable => {
        if (variable.category !== 'system') {
          variablesObj[variable.name] = {
            type: variable.type || 'string',
            required: variable.required || false,
            description: variable.description || '',
          };
        }
      });
      onSaveWithVariables(htmlContent, variablesObj);
    } else {
      onSave?.(htmlContent);
    }
    message.success('模板保存成功');
  }, [htmlContent, onSave, onSaveWithVariables, customVariables]);

  // 获取自定义变量（供外部使用）
  const getCustomVariables = useCallback(() => {
    const variablesObj: Record<string, any> = {};
    // 获取所有非系统变量（包括模板变量和自定义变量）
    const allNonSystemVariables = allVariables.filter(v => v.category !== 'system');
    
    allNonSystemVariables.forEach(variable => {
      variablesObj[variable.name] = {
        label: variable.label,
        type: variable.type || 'string',
        required: variable.required || false,
        description: variable.description || '',
      };
    });
    return variablesObj;
  }, [allVariables]);

  // 处理预览
  const handlePreview = useCallback(() => {
    if (onPreview) {
      onPreview(htmlContent, previewVariables);
    } else {
      setPreviewModalVisible(true);
    }
  }, [htmlContent, previewVariables, onPreview]);

  // 生成预览内容
  const generatePreviewContent = useCallback(() => {
    let previewContent = htmlContent;
    
    // 替换变量
    Object.entries(previewVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      previewContent = previewContent.replace(regex, value || `{{${key}}}`);
    });
    
    return previewContent;
  }, [htmlContent, previewVariables]);

  // 验证变量名和显示名称是否重复
  const validateVariableUniqueness = useCallback((name: string, label: string, excludeName?: string) => {
    // 检查变量名是否重复
    const existingVariableNames = allVariables.map(v => v.name);
    if (existingVariableNames.includes(name) && name !== excludeName) {
      return { isValid: false, message: `变量名 "${name}" 已存在，请使用其他名称` };
    }

    // 检查显示名称是否重复
    const existingLabels = allVariables.map(v => v.label);
    if (existingLabels.includes(label) && label !== excludeName) {
      return { isValid: false, message: `显示名称 "${label}" 已存在，请使用其他名称` };
    }

    return { isValid: true, message: '' };
  }, [allVariables]);

  // 添加自定义变量
  const handleAddVariable = useCallback(async (values: any) => {
    setAddingVariable(true);
    
    try {
      // 字段验证
      if (!values.name || !values.name.trim()) {
        message.error('变量名不能为空');
        return;
      }
      if (!values.label || !values.label.trim()) {
        message.error('显示名称不能为空');
        return;
      }
      if (!values.description || !values.description.trim()) {
        message.error('变量描述不能为空');
        return;
      }
      if (!values.type) {
        message.error('请选择变量类型');
        return;
      }

      // 变量名格式验证：只能包含小写字母、数字和下划线，且必须以字母开头
      const namePattern = /^[a-z][a-z0-9_]*$/;
      if (!namePattern.test(values.name)) {
        message.error('变量名只能包含小写字母、数字和下划线，且必须以字母开头');
        return;
      }

      // 验证变量名和显示名称的唯一性
      const uniquenessCheck = validateVariableUniqueness(values.name, values.label);
      if (!uniquenessCheck.isValid) {
        message.error(uniquenessCheck.message);
        return;
      }

      const newVariable: EmailVariable = {
        name: values.name,
        label: values.label,
        description: values.description,
        type: values.type,
        required: values.required,
        default_value: values.default_value,
        category: 'custom',
      };
      
      // 如果有templateId，将本地自定义数据全量提交给服务端
      if (templateId) {
        // 获取所有非系统变量（包括模板变量和自定义变量）
        const allNonSystemVariables = allVariables.filter(v => v.category !== 'system');
        
        // 构建包含所有非系统变量的对象（包括新添加的）
        const allVariablesToSubmit = [...allNonSystemVariables, newVariable];
        const variablesObj: Record<string, any> = {};
        
        // 将所有非系统变量转换为服务端格式
        allVariablesToSubmit.forEach(variable => {
          variablesObj[variable.name] = {
            label: variable.label,
            type: variable.type || 'string',
            required: variable.required || false,
            description: variable.description || '',
          };
        });
        
        // 调用更新模板API，使用 variables_only 模式，服务端进行覆盖操作
        await emailTemplateService.updateTemplateVariables(templateId, variablesObj);
        
        // 更新本地状态
        setCustomVariables(prev => [...prev, newVariable]);
        setPreviewVariables(prev => ({ ...prev, [values.name]: values.default_value || '' }));
        
        // 清除服务端缓存
        await emailTemplateService.clearVariableCache({ template_id: templateId });
        
        // 重新获取服务端变量列表
        await fetchTemplateVariables();
        
        message.success('自定义变量已添加到模板');
      } else {
        // 没有templateId，保存到本地状态
        setCustomVariables(prev => [...prev, newVariable]);
        setPreviewVariables(prev => ({ ...prev, [values.name]: values.default_value || '' }));
        message.success('自定义变量已保存到本地，将在创建模板时一并提交');
      }
      
      variablesForm.resetFields();
      setVariablesModalVisible(false);
    } catch (error) {
      console.error('Failed to add variable:', error);
      message.error('添加变量失败');
    } finally {
      setAddingVariable(false);
    }
  }, [templateId, customVariables, allVariables, variablesForm, fetchTemplateVariables, validateVariableUniqueness]);

  // 删除变量
  const handleDeleteVariable = useCallback(async (variableToDelete: EmailVariable) => {
    // 设置删除加载状态
    setDeletingVariables(prev => new Set(prev).add(variableToDelete.name));
    
    try {
      // 如果有templateId，将本地自定义数据全量提交给服务端
      if (templateId) {
        // 获取删除后的所有非系统变量
        const allNonSystemVariables = allVariables.filter(v => v.category !== 'system');
        const remainingNonSystemVariables = allNonSystemVariables.filter(v => v.name !== variableToDelete.name);
        
        // 构建包含所有剩余非系统变量的对象
        const variablesObj: Record<string, any> = {};
        
        // 将所有剩余非系统变量转换为服务端格式
        remainingNonSystemVariables.forEach(variable => {
          variablesObj[variable.name] = {
            label: variable.label,
            type: variable.type || 'string',
            required: variable.required || false,
            description: variable.description || '',
          };
        });
        
        // 调用更新模板API，使用 variables_only 模式，服务端进行覆盖操作
        await emailTemplateService.updateTemplateVariables(templateId, variablesObj);
        
        // 更新本地状态
        setCustomVariables(prev => prev.filter(v => v.name !== variableToDelete.name));
        setPreviewVariables(prev => {
          const newPreviewVariables = { ...prev };
          delete newPreviewVariables[variableToDelete.name];
          return newPreviewVariables;
        });
        
        // 清除服务端缓存
        await emailTemplateService.clearVariableCache({ template_id: templateId });
        
        // 重新获取服务端变量列表
        await fetchTemplateVariables();
        
        message.success(`变量 "${variableToDelete.label}" 已从模板中删除`);
      } else {
        // 没有templateId，只从本地状态删除
        setCustomVariables(prev => prev.filter(v => v.name !== variableToDelete.name));
        setPreviewVariables(prev => {
          const newPreviewVariables = { ...prev };
          delete newPreviewVariables[variableToDelete.name];
          return newPreviewVariables;
        });
        message.success(`变量 "${variableToDelete.label}" 已从本地删除`);
      }
    } catch (error) {
      console.error('Failed to delete variable:', error);
      message.error('删除变量失败');
    } finally {
      setDeletingVariables(prev => {
        const newSet = new Set(prev);
        newSet.delete(variableToDelete.name);
        return newSet;
      });
    }
  }, [templateId, customVariables, fetchTemplateVariables]);

  // 初始化预览变量
  useEffect(() => {
    const initialVariables: Record<string, any> = {};
    allVariables.forEach(variable => {
      initialVariables[variable.name] = variable.default_value || '';
    });
    
    // 只有当变量发生变化时才更新
    setPreviewVariables(prev => {
      const hasChanges = JSON.stringify(prev) !== JSON.stringify(initialVariables);
      return hasChanges ? initialVariables : prev;
    });
  }, [allVariables]);

  // CKEditor配置
  const editorConfig = {
    toolbar: {
      items: [
        'heading',
        '|',
        'bold', 'italic', 'underline', 'strikethrough',
        '|',
        'fontColor', 'fontBackgroundColor',
        '|',
        'bulletedList', 'numberedList',
        '|',
        'alignment',
        '|',
        'link', 'blockQuote', 'insertTable', 'imageUpload',
        '|',
        'undo', 'redo'
      ]
    },
    language: 'zh-cn',
    table: {
      contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
    },
    image: {
      upload: {
        types: ['jpeg', 'png', 'gif', 'webp']
      }
    },
    link: {
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://'
    }
  };

  // 渲染编辑器内容
  const renderEditorContent = () => {
    if (showPreview) {
      return (
        <div className="email-preview-container">
          <div className="email-preview-content">
            {htmlContent ? (
              <div dangerouslySetInnerHTML={{ __html: generatePreviewContent() }} />
            ) : (
              <div className="email-preview-empty">
                暂无内容，请先编辑模板
              </div>
            )}
          </div>
        </div>
      );
    }

    if (editMode === 'code') {
      return (
        <div className="code-editor-container">
          <textarea
            value={htmlContent}
            onChange={(e) => {
              setHtmlContent(e.target.value);
              onChange?.(e.target.value);
            }}
            className="code-editor"
            placeholder="请输入HTML代码..."
          />
        </div>
      );
    }

    // 只有在非预览模式且为可视化模式时才渲染CKEditor
    return (
      <div className="ckeditor-container">
        <div ref={toolbarRef} className="ckeditor-toolbar"></div>
        <CKEditor
          key={`ckeditor-${editMode}-${showPreview}`} // 添加showPreview到key中
          editor={DecoupledEditor as any}
          config={editorConfig}
          data={htmlContent}
          onReady={(editor) => {
            // 安全清理之前的编辑器
            if (editorRef.current && editorRef.current !== editor && editorState === 'ready') {
              try {
                if (editorRef.current.isDestroyed === false) {
                  editorRef.current.destroy();
                }
              } catch (error) {
                console.warn('Previous editor destroy error:', error);
              }
            }
            
            editorRef.current = editor;
            setEditorState('ready');
            
            // 清理之前的工具栏内容
            if (toolbarRef.current) {
              toolbarRef.current.innerHTML = '';
              // 将工具栏插入到指定位置
              toolbarRef.current.appendChild(editor.ui.view.toolbar.element);
            }
          }}
          onChange={handleEditorChange}
        />
      </div>
    );
  };

  // 安全销毁编辑器
  const safeDestroyEditor = () => {
    if (editorRef.current && editorState === 'ready') {
      try {
        if (editorRef.current.isDestroyed === false) {
          editorRef.current.destroy();
        }
      } catch (error) {
        console.warn('Editor destroy error:', error);
      } finally {
        editorRef.current = null;
        setEditorState('destroyed');
      }
    }
  };

  // 模式切换处理
  const handleModeChange = (newMode: 'visual' | 'code') => {
    // 安全清理编辑器引用
    safeDestroyEditor();
    
    // 清理工具栏
    if (toolbarRef.current) {
      toolbarRef.current.innerHTML = '';
    }
    
    setEditMode(newMode);
    setShowPreview(false);
    setEditorState('initializing');
  };

  // 组件卸载时清理编辑器
  useEffect(() => {
    return () => {
      safeDestroyEditor();
      if (toolbarRef.current) {
        toolbarRef.current.innerHTML = '';
      }
    };
  }, []);

  // 预览模式切换处理
  const handlePreviewToggle = () => {
    if (showPreview) {
      // 退出预览模式
      setShowPreview(false);
    } else {
      // 进入预览模式 - 清理编辑器
      if (editMode === 'visual') {
        safeDestroyEditor();
        if (toolbarRef.current) {
          toolbarRef.current.innerHTML = '';
        }
      }
      setShowPreview(true);
    }
  };

  // 暴露给父组件的实例方法
  useImperativeHandle(ref, () => ({
    getCustomVariables,
  }));

  return (
    <div className="ckeditor-email-template">
      <Card 
        bodyStyle={{ padding: '24px' }}
        style={{
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: '8px'
        }}
      >
        {/* 工具栏 - 在预览模式下隐藏 */}
        {!showPreview && (
          <div className="editor-toolbar">
            <div className="toolbar-left">
              <Space>
                <GlobalTooltip title="可视化编辑">
                  <Button
                    size="small"
                    type={editMode === 'visual' ? 'primary' : 'default'}
                    icon={<EyeOutlined />}
                    onClick={() => handleModeChange('visual')}
                  >
                    可视化
                  </Button>
                </GlobalTooltip>
                
                <GlobalTooltip title="源码编辑">
                  <Button
                    size="small"
                    type={editMode === 'code' ? 'primary' : 'default'}
                    icon={<CodeOutlined />}
                    onClick={() => handleModeChange('code')}
                  >
                    源码
                  </Button>
                </GlobalTooltip>
                
                <GlobalTooltip title="预览效果">
                  <Button
                    size="small"
                    type="primary"
                    icon={<PlayCircleOutlined />}
                    onClick={handlePreviewToggle}
                  >
                    预览
                  </Button>
                </GlobalTooltip>
              </Space>
            </div>
            
            <div className="toolbar-right">
              <Space>
                <Divider type="vertical" />
                
                <Select
                  placeholder="插入变量"
                  style={{ width: 140 }}
                  size="small"
                  loading={variablesLoading}
                  onSelect={(value: string | undefined) => value && insertVariable(value)}
                  value={undefined}
                  options={allVariables.map(v => ({ 
                    value: v.name, 
                    label: v.label,
                    // 根据分类添加不同的样式
                    className: v.category === 'system' ? 'variable-option-system' : 
                              v.category === 'template' ? 'variable-option-template' : 
                              'variable-option-custom'
                  }))}
                />
                
                <GlobalTooltip title="管理变量">
                  <Button
                    size="small"
                    icon={<TableOutlined />}
                    onClick={() => setVariablesModalVisible(true)}
                  >
                    变量管理
                  </Button>
                </GlobalTooltip>
                
                {/* 添加刷新变量按钮（始终显示） */}
                <GlobalTooltip title="刷新变量列表">
                  <Button
                    size="small"
                    icon={<TableOutlined />}
                    loading={variablesLoading}
                    onClick={fetchTemplateVariables}
                  >
                    刷新变量
                  </Button>
                </GlobalTooltip>
                
                <GlobalTooltip title="预览模板">
                  <Button
                    size="small"
                    icon={<PlayCircleOutlined />}
                    onClick={handlePreview}
                  >
                    预览
                  </Button>
                </GlobalTooltip>
                
                {showSaveButton && (
                  <GlobalTooltip title="保存模板">
                    <Button
                      size="small"
                      type="primary"
                      icon={<SaveOutlined />}
                      loading={loading}
                      onClick={handleSave}
                    >
                      保存
                    </Button>
                  </GlobalTooltip>
                )}
              </Space>
            </div>
          </div>
        )}

        {/* 预览模式下的退出按钮 */}
        {showPreview && (
          <div className="preview-toolbar" style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            background: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '6px 6px 0 0',
            borderBottom: 'none'
          }}>
            <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
              预览模式
            </div>
            <Button
              size="small"
              icon={<EyeInvisibleOutlined />}
              onClick={handlePreviewToggle}
            >
              退出预览
            </Button>
          </div>
        )}

        {/* 编辑器内容 */}
        {renderEditorContent()}

        {/* 帮助卡片 - 在预览模式下隐藏 */}
        {showHelpCards && !showPreview && (
          <div className="help-cards" style={{ marginTop: '24px' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Card size="small" title="模板变量使用说明">
                  <Text type="secondary">
                    使用 <code>{'{{变量名}}'}</code> 格式插入变量，如：<code>{'{{user_name}}'}</code>
                  </Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="邮件兼容性建议">
                  <Text type="secondary">
                    建议使用表格布局和内联样式，避免使用外部CSS文件
                  </Text>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* 自定义变量管理模态框 */}
      <Modal
        title="管理变量"
        open={variablesModalVisible}
        onCancel={() => setVariablesModalVisible(false)}
        footer={null}
        width={800}
      >
        {/* 显示现有变量列表 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={5}>现有变量</Title>
          <div className="variable-list-container">
            {allVariables.map(variable => (
              <div
                key={variable.name}
                className={`variable-list-item variable-list-item-${variable.category}`}
              >
                <div className="variable-info">
                  <div className="variable-header">
                    <Tag 
                      color={
                        variable.category === 'system' ? 'green' : 
                        variable.category === 'template' ? 'blue' : 'purple'
                      }
                      className="variable-category-tag"
                    >
                      {variable.category === 'system' ? '系统' : 
                       variable.category === 'template' ? '模板' : '自定义'}
                    </Tag>
                    <span className="variable-name">{variable.label}</span>
                    <span className="variable-code">{variable.name}</span>
                  </div>
                  <div className="variable-description">
                    {variable.description}
                  </div>
                  <div className="variable-meta">
                    类型: {variable.type} | 必填: {variable.required ? '是' : '否'}
                    {variable.default_value && ` | 默认值: ${variable.default_value}`}
                  </div>
                </div>
                
                {/* 删除按钮 - 只有非系统变量可以删除 */}
                {variable.category !== 'system' && (
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteVariable(variable)}
                    loading={deletingVariables.has(variable.name)}
                    disabled={deletingVariables.has(variable.name)}
                    className="variable-delete-btn"
                  >
                    {deletingVariables.has(variable.name) ? '删除中...' : '删除'}
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 添加新变量表单 */}
        <Divider />
        <div className="variable-form-section">
          <Title level={5} className="variable-form-title">添加自定义变量</Title>
          <Form
            form={variablesForm}
            layout="vertical"
            onFinish={handleAddVariable}
            key={allVariables.length} // 添加key以确保表单重新渲染时验证器能访问最新的变量列表
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="name"
                  label="变量名"
                  rules={[
                    { required: true, message: '请输入变量名' },
                    { pattern: /^[a-z][a-z0-9_]*$/, message: '变量名只能包含小写字母、数字和下划线，且必须以字母开头' },
                    {
                      validator: async (_, value) => {
                        if (!value) return;
                        const existingVariableNames = allVariables.map(v => v.name);
                        if (existingVariableNames.includes(value)) {
                          throw new Error(`变量名 "${value}" 已存在，请使用其他名称`);
                        }
                      }
                    }
                  ]}
                >
                  <Input placeholder="例如: user_name" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="label"
                  label="显示名称"
                  rules={[
                    { required: true, message: '请输入显示名称' },
                    {
                      validator: async (_, value) => {
                        if (!value) return;
                        const existingLabels = allVariables.map(v => v.label);
                        if (existingLabels.includes(value)) {
                          throw new Error(`显示名称 "${value}" 已存在，请使用其他名称`);
                        }
                      }
                    }
                  ]}
                >
                  <Input placeholder="例如: 用户姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="type"
                  label="变量类型"
                  rules={[{ required: true, message: '请选择变量类型' }]}
                >
                  <Select placeholder="选择类型">
                    <Option value="string">字符串</Option>
                    <Option value="number">数字</Option>
                    <Option value="boolean">布尔值</Option>
                    <Option value="date">日期</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="description"
                  label="变量描述"
                  rules={[{ required: true, message: '请输入变量描述' }]}
                >
                  <Input.TextArea rows={2} placeholder="描述变量的用途" />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="required"
                  label="是否必填"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  name="default_value"
                  label="默认值"
                >
                  <Input placeholder="可选" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<PlusOutlined />}
                loading={addingVariable}
                disabled={addingVariable}
              >
                {addingVariable ? '添加中...' : '添加变量'}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title="模板预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ marginBottom: '16px' }}>
          <Title level={5}>预览变量设置</Title>
          <Row gutter={16}>
            {allVariables.map(variable => (
              <Col span={8} key={variable.name}>
                <Form.Item label={variable.label}>
                  <Input
                    value={previewVariables[variable.name] || ''}
                    onChange={(e) => setPreviewVariables(prev => ({
                      ...prev,
                      [variable.name]: e.target.value
                    }))}
                    placeholder={variable.defaultValue || ''}
                  />
                </Form.Item>
              </Col>
            ))}
          </Row>
        </div>
        
        <div className="email-preview-container">
          <div className="email-preview-content">
            {htmlContent ? (
              <div dangerouslySetInnerHTML={{ __html: generatePreviewContent() }} />
            ) : (
              <div className="email-preview-empty">
                暂无内容，请先编辑模板
              </div>
            )}
          </div>
        </div>
      </Modal>
    </div>
  );
});

export default CKEditorEmailTemplate; 