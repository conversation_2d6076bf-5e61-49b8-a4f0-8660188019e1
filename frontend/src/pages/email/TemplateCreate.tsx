import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Switch, 
  Button, 
  Space, 
  message,
  Row,
  Col,
  Tooltip
} from 'antd';
import { 
  SaveOutlined, 
  ExpandOutlined,
  CompressOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import TemplateContentEditor from './components/TemplateContentEditor';
import { emailTemplateService, emailAccountService } from '../../services/email';
import { CreateTemplateRequest, EmailAccount } from '../../types/email';
import { useTheme } from '../../contexts/ThemeContext';
import { showAPIError, applyFieldErrorsToForm } from '../../utils/errorHandler';
import { SUCCESS } from '../../constants/errorCodes';

const { Option } = Select;

const TemplateCreate: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // 新增：CKEditorEmailTemplate的ref
  const editorRef = useRef<any>(null);
  
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');
  
  // 邮件账户相关
  const [emailAccounts, setEmailAccounts] = useState<EmailAccount[]>([]);
  const [accountsLoading, setAccountsLoading] = useState(false);
  const [accountSearchText, setAccountSearchText] = useState('');
  
  
  // 变量管理相关（简化，主要功能在TemplateContentEditor中）
  const [variables, setVariables] = useState<Array<{key: string, name: string, defaultValue: string, description: string}>>([]);
  
  // 内容区域放大状态
  const [isContentExpanded, setIsContentExpanded] = useState(false);

  // 获取邮件账户列表（支持远程搜索）
  const fetchEmailAccounts = async (searchText = '') => {
    setAccountsLoading(true);
    try {
      const response = await emailAccountService.getEmailAccounts({
        page: 1,
        page_size: 50,
        is_active: true, // 只获取激活的账户
        keyword: searchText // 使用keyword字段进行搜索
      });
      
      // API返回 ApiResponse<EmailAccount[]>，所以accounts直接在response.data中
      if (response.data && Array.isArray(response.data)) {
        setEmailAccounts(response.data);
      } else {
        setEmailAccounts([]);
      }
    } catch (error) {
      console.error('Failed to fetch email accounts:', error);
      showAPIError(error);
      setEmailAccounts([]); // 出错时设置为空数组
    } finally {
      setAccountsLoading(false);
    }
  };

  // 处理账户搜索
  const handleAccountSearch = (value: string) => {
    setAccountSearchText(value);
    if (value.length >= 2 || value.length === 0) {
      fetchEmailAccounts(value);
    }
  };

  // 组件挂载时获取邮件账户
  useEffect(() => {
    fetchEmailAccounts();
  }, []);

  // 验证模板中的变量是否都已定义
  const validateTemplateVariables = (html: string, definedVariables: Array<{key: string, name: string, defaultValue: string, description: string}>) => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = Array.from(html.matchAll(variableRegex));
    const usedVariables = matches.map(match => match[1].trim());
    
    // 去重
    const uniqueUsedVariables = Array.from(new Set(usedVariables));
    
    // 检查哪些变量未定义
    const definedVariableKeys = definedVariables.map(v => v.key);
    const undefinedVariables = uniqueUsedVariables.filter(variable => 
      !definedVariableKeys.includes(variable)
    );
    
    return {
      isValid: undefinedVariables.length === 0,
      undefinedVariables,
      usedVariables: uniqueUsedVariables
    };
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    // 清除之前的表单错误
    applyFieldErrorsToForm([], form);
    
    if (!htmlContent.trim()) {
      message.error('请输入邮件内容');
      return;
    }

    // 验证必填字段
    if (!values.account_id) {
      message.error('请选择发件账户');
      return;
    }
    
    if (!values.template_name?.trim()) {
      message.error('请输入模板名称');
      return;
    }
    
    if (!values.scenario_code?.trim()) {
      message.error('请输入场景代码');
      return;
    }
    
    if (!values.subject?.trim()) {
      message.error('请输入邮件主题');
      return;
    }

    // 验证模板变量
    const variableValidation = validateTemplateVariables(htmlContent, variables);
    if (!variableValidation.isValid) {
      message.error(`模板中使用了未定义的变量：${variableValidation.undefinedVariables.join(', ')}，请在变量管理中添加这些变量或从模板中移除。`);
      return;
    }

    setLoading(true);
    try {
      // 获取编辑器中的自定义变量
      let customVariables: Record<string, any> = {};
      if (editorRef.current) {
        customVariables = editorRef.current.getCustomVariables();
      }
      
      // 合并本地变量和自定义变量
      const localVariablesObj = variables.reduce((acc, variable) => {
        acc[variable.key] = {
          label: variable.name, // 使用name作为label
          type: 'string',
          required: false,
          description: variable.description
        };
        return acc;
      }, {} as Record<string, any>);
      
      // 合并所有变量，自定义变量优先级更高（包含label字段）
      const variablesObj = { ...localVariablesObj, ...customVariables };

      const templateData: CreateTemplateRequest = {
        name: values.template_name.trim(),
        template_code: values.scenario_code.trim(),
        description: values.description?.trim(),
        type: values.template_type === 'html' ? 1 : 2,
        subject: values.subject.trim(),
        html_content: htmlContent,
        variables: variablesObj,
        status: values.status ?? 1, // 默认为草稿状态
        account_id: Number(values.account_id), // 确保account_id是数字类型
      };

      console.log('Sending template data:', templateData); // 临时调试日志
      const response = await emailTemplateService.createTemplate(templateData);
      
      // 处理验证错误 - 检查 errors 字段
      if (response.errors && response.errors.length > 0) {
        applyFieldErrorsToForm(response.errors, form);
        return;
      }

      // 处理业务错误 - 检查 code 字段
      if (response.code !== SUCCESS) {
        message.error(response.message || '模板创建失败');
        return;
      }

      // 处理成功响应
      message.success('模板创建成功！');
      navigate('/email/templates');
      

    } catch (error) {
      showAPIError(error);
    } finally {
      setLoading(false);
    }
  };

  // 返回列表页
  const handleBack = () => {
    navigate('/email/templates');
  };

  // HTML内容变化
  const handleHtmlChange = (html: string) => {
    setHtmlContent(html);
    // 自动解析变量
    autoParseVariables(html);
  };

  // 自动解析HTML中的变量（简化版，主要用于数据提交）
  const autoParseVariables = (html: string) => {
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = Array.from(html.matchAll(variableRegex));
    const foundVariables = matches.map(match => match[1].trim());
    
    // 去重
    const uniqueFoundVariables = Array.from(new Set(foundVariables));
    
    // 检查是否有新变量
    const existingVariableKeys = variables.map(v => v.key);
    const newVariables = uniqueFoundVariables.filter(varKey => 
      !existingVariableKeys.includes(varKey)
    );
    
    // 如果有新变量，自动添加到变量列表
    if (newVariables.length > 0) {
      const additionalVariables = newVariables.map(varKey => ({
        key: varKey,
        name: varKey.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
        defaultValue: '',
        description: `自动检测到的变量：${varKey}`
      }));
      
      setVariables(prev => [...prev, ...additionalVariables]);
      
      // 提示用户有新变量被检测到
      if (newVariables.length > 0) {
        message.info(`检测到新变量：${newVariables.join(', ')}，已自动添加到变量管理中`);
      }
    }
  };

  // 处理编辑器预览
  const handleEditorPreview = (html: string, vars: Record<string, any>) => {
    setHtmlContent(html);
    autoParseVariables(html);
  };

  // 处理内容区域展开/收缩
  const toggleContentExpanded = () => {
    setIsContentExpanded(!isContentExpanded);
  };

  return (
    <div style={{ padding: '12px', height: 'calc(100vh - 24px)' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          template_type: 'html',
          is_active: true,
        }}
        style={{ height: '100%' }}
      >
        <Row gutter={16} style={{ height: '100%' }}>
          {/* 左侧配置区域 - 在展开模式下隐藏 */}
          {!isContentExpanded && (
            <Col span={10} style={{ height: '100%', overflowY: 'auto', paddingRight: '8px' }}>
              {/* 基础信息 */}
              <Card size="small" title="基础信息" style={{ marginBottom: '12px' }} bodyStyle={{ padding: '12px' }}>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item
                        label="模板名称"
                        name="template_name"
                        rules={[
                          { required: true, message: '请输入模板名称' },
                          { min: 2, max: 100, message: '模板名称长度应在2-100字符之间' }
                        ]}
                      >
                        <Input placeholder="请输入模板名称" />
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="场景代码"
                        name="scenario_code"
                        rules={[
                          { required: true, message: '请输入场景代码' },
                          { 
                            pattern: /^[a-z_][a-z0-9_]*$/, 
                            message: '场景代码只能包含小写字母、数字和下划线，且以字母或下划线开头' 
                          }
                        ]}
                      >
                        <Input placeholder="如：user_register" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="模板类型"
                        name="template_type"
                        rules={[{ required: true, message: '请选择模板类型' }]}
                      >
                        <Select placeholder="选择模板类型">
                          <Option value="html">HTML富文本</Option>
                          <Option value="text">纯文本</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Row gutter={16}>
                    <Col span={18}>
                      <Form.Item
                        label="发件账户"
                        name="account_id"
                        rules={[{ required: true, message: '请选择发件账户' }]}
                      >
                        <Select 
                          placeholder="选择发件账户"
                          loading={accountsLoading}
                          showSearch
                          onSearch={handleAccountSearch}
                          searchValue={accountSearchText}
                          filterOption={false}
                          optionLabelProp="label"
                          notFoundContent={accountsLoading ? "加载中..." : "暂无数据"}
                        >
                          {emailAccounts.map((account) => (
                            <Option 
                              key={account.id} 
                              value={account.id}
                              label={`${account.name} (${account.from_address})`}
                            >
                              <div>
                                <div style={{ fontWeight: 500 }}>{account.name}</div>
                                <div style={{ fontSize: '12px', color: '#666' }}>
                                  {account.from_address} • {account.provider}
                                </div>
                              </div>
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="状态"
                        name="status"
                        initialValue={1}
                      >
                        <Select placeholder="选择模板状态">
                          <Select.Option value={1}>草稿</Select.Option>
                          <Select.Option value={2}>已发布</Select.Option>
                          <Select.Option value={3}>已停用</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                  
                  <Form.Item
                    label="邮件主题"
                    name="subject"
                    rules={[
                      { required: true, message: '请输入邮件主题' },
                      { min: 5, max: 200, message: '邮件主题长度应在5-200字符之间' }
                    ]}
                  >
                    <Input placeholder="请输入邮件主题，支持变量：{{user_name}}，{{company_name}} 等" />
                  </Form.Item>
                  
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item
                        label="模板描述"
                        name="description"
                        rules={[
                          { max: 255, message: '模板描述长度不能超过255字符' }
                        ]}
                      >
                        <Input.TextArea 
                          placeholder="请输入模板描述，用于说明模板的用途和使用场景" 
                          rows={3}
                          showCount
                          maxLength={255}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>

              {/* 操作按钮 */}
              <Card size="small" style={{ textAlign: 'center' }} bodyStyle={{ padding: '12px' }}>
                  <Space size="middle">
                    <Button onClick={handleBack} size="large">
                      取消
                    </Button>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={<SaveOutlined />}
                      size="large"
                    >
                      创建模板
                    </Button>
                  </Space>
                </Card>
              </Col>
            )}

          {/* 右侧内容编辑区域 */}
          <Col span={isContentExpanded ? 24 : 14} style={{ height: '100%', paddingLeft: isContentExpanded ? '0' : '8px' }}>
            <Card 
              size="small" 
              title={
                <Space>
                  <span>邮件内容编辑</span>
                  {isContentExpanded && (
                    <Tooltip title="显示表单配置">
                      <Button 
                        type="text" 
                        size="small"
                        icon={<CompressOutlined />}
                        onClick={toggleContentExpanded}
                      />
                    </Tooltip>
                  )}
                </Space>
              }
              extra={
                !isContentExpanded && (
                  <Tooltip title="专注模式 - 隐藏表单配置">
                    <Button 
                      type="text" 
                      size="small"
                      icon={<ExpandOutlined />}
                      onClick={toggleContentExpanded}
                    />
                  </Tooltip>
                )
              }
              style={{ height: '100%' }}
              bodyStyle={{ height: 'calc(100% - 32px)', padding: '4px' }}
            >
              <div style={{ height: '100%' }}>
                <TemplateContentEditor
                  ref={editorRef}
                  value={htmlContent}
                  onChange={handleHtmlChange}
                  onSave={(html: string) => {
                    setHtmlContent(html);
                    form.submit();
                  }}
                  loading={loading}
                  showSaveButton={isContentExpanded} // 展开模式下显示保存按钮
                  showHelpCards={!isContentExpanded} // 正常模式下显示帮助卡片
                  onPreview={handleEditorPreview}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default TemplateCreate; 