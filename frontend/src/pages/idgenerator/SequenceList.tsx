import React, {useState, useEffect} from 'react';
import {
    Table,
    Button,
    Card,
    Input,
    InputNumber,
    Select,
    Space,
    Tag,
    message,
    Row,
    Col,
    Modal,
    Form,
    Typography,
    Tooltip,
    Switch,
} from 'antd';
import {
    PlusOutlined,
    ReloadOutlined,
    EditOutlined,
    PauseCircleOutlined,
    PlayCircleOutlined,
    SearchOutlined,
    NumberOutlined,
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
} from '@ant-design/icons';
import {useNavigate} from 'react-router-dom';
import {idGeneratorService} from '../../services/idgenerator';
import {showAPIError, applyFieldErrorsToForm} from '../../utils/errorHandler';
import {SUCCESS} from '../../constants/errorCodes';
import {
    Sequence,
    SequenceQueryParams,
    CreateSequenceRequest,
    UpdateSequenceRequest,
} from '../../types';
import { useTheme } from '../../contexts/ThemeContext';

const { Search } = Input;
const { Option } = Select;
const { Text } = Typography;

const SequenceList: React.FC = () => {
    const navigate = useNavigate();
    const { isDarkMode } = useTheme();
    const [sequences, setSequences] = useState<Sequence[]>([]);
    const [loading, setLoading] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [filters, setFilters] = useState<SequenceQueryParams>({});
    const [searchText, setSearchText] = useState('');
    const [selectedStatus, setSelectedStatus] = useState<string>('');
    const [createModal, setCreateModal] = useState({
        visible: false,
    });
    const [editModal, setEditModal] = useState({
        visible: false,
        sequence: null as Sequence | null,
    });
    const [editForm] = Form.useForm();
    const [createForm] = Form.useForm();

    // 统计数据（基于实际数据计算）
    const stats = [
        { title: '总序列数', value: pagination.total, icon: <NumberOutlined />, color: '#1890ff' },
        { title: '活跃序列', value: Array.isArray(sequences) ? sequences.filter(s => s.isActive).length : 0, icon: <CheckCircleOutlined />, color: '#52c41a' },
        { title: '停用序列', value: Array.isArray(sequences) ? sequences.filter(s => !s.isActive).length : 0, icon: <CloseCircleOutlined />, color: '#ff4d4f' },
        { title: '今日生成', value: 0, icon: <ClockCircleOutlined />, color: '#fa8c16' },
    ];

    // 加载数据
    const loadData = async (params?: SequenceQueryParams) => {
        setLoading(true);
        try {
            const queryParams = {
                ...filters,
                ...params,
                page: params?.page || pagination.current,
                size: params?.size || pagination.pageSize,
                keyword: searchText || undefined,
                status: selectedStatus || undefined,
            };

            const sequenceResponse = await idGeneratorService.getSequences(queryParams);

            setSequences(sequenceResponse.sequences);
            setPagination(prev => ({
                ...prev,
                current: sequenceResponse.page,
                total: sequenceResponse.total,
            }));
        } catch (error) {
            message.error('加载数据失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadData();
    }, [pagination.current, pagination.pageSize, selectedStatus]);

    // 状态标签渲染
    const renderStatusTag = (isActive: boolean) => {
        return (
            <Tag color={isActive ? 'green' : 'red'}>
                {isActive ? '活跃' : '停用'}
            </Tag>
        );
    };

    // 操作按钮样式
    const actionButtonStyle = {
        color: isDarkMode ? '#40a9ff' : '#1890ff',
        fontWeight: 500,
    };

    const tooltipColor = isDarkMode ? '#1890ff' : '#1890ff';

    // 操作处理
    const handleAction = async (action: string, sequence: Sequence) => {
        try {
            if (action === 'pause') {
                await idGeneratorService.pauseSequence(sequence.id);
                message.success('序列已停用');
            } else if (action === 'resume') {
                await idGeneratorService.resumeSequence(sequence.id);
                message.success('序列已启用');
            }
            // 只有在成功时才刷新数据
            loadData();
        } catch (error) {
            showAPIError(error);
            // 错误时不刷新数据
        }
    };

    const handleBatchAction = async (action: 'pause' | 'resume') => {
        if (selectedRowKeys.length === 0) {
            message.warning('请选择要操作的序列');
            return;
        }

        try {
            if (action === 'pause') {
                await Promise.all(selectedRowKeys.map(id => idGeneratorService.pauseSequence(id)));
                message.success('批量停用成功');
            } else {
                await Promise.all(selectedRowKeys.map(id => idGeneratorService.resumeSequence(id)));
                message.success('批量启用成功');
            }
            // 只有在成功时才执行以下逻辑
            setSelectedRowKeys([]);
            loadData();
        } catch (error) {
            showAPIError(error);
            // 错误时不清除选择状态和刷新数据
        }
    };

    const handleCreateSequence = async (values: any) => {
        // 清除之前的表单错误
        applyFieldErrorsToForm([], createForm);

        try {
            const request: CreateSequenceRequest = {
                businessType: values.businessType.trim(),
                sequenceName: values.sequenceName.trim(),
                initialValue: values.initialValue,
                incrementStep: values.incrementStep,
                cacheSize: values.cacheSize,
                maxValue: values.maxValue || 0,
                threshold: values.threshold,
                remarks: values.remarks?.trim() || '',
            };

            const response = await idGeneratorService.createSequence(request);
            
            // createSequence 返回的是 Sequence 对象，表示创建成功
            message.success('序列创建成功');
            setCreateModal({visible: false});
            createForm.resetFields();
            loadData();
        } catch (error) {
            showAPIError(error);
        }
    };

    const handleEditSequence = (sequence: Sequence) => {
        setEditModal({visible: true, sequence});
        
        // 处理 maxValue：如果是字符串"不限制"，则设置为 undefined
        const maxValue = typeof sequence.maxValue === 'string' ? undefined : sequence.maxValue;
        
        editForm.setFieldsValue({
            sequenceName: sequence.sequenceName,
            businessType: sequence.businessType,
            incrementStep: sequence.incrementStep,
            cacheSize: sequence.cacheSize,
            maxValue: maxValue,
            threshold: sequence.threshold,
            isActive: sequence.isActive,
            remarks: sequence.remarks,
        });
    };

    const handleUpdateSequence = async (values: any) => {
        if (!editModal.sequence) return;

        // 清除之前的表单错误
        applyFieldErrorsToForm([], editForm);

        try {
            // 处理 maxValue：如果为空或 undefined，则设置为 0（表示不限制）
            const maxValue = values.maxValue || 0;
            
            const request: UpdateSequenceRequest = {
                sequenceName: values.sequenceName.trim(),
                incrementStep: values.incrementStep,
                cacheSize: values.cacheSize,
                maxValue: maxValue,
                threshold: values.threshold,
                isActive: values.isActive,
                remarks: values.remarks?.trim() || '',
            };

            const response = await idGeneratorService.updateSequence(editModal.sequence.id, request);
            
            // 处理验证错误 - 检查 errors 字段
            if (response.errors && response.errors.length > 0) {
                applyFieldErrorsToForm(response.errors, editForm);
                return;
            }

            // 检查返回值是否为 SUCCESS
            if (response.code === SUCCESS) {
                // 只有在成功时才执行以下逻辑
                message.success('序列更新成功');
                setEditModal({visible: false, sequence: null});
                editForm.resetFields();
                loadData();
            } else {
                // 如果返回码不是 SUCCESS，显示错误信息
                message.error(response.message || '序列更新失败');
            }
        } catch (error) {
            showAPIError(error);
            // 错误时不执行成功逻辑，保持弹窗打开状态
        }
    };

    const handleSearch = (value: string) => {
        setSearchText(value);
        setPagination(prev => ({ ...prev, current: 1 }));
        handleFilter('keyword', value);
    };

    const handleFilter = (key: string, value: any) => {
        // 处理清理事件：当 value 为 undefined、null 或空字符串时，移除该过滤条件
        const newFilters = { ...filters } as any;
        if (value === undefined || value === null || value === '') {
            delete newFilters[key];
        } else {
            newFilters[key] = value;
        }
        
        setFilters(newFilters);
        setPagination(prev => ({ ...prev, current: 1 }));
        
        // 如果是状态筛选，更新 selectedStatus
        if (key === 'status') {
            setSelectedStatus(value || '');
        }
        
        // 如果是关键词搜索，更新 searchText
        if (key === 'keyword') {
            setSearchText(value || '');
        }
        
        // 直接调用 loadData 而不是通过 newFilters，因为 loadData 会使用当前的 searchText
        loadData();
    };

    // 清理所有过滤条件
    const handleClearFilters = () => {
        setFilters({});
        setSearchText('');
        setSelectedStatus('');
        setPagination(prev => ({ ...prev, current: 1 }));
        loadData({});
    };

    // 表格列定义
    const columns = [
        {
            title: '序列信息',
            key: 'sequence_info',
            render: (record: Sequence) => (
                <div>
                    <div style={{
                        fontSize: 14,
                        fontWeight: 500,
                        color: isDarkMode ? '#ffffff' : '#262626',
                        marginBottom: 4,
                    }}>
                        {record.sequenceName}
                    </div>
                    <div style={{
                        fontSize: 12,
                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    }}>
                        {record.remarks || '暂无描述'}
                    </div>
                </div>
            ),
        },
        {
            title: '序列配置',
            key: 'sequence_config',
            render: (record: Sequence) => (
                <div>
                    <div style={{
                        fontSize: 14,
                        color: isDarkMode ? '#ffffff' : '#262626',
                        marginBottom: 4,
                    }}>
                        业务类型: {record.businessType}
                    </div>
                    <div style={{
                        fontSize: 12,
                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                        marginBottom: 2,
                    }}>
                        步长: {record.incrementStep}
                    </div>
                    <div style={{
                        fontSize: 12,
                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    }}>
                        缓存: {record.cacheSize}
                    </div>
                </div>
            ),
        },
        {
            title: '当前值',
            key: 'current_value',
            render: (record: Sequence) => (
                <div>
                    <div style={{
                        fontSize: 14,
                        color: isDarkMode ? '#ffffff' : '#262626',
                        marginBottom: 4,
                    }}>
                        {record.currentValue}
                    </div>
                    <div style={{
                        fontSize: 12,
                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    }}>
                        最大值: {record.maxValue || '不限制'}
                    </div>
                </div>
            ),
        },
        {
            title: '状态',
            key: 'status',
            render: (record: Sequence) => renderStatusTag(record.isActive),
        },
        {
            title: '创建时间',
            key: 'created_at',
            render: (record: Sequence) => (
                <Text style={{ fontSize: 12 }}>
                    {record.createdAt}
                </Text>
            ),
        },
        {
            title: '操作',
            key: 'actions',
            render: (record: Sequence) => (
                <Space size="small">
                    <Tooltip title="编辑" color={tooltipColor}>
                        <Button
                            type="link"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={() => handleEditSequence(record)}
                            style={actionButtonStyle}
                        >
                            编辑
                        </Button>
                    </Tooltip>
                    <Tooltip title={record.isActive ? "停用" : "启用"} color={tooltipColor}>
                        <Button
                            type="link"
                            size="small"
                            icon={record.isActive ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                            onClick={() => handleAction(record.isActive ? 'pause' : 'resume', record)}
                            style={actionButtonStyle}
                        >
                            {record.isActive ? "停用" : "启用"}
                        </Button>
                    </Tooltip>
                </Space>
            ),
        },
    ];

    return (
        <div className="sequence-list-page">
            {/* 页面标题区域 - 改为统计展示 */}
            <div style={{ marginBottom: 24 }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    {/* 统计信息 */}
                    <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
                        {stats.map((stat, index) => (
                            <div
                                key={index}
                                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
                            >
                                <div
                                    style={{
                                        width: 40,
                                        height: 40,
                                        borderRadius: 8,
                                        background: `${stat.color}15`,
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        color: stat.color,
                                        fontSize: 18,
                                    }}
                                >
                                    {stat.icon}
                                </div>
                                <div>
                                    <div style={{
                                        fontSize: 24,
                                        fontWeight: 600,
                                        color: isDarkMode ? '#ffffff' : '#262626',
                                        lineHeight: 1,
                                    }}>
                                        {stat.value.toLocaleString()}
                                    </div>
                                    <div style={{
                                        fontSize: 12,
                                        color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                                        marginTop: 2,
                                    }}>
                                        {stat.title}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* 操作按钮 */}
                    <Space>
                        <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModal({visible: true})}>
                            新增序列
                        </Button>
                    </Space>
                </div>
            </div>

            {/* 搜索和筛选区域 */}
            <Card
                style={{
                    borderRadius: 12,
                    borderStyle: 'none',
                    background: isDarkMode ? '#1f1f1f' : '#ffffff',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                    border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
                    marginBottom: 24,
                }}
                styles={{ body: { padding: 20 } }}
            >
                <Row gutter={[16, 16]} align="middle">
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Search
                            placeholder="搜索序列名称或业务类型"
                            value={searchText}
                            onChange={(e) => {
                                setSearchText(e.target.value);
                            }}
                            onSearch={(value) => {
                                setPagination(prev => ({ ...prev, current: 1 }));
                                handleFilter('keyword', value);
                            }}
                            allowClear
                        />
                    </Col>
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Select
                            placeholder="选择状态"
                            value={selectedStatus}
                            onChange={value => {
                                setPagination(prev => ({ ...prev, current: 1 }));
                                handleFilter('status', value);
                            }}
                            allowClear
                            style={{ width: '100%' }}
                        >
                            <Option value="true">活跃</Option>
                            <Option value="false">停用</Option>
                        </Select>
                    </Col>
                    <Col xs={24} sm={12} md={8} lg={6}>
                        <Space>
                            <Button icon={<ReloadOutlined />} onClick={() => loadData()}>
                                刷新
                            </Button>
                            <Button onClick={handleClearFilters}>
                                重置
                            </Button>
                        </Space>
                    </Col>
                </Row>
            </Card>

            {/* 序列表格 */}
            <div style={{ marginBottom: 24 }}>
                <Card
                    style={{
                        borderRadius: 12,
                        borderStyle: 'none',
                        background: isDarkMode ? '#1f1f1f' : '#ffffff',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
                        border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
                    }}
                    styles={{ body: { padding: 0 } }}
                >
                    <Table
                        columns={columns}
                        dataSource={sequences}
                        loading={loading}
                        rowKey="id"
                        pagination={{
                            current: pagination.current,
                            pageSize: pagination.pageSize,
                            total: pagination.total,
                            showSizeChanger: true,
                            showQuickJumper: true,
                            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                            onChange: (page, pageSize) => setPagination(prev => ({ ...prev, current: page, pageSize })),
                        }}
                        style={{ background: 'transparent' }}
                    />
                </Card>
            </div>

            {/* 创建序列弹窗 */}
            <Modal
                title="创建序列"
                open={createModal.visible}
                onCancel={() => setCreateModal({visible: false})}
                footer={null}
                width={600}
            >
                <Form
                    form={createForm}
                    layout="vertical"
                    onFinish={handleCreateSequence}
                >
                    <Form.Item
                        label="业务类型"
                        name="businessType"
                        rules={[{required: true, message: '请选择业务类型'}]}
                    >
                        <Input placeholder="例如：user, order, payment"/>
                    </Form.Item>
                    <Form.Item
                        label="序列名称"
                        name="sequenceName"
                        rules={[{required: true, message: '请输入序列名称'}]}
                    >
                        <Input placeholder="例如：用户ID序列"/>
                    </Form.Item>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="初始值"
                                name="initialValue"
                                initialValue={1}
                                rules={[
                                    {required: true, message: '请输入初始值'},
                                    {type: 'number', min: 0, message: '初始值不能小于0'}
                                ]}
                            >
                                <InputNumber min={1} style={{width: '100%'}}/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="步长"
                                name="incrementStep"
                                initialValue={1}
                                rules={[
                                    {required: true, message: '请输入步长'},
                                    {type: 'number', min: 1, max: 1000, message: '步长必须在1-1000之间'},
                                    {
                                        validator: (_, value) => {
                                            if (value && (value < 1 || value > 1000)) {
                                                return Promise.reject(new Error('步长必须在1-1000之间'));
                                            }
                                            if (value && value > 100) {
                                                return Promise.reject(new Error('步长不建议超过100，可能影响ID连续性'));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <InputNumber 
                                    min={1} 
                                    max={1000}
                                    style={{width: '100%'}}
                                    placeholder="建议值：1-100"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="缓存大小"
                                name="cacheSize"
                                initialValue={100}
                                rules={[
                                    {required: true, message: '请输入缓存大小'},
                                    {type: 'number', min: 1, max: 10000, message: '缓存大小必须在1-10000之间'},
                                    {
                                        validator: (_, value) => {
                                            if (value && (value < 1 || value > 10000)) {
                                                return Promise.reject(new Error('缓存大小必须在1-10000之间'));
                                            }
                                            if (value && value > 1000) {
                                                return Promise.reject(new Error('缓存大小不建议超过1000，可能影响性能'));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <InputNumber 
                                    min={1} 
                                    max={10000}
                                    style={{width: '100%'}}
                                    placeholder="建议值：10-1000"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="预分配阈值(%)"
                                name="threshold"
                                initialValue={20}
                                rules={[
                                    {required: true, message: '请输入预分配阈值'},
                                    {type: 'number', min: 1, max: 100, message: '预分配阈值必须在1-100之间'}
                                ]}
                            >
                                <InputNumber min={1} max={100} style={{width: '100%'}}/>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item
                        label="最大值"
                        name="maxValue"
                        rules={[
                            {type: 'number', min: 1, message: '最大值必须大于0'}
                        ]}
                    >
                        <InputNumber min={1} style={{width: '100%'}} placeholder="不填表示不限制"/>
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remarks"
                    >
                        <Input.TextArea rows={3} placeholder="选填：序列用途说明"/>
                    </Form.Item>
                    <Form.Item style={{marginBottom: 0, textAlign: 'right'}}>
                        <Space>
                            <Button onClick={() => setCreateModal({visible: false})}>
                                取消
                            </Button>
                            <Button type="primary" htmlType="submit">
                                创建
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>

            {/* 编辑序列弹窗 */}
            <Modal
                title="编辑序列"
                open={editModal.visible}
                onCancel={() => setEditModal({visible: false, sequence: null})}
                footer={null}
                width={600}
            >
                <Form
                    form={editForm}
                    layout="vertical"
                    onFinish={handleUpdateSequence}
                >
                    <Form.Item
                        label="序列名称"
                        name="sequenceName"
                        rules={[{required: true, message: '请输入序列名称'}]}
                    >
                        <Input placeholder="例如：用户ID序列"/>
                    </Form.Item>
                    <Form.Item
                        label="业务类型"
                        name="businessType"
                    >
                        <Input disabled/>
                    </Form.Item>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="步长"
                                name="incrementStep"
                                rules={[
                                    {required: true, message: '请输入步长'},
                                    {type: 'number', min: 1, max: 1000, message: '步长必须在1-1000之间'},
                                    {
                                        validator: (_, value) => {
                                            if (value && (value < 1 || value > 1000)) {
                                                return Promise.reject(new Error('步长必须在1-1000之间'));
                                            }
                                            if (value && value > 100) {
                                                return Promise.reject(new Error('步长不建议超过100，可能影响ID连续性'));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <InputNumber 
                                    min={1} 
                                    max={1000}
                                    style={{width: '100%'}}
                                    placeholder="建议值：1-100"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="缓存大小"
                                name="cacheSize"
                                rules={[
                                    {required: true, message: '请输入缓存大小'},
                                    {type: 'number', min: 1, max: 10000, message: '缓存大小必须在1-10000之间'},
                                    {
                                        validator: (_, value) => {
                                            if (value && (value < 1 || value > 10000)) {
                                                return Promise.reject(new Error('缓存大小必须在1-10000之间'));
                                            }
                                            if (value && value > 1000) {
                                                return Promise.reject(new Error('缓存大小不建议超过1000，可能影响性能'));
                                            }
                                            return Promise.resolve();
                                        }
                                    }
                                ]}
                            >
                                <InputNumber 
                                    min={1} 
                                    max={10000}
                                    style={{width: '100%'}}
                                    placeholder="建议值：10-1000"
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col span={12}>
                            <Form.Item
                                label="最大值"
                                name="maxValue"
                            >
                                <InputNumber min={1} style={{width: '100%'}} placeholder="不填表示不限制"/>
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item
                                label="预分配阈值(%)"
                                name="threshold"
                                rules={[{required: true, message: '请输入预分配阈值'}]}
                            >
                                <InputNumber min={1} max={100} style={{width: '100%'}}/>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Form.Item
                        label="状态"
                        name="isActive"
                        valuePropName="checked"
                    >
                        <Switch checkedChildren="活跃" unCheckedChildren="停用"/>
                    </Form.Item>
                    <Form.Item
                        label="备注"
                        name="remarks"
                    >
                        <Input.TextArea rows={3} placeholder="选填：序列用途说明"/>
                    </Form.Item>
                    <Form.Item style={{marginBottom: 0, textAlign: 'right'}}>
                        <Space>
                            <Button onClick={() => setEditModal({visible: false, sequence: null})}>
                                取消
                            </Button>
                            <Button type="primary" htmlType="submit">
                                更新
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default SequenceList; 