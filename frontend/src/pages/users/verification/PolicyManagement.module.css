.container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.searchArea {
  margin-bottom: 16px;
}

.searchArea .ant-col {
  display: flex;
  align-items: center;
}

.batchActions {
  padding: 12px 16px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  margin-bottom: 16px;
}

.batchActions .ant-space {
  width: 100%;
  justify-content: space-between;
}

.statusTag {
  font-weight: 500;
}

.priorityBadge {
  font-weight: bold;
}

.actionButtons {
  display: flex;
  gap: 4px;
}

.actionButtons .ant-btn {
  padding: 4px 8px;
  height: auto;
  line-height: 1.2;
}

.tableContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.tableContainer .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
}

.tableContainer .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.ellipsisCell {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.statusSwitch {
  min-width: 44px;
}

.priorityCell {
  text-align: center;
}

.timeCell {
  color: #666;
  font-size: 12px;
}

.descriptionCell {
  max-width: 300px;
  word-break: break-all;
  line-height: 1.4;
}

.conditionCell {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f6f8fa;
  padding: 4px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.levelTag {
  font-weight: 500;
  border-radius: 12px;
}

.levelTag.low {
  background: #f6ffed;
  border-color: #b7eb8f;
  color: #389e0d;
}

.levelTag.medium {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.levelTag.high {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.levelTag.strict {
  background: #fff2f0;
  border-color: #ffccc7;
  color: #f5222d;
}

.typeTag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.dimensionTag {
  background: #f0f0f0;
  color: #666;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.sceneTag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 500;
  border: none;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toolbar .ant-space {
  flex-wrap: wrap;
}

.searchInput {
  width: 300px;
}

.filterSelect {
  width: 150px;
}

.statsCard {
  text-align: center;
  padding: 16px;
}

.statsCard .ant-statistic-title {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.statsCard .ant-statistic-content {
  color: #1890ff;
  font-weight: bold;
}

.emptyState {
  text-align: center;
  padding: 48px 24px;
  color: #999;
}

.emptyState .ant-empty-description {
  color: #999;
  margin-top: 16px;
}

.loadingState {
  text-align: center;
  padding: 48px 24px;
}

.errorState {
  text-align: center;
  padding: 48px 24px;
  color: #ff4d4f;
}

.refreshButton {
  margin-left: 8px;
}

.batchOperations {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 12px 16px;
  margin: 16px 0;
}

.selectedInfo {
  color: #1890ff;
  font-weight: 500;
  margin-right: 16px;
}

.operationButtons .ant-btn {
  margin-right: 8px;
}

.operationButtons .ant-btn:last-child {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .searchArea {
    flex-direction: column;
  }
  
  .searchArea .ant-col {
    width: 100%;
    margin-bottom: 8px;
  }
  
  .toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .searchInput {
    width: 100%;
  }
  
  .filterSelect {
    width: 100%;
  }
  
  .actionButtons {
    flex-direction: column;
  }
  
  .actionButtons .ant-btn {
    width: 100%;
    margin-bottom: 4px;
  }
}

/* 表格优化 */
.ant-table-wrapper {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

/* 分页器样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination-total-text {
  color: #666;
  font-size: 14px;
}

/* 开关样式 */
.ant-switch {
  min-width: 44px;
}

.ant-switch-checked {
  background-color: #52c41a;
}

/* 标签样式优化 */
.ant-tag {
  border-radius: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 按钮组样式 */
.ant-btn-group {
  display: flex;
  gap: 4px;
}

.ant-btn-group .ant-btn {
  flex: 1;
  min-width: auto;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  max-width: 300px;
  word-break: break-all;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

.ant-modal-body {
  padding: 24px;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 10px 16px;
  text-align: right;
}

/* 上传组件样式 */
.ant-upload-btn {
  width: 100%;
}

/* 消息提示样式 */
.ant-message {
  z-index: 9999;
}

/* 确认框样式 */
.ant-popover-inner-content {
  padding: 12px 16px;
}

.ant-popover-buttons {
  text-align: right;
  margin-top: 8px;
}

.ant-popover-buttons .ant-btn {
  margin-left: 8px;
}