{"menus": [{"key": "dashboard", "label": "仪表盘", "path": "/dashboard", "icon": "DashboardOutlined", "type": "menu", "resource_key": "dashboard", "resource_type": "menu", "sort_order": 1}, {"key": "divider-1", "type": "divider"}, {"key": "user-management", "label": "用户管理", "icon": "TeamOutlined", "type": "submenu", "resource_key": "user-management", "resource_type": "menu", "sort_order": 2, "children": [{"key": "user", "label": "用户列表", "path": "/user", "icon": "UserOutlined", "type": "menu", "resource_key": "user", "resource_type": "menu", "sort_order": 1}, {"key": "department", "label": "组织架构", "path": "/department", "icon": "ApartmentOutlined", "type": "menu", "resource_key": "department", "resource_type": "menu", "sort_order": 2}, {"key": "position", "label": "职位管理", "path": "/position", "icon": "SolutionOutlined", "type": "menu", "resource_key": "position", "resource_type": "menu", "sort_order": 3}]}, {"key": "system-management", "label": "系统管理", "icon": "SettingOutlined", "type": "submenu", "resource_key": "system-management", "resource_type": "menu", "sort_order": 3, "children": [{"key": "role", "label": "角色管理", "path": "/role", "icon": "CrownOutlined", "type": "menu", "resource_key": "role", "resource_type": "menu", "sort_order": 1}, {"key": "permission", "label": "权限管理", "path": "/permission", "icon": "SafetyCertificateOutlined", "type": "menu", "resource_key": "permission", "resource_type": "menu", "sort_order": 2}, {"key": "permission-group", "label": "权限组管理", "path": "/permission-group", "icon": "TeamOutlined", "type": "menu", "resource_key": "permission-group", "resource_type": "menu", "sort_order": 3}, {"key": "resource", "label": "资源管理", "path": "/resource", "icon": "DatabaseOutlined", "type": "menu", "resource_key": "resource", "resource_type": "menu", "sort_order": 4}, {"key": "api-management", "label": "API管理", "path": "/api-management", "icon": "ApiOutlined", "type": "menu", "resource_key": "api-management", "resource_type": "menu", "sort_order": 5}]}, {"key": "divider-2", "type": "divider"}, {"key": "file-system", "label": "文件系统", "icon": "FolderOutlined", "type": "submenu", "resource_key": "file-system", "resource_type": "menu", "sort_order": 4, "children": [{"key": "file-system/files", "label": "文件管理", "path": "/file-system/files", "icon": "FileTextOutlined", "type": "menu", "resource_key": "file-system/files", "resource_type": "menu", "sort_order": 1}, {"key": "file-system/scenes", "label": "场景配置", "path": "/file-system/scenes", "icon": "SettingOutlined", "type": "menu", "resource_key": "file-system/scenes", "resource_type": "menu", "sort_order": 2}, {"key": "file-system/upload", "label": "文件上传", "path": "/file-system/upload", "icon": "UploadOutlined", "type": "menu", "resource_key": "file-system/upload", "resource_type": "menu", "sort_order": 3}]}, {"key": "communication", "label": "通讯管理", "icon": "MailOutlined", "type": "submenu", "resource_key": "communication", "resource_type": "menu", "sort_order": 5, "children": [{"key": "email/accounts", "label": "邮件账户", "path": "/email/accounts", "icon": "UserOutlined", "type": "menu", "resource_key": "email/accounts", "resource_type": "menu", "sort_order": 1}, {"key": "email/templates", "label": "邮件模板", "path": "/email/templates", "icon": "FileTextOutlined", "type": "menu", "resource_key": "email/templates", "resource_type": "menu", "sort_order": 2}]}, {"key": "user-system", "label": "用户系统", "icon": "UserSwitchOutlined", "type": "submenu", "resource_key": "user-system", "resource_type": "menu", "sort_order": 6, "children": [{"key": "verification/configs", "label": "验证配置", "path": "/verification/configs", "icon": "SettingOutlined", "type": "menu", "resource_key": "verification/configs", "resource_type": "menu", "sort_order": 1}]}, {"key": "tools", "label": "工具箱", "icon": "ThunderboltOutlined", "type": "submenu", "resource_key": "tools", "resource_type": "menu", "sort_order": 7, "children": [{"key": "idgenerator", "label": "ID生成器", "path": "/idgenerator/sequences", "icon": "KeyOutlined", "type": "menu", "resource_key": "idgenerator", "resource_type": "menu", "sort_order": 1}]}, {"key": "divider-3", "type": "divider"}, {"key": "tenant", "label": "租户管理", "path": "/tenant", "icon": "BankOutlined", "type": "menu", "resource_key": "tenant", "resource_type": "menu", "sort_order": 8}]}