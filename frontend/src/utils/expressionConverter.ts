/**
 * Expression conversion utilities for condition builder
 */

import { ConditionExpression, Condition, DIMENSION_SCHEMAS } from '../types/verification';

/**
 * Expression converter class for handling conversion between visual and backend formats
 */
export class ExpressionConverter {
  /**
   * Convert structured expression to backend-compatible string format
   */
  static toBackendFormat(expression: ConditionExpression): string {
    if (!expression || !expression.conditions || expression.conditions.length === 0) {
      return '';
    }

    const convertCondition = (condition: Condition | ConditionExpression): string => {
      if ('logic' in condition) {
        // This is a nested ConditionExpression
        const nestedConditions = condition.conditions
          .map(convertCondition)
          .filter(c => c.trim() !== '');
        
        if (nestedConditions.length === 0) return '';
        if (nestedConditions.length === 1) return nestedConditions[0];
        
        const operator = condition.logic === 'NOT' ? 'NOT' : ` ${condition.logic} `;
        if (condition.logic === 'NOT') {
          return `NOT (${nestedConditions.join(' AND ')})`;
        }
        return `(${nestedConditions.join(operator)})`;
      } else {
        // This is a single Condition
        const { dimension, key, operator, value } = condition;
        const fieldName = `${dimension}.${key}`;
        
        if (operator === 'between' && Array.isArray(value) && value.length === 2) {
          return `${fieldName} >= ${value[0]} AND ${fieldName} <= ${value[1]}`;
        } else if (operator === 'in' && Array.isArray(value)) {
          const valueList = value.map(v => typeof v === 'string' ? `'${v}'` : v).join(', ');
          return `${fieldName} IN (${valueList})`;
        } else if (operator === 'notIn' && Array.isArray(value)) {
          const valueList = value.map(v => typeof v === 'string' ? `'${v}'` : v).join(', ');
          return `${fieldName} NOT IN (${valueList})`;
        } else {
          const formattedValue = typeof value === 'string' ? `'${value}'` : value;
          return `${fieldName} ${operator} ${formattedValue}`;
        }
      }
    };

    const conditions = expression.conditions
      .map(convertCondition)
      .filter(c => c.trim() !== '');
    
    if (conditions.length === 0) return '';
    if (conditions.length === 1) return conditions[0];
    
    const operator = expression.logic === 'NOT' ? 'NOT' : ` ${expression.logic} `;
    if (expression.logic === 'NOT') {
      return `NOT (${conditions.join(' AND ')})`;
    }
    return conditions.join(operator);
  }

  /**
   * Parse backend expression string to structured format
   * This is a simplified parser for backward compatibility
   */
  static fromBackendFormat(expr: string): ConditionExpression | null {
    if (!expr || expr.trim() === '') {
      return null;
    }

    // For now, return a simple structure that can be edited
    // In a full implementation, this would parse the expression string
    // For backward compatibility, we'll create a single condition with the raw expression
    return {
      logic: 'AND',
      conditions: [
        {
          id: Date.now().toString(),
          dimension: 'custom',
          key: 'expression',
          operator: '==',
          value: expr
        }
      ]
    };
  }

  /**
   * Convert to human-readable format for preview
   */
  static toHumanReadable(expression: ConditionExpression): string {
    if (!expression || !expression.conditions || expression.conditions.length === 0) {
      return '';
    }

    const convertCondition = (condition: Condition | ConditionExpression): string => {
      if ('logic' in condition) {
        // This is a nested ConditionExpression
        const nestedConditions = condition.conditions
          .map(convertCondition)
          .filter(c => c.trim() !== '');
        
        if (nestedConditions.length === 0) return '';
        if (nestedConditions.length === 1) return nestedConditions[0];
        
        const logicText = {
          'AND': '并且',
          'OR': '或者',
          'NOT': '非'
        }[condition.logic];
        
        if (condition.logic === 'NOT') {
          return `非 (${nestedConditions.join(' 并且 ')})`;
        }
        return `(${nestedConditions.join(` ${logicText} `)})`;
      } else {
        // This is a single Condition
        const { dimension, key, operator, value } = condition;
        
        // Find dimension and key labels
        const dimensionSchema = DIMENSION_SCHEMAS.find(d => d.value === dimension);
        const dimensionLabel = dimensionSchema?.label || dimension;
        const keySchema = dimensionSchema?.keys.find(k => k.value === key);
        const keyLabel = keySchema?.label || key;
        
        const operatorText = {
          '>': '大于',
          '<': '小于',
          '>=': '大于等于',
          '<=': '小于等于',
          '==': '等于',
          '!=': '不等于',
          'between': '在区间',
          'contains': '包含',
          'startsWith': '开始于',
          'endsWith': '结束于',
          'in': '在列表中',
          'notIn': '不在列表中'
        }[operator] || operator;
        
        if (operator === 'between' && Array.isArray(value) && value.length === 2) {
          return `${dimensionLabel}${keyLabel} ${operatorText} ${value[0]} 到 ${value[1]}`;
        } else if ((operator === 'in' || operator === 'notIn') && Array.isArray(value)) {
          return `${dimensionLabel}${keyLabel} ${operatorText} [${value.join(', ')}]`;
        } else {
          return `${dimensionLabel}${keyLabel} ${operatorText} ${value}`;
        }
      }
    };

    const conditions = expression.conditions
      .map(convertCondition)
      .filter(c => c.trim() !== '');
    
    if (conditions.length === 0) return '';
    if (conditions.length === 1) return conditions[0];
    
    const logicText = {
      'AND': '并且',
      'OR': '或者',
      'NOT': '非'
    }[expression.logic];
    
    if (expression.logic === 'NOT') {
      return `非 (${conditions.join(' 并且 ')})`;
    }
    return conditions.join(` ${logicText} `);
  }

  /**
   * Validate expression structure
   */
  static validateExpression(expression: ConditionExpression): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!expression) {
      errors.push('表达式不能为空');
      return { isValid: false, errors };
    }

    if (!expression.conditions || expression.conditions.length === 0) {
      errors.push('至少需要一个条件');
      return { isValid: false, errors };
    }

    const validateCondition = (condition: Condition | ConditionExpression, path: string = ''): void => {
      if ('logic' in condition) {
        // Validate nested expression
        if (!condition.conditions || condition.conditions.length === 0) {
          errors.push(`${path}条件组不能为空`);
        } else {
          condition.conditions.forEach((nestedCondition, index) => {
            validateCondition(nestedCondition, `${path}[${index}].`);
          });
        }
      } else {
        // Validate single condition
        if (!condition.dimension) {
          errors.push(`${path}维度不能为空`);
        }
        if (!condition.key) {
          errors.push(`${path}维度键不能为空`);
        }
        if (!condition.operator) {
          errors.push(`${path}操作符不能为空`);
        }
        if (condition.value === undefined || condition.value === null || condition.value === '') {
          errors.push(`${path}值不能为空`);
        }

        // Validate against schema
        const dimensionSchema = DIMENSION_SCHEMAS.find(d => d.value === condition.dimension);
        if (dimensionSchema) {
          const keySchema = dimensionSchema.keys.find(k => k.value === condition.key);
          if (keySchema) {
            if (!keySchema.operators.includes(condition.operator)) {
              errors.push(`${path}操作符 ${condition.operator} 不适用于 ${keySchema.label}`);
            }

            // Validate value type and constraints
            if (keySchema.validation) {
              const { min, max, pattern } = keySchema.validation;
              if (keySchema.type === 'number' && typeof condition.value === 'number') {
                if (min !== undefined && condition.value < min) {
                  errors.push(`${path}值不能小于 ${min}`);
                }
                if (max !== undefined && condition.value > max) {
                  errors.push(`${path}值不能大于 ${max}`);
                }
              }
              if (keySchema.type === 'string' && typeof condition.value === 'string' && pattern) {
                const regex = new RegExp(pattern);
                if (!regex.test(condition.value)) {
                  errors.push(`${path}值格式不正确`);
                }
              }
            }
          }
        }
      }
    };

    expression.conditions.forEach((condition, index) => {
      validateCondition(condition, `条件[${index}].`);
    });

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Generate unique ID for conditions
   */
  static generateConditionId(): string {
    return `condition_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create empty condition with default values
   */
  static createEmptyCondition(): Condition {
    return {
      id: this.generateConditionId(),
      dimension: '',
      key: '',
      operator: '',
      value: ''
    };
  }

  /**
   * Create empty condition group
   */
  static createEmptyConditionGroup(logic: 'AND' | 'OR' | 'NOT' = 'AND'): ConditionExpression {
    return {
      logic,
      conditions: [this.createEmptyCondition()]
    };
  }
}