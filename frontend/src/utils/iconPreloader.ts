import { preloadIcons } from './iconLoader';

// 常用图标列表 - 在应用启动时预加载
const COMMON_ICONS = [
  'DashboardOutlined',
  'TeamOutlined', 
  'UserOutlined',
  'ApartmentOutlined',
  'SettingOutlined',
  'PlusOutlined',
  'EditOutlined',
  'DeleteOutlined',
  'ReloadOutlined',
  'SearchOutlined',
  'HomeOutlined',
  'EyeOutlined',
  'MoreOutlined',
  'SaveOutlined',
  'InfoCircleOutlined',
];

// 用户管理相关图标 - 按需预加载
const USER_MANAGEMENT_ICONS = [
  'SolutionOutlined',
  'CrownOutlined',
  'SafetyCertificateOutlined',
  'KeyOutlined',
  'SecurityScanOutlined',
  'UserSwitchOutlined',
];

// 系统管理相关图标 - 按需预加载
const SYSTEM_MANAGEMENT_ICONS = [
  'DatabaseOutlined',
  'FileTextOutlined',
  'FolderOutlined',
  'ApiOutlined',
  'GlobalOutlined',
  'AppstoreOutlined',
];

// 通信相关图标 - 按需预加载
const COMMUNICATION_ICONS = [
  'MailOutlined',
  'BellOutlined',
];

// 功能图标 - 按需预加载
const FUNCTION_ICONS = [
  'ThunderboltOutlined',
  'UploadOutlined',
  'DownloadOutlined',
  'BankOutlined',
  'FileOutlined',
  'BranchesOutlined',
  'EyeOutlined',
  'MoreOutlined',
  'SaveOutlined',
  'InfoCircleOutlined',
  'LockOutlined',
  'UnlockOutlined',
  'CheckCircleOutlined',
  'CloseCircleOutlined',
];

/**
 * 预加载常用图标
 */
export const preloadCommonIcons = async (): Promise<void> => {
  try {
    console.log('Preloading common icons...');
    await preloadIcons(COMMON_ICONS);
    console.log('Common icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload common icons:', error);
  }
};

/**
 * 预加载用户管理相关图标
 */
export const preloadUserManagementIcons = async (): Promise<void> => {
  try {
    console.log('Preloading user management icons...');
    await preloadIcons(USER_MANAGEMENT_ICONS);
    console.log('User management icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload user management icons:', error);
  }
};

/**
 * 预加载系统管理相关图标
 */
export const preloadSystemManagementIcons = async (): Promise<void> => {
  try {
    console.log('Preloading system management icons...');
    await preloadIcons(SYSTEM_MANAGEMENT_ICONS);
    console.log('System management icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload system management icons:', error);
  }
};

/**
 * 预加载通信相关图标
 */
export const preloadCommunicationIcons = async (): Promise<void> => {
  try {
    console.log('Preloading communication icons...');
    await preloadIcons(COMMUNICATION_ICONS);
    console.log('Communication icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload communication icons:', error);
  }
};

/**
 * 预加载功能图标
 */
export const preloadFunctionIcons = async (): Promise<void> => {
  try {
    console.log('Preloading function icons...');
    await preloadIcons(FUNCTION_ICONS);
    console.log('Function icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload function icons:', error);
  }
};

/**
 * 预加载所有图标
 */
export const preloadAllIcons = async (): Promise<void> => {
  try {
    console.log('Preloading all icons...');
    await Promise.all([
      preloadCommonIcons(),
      preloadUserManagementIcons(),
      preloadSystemManagementIcons(),
      preloadCommunicationIcons(),
      preloadFunctionIcons(),
    ]);
    console.log('All icons preloaded successfully');
  } catch (error) {
    console.warn('Failed to preload all icons:', error);
  }
};

/**
 * 根据路由预加载相关图标
 */
export const preloadIconsByRoute = async (route: string): Promise<void> => {
  const routeIconMap: { [key: string]: string[] } = {
    '/user': USER_MANAGEMENT_ICONS,
    '/department': USER_MANAGEMENT_ICONS,
    '/position': USER_MANAGEMENT_ICONS,
    '/role': SYSTEM_MANAGEMENT_ICONS,
    '/permission': SYSTEM_MANAGEMENT_ICONS,
    '/resource': SYSTEM_MANAGEMENT_ICONS,
    '/tenant': SYSTEM_MANAGEMENT_ICONS,
    '/email': COMMUNICATION_ICONS,
    '/file-system': FUNCTION_ICONS,
  };

  const iconsToPreload = routeIconMap[route];
  if (iconsToPreload) {
    try {
      console.log(`Preloading icons for route: ${route}`);
      await preloadIcons(iconsToPreload);
      console.log(`Icons for route ${route} preloaded successfully`);
    } catch (error) {
      console.warn(`Failed to preload icons for route ${route}:`, error);
    }
  }
};

/**
 * 初始化图标预加载
 */
export const initializeIconPreloading = (): void => {
  // 在应用启动时预加载常用图标
  preloadCommonIcons();
  
  // 监听路由变化，预加载相关图标
  if (typeof window !== 'undefined') {
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      const url = args[2] as string;
      if (url) {
        preloadIconsByRoute(url);
      }
    };
    
    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      const url = args[2] as string;
      if (url) {
        preloadIconsByRoute(url);
      }
    };
  }
}; 