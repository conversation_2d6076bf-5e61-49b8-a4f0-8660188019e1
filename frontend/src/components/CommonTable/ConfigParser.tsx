import React, { ReactNode } from 'react';
import { 
  Avatar, 
  Tag, 
  Progress, 
  Space, 
  Button, 
  Tooltip, 
  Image,
  Typography,
  Badge,
  Popconfirm
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  CopyOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { 
  TableColumnConfig, 
  ActionButton, 
  BaseRecord,
  CellRenderType 
} from './types';

const { Text, Link } = Typography;

// 默认头像颜色
const AVATAR_COLORS = [
  '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068',
  '#108ee9', '#2db7f5', '#f50', '#87d068', '#722ed1'
];

// 获取头像颜色
const getAvatarColor = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  return AVATAR_COLORS[Math.abs(hash) % AVATAR_COLORS.length];
};

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text).then(() => {
    // message.success('复制成功');
  }).catch(() => {
    // message.error('复制失败');
  });
};

// 列渲染器工厂
export class ColumnRenderer {
  
  // 文本渲染器
  static text(value: any, config?: TableColumnConfig): ReactNode {
    if (value === null || value === undefined) return '-';
    
    const text = String(value);
    const element = config?.copyable ? (
      <Text copyable={{ text, icon: <CopyOutlined />, tooltip: '点击复制' }}>
        {text}
      </Text>
    ) : (
      <Text>{text}</Text>
    );
    
    return element;
  }

  // 数字渲染器
  static number(value: any, config?: TableColumnConfig): ReactNode {
    if (value === null || value === undefined) return '-';
    
    const num = Number(value);
    if (isNaN(num)) return '-';
    
    const formatted = config?.format ? 
      num.toLocaleString('zh-CN', JSON.parse(config.format)) : 
      num.toLocaleString('zh-CN');
    
    return <Text>{formatted}</Text>;
  }

  // 日期渲染器
  static date(value: any, config?: TableColumnConfig): ReactNode {
    if (!value) return '-';
    
    const format = config?.format || 'YYYY-MM-DD';
    const formatted = dayjs(value).format(format);
    
    return <Text>{formatted}</Text>;
  }

  // 日期时间渲染器
  static datetime(value: any, config?: TableColumnConfig): ReactNode {
    if (!value) return '-';
    
    const format = config?.format || 'YYYY-MM-DD HH:mm:ss';
    const formatted = dayjs(value).format(format);
    
    return <Text>{formatted}</Text>;
  }

  // 状态渲染器
  static status(value: any, config?: TableColumnConfig): ReactNode {
    if (!value || !config?.statusMap) return '-';
    
    const statusConfig = config.statusMap[value];
    if (!statusConfig) return <Tag>{value}</Tag>;
    
    return (
      <Tag color={statusConfig.color}>
        {statusConfig.text}
      </Tag>
    );
  }

  // 头像渲染器
  static avatar(value: any, record: BaseRecord, config?: TableColumnConfig): ReactNode {
    const avatarConfig = config?.avatarConfig || {};
    const { src, fallback, shape = 'circle', size = 'default' } = avatarConfig;
    
    // 获取头像源
    let avatarSrc: string | undefined;
    if (typeof src === 'string') {
      avatarSrc = src;
    } else if (typeof src === 'function') {
      avatarSrc = src(record);
    } else if (record.avatar) {
      avatarSrc = record.avatar;
    }
    
    // 获取备用文本
    let fallbackText: string;
    if (typeof fallback === 'string') {
      fallbackText = fallback;
    } else if (typeof fallback === 'function') {
      fallbackText = fallback(record);
    } else {
      fallbackText = String(value || record.name || '?').charAt(0).toUpperCase();
    }
    
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <Avatar
          src={avatarSrc}
          size={size}
          shape={shape}
          style={{ 
            backgroundColor: avatarSrc ? undefined : getAvatarColor(fallbackText),
            flexShrink: 0
          }}
        >
          {!avatarSrc && fallbackText}
        </Avatar>
        <div style={{ minWidth: 0 }}>
          <div style={{ fontWeight: 500, overflow: 'hidden', textOverflow: 'ellipsis' }}>
            {record.name || record.title || value}
          </div>
          {record.description && (
            <div style={{ 
              fontSize: 12, 
              color: '#666', 
              overflow: 'hidden', 
              textOverflow: 'ellipsis' 
            }}>
              {record.description}
            </div>
          )}
        </div>
      </div>
    );
  }

  // 图片渲染器
  static image(value: any, config?: TableColumnConfig): ReactNode {
    if (!value) return '-';
    
    return (
      <Image
        src={value}
        alt="图片"
        width={60}
        height={60}
        style={{ objectFit: 'cover' }}
        placeholder={
          <div style={{ 
            width: 60, 
            height: 60, 
            background: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 12,
            color: '#999'
          }}>
            加载中...
          </div>
        }
      />
    );
  }

  // 链接渲染器
  static link(value: any, record: BaseRecord, config?: TableColumnConfig): ReactNode {
    if (!value) return '-';
    
    const linkConfig = config?.linkConfig;
    const href = typeof linkConfig?.href === 'function' ? 
      linkConfig.href(record) : 
      (linkConfig?.href || value);
    
    return (
      <Link 
        href={href} 
        target={linkConfig?.target || '_blank'}
      >
        {value}
      </Link>
    );
  }

  // 徽章渲染器
  static badge(value: any, config?: TableColumnConfig): ReactNode {
    if (value === null || value === undefined) return '-';
    
    return <Badge count={value} showZero />;
  }

  // 进度条渲染器
  static progress(value: any, record: BaseRecord, config?: TableColumnConfig): ReactNode {
    const progressConfig = config?.progressConfig;
    if (!progressConfig) return '-';
    
    const percent = typeof progressConfig.percent === 'function' ? 
      progressConfig.percent(record) : 
      Number(value);
    
    if (isNaN(percent)) return '-';
    
    return (
      <Progress
        percent={percent}
        status={progressConfig.status}
        showInfo={progressConfig.showInfo !== false}
        size="small"
      />
    );
  }

  // 标签渲染器
  static tags(value: any, config?: TableColumnConfig): ReactNode {
    if (!Array.isArray(value) || value.length === 0) return '-';
    
    const tagsConfig = config?.tagsConfig || {};
    const { colorMap = {}, maxCount } = tagsConfig;
    
    const renderTag = (tag: any, index: number) => (
      <Tag key={index} color={colorMap[tag] || undefined}>
        {tag}
      </Tag>
    );
    
    if (maxCount && value.length > maxCount) {
      return (
        <Space size={4} wrap>
          {value.slice(0, maxCount).map(renderTag)}
          <Tag>+{value.length - maxCount}</Tag>
        </Space>
      );
    }
    
    return (
      <Space size={4} wrap>
        {value.map(renderTag)}
      </Space>
    );
  }

  // 操作按钮渲染器
  static actions<T extends BaseRecord>(
    value: any, 
    record: T, 
    index: number, 
    config?: TableColumnConfig<T>
  ): ReactNode {
    const actions = config?.actionsConfig;
    if (!actions || actions.length === 0) return null;
    
    const renderAction = (action: ActionButton<T>) => {
      // 检查可见性
      if (typeof action.visible === 'function' && !action.visible(record)) return null;
      if (typeof action.visible === 'boolean' && !action.visible) return null;
      
      // 检查禁用状态
      const disabled = typeof action.disabled === 'function' ? 
        action.disabled(record) : 
        action.disabled;
      
      // 检查加载状态
      const loading = typeof action.loading === 'function' ? 
        action.loading(record) : 
        action.loading;
      
      const button = (
        <Button
          key={action.key}
          type={action.type || 'text'}
          size="small"
          icon={action.icon}
          danger={action.danger}
          disabled={disabled}
          loading={loading}
          onClick={() => action.onClick(record, index)}
        >
          {action.label}
        </Button>
      );
      
      // 如果需要确认对话框
      if (action.confirm) {
        return (
          <Popconfirm
            key={action.key}
            title={action.confirm.title}
            description={action.confirm.content}
            okText={action.confirm.okText || '确定'}
            cancelText={action.confirm.cancelText || '取消'}
            onConfirm={() => action.onClick(record, index)}
          >
            {React.cloneElement(button, { onClick: undefined })}
          </Popconfirm>
        );
      }
      
      return button;
    };
    
    const visibleActions = actions
      .map(renderAction)
      .filter(Boolean);
    
    if (visibleActions.length === 0) return null;
    
    return (
      <Space size="small">
        {visibleActions}
      </Space>
    );
  }

  // 主渲染方法
  static render<T extends BaseRecord>(
    value: any,
    record: T,
    index: number,
    config: TableColumnConfig<T>
  ): ReactNode {
    // 如果有自定义渲染器，优先使用
    if (config.render) {
      return config.render(value, record, index);
    }
    
    // 根据渲染类型选择渲染器
    switch (config.renderType) {
      case 'number':
        return this.number(value, config);
      case 'date':
        return this.date(value, config);
      case 'datetime':
        return this.datetime(value, config);
      case 'status':
        return this.status(value, config);
      case 'avatar':
        return this.avatar(value, record, config);
      case 'image':
        return this.image(value, config);
      case 'link':
        return this.link(value, record, config);
      case 'badge':
        return this.badge(value, config);
      case 'progress':
        return this.progress(value, record, config);
      case 'tags':
        return this.tags(value, config);
      case 'actions':
        return this.actions(value, record, index, config);
      case 'text':
      default:
        return this.text(value, config);
    }
  }
}

// 配置解析器
export class ConfigParser {
  
  // 解析列配置为Antd Table列
  static parseColumns<T extends BaseRecord>(
    columns: TableColumnConfig<T>[]
  ): any[] {
    return columns
      .filter(col => {
        // 处理列可见性
        if (typeof col.visible === 'boolean') return col.visible;
        return true; // 对于函数类型的visible，在渲染时处理
      })
      .map(col => {
        const antdColumn: any = {
          key: col.key,
          title: col.title,
          dataIndex: col.dataIndex || col.key,
          width: col.width,
          fixed: col.fixed,
          align: col.align,
          sorter: col.sortable,
          
          render: (value: any, record: T, index: number) => {
            // 动态检查可见性
            if (typeof col.visible === 'function' && !col.visible(record)) {
              return null;
            }
            
            return ColumnRenderer.render(value, record, index, col);
          }
        };
        
        // 处理筛选
        if (col.filterable && col.statusMap) {
          antdColumn.filters = Object.entries(col.statusMap).map(([value, config]) => ({
            text: config.text,
            value
          }));
          antdColumn.onFilter = (value: any, record: T) => {
            const fieldValue = col.dataIndex ? record[col.dataIndex as keyof T] : record[col.key as keyof T];
            return fieldValue === value;
          };
        }
        
        return antdColumn;
      });
  }
  
  // 合并配置：JSON配置 + Props配置
  static mergeConfig<T extends BaseRecord>(
    jsonConfig?: any,
    propsConfig?: Partial<any>
  ): any {
    if (!jsonConfig && !propsConfig) {
      throw new Error('至少需要提供一种配置方式');
    }
    
    // 如果只有props配置，直接返回
    if (!jsonConfig) return propsConfig;
    
    // 如果只有JSON配置，直接返回
    if (!propsConfig) return jsonConfig;
    
    // 合并配置，props优先级更高
    return {
      ...jsonConfig,
      ...propsConfig,
      
      // 特殊处理数组和对象
      columns: propsConfig.columns || jsonConfig.columns,
      actions: propsConfig.actions || jsonConfig.actions,
      filters: propsConfig.filters || jsonConfig.filters,
      stats: propsConfig.stats || jsonConfig.stats,
      
      // 合并features
      features: {
        ...jsonConfig.features,
        ...propsConfig.features
      },
      
      // 合并dataSource
      dataSource: {
        ...jsonConfig.dataSource,
        ...propsConfig.dataSource
      },
      
      // 合并style
      style: {
        ...jsonConfig.style,
        ...propsConfig.style
      }
    };
  }
  
  // 验证配置
  static validateConfig<T extends BaseRecord>(config: any): boolean {
    if (!config) return false;
    if (!config.dataSource?.fetch) return false;
    if (!config.columns || !Array.isArray(config.columns)) return false;
    
    return true;
  }
}

export { AVATAR_COLORS, getAvatarColor, copyToClipboard };