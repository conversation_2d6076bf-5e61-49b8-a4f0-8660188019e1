import {API_ENDPOINTS, apiService} from '../utils/request';
import type {ApiResponse, EmailAccount} from '../types';
import {
  CreateEmailAccountRequest,
  CreateTemplateRequest,
  EmailTemplate,
  ListEmailAccountsRequest,
  ListEmailAccountsResponse,
  ListTemplatesRequest,
  ListTemplatesResponse,
  TemplateCloneRequest,
  TemplateComparison,
  TemplatePreviewRequest,
  TemplatePreviewResponse,
  TemplateSelectRequest,
  TemplateSelectResponse,
  TemplateValidationRequest,
  TemplateValidationResponse,
  TestEmailAccountRequest,
  UpdateEmailAccountRequest,
  UpdateTemplateRequest,
  GetTemplateVariablesRequest,
  GetTemplateVariablesResponse,
} from '../types';

// Email Account API Service
class EmailAccountService {
  // 获取邮件账号列表
  async getEmailAccounts(params?: ListEmailAccountsRequest): Promise<ApiResponse<EmailAccount[]>> {
    // API直接返回EmailAccount数组，而不是ListEmailAccountsResponse格式
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_LIST, params);
  }

  // 创建邮件账号
  async createEmailAccount(data: CreateEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_CREATE, data);
  }

  // 更新邮件账号
  async updateEmailAccount(data: UpdateEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_UPDATE, data);
  }

  // 获取邮件账号详情
  async getEmailAccount(id: string): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_DETAIL, { params: { account_id: id } });
  }

  // 删除邮件账号
  async deleteEmailAccount(id: number): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_DELETE, {id});
  }

  // 测试邮件账号
  async testEmailAccount(data: TestEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_TEST, data);
  }

  // 获取邮件账号类型列表
  async getEmailAccountTypes(): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_TYPES);
  }

  // 获取邮件账号测试状态列表
  async getEmailAccountTestStatus(): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_TEST_STATUS);
  }
}

export const emailAccountService = new EmailAccountService();

// Email Template API Service
class EmailTemplateService {
  // 智能模板选择
  async selectTemplate(data: TemplateSelectRequest): Promise<ApiResponse<TemplateSelectResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_SELECT, data);
  }

  // 获取模板列表
  async getTemplates(params: ListTemplatesRequest): Promise<ApiResponse<EmailTemplate[]>> {
    // 适配后端统一响应格式
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_LIST, params);
  }

  // 获取模板详情
  async getTemplate(id: string | number): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.get(API_ENDPOINTS.EMAIL.TEMPLATES_GET, { params: { id: Number(id) } });
  }

  // 创建模板
  async createTemplate(data: CreateTemplateRequest): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_CREATE, data);
  }

  // 更新模板
  async updateTemplate(data: UpdateTemplateRequest, updateMode?: string): Promise<ApiResponse<EmailTemplate>> {
    const url = updateMode ? `${API_ENDPOINTS.EMAIL.TEMPLATES_UPDATE}?update_mode=${updateMode}` : API_ENDPOINTS.EMAIL.TEMPLATES_UPDATE;
    return await apiService.post(url, data);
  }

  // 仅更新模板变量（使用 variables_only 模式）
  async updateTemplateVariables(templateId: number, variables: Record<string, {
    label: string;
    type: string;
    required: boolean;
    description: string;
  }>): Promise<ApiResponse<EmailTemplate>> {
    return await this.updateTemplate({
      id: templateId,
      variables: variables,
    }, 'variables_only');
  }

  // 添加变量（使用 variables_only 模式）
  async addTemplateVariable(templateId: number, variable: {
    name: string;
    label: string;
    type: string;
    required: boolean;
    description: string;
  }): Promise<ApiResponse<EmailTemplate>> {
    // 先获取当前变量
    const response = await this.getTemplateVariables({ template_id: templateId });
    const currentVariables = response.data || [];
    
    // 转换为变量对象格式
    const variablesObj: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
    currentVariables.forEach(v => {
      variablesObj[v.name] = {
        label: v.label,
        type: v.type,
        required: v.required,
        description: v.description,
      };
    });
    
    // 添加新变量
    variablesObj[variable.name] = variable;
    
    return await this.updateTemplateVariables(templateId, variablesObj);
  }

  // 删除变量（使用 variables_only 模式）
  async deleteTemplateVariable(templateId: number, variableName: string): Promise<ApiResponse<EmailTemplate>> {
    // 先获取当前变量
    const response = await this.getTemplateVariables({ template_id: templateId });
    const currentVariables = response.data || [];
    
    // 转换为变量对象格式，排除要删除的变量
    const variablesObj: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
    currentVariables.forEach(v => {
      if (v.name !== variableName) {
        variablesObj[v.name] = {
          label: v.label,
          type: v.type,
          required: v.required,
          description: v.description,
        };
      }
    });
    
    return await this.updateTemplateVariables(templateId, variablesObj);
  }

  // 更新变量（使用 variables_only 模式）
  async updateTemplateVariable(templateId: number, variableName: string, variable: {
    label: string;
    type: string;
    required: boolean;
    description: string;
  }): Promise<ApiResponse<EmailTemplate>> {
    // 先获取当前变量
    const response = await this.getTemplateVariables({ template_id: templateId });
    const currentVariables = response.data || [];
    
    // 转换为变量对象格式
    const variablesObj: Record<string, { label: string; type: string; required: boolean; description: string }> = {};
    currentVariables.forEach(v => {
      variablesObj[v.name] = {
        label: v.label,
        type: v.type,
        required: v.required,
        description: v.description,
      };
    });
    
    // 更新指定变量
    variablesObj[variableName] = variable;
    
    return await this.updateTemplateVariables(templateId, variablesObj);
  }

  // 删除模板
  async deleteTemplate(id: string | number): Promise<void> {
    await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_DELETE, { id: Number(id) });
  }

  // 获取系统模板列表
  async getSystemTemplates(params: ListTemplatesRequest): Promise<ApiResponse<ListTemplatesResponse>> {
    // 适配后端统一响应格式
    return await apiService.get(API_ENDPOINTS.EMAIL.TEMPLATES_SYSTEM, {params});
  }

  // 获取租户模板列表
  async getTenantTemplates(params: ListTemplatesRequest): Promise<ApiResponse<ListTemplatesResponse>> {
    // 适配后端统一响应格式
    return await apiService.get(API_ENDPOINTS.EMAIL.TEMPLATES_TENANT, {params});
  }

  // 创建租户模板
  async createTenantTemplate(data: CreateTemplateRequest): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_TENANT_CREATE, data);
  }

  // 克隆系统模板
  async cloneSystemTemplate(data: TemplateCloneRequest): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_CLONE, data);
  }

  // 模板预览
  async previewTemplate(data: TemplatePreviewRequest): Promise<ApiResponse<TemplatePreviewResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_PREVIEW, data);
  }

  // 模板验证
  async validateTemplate(data: TemplateValidationRequest): Promise<ApiResponse<TemplateValidationResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_VALIDATE, data);
  }

  // 模板比较
  async compareTemplates(scenarioCode: string): Promise<ApiResponse<TemplateComparison>> {
    return await apiService.get(API_ENDPOINTS.EMAIL.TEMPLATES_COMPARE, {
      params: {scenario_code: scenarioCode}
    });
  }

  // 按场景获取模板
  async getTemplatesByScenario(scenarioCode: string): Promise<ApiResponse<EmailTemplate[]>> {
    const params = { scenario_code: scenarioCode };
    // 适配后端统一响应格式 - 直接返回data数组
    return await apiService.get(API_ENDPOINTS.EMAIL.TEMPLATES_SCENARIO, {params});
  }

  // 批量操作
  async batchDeleteTemplates(ids: string[]): Promise<void> {
    await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_BATCH_DELETE, { ids });
  }

  async batchUpdateTemplates(updates: Array<{ id: string; status: number }>): Promise<void> {
    await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_BATCH_UPDATE, { updates });
  }

  // 获取模板变量
  async getTemplateVariables(data: GetTemplateVariablesRequest): Promise<ApiResponse<GetTemplateVariablesResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_VARIABLES, data);
  }

  // 清除变量缓存
  async clearVariableCache(data: { template_id: number }): Promise<void> {
    // 这里可以调用后端API清除缓存，或者直接重新获取变量
    // 目前先实现为重新获取变量来刷新缓存
    await this.getTemplateVariables({ template_id: data.template_id });
  }
}

export const emailTemplateService = new EmailTemplateService(); 