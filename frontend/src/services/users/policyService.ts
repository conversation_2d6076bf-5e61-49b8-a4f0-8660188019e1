/**
 * 验证策略服务
 */

import {apiService, API_ENDPOINTS} from '../../utils/request';
import { ApiResponse } from '../../types/api';
import {
    CreatePolicyRequest,
    UpdatePolicyRequest,
    DeletePolicyRequest,
    SetPolicyStatusRequest,
    PolicyListParams,
    PolicyListResponse,
    PolicyDetailRequest,
    ExprValidateRequest,
    ExprValidateResponse,
    ExprTestRequest,
    ExprTestResponse,
    VerificationPolicy
} from '../../types/verification';

/**
 * 验证策略服务类
 */
export class PolicyService {

    /**
     * 获取策略列表
     */
    async getPolicyList(params: PolicyListParams): Promise<ApiResponse<PolicyListResponse>> {
        return await apiService.post<PolicyListResponse>(API_ENDPOINTS.VERIFICATION_POLICY.LIST, params);
    }

    /**
     * 获取策略详情
     */
    async getPolicyDetail(params: PolicyDetailRequest): Promise<ApiResponse<VerificationPolicy>> {
        return await apiService.post<VerificationPolicy>(API_ENDPOINTS.VERIFICATION_POLICY.GET, params);
    }

    /**
     * 创建策略
     */
    async createPolicy(data: CreatePolicyRequest): Promise<ApiResponse<{ id: number }>> {
        return await apiService.post<{ id: number }>(API_ENDPOINTS.VERIFICATION_POLICY.CREATE, data);
    }

    /**
     * 更新策略
     */
    async updatePolicy(data: UpdatePolicyRequest): Promise<ApiResponse<null>> {
        return await apiService.post<null>(API_ENDPOINTS.VERIFICATION_POLICY.UPDATE, data);
    }

    /**
     * 删除策略
     */
    async deletePolicy(data: DeletePolicyRequest): Promise<ApiResponse<null>> {
        return await apiService.post<null>(API_ENDPOINTS.VERIFICATION_POLICY.DELETE, data);
    }

    /**
     * 设置策略状态
     */
    async setPolicyStatus(data: SetPolicyStatusRequest): Promise<ApiResponse<null>> {
        return await apiService.post<null>(API_ENDPOINTS.VERIFICATION_POLICY.SET_STATUS, data);
    }

    /**
     * 验证表达式
     */
    async validateExpression(data: ExprValidateRequest): Promise<ApiResponse<ExprValidateResponse>> {
        return await apiService.post<ExprValidateResponse>(API_ENDPOINTS.VERIFICATION_POLICY.VALIDATE_EXPR, data);
    }

    /**
     * 测试表达式
     */
    async testExpression(data: ExprTestRequest): Promise<ApiResponse<ExprTestResponse>> {
        return await apiService.post<ExprTestResponse>(API_ENDPOINTS.VERIFICATION_POLICY.TEST_EXPR, data);
    }

    /**
     * 批量删除策略
     * 注意：后端暂未实现此功能，需要逐个删除
     */
    async batchDeletePolicies(policyIds: number[]): Promise<ApiResponse<{ responses: any[] }>> {
        // TODO: 后端暂未实现批量删除，需要逐个删除
        const promises = policyIds.map(id =>
            apiService.post<null>(API_ENDPOINTS.VERIFICATION_POLICY.DELETE, {policy_id: id})
        );
        const responses = await Promise.all(promises);
        return {
            code: 0,
            message: '批量删除成功',
            data: { responses: responses.map(r => r.data) }
        };
    }

    /**
     * 批量设置策略状态
     * 注意：后端暂未实现此功能，需要逐个设置
     */
    async batchSetPolicyStatus(policyIds: number[], enabled: boolean): Promise<ApiResponse<{ responses: any[] }>> {
        // TODO: 后端暂未实现批量设置状态，需要逐个设置
        const promises = policyIds.map(id =>
            apiService.post<null>(API_ENDPOINTS.VERIFICATION_POLICY.SET_STATUS, {policy_id: id, enabled})
        );
        const responses = await Promise.all(promises);
        return {
            code: 0,
            message: '批量设置状态成功',
            data: { responses: responses.map(r => r.data) }
        };
    }

    /**
     * 复制策略
     * 注意：后端暂未实现此功能
     */
    async copyPolicy(policyId: number): Promise<ApiResponse<VerificationPolicy>> {
        // TODO: 后端暂未实现复制功能
        throw new Error('复制策略功能暂未实现');
    }

    /**
     * 导出策略
     * 注意：后端暂未实现此功能
     */
    async exportPolicies(params?: PolicyListParams): Promise<Blob> {
        // TODO: 后端暂未实现导出功能
        throw new Error('导出策略功能暂未实现');
    }

    /**
     * 导入策略
     * 注意：后端暂未实现此功能
     */
    async importPolicies(file: File): Promise<ApiResponse<{ imported_count: number }>> {
        // TODO: 后端暂未实现导入功能
        throw new Error('导入策略功能暂未实现');
    }
}

// 导出单例实例
export const policyService = new PolicyService();