import {API_CONFIG, apiService, request} from '../utils/request';
import {API_ENDPOINTS} from '../utils/request';
import {ApiResponse} from '../types';

// 密码策略类型
export interface PasswordPolicy {
    min_length: number;
    max_length: number;
    require_uppercase: boolean;
    require_lowercase: boolean;
    require_digits: boolean;
    require_special_chars: boolean;
    forbidden_patterns: string[];
    password_history_count: number;
    expire_days: number;
}

// 注册方式类型
export interface RegistrationMethods {
    email: {
        enabled: boolean;
        require_verification: boolean;
        manual_activation: boolean; // 手动激活
        verification_template_code?: string; // 邮箱验证模板代码
    };
    phone: {
        enabled: boolean;
        require_verification: boolean;
        manual_activation: boolean; // 手动激活
        verification_template_code?: string; // 短信验证模板代码
    };
    oauth: {
        enabled: boolean;
        manual_activation: boolean; // 手动激活
    };
    admin_creation: {
        enabled: boolean;
        require_approval: boolean;
    };
}

// 租户信息类型
export interface TenantInfo {
    type: 'enterprise' | 'personal'; // 企业或个人
    system_name: string; // 系统名称
    service_email: string; // 客服邮箱
    address: string; // 地址
    contact_person: string; // 联系人
    contact_phone: string; // 联系电话
    website: string; // 官网地址
    logo: string; // 企业/个人标志
    description: string; // 描述
    business_license: string; // 营业执照（企业）
}

// 租户配置类型
export interface TenantConfig {
    tenant_id: number;
    config_key: string;
    config_value: string;
    config_type: string;
}

// 请求类型
export interface GetPasswordPolicyRequest {
    tenant_id: number;
}

export interface UpdatePasswordPolicyRequest {
    tenant_id: number;
    policy: PasswordPolicy;
}

export interface GetRegistrationMethodsRequest {
    tenant_id: number;
}

export interface UpdateRegistrationMethodsRequest {
    tenant_id: number;
    methods: RegistrationMethods;
}

export interface GetTenantInfoRequest {
    tenant_id: number;
}

export interface UpdateTenantInfoRequest {
    tenant_id: number;
    info: TenantInfo;
}

export interface GetTenantConfigsRequest {
    tenant_id: number;
}

export interface UpdateTenantConfigRequest {
    tenant_id: number;
    config_key: string;
    config_value: string;
}

export interface CopySystemConfigsRequest {
    tenant_id: number;
}

// 响应类型
export interface PasswordPolicyResponse {
    tenant_id: number;
    policy: PasswordPolicy;
}

export interface RegistrationMethodsResponse {
    tenant_id: number;
    methods: RegistrationMethods;
}

export interface TenantInfoResponse {
    tenant_id: number;
    info: TenantInfo;
}

export interface TenantConfigListResponse {
    configs: TenantConfig[];
    total: number;
}

export class TenantConfigService {
    /**
     * 获取密码策略
     */
    static async getPasswordPolicy(tenantId: number): Promise<ApiResponse<PasswordPolicyResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/tenant-config/password-policy', {
            params: {tenant_id: tenantId}
        });
    }

    /**
     * 更新密码策略
     */
    static async updatePasswordPolicy(data: UpdatePasswordPolicyRequest): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/tenant-config/password-policy', data);
    }

    /**
     * 获取注册方式配置
     */
    static async getRegistrationMethods(tenantId: number): Promise<ApiResponse<RegistrationMethodsResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/tenant-config/registration-methods', {
            params: {tenant_id: tenantId}
        });
    }

    /**
     * 更新注册方式配置
     */
    static async updateRegistrationMethods(data: UpdateRegistrationMethodsRequest): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/tenant-config/registration-methods', data);
    }

    /**
     * 获取租户所有配置
     */
    static async getTenantConfigs(tenantId: number): Promise<ApiResponse<TenantConfigListResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/tenant-config/configs', {
            params: {tenant_id: tenantId}
        });
    }

    /**
     * 更新租户配置
     */
    static async updateTenantConfig(data: UpdateTenantConfigRequest): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/tenant-config/config', data);
    }

    /**
     * 复制系统配置到租户
     */
    static async copySystemConfigs(tenantId: number): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/tenant-config/copy-system', {
            params: {tenant_id: tenantId}
        });
    }

    /**
     * 获取租户信息配置
     */
    static async getTenantInfo(tenantId: number): Promise<ApiResponse<TenantInfoResponse>> {
        return await apiService.get(API_CONFIG.PREFIXES.USER + '/tenant-config/tenant-info', {
            params: {tenant_id: tenantId}
        });
    }

    /**
     * 更新租户信息配置
     */
    static async updateTenantInfo(data: UpdateTenantInfoRequest): Promise<ApiResponse<void>> {
        return await apiService.post(API_CONFIG.PREFIXES.USER + '/tenant-config/tenant-info', data);
    }
} 