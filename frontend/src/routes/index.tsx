import React, {Suspense} from 'react';
import {Routes, Route, Navigate} from 'react-router-dom';
import {Spin} from 'antd';
import MainLayout from '../layouts/MainLayout';
import LoginPage from '../pages/login/LoginPage';

// 懒加载组件
const Dashboard = React.lazy(() => import('../pages/dashboard/Dashboard'));
const SceneList = React.lazy(() => import('../pages/file-system/config/SceneList'));
const FileList = React.lazy(() => import('../pages/file-system/record/FileList'));
const FileDetail = React.lazy(() => import('../pages/file-system/record/FileDetail'));
const FileUploadPage = React.lazy(() => import('../pages/file-system/upload/FileUploadPage'));
const MenuTest = React.lazy(() => import('../pages/dashboard/components/MenuTest'));
const UserPage = React.lazy(() => import('../pages/user/UserPage'));
const DepartmentPage = React.lazy(() => import('../pages/department/DepartmentPage'));

// 加载中组件
const LoadingComponent = () => (
    <div style={{textAlign: 'center', padding: '50px'}}>
        <Spin size="large"/>
    </div>
);

// 路由配置
const AppRoutes: React.FC = () => {
    return (
        <Routes>
            <Route path="/login" element={<LoginPage/>}/>
            <Route path="/" element={<MainLayout/>}>
                <Route index element={<Navigate to="/dashboard" replace/>}/>
                <Route path="dashboard" element={
                    <Suspense fallback={<LoadingComponent/>}>
                        <Dashboard/>
                    </Suspense>
                }/>

                {/* 菜单测试页面 */}
                <Route path="menu-test" element={
                    <Suspense fallback={<LoadingComponent/>}>
                        <MenuTest/>
                    </Suspense>
                }/>

                {/* 用户管理 */}
                <Route path="user" element={
                    <Suspense fallback={<LoadingComponent/>}>
                        <UserPage/>
                    </Suspense>
                }/>
                <Route path="department" element={
                    <Suspense fallback={<LoadingComponent/>}>
                        <DepartmentPage/>
                    </Suspense>
                }/>

                {/* 用户系统相关路由已移除 - 验证配置迁移到 /verification/configs */}

                {/* 文件系统相关路由 */}
                <Route path="file-system">
                    <Route path="scenes" element={
                        <Suspense fallback={<LoadingComponent/>}>
                            <SceneList/>
                        </Suspense>
                    }/>
                    <Route path="files" element={
                        <Suspense fallback={<LoadingComponent/>}>
                            <FileList/>
                        </Suspense>
                    }/>
                    <Route path="files/:id" element={
                        <Suspense fallback={<LoadingComponent/>}>
                            <FileDetail/>
                        </Suspense>
                    }/>
                    <Route path="upload" element={
                        <Suspense fallback={<LoadingComponent/>}>
                            <FileUploadPage/>
                        </Suspense>
                    }/>
                </Route>
            </Route>
            <Route path="*" element={<Navigate to="/login" replace/>}/>
        </Routes>
    );
};

export default AppRoutes;