// 邮件账户相关类型定义

// 邮件账户类型枚举
export const EMAIL_ACCOUNT_TYPES = {
  SMTP: 1,
  IMAP: 2,
  POP3: 3,
  Exchange: 4,
  API: 5,
} as const;

// 邮件提供商枚举
export const EMAIL_PROVIDERS = {
  GMAIL: 'Gmail',
  QQ: 'QQ',
  ALIYUN: 'Aliyun',
  EXCHANGE: 'Exchange',
  CUSTOM: 'Custom',
} as const;

// 邮件账户测试状态枚举
export const EMAIL_ACCOUNT_TEST_STATUS = {
  UNTESTED: 0,
  SUCCESS: 1,
  FAILED: 2,
} as const;

// 阿里云地域选项
export const ALIYUN_REGIONS = [
  { value: 'cn-hangzhou', label: '华东1（杭州）' },
  { value: 'cn-shanghai', label: '华东2（上海）' },
  { value: 'cn-qingdao', label: '华北1（青岛）' },
  { value: 'cn-beijing', label: '华北2（北京）' },
  { value: 'cn-zhang<PERSON><PERSON><PERSON>', label: '华北3（张家口）' },
  { value: 'cn-huhehaote', label: '华北5（呼和浩特）' },
  { value: 'cn-wulanchabu', label: '华北6（乌兰察布）' },
  { value: 'cn-shenzhen', label: '华南1（深圳）' },
  { value: 'cn-heyuan', label: '华南2（河源）' },
  { value: 'cn-guangzhou', label: '华南3（广州）' },
  { value: 'cn-chengdu', label: '西南1（成都）' },
  { value: 'cn-hongkong', label: '中国香港' },
] as const;

// 邮件账户类型
export interface EmailAccountType {
  value: number;
  label: string;
  description: string;
}

// 邮件账户基础信息
export interface EmailAccount {
  id: number;
  tenant_id: string;
  name: string;
  type: number; // 1: SMTP, 2: IMAP, 3: POP3, 4: API
  provider: string; // 'Gmail', 'QQ', 'Aliyun', etc.
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  from_address: string;
  from_name?: string;
  reply_to_address?: string;
  is_ssl: boolean;
  is_active: boolean;
  daily_limit: number;
  monthly_limit: number;
  sent_today: number;
  sent_this_month: number;
  last_sent_at?: string;
  test_status: number;
  test_message?: string;
  config?: {
    // 阿里云配置
    access_key_id?: string;
    access_key_secret?: string;
    region?: string;
    domain?: string;
    // 其他配置
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  version: number;
}

// 创建邮件账户请求
export interface CreateEmailAccountRequest {
  name: string;
  type: number;
  provider: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  from_address: string;
  from_name?: string;
  reply_to_address?: string;
  is_ssl: boolean;
  is_active: boolean;
  daily_limit: number;
  monthly_limit: number;
  config?: Record<string, any>;
}

// 更新邮件账户请求
export interface UpdateEmailAccountRequest {
  id: number;
  name?: string;
  type?: number;
  provider?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  from_address?: string;
  from_name?: string;
  reply_to_address?: string;
  is_ssl?: boolean;
  is_active?: boolean;
  daily_limit?: number;
  monthly_limit?: number;
  config?: Record<string, any>;
}

// 列表查询参数
export interface ListEmailAccountsRequest {
  page: number;
  page_size: number;
  keyword?: string;
  type?: number;
  is_active?: boolean;
}

// 列表响应
export interface ListEmailAccountsResponse {
  accounts: EmailAccount[];
  page: number;
  page_size: number;
  total: number;
}

// 测试邮件账户请求
export interface TestEmailAccountRequest {
  id: number;
  test_email: string;
}

// 测试邮件账户响应
export interface TestEmailAccountResponse {
  success: boolean;
  message: string;
}

// Email Template Types
export interface EmailTemplate {
  id: number;
  tenant_id: number;
  template_code: string; // API返回的是 template_code
  account_id: number;
  name: string; // API返回的是 name，不是 template_name
  description?: string; // 模板描述
  type: number; // API返回的是数字类型：1=html, 其他=text
  subject: string;
  html_content: string; // API返回的是 html_content，不是 body
  plain_text_content: string; // API返回的纯文本内容
  variables: Record<string, {
    type: string;
    required: boolean;
    description: string;
  }>;
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  is_responsive: boolean;
  status: number; // 模板状态：1=草稿，2=已发布，3=已停用，4=已删除
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  version: number;
  is_system: boolean;
}

export interface CreateTemplateRequest {
  name: string; // API使用的是name字段，不是template_name
  template_code: string; // API使用的是template_code字段，不是scenario_code
  description?: string; // 模板描述
  type: number; // API使用的是数字类型：1=html, 其他=text
  subject: string;
  html_content: string; // API使用的是html_content字段，不是body
  plain_text_content?: string; // API的纯文本内容
  variables?: Record<string, {
    label: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  status?: number; // 模板状态：1=草稿，2=已发布，3=已停用，4=已删除
  account_id: number; // 发件账户ID - 改为必填字段
}

export interface UpdateTemplateRequest {
  id: number; // 改为number类型
  name?: string; // API使用的是name字段
  template_code?: string; // API使用的是template_code字段
  description?: string; // 模板描述
  type?: number; // API使用的是数字类型
  subject?: string;
  html_content?: string; // API使用的是html_content字段
  plain_text_content?: string; // API返回的纯文本内容
  variables?: Record<string, {
    label: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  status?: number; // 模板状态：1=草稿，2=已发布，3=已停用，4=已删除
  account_id?: number; // 发件账户ID
  update_mode?: string; // 更新模式：'full'(完整更新), 'variables_only'(仅更新变量)
}

export interface ListTemplatesRequest {
  scenario_code?: string;
  template_type?: string;
  status?: number; // 模板状态：1=草稿，2=已发布，3=已停用，4=已删除
  keyword?: string;
  page?: number;
  page_size?: number;
}

export interface ListTemplatesResponse {
  templates: EmailTemplate[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export interface TemplateSelectRequest {
  scenario_code: string;
}

export interface TemplateSelectResponse {
  template?: EmailTemplate;
  message: string;
  source: 'tenant' | 'system';
}

export interface TemplateCloneRequest {
  scenario_code: string;
  template_name: string;
}

export interface TemplatePreviewRequest {
  template_id: number; // 改为number类型
  variables: Record<string, any>;
}

export interface TemplatePreviewResponse {
  subject: string;
  body: string;
  html: string;
}

export interface TemplateValidationRequest {
  subject: string;
  html_content: string; // 使用html_content字段
  variables: Record<string, any>;
}

export interface TemplateValidationResponse {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface TemplateComparison {
  tenant_template?: EmailTemplate;
  system_template?: EmailTemplate;
  differences: {
    subject?: boolean;
    html_content?: boolean; // 使用html_content字段
    variables?: boolean;
  };
}

// Template Constants
export const TEMPLATE_TYPES = {
  HTML: 'html',
  TEXT: 'text',
  MIXED: 'mixed',
} as const;

export const SCENARIO_CODES = {
  USER_REGISTER: 'user_register',
  USER_LOGIN: 'user_login',
  PASSWORD_RESET: 'password_reset',
  EMAIL_VERIFICATION: 'email_verification',
  ACCOUNT_ACTIVATION: 'account_activation',
  PROMOTION: 'promotion',
  NOTIFICATION: 'notification',
} as const;

// 模板变量相关类型
export interface TemplateVariableInfo {
  name: string;
  label: string;
  type: string;
  description: string;
  required: boolean;
  default_value: string;
  category: 'system' | 'template' | 'custom';
}

export interface GetTemplateVariablesRequest {
  template_id: number;
}

export type GetTemplateVariablesResponse = TemplateVariableInfo[]; 