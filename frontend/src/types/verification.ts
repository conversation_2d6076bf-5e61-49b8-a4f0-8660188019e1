/**
 * 验证策略相关类型定义
 */

import { PaginationParams } from './api';

/**
 * 条件表达式 - 单个条件
 */
export interface Condition {
  id: string;
  dimension: string;
  key: string;
  operator: string;
  value: any;
  validation?: {
    isValid: boolean;
    errors: string[];
  };
}

/**
 * 条件表达式 - 条件组合
 */
export interface ConditionExpression {
  logic: 'AND' | 'OR' | 'NOT';
  conditions: Array<Condition | ConditionExpression>;
}

/**
 * 维度键配置
 */
export interface DimensionKey {
  value: string;
  label: string;
  type: 'number' | 'boolean' | 'string' | 'date' | 'array';
  operators: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
  };
}

/**
 * 维度配置
 */
export interface DimensionSchema {
  value: string;
  label: string;
  keys: DimensionKey[];
}

/**
 * 验证错误类型
 */
export enum ValidationErrorType {
  REQUIRED_FIELD = 'required_field',
  INVALID_VALUE = 'invalid_value',
  INVALID_EXPRESSION = 'invalid_expression',
  CIRCULAR_REFERENCE = 'circular_reference',
  TOO_COMPLEX = 'too_complex'
}

/**
 * 验证错误
 */
export interface ValidationError {
  type: ValidationErrorType;
  field?: string;
  message: string;
  path?: string;
}

/**
 * 表单验证状态
 */
export interface FormValidationState {
  isValid: boolean;
  errors: Record<string, string[]>;
  conditionErrors: string[];
}

/**
 * 条件模板
 */
export interface ConditionTemplate {
  id: string;
  name: string;
  description: string;
  expression: ConditionExpression;
  category: string;
}

/**
 * 策略表单数据
 */
export interface PolicyFormData extends Omit<CreatePolicyRequest, 'condition_expr'> {
  condition_expression?: ConditionExpression;
  condition_expr?: string; // Generated from condition_expression
}

/**
 * 验证策略
 */
export interface VerificationPolicy {
  id: number;
  tenant_id: number;
  scene: string;
  dimension: string;
  condition_expr: string;
  need_verification: boolean;
  verification_level: string;
  target_type: number;
  token_type: number;
  token_length: number;
  expire_minutes: number;
  max_attempts: number;
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  template_code: string;
  is_active: boolean;
  priority: number;
  description: string;
  created_at: string;
  updated_at: string;
}

/**
 * 创建策略请求
 */
export interface CreatePolicyRequest {
  scene: string;
  dimension: string;
  condition_expr?: string;
  need_verification: boolean;
  verification_level: string;
  target_type: number;
  token_type: number;
  token_length: number;
  expire_minutes: number;
  max_attempts: number;
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  template_code?: string;
  priority: number;
  description?: string;
}

/**
 * 更新策略请求
 */
export interface UpdatePolicyRequest {
  policy_id: number;
  scene: string;
  dimension: string;
  condition_expr?: string;
  need_verification: boolean;
  verification_level: string;
  target_type: number;
  token_type: number;
  token_length: number;
  expire_minutes: number;
  max_attempts: number;
  rate_limit_per_minute: number;
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  template_code?: string;
  priority: number;
  description?: string;
}

/**
 * 删除策略请求
 */
export interface DeletePolicyRequest {
  policy_id: number;
}

/**
 * 设置策略状态请求
 */
export interface SetPolicyStatusRequest {
  policy_id: number;
  enabled: boolean;
}

/**
 * 策略列表查询参数
 */
export interface PolicyListParams extends PaginationParams {
  scene?: string;
  is_active?: boolean;
  keyword?: string;
}

/**
 * 策略列表响应
 */
export interface PolicyListResponse {
  total: number;
  page: number;
  page_size: number;
  list: VerificationPolicy[];
}

/**
 * 策略详情请求
 */
export interface PolicyDetailRequest {
  policy_id: number;
}

/**
 * 表达式验证请求
 */
export interface ExprValidateRequest {
  condition_expr: string;
}

/**
 * 表达式验证响应
 */
export interface ExprValidateResponse {
  valid: boolean;
  error?: string;
}

/**
 * 表达式测试请求
 */
export interface ExprTestRequest {
  condition_expr: string;
  sample: Record<string, any>;
}

/**
 * 表达式测试响应
 */
export interface ExprTestResponse {
  result: boolean;
}

/**
 * 验证级别选项
 */
export const VERIFICATION_LEVELS = [
  { value: 'low', label: '低' },
  { value: 'medium', label: '中' },
  { value: 'high', label: '高' },
  { value: 'strict', label: '严格' }
] as const;

/**
 * 目标类型选项
 */
export const TARGET_TYPES = [
  { value: 1, label: '手机号' },
  { value: 2, label: '邮箱' }
] as const;

/**
 * 令牌类型选项
 */
export const TOKEN_TYPES = [
  { value: 1, label: '验证码' },
  { value: 2, label: '链接' }
] as const;

/**
 * 业务场景选项
 */
export const BUSINESS_SCENES = [
  { value: 'login', label: '登录' },
  { value: 'register', label: '注册' },
  { value: 'reset_password', label: '重置密码' },
  { value: 'change_email', label: '修改邮箱' },
  { value: 'change_phone', label: '修改手机号' },
  { value: 'bind_account', label: '绑定账户' },
  { value: 'unbind_account', label: '解绑账户' },
  { value: 'sensitive_operation', label: '敏感操作' }
] as const;

/**
 * 维度选项
 */
export const DIMENSIONS = [
  { value: 'IP', label: 'IP地址' },
  { value: 'USER_ID', label: '用户ID' },
  { value: 'DEVICE', label: '设备' },
  { value: 'LOCATION', label: '地理位置' },
  { value: 'TIME', label: '时间' },
  { value: 'FREQUENCY', label: '频率' }
] as const;

/**
 * 操作符选项
 */
export const OPERATORS = [
  { value: '>', label: '大于' },
  { value: '<', label: '小于' },
  { value: '>=', label: '大于等于' },
  { value: '<=', label: '小于等于' },
  { value: '==', label: '等于' },
  { value: '!=', label: '不等于' },
  { value: 'between', label: '区间' },
  { value: 'contains', label: '包含' },
  { value: 'startsWith', label: '开始于' },
  { value: 'endsWith', label: '结束于' },
  { value: 'in', label: '在列表中' },
  { value: 'notIn', label: '不在列表中' }
] as const;

/**
 * 维度配置架构
 */
export const DIMENSION_SCHEMAS: DimensionSchema[] = [
  {
    value: 'ip',
    label: 'IP地址',
    keys: [
      {
        value: 'fail_count',
        label: '失败次数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 1000 }
      },
      {
        value: 'is_black_ip',
        label: '黑名单IP',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'address',
        label: 'IP地址',
        type: 'string',
        operators: ['==', '!=', 'contains', 'startsWith', 'endsWith'],
        validation: { pattern: '^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$' }
      },
      {
        value: 'country',
        label: '国家',
        type: 'string',
        operators: ['==', '!=', 'in', 'notIn']
      }
    ]
  },
  {
    value: 'user',
    label: '用户',
    keys: [
      {
        value: 'fail_count',
        label: '失败次数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 100 }
      },
      {
        value: 'is_new_user',
        label: '新用户',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'registration_days',
        label: '注册天数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 36500 }
      },
      {
        value: 'is_verified',
        label: '已验证',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'user_level',
        label: '用户等级',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 1, max: 10 }
      }
    ]
  },
  {
    value: 'device',
    label: '设备',
    keys: [
      {
        value: 'is_new_device',
        label: '新设备',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'is_trusted',
        label: '可信设备',
        type: 'boolean',
        operators: ['==', '!=']
      },
      {
        value: 'device_type',
        label: '设备类型',
        type: 'string',
        operators: ['==', '!=', 'in', 'notIn']
      },
      {
        value: 'os_version',
        label: '操作系统版本',
        type: 'string',
        operators: ['==', '!=', 'contains']
      }
    ]
  },
  {
    value: 'time',
    label: '时间',
    keys: [
      {
        value: 'within_minutes',
        label: '时间范围(分钟)',
        type: 'number',
        operators: ['<=', '<'],
        validation: { min: 1, max: 1440 }
      },
      {
        value: 'hour_of_day',
        label: '小时',
        type: 'number',
        operators: ['>=', '<=', 'between'],
        validation: { min: 0, max: 23 }
      },
      {
        value: 'day_of_week',
        label: '星期',
        type: 'number',
        operators: ['==', '!=', 'in', 'notIn'],
        validation: { min: 1, max: 7 }
      },
      {
        value: 'is_weekend',
        label: '周末',
        type: 'boolean',
        operators: ['==', '!=']
      }
    ]
  },
  {
    value: 'location',
    label: '地理位置',
    keys: [
      {
        value: 'country',
        label: '国家',
        type: 'string',
        operators: ['==', '!=', 'in', 'notIn']
      },
      {
        value: 'city',
        label: '城市',
        type: 'string',
        operators: ['==', '!=', 'in', 'notIn']
      },
      {
        value: 'is_domestic',
        label: '国内',
        type: 'boolean',
        operators: ['==', '!=']
      }
    ]
  },
  {
    value: 'frequency',
    label: '频率',
    keys: [
      {
        value: 'requests_per_minute',
        label: '每分钟请求数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 1000 }
      },
      {
        value: 'requests_per_hour',
        label: '每小时请求数',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 10000 }
      },
      {
        value: 'unique_ips_count',
        label: '唯一IP数量',
        type: 'number',
        operators: ['>', '<', '>=', '<=', '==', '!=', 'between'],
        validation: { min: 0, max: 1000 }
      }
    ]
  }
];

/**
 * 预定义条件模板
 */
export const PREDEFINED_TEMPLATES: ConditionTemplate[] = [
  {
    id: 'brute_force_protection',
    name: '暴力破解防护',
    description: 'IP失败次数超过5次或在黑名单中',
    category: 'security',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'ip',
          key: 'fail_count',
          operator: '>',
          value: 5
        },
        {
          id: '2',
          dimension: 'ip',
          key: 'is_black_ip',
          operator: '==',
          value: true
        }
      ]
    }
  },
  {
    id: 'new_device_login',
    name: '新设备登录',
    description: '新设备或不可信设备登录',
    category: 'device',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'device',
          key: 'is_new_device',
          operator: '==',
          value: true
        },
        {
          id: '2',
          dimension: 'device',
          key: 'is_trusted',
          operator: '==',
          value: false
        }
      ]
    }
  },
  {
    id: 'suspicious_location',
    name: '可疑地理位置',
    description: '海外登录或非常用城市',
    category: 'location',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'location',
          key: 'is_domestic',
          operator: '==',
          value: false
        },
        {
          id: '2',
          dimension: 'location',
          key: 'city',
          operator: 'notIn',
          value: ['北京', '上海', '广州', '深圳']
        }
      ]
    }
  },
  {
    id: 'high_frequency_access',
    name: '高频访问',
    description: '短时间内大量请求',
    category: 'frequency',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'frequency',
          key: 'requests_per_minute',
          operator: '>',
          value: 10
        },
        {
          id: '2',
          dimension: 'frequency',
          key: 'requests_per_hour',
          operator: '>',
          value: 100
        }
      ]
    }
  },
  {
    id: 'new_user_protection',
    name: '新用户保护',
    description: '新注册用户或未验证用户',
    category: 'user',
    expression: {
      logic: 'OR',
      conditions: [
        {
          id: '1',
          dimension: 'user',
          key: 'is_new_user',
          operator: '==',
          value: true
        },
        {
          id: '2',
          dimension: 'user',
          key: 'is_verified',
          operator: '==',
          value: false
        }
      ]
    }
  }
];