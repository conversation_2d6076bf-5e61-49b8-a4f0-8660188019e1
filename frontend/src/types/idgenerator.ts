// ID生成器相关类型定义

// 序列状态枚举
export enum SequenceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PAUSED = 'paused',
  DELETED = 'deleted'
}

// 序列基本信息
export interface Sequence {
  id: number;
  businessType: string;          // 业务类型
  sequenceName: string;          // 序列名称（使用服务端标准字段名）
  currentValue: number;          // 当前值
  incrementStep: number;         // 步长（使用服务端标准字段名）
  cacheSize: number;            // 缓存大小
  maxValue?: number | string;    // 最大值，支持"不限制"字符串
  isActive: boolean;            // 是否激活（使用服务端标准字段名）
  threshold: number;            // 预分配阈值百分比
  tags?: string;                // 标签
  remarks?: string;             // 备注
  createdAt: string;            // 创建时间
  updatedAt: string;            // 更新时间
  createdBy?: string;           // 创建人
}

// 序列统计信息
export interface SequenceStats {
  todayGenerated: number;       // 今日生成数量
  totalGenerated: number;       // 总生成数量
  averageQps: number;          // 平均QPS
  peakQps: number;             // 峰值QPS
  lastAllocatedAt?: string;    // 上次预分配时间
  currentAllocation?: AllocationInfo; // 当前分配段信息
}

// ID分配段信息
export interface AllocationInfo {
  id: number;
  sequenceId: number;
  startValue: number;           // 开始值
  endValue: number;            // 结束值
  currentValue: number;        // 当前使用到的值
  used: number;                // 已使用数量
  remaining: number;           // 剩余数量
  usageRate: number;           // 使用率百分比
  allocatedAt: string;         // 分配时间
  expiredAt?: string;          // 过期时间
}

// 序列详情（包含统计信息）
export interface SequenceDetail extends Sequence {
  stats: SequenceStats;
  allocations: AllocationInfo[]; // 历史分配记录
}

// 创建序列请求
export interface CreateSequenceRequest {
  businessType: string;
  sequenceName: string;         // 序列名称（使用服务端标准字段名）
  initialValue?: number;        // 初始值，默认1
  incrementStep?: number;       // 步长（使用服务端标准字段名）
  cacheSize?: number;          // 缓存大小，默认1000
  maxValue?: number;           // 最大值，0表示不限制
  threshold?: number;          // 预分配阈值，默认20%
  tags?: string;               // 标签
  remarks?: string;            // 备注
}

// 更新序列请求
export interface UpdateSequenceRequest {
  sequenceName?: string;        // 序列名称（使用服务端标准字段名）
  incrementStep?: number;       // 步长（使用服务端标准字段名）
  cacheSize?: number;
  maxValue?: number;           // 最大值，0表示不限制
  threshold?: number;
  isActive?: boolean;           // 是否激活（使用服务端标准字段名）
  tags?: string;
  remarks?: string;
}

// 序列查询参数
export interface SequenceQueryParams {
  page?: number;
  size?: number;
  keyword?: string;           // 搜索关键词
  status?: string;            // 状态筛选（匹配后端接口）
  businessType?: string;      // 业务类型筛选
  sortBy?: 'businessType' | 'currentValue' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

// 序列列表响应
export interface SequenceListResponse {
  sequences: Sequence[];
  total: number;
  page: number;
  size: number;
  totalPages: number;
}

// 预分配请求
export interface AllocateRequest {
  sequenceId: number;
  size?: number;              // 分配大小，不指定则使用默认缓存大小
  reason?: string;            // 分配原因
}

// 业务类型申请请求
export interface BusinessTypeRequest {
  businessType: string;       // 业务类型名称
  sequenceName: string;       // 序列名称（使用服务端标准字段名）
  expectedQps?: number;       // 预期QPS
  department?: string;        // 申请部门
  contact?: string;           // 联系人
  reason: string;             // 申请原因
}

// 业务类型申请状态
export enum BusinessTypeStatus {
  PENDING = 'pending',        // 待审核
  APPROVED = 'approved',      // 已批准
  REJECTED = 'rejected',      // 已拒绝
  IN_USE = 'in_use'          // 使用中
}

// 业务类型申请记录
export interface BusinessTypeApplication {
  id: number;
  businessType: string;
  sequenceName: string;       // 序列名称（使用服务端标准字段名）
  expectedQps?: number;
  department?: string;
  contact?: string;
  reason: string;
  status: BusinessTypeStatus;
  applicant: string;          // 申请人
  approver?: string;          // 审批人
  appliedAt: string;          // 申请时间
  approvedAt?: string;        // 审批时间
  rejectReason?: string;      // 拒绝原因
}

// 系统统计
export interface SystemStats {
  totalSequences: number;     // 总序列数
  activeSequences: number;    // 活跃序列数
  todayGenerated: number;     // 今日生成ID总数
  totalGenerated: number;     // 历史生成ID总数
  averageQps: number;         // 系统平均QPS
  peakQps: number;           // 系统峰值QPS
  lowCapacitySequences: number; // 低容量序列数
  systemUptime: string;       // 系统运行时间
}

// ID生成请求
export interface GenerateIdRequest {
  businessType: string;
}

// ID生成响应
export interface GenerateIdResponse {
  id: number;
  businessType: string;
  generatedAt: string;
}

// 批量ID生成请求
export interface GenerateBatchIdsRequest {
  businessType: string;
  count: number;              // 生成数量
}

// 批量ID生成响应
export interface GenerateBatchIdsResponse {
  ids: number[];
  businessType: string;
  count: number;
  generatedAt: string;
} 