---
alwaysApply: false
---

# GORM 数据库层代码规范（摘要）

## 目录结构与分层
- 仅在 infrastructure/persistence/ 层出现 GORM 代码，domain 层只依赖接口。
- domain/repositories/ 定义仓储接口，domain/entities/ 定义领域实体，application/services/ 只依赖仓储接口。
- pkg/db/ 负责数据库连接、事务、分库分表等通用能力。

## 依赖倒置与接口驱动
- 领域层依赖接口，基础设施层实现接口，便于测试和 ORM 切换。

## 连接与性能
- 连接池参数必须合理配置，连接统一由 pkg/db 管理。
- 支持读写分离、分库分表。

## 事务管理
- 事务由应用服务层协调，repository 层禁止直接提交/回滚。
- 事务上下文通过 context.Context 传递，支持链路追踪。
- 推荐事务装饰器/中间件简化管理。

## 查询与性能
- 禁止 N+1 查询，优先批量加载。
- 查询需有索引，避免全表扫描。
- 大表分页用游标分页，禁止 SELECT *。
- 支持 SQL 审计与慢查询日志。

## 错误处理与幂等
- 所有 DB 错误需包装，统一转换为业务错误码。
- 支持幂等，防止重复写入。

## 并发与锁
- 支持乐观锁/悲观锁，避免热点行。

## 安全与防护
- 所有输入参数校验，禁止拼接 SQL，优先参数化查询。
- 支持限流、熔断、重试。

## 可观测性
- 集成 OpenTelemetry，自动注入 trace/span。
- 记录 SQL、耗时、影响行数、错误等。

## 代码风格与规范
- 结构体字段用驼峰命名，gorm tag 明确指定 db 字段名。
- repository 层禁止业务逻辑，单元测试需覆盖所有方法。
- 代码注释齐全，关键 SQL/业务流程需说明。

## 规范清单
- 依赖倒置、接口驱动
- 连接池与性能优化
- 事务与幂等
- 查询优化与索引
- 错误处理与日志
- 并发安全
- 安全防护
- 可观测性
- 单元测试
- 代码注释与文档
# GORM 数据库层代码规范（摘要）

## 目录结构与分层
- 仅在 infrastructure/persistence/ 层出现 GORM 代码，domain 层只依赖接口。
- domain/repositories/ 定义仓储接口，domain/entities/ 定义领域实体，application/services/ 只依赖仓储接口。
- pkg/db/ 负责数据库连接、事务、分库分表等通用能力。

## 依赖倒置与接口驱动
- 领域层依赖接口，基础设施层实现接口，便于测试和 ORM 切换。

## 连接与性能
- 连接池参数必须合理配置，连接统一由 pkg/db 管理。
- 支持读写分离、分库分表。

## 事务管理
- 事务由应用服务层协调，repository 层禁止直接提交/回滚。
- 事务上下文通过 context.Context 传递，支持链路追踪。
- 推荐事务装饰器/中间件简化管理。

## 查询与性能
- 禁止 N+1 查询，优先批量加载。
- 查询需有索引，避免全表扫描。
- 大表分页用游标分页，禁止 SELECT *。
- 支持 SQL 审计与慢查询日志。

## 错误处理与幂等
- 所有 DB 错误需包装，统一转换为业务错误码。
- 支持幂等，防止重复写入。

## 并发与锁
- 支持乐观锁/悲观锁，避免热点行。

## 安全与防护
- 所有输入参数校验，禁止拼接 SQL，优先参数化查询。
- 支持限流、熔断、重试。

## 可观测性
- 集成 OpenTelemetry，自动注入 trace/span。
- 记录 SQL、耗时、影响行数、错误等。

## 代码风格与规范
- 结构体字段用驼峰命名，gorm tag 明确指定 db 字段名。
- repository 层禁止业务逻辑，单元测试需覆盖所有方法。
- 代码注释齐全，关键 SQL/业务流程需说明。

## 规范清单
- 依赖倒置、接口驱动
- 连接池与性能优化
- 事务与幂等
- 查询优化与索引
- 错误处理与日志
- 并发安全
- 安全防护
- 可观测性
- 单元测试
- 代码注释与文档
