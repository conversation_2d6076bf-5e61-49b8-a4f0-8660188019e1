# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Frontend Development
- **Start development server**: `cd frontend && npm start`
- **Build production**: `cd frontend && npm run build`
- **Run tests**: `cd frontend && npm test`

### Backend Services (Go)

#### Users Service
- **Start service**: `cd users && ./start.sh` (or `go run cmd/main.go`)
- **Build binary**: `cd users && go build -o bin/platforms-user cmd/main.go`
- **Run tests**: `cd users && go test ./...`
- **Generate protobuf**: `cd users && ./scripts/generate-proto.sh`

#### Email Service
- **Start service**: `cd email && go run cmd/main.go`
- **Build binary**: `cd email && go build -o bin/email-service cmd/main.go`
- **Run tests**: `cd email && go test ./...`

#### Prompts Service
- **Start service**: `cd prompts/backend && go run cmd/main.go`
- **Build binary**: `cd prompts/backend && go build -o bin/prompts-backend cmd/main.go`
- **Test API**: `cd prompts/backend && ./test_api.sh`

#### iLike Backend
- **Start service**: `cd ilike/backend-go && ./start.sh`
- **Build binary**: `cd ilike/backend-go && go build -o bin/ilike-backend cmd/main.go`
- **Test APIs**: `cd ilike/backend-go && ./test_apis.sh`

### Package Management
- **Install dependencies**: `cd <service> && go mod tidy`
- **Update dependencies**: `cd <service> && go get -u ./...`

## Architecture Overview

This is a microservices-based platform with the following key components:

### Core Services
1. **Users Service** (`users/`): Central authentication, authorization, user management, and RBAC
2. **Email Service** (`email/`): Email marketing, templates, campaigns, and delivery
3. **Prompts Service** (`prompts/`): Prompt management and sharing platform
4. **iLike Service** (`ilike/`): Social bookmarking and content management

### Shared Infrastructure
- **pkg/**: Common Go libraries and utilities shared across services
  - `usercontext/`: User context and authentication management
  - `common/errors/`: Standardized error handling and response formats
  - `httpmiddleware/`: HTTP middleware for authentication and logging
  - `grpcmiddleware/`: gRPC middleware for cross-service communication
  - `logiface/`: Unified logging interface
  - `grpcregistry/`: Service discovery and registration
  - `otel/`: OpenTelemetry integration

### Frontend
- **frontend/**: React/TypeScript admin dashboard with Ant Design
- **ilike/app/**: Flutter mobile application

### Key Architectural Patterns
- **Domain-Driven Design (DDD)**: All Go services follow DDD layering:
  - `domain/`: Business logic and entities
  - `application/`: Application services and use cases
  - `infrastructure/`: External dependencies and data access
  - `interfaces/`: HTTP/gRPC handlers and external interfaces

- **Microservices**: Services communicate via gRPC with HTTP REST APIs for frontend
- **Multi-tenant**: All services support tenant isolation via `tenant_id`
- **Event-driven**: Services use event sourcing for some operations

## Development Standards

### Code Organization
- All Go services use replace directives for `platforms-pkg` dependency
- Services follow unified error handling via `pkg/common/errors`
- User authentication managed through `pkg/usercontext`
- Structured logging with OpenTelemetry tracing

### API Design
- HTTP APIs use only GET (query) and POST (operations) methods
- All parameters passed via query strings or request body (no path parameters)
- Standardized response format: `{ code, message, data, errors, meta }`
- Error codes follow platform-wide numbering scheme

### Database Design
- All tables include `tenant_id` for multi-tenancy
- Standardized field naming (snake_case)
- Audit fields: `created_at`, `updated_at`, `created_by`, `updated_by`

### Security
- JWT-based authentication via Users service
- Role-based access control (RBAC)
- Tenant isolation enforced at database level
- All services validate user context from shared middleware

## Configuration

### Environment Setup
- Services use Nacos for configuration management
- Environment-specific configs in `configs/` directories
- Replace directives in go.mod point to shared `pkg/` module

### Database
- MySQL used across all services
- Connection pooling and transaction management via GORM
- Database migrations in `migrations/` directories

### Service Discovery
- gRPC services register with Nacos
- HTTP services expose health check endpoints
- Cross-service communication via gRPC clients

## Testing

### Unit Testing
- Go services: `go test ./...`
- Frontend: `npm test`

### Integration Testing
- API test scripts available in service directories
- Example: `prompts/backend/test_api.sh`

### Load Testing
- Test scripts in `ilike/backend-go/test_apis.sh`

## Deployment

### Local Development
- Use service-specific start scripts (e.g., `users/start.sh`)
- Configure local database connections
- Set up Nacos configuration server

### Production
- Services containerized with Docker
- Kubernetes deployment configurations
- External service dependencies (MySQL, Redis, Nacos)

## Key Files to Reference

### Standards and Guidelines
- `code-standards/`: Complete development standards
- `README.md`: Project overview and integration guides
- Service-specific README files in each service directory

### Configuration Examples
- `users/configs/`: Service configuration templates
- `email/configs/`: Email service configuration
- `frontend/src/config/`: Frontend API configuration

### Shared Libraries
- `pkg/usercontext/`: User authentication and context
- `pkg/common/errors/`: Error handling patterns
- `pkg/httpmiddleware/`: HTTP middleware implementations