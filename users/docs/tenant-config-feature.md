# 租户配置功能说明

## 功能概述

租户配置功能允许每个租户自定义其系统行为，包括密码策略和注册方式配置。该功能基于现有的 `system_config` 表实现，支持租户级别的配置覆盖系统默认配置。

## 架构设计

### 配置层级
1. **系统默认配置** (`tenant_id = 0`) - 全局默认值
2. **租户自定义配置** (`tenant_id > 0`) - 租户特定配置，覆盖系统默认值

### 配置类型
1. **密码策略配置** (`password_policy`)
   - 密码长度限制
   - 复杂度要求
   - 历史记录管理
   - 过期策略

2. **注册方式配置** (`registration_methods`)
   - 邮箱注册设置
   - 手机号注册设置
   - 三方登录设置
   - 管理员创建用户设置

## 密码策略在注册中的应用

### 验证流程
在用户注册时，系统会自动应用租户的密码策略进行验证：

1. **获取密码策略** - 从租户配置中获取密码策略，如果没有则使用系统默认策略
2. **密码验证** - 根据策略验证密码的复杂度要求
3. **错误处理** - 如果密码不符合策略，返回详细的错误信息

### 验证规则
密码策略验证包括以下规则：

- **长度验证** - 检查密码长度是否在最小和最大长度范围内
- **大写字母** - 如果启用，检查是否包含大写字母
- **小写字母** - 如果启用，检查是否包含小写字母
- **数字** - 如果启用，检查是否包含数字
- **特殊字符** - 如果启用，检查是否包含特殊字符
- **禁止模式** - 检查是否包含禁止的密码模式（如 "123456", "password" 等）

### 错误消息示例
```json
{
  "code": 400,
  "message": "密码不符合策略要求",
  "details": "密码长度不能少于8位"
}
```

### 测试密码策略
可以使用提供的测试脚本验证密码策略功能：
```bash
chmod +x test/password_policy_validation_test.sh
./test/password_policy_validation_test.sh
```

## API 接口

### 密码策略管理

#### 获取密码策略
```http
GET /api/user/tenant-config/password-policy?tenant_id={tenant_id}
```

#### 更新密码策略
```http
POST /api/user/tenant-config/password-policy
Content-Type: application/json

{
  "tenant_id": 1,
  "policy": {
    "min_length": 8,
    "max_length": 32,
    "require_uppercase": true,
    "require_lowercase": true,
    "require_digits": true,
    "require_special_chars": false,
    "forbidden_patterns": ["123456", "password", "admin"],
    "password_history_count": 5,
    "expire_days": 90
  }
}
```

### 注册方式管理

#### 获取注册方式配置
```http
GET /api/user/tenant-config/registration-methods?tenant_id={tenant_id}
```

#### 更新注册方式配置
```http
POST /api/user/tenant-config/registration-methods
Content-Type: application/json

{
  "tenant_id": 1,
  "methods": {
    "email": {
      "enabled": true,
      "require_verification": true,
      "auto_activate": false
    },
    "phone": {
      "enabled": true,
      "require_verification": true,
      "auto_activate": false
    },
    "oauth": {
      "enabled": true,
      "auto_activate": true
    },
    "admin_creation": {
      "enabled": true,
      "require_approval": false
    }
  }
}
```

### 通用配置管理

#### 获取租户所有配置
```http
GET /api/user/tenant-config/configs?tenant_id={tenant_id}
```

#### 更新租户配置
```http
POST /api/user/tenant-config/config
Content-Type: application/json

{
  "tenant_id": 1,
  "config_key": "custom_config",
  "config_value": "{\"key\": \"value\"}"
}
```

#### 复制系统配置到租户
```http
POST /api/user/tenant-config/copy-system?tenant_id={tenant_id}
```

## 前端使用

### 1. 租户管理页面
在租户列表的操作栏中，每个租户都有一个"配置"按钮，点击后跳转到租户配置页面。

### 2. 租户配置页面
配置页面包含两个主要标签页：
- **密码策略** - 配置密码复杂度、长度、历史记录等
- **注册方式** - 配置各种注册方式的开关和验证要求

### 3. 注册页面集成
注册页面应该：
- 实时显示密码策略要求
- 提供密码强度指示器
- 在提交前验证密码是否符合策略
- 显示详细的错误信息

### 4. 功能特性
- 实时保存配置
- 支持重置为默认值
- 支持复制系统配置
- 配置验证和错误提示
- 注册时的密码策略验证

## 数据库结构

### system_config 表
```sql
CREATE TABLE system_config (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  tenant_id BIGINT NOT NULL DEFAULT 0,
  config_key VARCHAR(100) NOT NULL,
  config_value TEXT,
  config_type VARCHAR(20) DEFAULT 'json',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_tenant_config (tenant_id, config_key)
);
```

### 配置键说明
- `password_policy` - 密码策略配置
- `registration_methods` - 注册方式配置

## 部署说明

### 1. 数据库迁移
执行迁移脚本初始化系统默认配置：
```bash
mysql -u username -p database_name < migrations/0008_init_system_configs.sql
```

### 2. 后端启动
确保后端服务正常启动，租户配置相关的路由会自动注册。

### 3. 前端部署
确保前端路由配置正确，租户配置页面可以正常访问。

## 测试

### 1. 功能测试
使用提供的测试脚本验证API功能：
```bash
chmod +x test/tenant_config_test.sh
./test/tenant_config_test.sh
```

### 2. 密码策略测试
使用专门的密码策略测试脚本：
```bash
chmod +x test/password_policy_validation_test.sh
./test/password_policy_validation_test.sh
```

### 3. 前端测试
1. 登录系统
2. 进入租户管理页面
3. 点击任意租户的"配置"按钮
4. 测试密码策略和注册方式配置功能
5. 测试注册时的密码策略验证

## 注意事项

1. **权限控制** - 只有管理员可以访问租户配置功能
2. **配置验证** - 所有配置都有相应的验证规则
3. **默认值** - 如果租户没有配置，系统会使用默认配置
4. **配置继承** - 租户配置会覆盖系统默认配置
5. **数据一致性** - 配置更新是原子操作，确保数据一致性
6. **注册验证** - 注册时会自动应用密码策略验证
7. **错误处理** - 密码不符合策略时会返回详细的错误信息 