# 用户登录接口代码修改建议

## 1. 验证码集成修改

### 1.1 修改登录DTO

**文件**: `users/internal/application/auth/dto/auth_dto.go`

```go
// LoginDTO 登录DTO
type LoginDTO struct {
    Username    string `json:"username" binding:"required"`
    Password    string `json:"password" binding:"required"`
    TenantCode  string `json:"tenant_code"`
    CaptchaID   string `json:"captcha_id" binding:"required"`      // ✅ 修改：添加必填验证
    CaptchaCode string `json:"captcha_code" binding:"required"`    // ✅ 修改：添加必填验证
}
```

### 1.2 添加验证码服务接口

**文件**: `users/internal/application/auth/service/captcha_service.go` (新建)

```go
package service

import "context"

// CaptchaService 验证码服务接口
type CaptchaService interface {
    // ValidateCaptcha 验证验证码
    ValidateCaptcha(ctx context.Context, captchaID, captchaCode string) error
    // GenerateCaptcha 生成验证码
    GenerateCaptcha(ctx context.Context) (string, string, error)
}

// CaptchaServiceAdapter 验证码服务适配器
type CaptchaServiceAdapter struct {
    captchaService *captcha.CaptchaService
}

// NewCaptchaServiceAdapter 创建验证码服务适配器
func NewCaptchaServiceAdapter(captchaService *captcha.CaptchaService) *CaptchaServiceAdapter {
    return &CaptchaServiceAdapter{
        captchaService: captchaService,
    }
}

// ValidateCaptcha 验证验证码
func (a *CaptchaServiceAdapter) ValidateCaptcha(ctx context.Context, captchaID, captchaCode string) error {
    if !a.captchaService.ValidateCaptcha(captchaID, captchaCode) {
        return fmt.Errorf("验证码错误")
    }
    return nil
}

// GenerateCaptcha 生成验证码
func (a *CaptchaServiceAdapter) GenerateCaptcha(ctx context.Context) (string, string, error) {
    return a.captchaService.GenerateCaptcha()
}
```

### 1.3 修改认证服务

**文件**: `users/internal/application/auth/service/auth_application_service.go`

```go
// AuthApplicationService 认证应用服务
type AuthApplicationService struct {
    logger              logiface.Logger
    authRepo            authRepo.AuthRepository
    userRepo            userRepo.UserRepository
    jwtService          *jwt.JWTService
    tenantLookupService *TenantLookupService
    captchaService      CaptchaService // ✅ 新增：验证码服务
}

// NewAuthApplicationService 创建认证应用服务
func NewAuthApplicationService(
    logger logiface.Logger, 
    authRepo authRepo.AuthRepository, 
    userRepo userRepo.UserRepository, 
    jwtService *jwt.JWTService, 
    tenantLookupService *TenantLookupService,
    captchaService CaptchaService, // ✅ 新增：验证码服务参数
) *AuthApplicationService {
    return &AuthApplicationService{
        logger:              logger,
        authRepo:            authRepo,
        userRepo:            userRepo,
        jwtService:          jwtService,
        tenantLookupService: tenantLookupService,
        captchaService:      captchaService, // ✅ 新增
    }
}

// Login 用户登录
func (s *AuthApplicationService) Login(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
    s.logger.Debug(ctx, "auth service login called",
        logiface.String("username", loginDTO.Username),
        logiface.String("tenant_code", loginDTO.TenantCode),
        logiface.String("ip", ipAddress),
        logiface.String("user_agent", userAgent),
    )

    // ✅ 新增：1. 验证验证码
    if err := s.captchaService.ValidateCaptcha(ctx, loginDTO.CaptchaID, loginDTO.CaptchaCode); err != nil {
        s.logger.Warn(ctx, "captcha validation failed",
            logiface.String("username", loginDTO.Username),
            logiface.String("captcha_id", loginDTO.CaptchaID),
            logiface.String("ip", ipAddress),
        )
        s.recordLoginAttempt(ctx, 0, loginDTO.Username, ipAddress, userAgent, "Captcha validation failed")
        return nil, fmt.Errorf("验证码错误: %w", err)
    }

    // ✅ 新增：2. 检查登录限制
    if err := s.checkLoginLimits(ctx, loginDTO.Username, ipAddress); err != nil {
        return nil, err
    }

    // 继续原有登录逻辑...
    // 根据租户代码查找租户ID
    var tenantID int64
    var err error

    if s.tenantLookupService != nil {
        s.logger.Debug(ctx, "using tenant lookup service",
            logiface.String("tenant_code", loginDTO.TenantCode),
        )
        tenantID, err = s.tenantLookupService.FindTenantIDByCode(ctx, loginDTO.TenantCode)
        if err != nil {
            s.logger.Error(ctx, "tenant lookup failed",
                logiface.Error(err),
                logiface.String("error_type", fmt.Sprintf("%T", err)),
                logiface.String("error_message", err.Error()),
                logiface.String("tenant_code", loginDTO.TenantCode),
                logiface.String("ip", ipAddress),
            )
            s.recordLoginAttempt(ctx, 0, loginDTO.Username, ipAddress, userAgent, "Tenant lookup failed")
            return nil, fmt.Errorf("tenant lookup failed: %w", err)
        }
        s.logger.Debug(ctx, "tenant lookup successful",
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.Int64("tenant_id", tenantID),
        )
    } else {
        // 回退到默认租户ID
        tenantID = int64(1)
        s.logger.Warn(ctx, "tenant lookup service not available, using default tenant ID",
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.Int64("default_tenant_id", tenantID),
        )
    }

    // ... 其余登录逻辑保持不变 ...
}
```

## 2. 登录失败次数限制修改

### 2.1 添加限流配置

**文件**: `users/internal/application/auth/service/login_limit_config.go` (新建)

```go
package service

import "time"

// LoginLimitConfig 登录限流配置
type LoginLimitConfig struct {
    MaxFailedAttemptsPerUser int           `json:"max_failed_attempts_per_user"` // 用户最大失败次数
    MaxFailedAttemptsPerIP   int           `json:"max_failed_attempts_per_ip"`   // IP最大失败次数
    LockDuration             time.Duration `json:"lock_duration"`                // 锁定时长
    ResetDuration            time.Duration `json:"reset_duration"`               // 重置时长
}

// DefaultLoginLimitConfig 默认登录限流配置
var DefaultLoginLimitConfig = LoginLimitConfig{
    MaxFailedAttemptsPerUser: 5,           // 用户5次失败后锁定
    MaxFailedAttemptsPerIP:   10,          // IP10次失败后锁定
    LockDuration:             30 * time.Minute, // 锁定30分钟
    ResetDuration:            24 * time.Hour,   // 24小时后重置
}
```

### 2.2 添加失败次数检查方法

**文件**: `users/internal/application/auth/service/auth_application_service.go`

```go
// checkLoginLimits 检查登录限制
func (s *AuthApplicationService) checkLoginLimits(ctx context.Context, username, ipAddress string) error {
    config := DefaultLoginLimitConfig
    since := time.Now().Add(-config.ResetDuration)

    // 检查用户失败次数
    userFailedCount, err := s.authRepo.CountFailedAttemptsByUsername(ctx, 0, username, since)
    if err != nil {
        s.logger.Error(ctx, "failed to count user failed attempts", logiface.Error(err))
        return fmt.Errorf("系统错误")
    }

    if userFailedCount >= int64(config.MaxFailedAttemptsPerUser) {
        s.logger.Warn(ctx, "user login blocked due to too many failed attempts",
            logiface.String("username", username),
            logiface.Int64("failed_count", userFailedCount),
        )
        return fmt.Errorf("账户已被锁定，请%d分钟后重试", int(config.LockDuration.Minutes()))
    }

    // 检查IP失败次数
    ipFailedCount, err := s.authRepo.CountFailedAttemptsByIP(ctx, 0, ipAddress, since)
    if err != nil {
        s.logger.Error(ctx, "failed to count IP failed attempts", logiface.Error(err))
        return fmt.Errorf("系统错误")
    }

    if ipFailedCount >= int64(config.MaxFailedAttemptsPerIP) {
        s.logger.Warn(ctx, "IP login blocked due to too many failed attempts",
            logiface.String("ip", ipAddress),
            logiface.Int64("failed_count", ipFailedCount),
        )
        return fmt.Errorf("IP已被限制，请%d分钟后重试", int(config.LockDuration.Minutes()))
    }

    return nil
}

// recordLoginAttempt 记录登录尝试
func (s *AuthApplicationService) recordLoginAttempt(ctx context.Context, tenantID int64, username, ipAddress, userAgent, reason string) {
    attempt := authEntity.NewLoginAttempt(tenantID, username, ipAddress, userAgent)

    if reason == "Login successful" {
        attempt.MarkSuccess()
    } else {
        attempt.MarkFailed()
    }

    // ✅ 修改：保存登录尝试记录
    if err := s.authRepo.CreateLoginAttempt(ctx, attempt); err != nil {
        s.logger.Error(ctx, "failed to create login attempt",
            logiface.Error(err),
            logiface.String("username", username),
            logiface.String("ip", ipAddress),
        )
    }
}
```

## 3. MFA功能完善修改

### 3.1 添加MFA验证服务接口

**文件**: `users/internal/application/auth/service/mfa_service.go` (新建)

```go
package service

import "context"

// MFAService MFA服务接口
type MFAService interface {
    // VerifyCode 验证MFA代码
    VerifyCode(ctx context.Context, userID int64, code string) error
    // GenerateCode 生成MFA代码
    GenerateCode(ctx context.Context, userID int64) (string, error)
}

// SimpleMFAService 简单MFA服务实现
type SimpleMFAService struct {
    logger logiface.Logger
}

// NewSimpleMFAService 创建简单MFA服务
func NewSimpleMFAService(logger logiface.Logger) *SimpleMFAService {
    return &SimpleMFAService{
        logger: logger,
    }
}

// VerifyCode 验证MFA代码
func (s *SimpleMFAService) VerifyCode(ctx context.Context, userID int64, code string) error {
    // TODO: 实现实际的MFA验证逻辑
    // 这里应该集成真实的MFA服务，如TOTP、短信验证码等
    s.logger.Info(ctx, "MFA code verification",
        logiface.Int64("user_id", userID),
        logiface.String("code", code),
    )
    
    // 临时实现：简单验证6位数字
    if len(code) != 6 {
        return fmt.Errorf("MFA代码格式错误")
    }
    
    // 这里应该调用实际的MFA验证逻辑
    return fmt.Errorf("MFA验证服务未实现")
}

// GenerateCode 生成MFA代码
func (s *SimpleMFAService) GenerateCode(ctx context.Context, userID int64) (string, error) {
    // TODO: 实现实际的MFA代码生成逻辑
    return "", fmt.Errorf("MFA代码生成服务未实现")
}
```

### 3.2 修改认证服务添加MFA支持

**文件**: `users/internal/application/auth/service/auth_application_service.go`

```go
// AuthApplicationService 认证应用服务
type AuthApplicationService struct {
    logger              logiface.Logger
    authRepo            authRepo.AuthRepository
    userRepo            userRepo.UserRepository
    jwtService          *jwt.JWTService
    tenantLookupService *TenantLookupService
    captchaService      CaptchaService
    mfaService          MFAService // ✅ 新增：MFA服务
}

// NewAuthApplicationService 创建认证应用服务
func NewAuthApplicationService(
    logger logiface.Logger, 
    authRepo authRepo.AuthRepository, 
    userRepo userRepo.UserRepository, 
    jwtService *jwt.JWTService, 
    tenantLookupService *TenantLookupService,
    captchaService CaptchaService,
    mfaService MFAService, // ✅ 新增：MFA服务参数
) *AuthApplicationService {
    return &AuthApplicationService{
        logger:              logger,
        authRepo:            authRepo,
        userRepo:            userRepo,
        jwtService:          jwtService,
        tenantLookupService: tenantLookupService,
        captchaService:      captchaService,
        mfaService:          mfaService, // ✅ 新增
    }
}

// MFALogin MFA登录
func (s *AuthApplicationService) MFALogin(ctx context.Context, mfaDTO *dto.MFALoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
    // 1. 查找临时会话
    session, err := s.authRepo.FindSessionByID(ctx, mfaDTO.SessionID)
    if err != nil {
        return nil, fmt.Errorf("会话不存在或已过期")
    }

    // 2. 验证会话状态
    if session.Status != "pending" || session.ExpiresAt.Before(time.Now()) {
        return nil, fmt.Errorf("会话已过期或无效")
    }

    // 3. 验证MFA代码
    if err := s.mfaService.VerifyCode(ctx, session.UserID, mfaDTO.Code); err != nil {
        s.recordLoginAttempt(ctx, session.TenantID, "", ipAddress, userAgent, "MFA verification failed")
        return nil, fmt.Errorf("MFA验证失败: %w", err)
    }

    // 4. 获取用户信息
    user, err := s.userRepo.FindByID(ctx, session.UserID)
    if err != nil {
        return nil, fmt.Errorf("用户不存在")
    }

    // 5. 生成正式令牌
    accessToken, err := s.jwtService.GenerateAccessToken(user.ID, session.TenantID, user.Username)
    if err != nil {
        return nil, fmt.Errorf("令牌生成失败")
    }

    refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, session.TenantID)
    if err != nil {
        return nil, fmt.Errorf("刷新令牌生成失败")
    }

    // 6. 更新会话状态
    session.AccessToken = accessToken
    session.RefreshToken = refreshToken
    session.Status = "active"
    session.ExpiresAt = time.Now().Add(24 * time.Hour)
    
    if err := s.authRepo.UpdateSession(ctx, session); err != nil {
        return nil, fmt.Errorf("会话更新失败")
    }

    // 7. 记录登录成功
    s.recordLoginAttempt(ctx, session.TenantID, user.Username, ipAddress, userAgent, "Login successful")

    return &dto.LoginResponseDTO{
        AccessToken:  accessToken,
        RefreshToken: refreshToken,
        TokenType:    "Bearer",
        ExpiresIn:    24 * 60 * 60,
        User:         s.toUserInfoDTO(user),
        RequiresMFA:  false,
    }, nil
}
```

## 4. 错误处理优化修改

### 4.1 添加登录错误类型

**文件**: `users/internal/application/auth/errors/login_errors.go` (新建)

```go
package errors

import (
    "fmt"
    "time"
)

// 登录相关错误码 (2000-2099)
const (
    CodeInvalidCredentials     = 2001 // 用户名或密码错误
    CodeAccountLocked          = 2002 // 账户已锁定
    CodeAccountDisabled        = 2003 // 账户已禁用
    CodeCaptchaInvalid         = 2004 // 验证码错误
    CodeCaptchaExpired         = 2005 // 验证码已过期
    CodeTooManyFailedAttempts  = 2006 // 失败次数过多
    CodeIPBlocked              = 2007 // IP被阻止
    CodeMFARequired            = 2008 // 需要MFA验证
    CodeMFAInvalid             = 2009 // MFA验证失败
    CodeSessionExpired         = 2010 // 会话已过期
    CodeTenantNotFound         = 2011 // 租户不存在
    CodeTenantDisabled         = 2012 // 租户已禁用
)

// LoginError 登录错误
type LoginError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

func (e *LoginError) Error() string {
    return fmt.Sprintf("login error %d: %s", e.Code, e.Message)
}

// NewInvalidCredentialsError 创建用户名或密码错误
func NewInvalidCredentialsError() *LoginError {
    return &LoginError{
        Code:    CodeInvalidCredentials,
        Message: "用户名或密码错误",
    }
}

// NewAccountLockedError 创建账户锁定错误
func NewAccountLockedError(duration time.Duration) *LoginError {
    return &LoginError{
        Code:    CodeAccountLocked,
        Message: fmt.Sprintf("账户已被锁定，请%d分钟后重试", int(duration.Minutes())),
    }
}

// NewCaptchaInvalidError 创建验证码错误
func NewCaptchaInvalidError() *LoginError {
    return &LoginError{
        Code:    CodeCaptchaInvalid,
        Message: "验证码错误",
    }
}

// NewTooManyFailedAttemptsError 创建失败次数过多错误
func NewTooManyFailedAttemptsError(duration time.Duration) *LoginError {
    return &LoginError{
        Code:    CodeTooManyFailedAttempts,
        Message: fmt.Sprintf("失败次数过多，请%d分钟后重试", int(duration.Minutes())),
    }
}

// NewIPBlockedError 创建IP被阻止错误
func NewIPBlockedError(duration time.Duration) *LoginError {
    return &LoginError{
        Code:    CodeIPBlocked,
        Message: fmt.Sprintf("IP已被限制，请%d分钟后重试", int(duration.Minutes())),
    }
}

// NewMFARequiredError 创建需要MFA验证错误
func NewMFARequiredError() *LoginError {
    return &LoginError{
        Code:    CodeMFARequired,
        Message: "需要MFA验证",
    }
}

// NewMFAInvalidError 创建MFA验证失败错误
func NewMFAInvalidError() *LoginError {
    return &LoginError{
        Code:    CodeMFAInvalid,
        Message: "MFA验证失败",
    }
}
```

### 4.2 修改认证服务使用新的错误类型

**文件**: `users/internal/application/auth/service/auth_application_service.go`

```go
// Login 用户登录
func (s *AuthApplicationService) Login(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
    // ... 现有代码 ...

    // 验证验证码
    if err := s.captchaService.ValidateCaptcha(ctx, loginDTO.CaptchaID, loginDTO.CaptchaCode); err != nil {
        s.logger.Warn(ctx, "captcha validation failed",
            logiface.String("username", loginDTO.Username),
            logiface.String("captcha_id", loginDTO.CaptchaID),
            logiface.String("ip", ipAddress),
        )
        s.recordLoginAttempt(ctx, 0, loginDTO.Username, ipAddress, userAgent, "Captcha validation failed")
        return nil, errors.NewCaptchaInvalidError() // ✅ 修改：使用新的错误类型
    }

    // 检查登录限制
    if err := s.checkLoginLimits(ctx, loginDTO.Username, ipAddress); err != nil {
        return nil, err // 这里返回的错误已经是LoginError类型
    }

    // ... 继续登录逻辑 ...

    if user == nil {
        s.logger.Warn(ctx, "user not found during login",
            logiface.String("username", loginDTO.Username),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.Int64("tenant_id", tenantID),
            logiface.String("ip", ipAddress),
        )
        s.recordLoginAttempt(ctx, tenantID, loginDTO.Username, ipAddress, userAgent, "User not found")
        return nil, errors.NewInvalidCredentialsError() // ✅ 修改：使用新的错误类型
    }

    if user.Status == userValueObject.UserStatusLocked {
        s.logger.Warn(ctx, "locked user login attempt",
            logiface.String("username", loginDTO.Username),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.String("ip", ipAddress),
            logiface.String("user_status", string(user.Status)),
        )
        s.recordLoginAttempt(ctx, tenantID, loginDTO.Username, ipAddress, userAgent, "Account locked")
        return nil, errors.NewAccountLockedError(DefaultLoginLimitConfig.LockDuration) // ✅ 修改：使用新的错误类型
    }

    if user.Status == userValueObject.UserStatusDisabled {
        s.logger.Warn(ctx, "disabled user login attempt",
            logiface.String("username", loginDTO.Username),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.String("ip", ipAddress),
            logiface.String("user_status", string(user.Status)),
        )
        s.recordLoginAttempt(ctx, tenantID, loginDTO.Username, ipAddress, userAgent, "Account disabled")
        return nil, errors.NewAccountLockedError(0) // ✅ 修改：使用新的错误类型
    }

    // 验证密码
    if !user.Password.Verify(loginDTO.Password) {
        s.logger.Warn(ctx, "invalid password during login",
            logiface.String("username", loginDTO.Username),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.String("ip", ipAddress),
            logiface.Int64("user_id", user.ID),
        )
        s.recordLoginAttempt(ctx, tenantID, loginDTO.Username, ipAddress, userAgent, "Invalid password")
        return nil, errors.NewInvalidCredentialsError() // ✅ 修改：使用新的错误类型
    }

    // 检查是否需要MFA
    if user.IsMFAEnabled() {
        s.logger.Info(ctx, "mfa required for login",
            logiface.String("username", loginDTO.Username),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.String("ip", ipAddress),
        )
        // 创建临时会话用于MFA验证
        deviceInfo := authValueObject.NewDeviceInfo(userAgent)
        session := authEntity.NewAuthSession(user.ID, tenantID, "", "", deviceInfo, time.Now().Add(30*time.Minute))
        if err := s.authRepo.CreateSession(ctx, session); err != nil {
            s.logger.Error(ctx, "failed to create mfa session",
                logiface.Error(err),
                logiface.String("username", loginDTO.Username),
                logiface.String("tenant_code", loginDTO.TenantCode),
                logiface.String("ip", ipAddress),
            )
            return nil, fmt.Errorf("failed to create session")
        }

        return &dto.LoginResponseDTO{
            RequiresMFA: true,
            MFAType:     "totp",
            User:        s.toUserInfoDTO(user),
        }, nil
    }

    // ... 其余登录逻辑保持不变 ...
}

// checkLoginLimits 检查登录限制
func (s *AuthApplicationService) checkLoginLimits(ctx context.Context, username, ipAddress string) error {
    config := DefaultLoginLimitConfig
    since := time.Now().Add(-config.ResetDuration)

    // 检查用户失败次数
    userFailedCount, err := s.authRepo.CountFailedAttemptsByUsername(ctx, 0, username, since)
    if err != nil {
        s.logger.Error(ctx, "failed to count user failed attempts", logiface.Error(err))
        return fmt.Errorf("系统错误")
    }

    if userFailedCount >= int64(config.MaxFailedAttemptsPerUser) {
        s.logger.Warn(ctx, "user login blocked due to too many failed attempts",
            logiface.String("username", username),
            logiface.Int64("failed_count", userFailedCount),
        )
        return errors.NewTooManyFailedAttemptsError(config.LockDuration) // ✅ 修改：使用新的错误类型
    }

    // 检查IP失败次数
    ipFailedCount, err := s.authRepo.CountFailedAttemptsByIP(ctx, 0, ipAddress, since)
    if err != nil {
        s.logger.Error(ctx, "failed to count IP failed attempts", logiface.Error(err))
        return fmt.Errorf("系统错误")
    }

    if ipFailedCount >= int64(config.MaxFailedAttemptsPerIP) {
        s.logger.Warn(ctx, "IP login blocked due to too many failed attempts",
            logiface.String("ip", ipAddress),
            logiface.Int64("failed_count", ipFailedCount),
        )
        return errors.NewIPBlockedError(config.LockDuration) // ✅ 修改：使用新的错误类型
    }

    return nil
}
```

### 4.3 修改处理器使用新的错误处理

**文件**: `users/internal/interfaces/http/handlers/auth_handler.go`

```go
// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
    // ... 现有代码 ...

    result, err := h.authService.Login(c.Request.Context(), &req, ipAddress, userAgent)
    if err != nil {
        h.logger.Error(c.Request.Context(), "user login failed",
            logiface.Error(err),
            logiface.String("username", req.Username),
            logiface.String("tenant_code", req.TenantCode),
            logiface.String("ip", ipAddress),
        )

        // ✅ 修改：优化错误处理
        if loginErr, ok := err.(*errors.LoginError); ok {
            commonErrors.Error(c, loginErr.Code, loginErr.Message)
            return
        }

        // 处理其他业务错误
        if isBusinessError(err) {
            code := getBusinessErrorCode(err)
            commonErrors.Error(c, code, err.Error())
            return
        }

        // 处理字段验证错误
        if vErr, ok := err.(*commonErrors.FieldValidationError); ok {
            commonErrors.FieldError(c, vErr.Field, vErr.Message)
            return
        }

        // 通用内部错误
        commonErrors.InternalError(c, err)
        return
    }

    // ... 现有代码 ...
}

// MFALogin MFA登录
func (h *AuthHandler) MFALogin(c *gin.Context) {
    // ... 现有代码 ...

    result, err := h.authService.MFALogin(c.Request.Context(), &req, ipAddress, userAgent)
    if err != nil {
        h.logger.Error(c.Request.Context(), "mfa login failed",
            logiface.Error(err),
            logiface.String("session_id", req.SessionID),
            logiface.String("ip", ipAddress),
        )

        // ✅ 修改：优化错误处理
        if loginErr, ok := err.(*errors.LoginError); ok {
            commonErrors.Error(c, loginErr.Code, loginErr.Message)
            return
        }

        commonErrors.InternalError(c, err)
        return
    }

    // ... 现有代码 ...
}
```

## 5. 租户查找优化修改

### 5.1 修改认证服务添加智能租户查找

**文件**: `users/internal/application/auth/service/auth_application_service.go`

```go
// Login 用户登录
func (s *AuthApplicationService) Login(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
    // ✅ 修改：1. 智能租户查找
    tenantID, err := s.resolveTenantID(ctx, loginDTO.TenantCode, loginDTO.Username)
    if err != nil {
        s.logger.Error(ctx, "tenant resolution failed",
            logiface.Error(err),
            logiface.String("tenant_code", loginDTO.TenantCode),
            logiface.String("username", loginDTO.Username),
        )
        return nil, fmt.Errorf("租户信息无效: %w", err)
    }

    // ... 继续登录逻辑 ...
}

// resolveTenantID 解析租户ID
func (s *AuthApplicationService) resolveTenantID(ctx context.Context, tenantCode, username string) (int64, error) {
    // 1. 优先通过租户代码查找
    if tenantCode != "" && s.tenantLookupService != nil {
        tenantID, err := s.tenantLookupService.FindTenantIDByCode(ctx, tenantCode)
        if err == nil {
            return tenantID, nil
        }
        s.logger.Warn(ctx, "tenant lookup by code failed, falling back to username lookup",
            logiface.String("tenant_code", tenantCode),
            logiface.Error(err),
        )
    }

    // 2. 通过用户名查找租户（如果租户代码查找失败）
    if username != "" {
        tenantID, err := s.findTenantIDByUsername(ctx, username)
        if err == nil {
            return tenantID, nil
        }
        s.logger.Warn(ctx, "tenant lookup by username failed",
            logiface.String("username", username),
            logiface.Error(err),
        )
    }

    // 3. 使用默认租户（仅作为最后的降级策略）
    s.logger.Warn(ctx, "using default tenant ID as fallback",
        logiface.String("tenant_code", tenantCode),
        logiface.String("username", username),
    )
    return int64(1), nil
}

// findTenantIDByUsername 通过用户名查找租户ID
func (s *AuthApplicationService) findTenantIDByUsername(ctx context.Context, username string) (int64, error) {
    // 这里需要实现通过用户名查找租户的逻辑
    // 可能需要修改用户仓储接口，添加按用户名查找用户的方法
    // 暂时返回错误，表示未实现
    return 0, fmt.Errorf("username-based tenant lookup not implemented")
}
```

## 6. 依赖注入配置修改

### 6.1 修改主程序依赖注入

**文件**: `users/cmd/main.go`

```go
func main() {
    // ... 现有代码 ...

    // 创建验证码服务
    captchaService := captcha.NewCaptchaService()
    captchaServiceAdapter := service.NewCaptchaServiceAdapter(captchaService)

    // 创建MFA服务
    mfaService := service.NewSimpleMFAService(logger)

    // 创建认证服务
    authService := service.NewAuthApplicationService(
        logger,
        authRepo,
        userRepo,
        jwtService,
        tenantLookupService,
        captchaServiceAdapter, // ✅ 新增：验证码服务
        mfaService,           // ✅ 新增：MFA服务
    )

    // ... 其余代码 ...
}
```

## 7. 测试用例修改

### 7.1 添加验证码测试

**文件**: `users/internal/application/auth/service/auth_application_service_test.go`

```go
func TestAuthApplicationService_Login_WithCaptcha(t *testing.T) {
    // 设置测试环境
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockAuthRepo := mock.NewMockAuthRepository(ctrl)
    mockUserRepo := mock.NewMockUserRepository(ctrl)
    mockJWTService := mock.NewMockJWTService(ctrl)
    mockTenantLookupService := mock.NewMockTenantLookupService(ctrl)
    mockCaptchaService := mock.NewMockCaptchaService(ctrl)
    mockMFAService := mock.NewMockMFAService(ctrl)

    service := NewAuthApplicationService(
        logger,
        mockAuthRepo,
        mockUserRepo,
        mockJWTService,
        mockTenantLookupService,
        mockCaptchaService,
        mockMFAService,
    )

    // 测试验证码验证失败
    loginDTO := &dto.LoginDTO{
        Username:    "testuser",
        Password:    "password",
        TenantCode:  "test",
        CaptchaID:   "captcha123",
        CaptchaCode: "wrong",
    }

    mockCaptchaService.EXPECT().
        ValidateCaptcha(gomock.Any(), "captcha123", "wrong").
        Return(fmt.Errorf("验证码错误"))

    result, err := service.Login(context.Background(), loginDTO, "127.0.0.1", "test-agent")
    
    assert.Error(t, err)
    assert.Nil(t, result)
    assert.Contains(t, err.Error(), "验证码错误")
}
```

## 8. 配置更新

### 8.1 添加验证码配置

**文件**: `users/configs/app.yaml`

```yaml
# 验证码配置
captcha:
  enabled: true
  type: "digit"  # digit, string, math
  length: 6
  width: 200
  height: 80
  expire_minutes: 5

# 登录限流配置
login_limit:
  max_failed_attempts_per_user: 5
  max_failed_attempts_per_ip: 10
  lock_duration_minutes: 30
  reset_duration_hours: 24

# MFA配置
mfa:
  enabled: true
  type: "totp"  # totp, sms, email
  code_length: 6
  expire_minutes: 5
```

## 9. 部署注意事项

### 9.1 数据库迁移
确保`login_attempts`表已创建并包含必要的索引：

```sql
-- 检查表是否存在
SHOW TABLES LIKE 'login_attempts';

-- 如果不存在，创建表
CREATE TABLE IF NOT EXISTS login_attempts (
    id BIGINT PRIMARY KEY COMMENT '分布式ID，雪花算法生成',
    user_id BIGINT COMMENT '用户ID（可能为空，因为用户名可能不存在）',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status VARCHAR(20) NOT NULL DEFAULT 'failed' COMMENT '尝试状态：success-成功，failed-失败，locked-锁定',
    attempted_at DATETIME NOT NULL COMMENT '尝试时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_login_attempts_user_id (user_id),
    INDEX idx_login_attempts_tenant_id (tenant_id),
    INDEX idx_login_attempts_username (username),
    INDEX idx_login_attempts_ip_address (ip_address),
    INDEX idx_login_attempts_attempted_at (attempted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录尝试表';
```

### 9.2 配置更新
确保所有新功能都有配置开关，支持运行时动态调整。

### 9.3 监控配置
添加新的监控指标和告警规则，确保能够及时发现和处理问题。

## 10. 回滚计划

### 10.1 快速回滚
如果新功能出现问题，可以通过配置开关快速禁用：

```yaml
captcha:
  enabled: false  # 禁用验证码

login_limit:
  enabled: false  # 禁用登录限制

mfa:
  enabled: false  # 禁用MFA
```

### 10.2 代码回滚
保留原有代码的备份，如果问题严重，可以快速回滚到之前的版本。

### 10.3 数据清理
如果登录限制功能出现问题，可能需要清理相关的限制数据：

```sql
-- 清理登录尝试记录
DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 清理被锁定的用户
UPDATE users SET status = 'active', locked_at = NULL, locked_until = NULL WHERE status = 'locked';
```

这个修改方案提供了完整的代码实现，包括所有必要的接口、服务和错误处理。建议按照文档中的实施计划分阶段进行修改和测试。 