# 用户注册接口设计文档

## 概述

用户注册接口支持HTTP和gRPC两种协议，仅处理传统注册方式（用户名+密码+邮箱），注册成功后不返回认证令牌，用户需要手动登录。第三方登录通过登录接口自动创建账户。

## 设计原则

- **单一职责**：注册接口只负责用户创建，不处理认证
- **多租户支持**：必须传递tenantId，支持租户隔离
- **统一响应**：遵循REST API统一响应结构
- **无第三方**：不处理第三方登录，第三方登录通过登录接口自动创建账户

## HTTP接口设计

### 用户注册接口

#### 接口信息
- **路径**: `POST /api/user/auth/register`
- **描述**: 用户注册接口
- **认证**: 不需要认证
- **限流**: 是（防止批量注册）

#### 请求参数
```json
{
  "tenant_id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "real_name": "<PERSON>",
  "phone": "13800138000",
  "captcha_id": "captcha_123456",
  "captcha_code": "ABCD",
  "invite_code": "INVITE2024"
}
```

#### 参数说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenant_id | int64 | 是 | 租户ID |
| username | string | 是 | 用户名 |
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码 |
| real_name | string | 否 | 真实姓名 |
| phone | string | 否 | 手机号 |
| captcha_id | string | 否 | 验证码ID |
| captcha_code | string | 否 | 验证码 |
| invite_code | string | 否 | 邀请码 |

#### 成功响应
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1234567890123456789,
      "tenant_id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "real_name": "John Doe",
      "phone": "13800138000",
      "status": "active",
      "created_at": "2024-01-01T12:00:00Z"
    }
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1704067200000
  }
}
```

#### 错误响应示例
```json
{
  "code": 3002,
  "message": "用户名已存在",
  "data": null,
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1704067200000
  }
}
```

### 验证码接口

#### 获取验证码
- **路径**: `POST /api/user/captcha/generate`
- **描述**: 生成注册验证码
- **认证**: 不需要认证

#### 请求参数
```json
{
  "type": "register",
  "width": 200,
  "height": 80
}
```

#### 响应参数
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "captcha_id": "captcha_123456",
    "captcha_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expires_in": 300
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1704067200000
  }
}
```

## gRPC接口设计

### 更新proto文件

```protobuf
syntax = "proto3";

package user;

option go_package = "/api/userpb";

// 用户服务
service UserService {
  // 现有接口
  rpc GetUserInfoByToken(GetUserInfoByTokenRequest) returns (GetUserInfoByTokenResponse);
  rpc CheckUserPermissions(CheckUserPermissionsRequest) returns (CheckUserPermissionsResponse);
  
  // 新增用户注册接口
  rpc RegisterUser(RegisterUserRequest) returns (RegisterUserResponse);
}

// 用户注册请求
message RegisterUserRequest {
  int64 tenant_id = 1;           // 租户ID（必填）
  string username = 2;           // 用户名（必填）
  string email = 3;              // 邮箱（必填）
  string password = 4;           // 密码（必填）
  string real_name = 5;          // 真实姓名（可选）
  string phone = 6;              // 手机号（可选）
  string captcha_id = 7;         // 验证码ID（可选）
  string captcha_code = 8;       // 验证码（可选）
  string invite_code = 9;        // 邀请码（可选）
}

// 用户注册响应
message RegisterUserResponse {
  int32 code = 1;
  string message = 2;
  RegisterUserData data = 3;
}

// 注册响应数据
message RegisterUserData {
  UserInfo user = 1;
}

// 用户基础信息
message UserInfo {
  int64 user_id = 1;
  int64 tenant_id = 2;
  string username = 3;
  string email = 4;
  string real_name = 5;
  string phone = 6;
  string status = 7;
  int64 created_at = 8;
}

// 现有接口定义保持不变...
message GetUserInfoByTokenRequest {
  string token = 1;
}

message GetUserInfoByTokenResponse {
  int32 code = 1;
  string message = 2;
  UserInfo data = 3;
}

message CheckUserPermissionsRequest {
  int64 user_id = 1;
  repeated string permission_codes = 2;
}

message CheckUserPermissionsResponse {
  int32 code = 1;
  string message = 2;
  repeated PermissionCheckResult results = 3;
}

message PermissionCheckResult {
  string permission_code = 1;
  bool has_permission = 2;
}
```

### gRPC接口调用示例

#### 注册请求示例
```go
request := &user.RegisterUserRequest{
    TenantId:   1,
    Username:   "john_doe",
    Email:      "<EMAIL>",
    Password:   "SecurePass123!",
    RealName:   "John Doe",
    Phone:      "13800138000",
    CaptchaId:  "captcha_123456",
    CaptchaCode: "ABCD",
    InviteCode: "INVITE2024",
}

response, err := client.RegisterUser(ctx, request)
```

#### 注册响应示例
```go
response := &user.RegisterUserResponse{
    Code:    0,
    Message: "注册成功",
    Data: &user.RegisterUserData{
        User: &user.UserInfo{
            UserId:    1234567890123456789,
            TenantId:  1,
            Username:  "john_doe",
            Email:     "<EMAIL>",
            RealName:  "John Doe",
            Phone:     "13800138000",
            Status:    "active",
            CreatedAt: 1704067200,
        },
    },
}
```

## 错误码定义

### 注册相关错误码

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 0 | 成功 | 200 |
| 3001 | 租户不存在 | 400 |
| 3002 | 用户名已存在 | 409 |
| 3003 | 邮箱已存在 | 409 |
| 3004 | 手机号已存在 | 409 |
| 3005 | 密码格式错误 | 400 |
| 3006 | 验证码错误 | 400 |
| 3007 | 验证码已过期 | 400 |
| 3008 | 邀请码无效 | 400 |
| 3009 | 注册功能已禁用 | 403 |
| 3010 | 参数验证失败 | 400 |

## 业务规则

### 1. 参数验证规则
- **用户名**: 3-50位字母、数字、下划线，租户内唯一
- **邮箱**: 有效邮箱格式，租户内唯一
- **密码**: 8-128位，包含大小写字母、数字、特殊字符
- **手机号**: 11位手机号格式，租户内唯一（可选）

### 2. 租户验证规则
- 租户必须存在且状态为active
- 检查租户用户数量限制
- 验证租户注册功能是否开启

### 3. 验证码规则
- 验证码有效期：5分钟
- 验证码长度：4-6位
- 防刷机制：同一IP限制获取频率

### 4. 邀请码规则
- 邀请码有效期：30天
- 邀请码使用次数限制
- 邀请码与租户关联

## 安全考虑

### 1. 输入验证
- 所有输入参数进行严格验证
- 防止SQL注入和XSS攻击

### 2. 限流控制
- 注册接口限流：每分钟最多10次
- IP地址限流：同一IP每小时最多50次
- 验证码获取限流：同一IP每分钟最多3次

### 3. 数据保护
- 密码使用BCrypt加密存储
- 敏感信息不在日志中记录

## 第三方登录说明

第三方登录通过登录接口实现，当用户使用第三方登录时：

1. 验证第三方Token
2. 根据第三方用户ID查找用户
3. 如果用户不存在，自动创建用户账户
4. 绑定第三方账户信息
5. 返回登录Token

注册接口不处理第三方登录，保持接口职责单一。

## 接口流程

### 注册流程
1. 获取验证码（可选）
2. 提交注册请求
3. 验证参数和业务规则
4. 创建用户账户
5. 返回用户信息（不包含Token）

### 登录流程
1. 提交登录请求
2. 验证用户名密码
3. 生成JWT Token
4. 返回Token和用户信息

### 第三方登录流程
1. 提交第三方登录请求
2. 验证第三方Token
3. 查找或创建用户
4. 绑定第三方账户
5. 生成JWT Token
6. 返回Token和用户信息 