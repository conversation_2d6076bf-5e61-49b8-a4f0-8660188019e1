# 第三方登录接口设计文档

## 概述

第三方登录接口支持Apple、微信、Google三种主流平台，当用户使用第三方登录时，如果账户不存在则自动创建，如果已存在则直接登录。

## 设计原则

- **统一接口**：所有第三方平台使用统一的接口格式
- **自动创建**：不存在的用户自动创建账户
- **自动绑定**：第三方账户自动绑定到系统用户
- **返回Token**：登录成功后返回JWT Token
- **多租户支持**：必须传递tenantId

## HTTP接口设计

### 第三方登录接口

#### 接口信息
- **路径**: `POST /api/user/auth/third-party/login`
- **描述**: 第三方登录接口
- **认证**: 不需要认证
- **限流**: 是（防止批量登录）

#### 请求参数
```json
{
  "tenant_id": 1,
  "provider": "apple",
  "authorization_code": "auth_code_xxx",
  "user_info": {
    "sub": "001234.567890abcdef.1234",
    "email": "<EMAIL>",
    "name": {
      "firstName": "John",
      "lastName": "Doe"
    }
  }
}
```

#### 参数说明
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenant_id | int64 | 是 | 租户ID |
| provider | string | 是 | 第三方平台：apple, wechat, google |
| authorization_code | string | 是 | 第三方授权码 |
| user_info | object | 是 | 第三方用户信息 |

#### 第三方用户信息格式

**Apple登录**
```json
{
  "sub": "001234.567890abcdef.1234",
  "email": "<EMAIL>",
  "email_verified": "true",
  "real_user_status": "2",
  "name": {
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

**微信登录**
```json
{
  "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
  "unionid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
  "nickname": "用户昵称",
  "headimgurl": "http://example.com/avatar.jpg",
  "sex": 1,
  "country": "CN",
  "province": "广东",
  "city": "深圳"
}
```

**Google登录**
```json
{
  "sub": "110169484474386276334",
  "name": "John Doe",
  "given_name": "John",
  "family_name": "Doe",
  "picture": "https://example.com/avatar.jpg",
  "email": "<EMAIL>",
  "email_verified": true,
  "locale": "zh-CN"
}
```

#### 成功响应
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "access_token": "jwt_access_token",
    "refresh_token": "jwt_refresh_token",
    "token_type": "Bearer",
    "expires_in": 86400,
    "user": {
      "id": 1234567890123456789,
      "tenant_id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "real_name": "John Doe",
      "status": "active",
      "roles": []
    },
    "third_party_account": {
      "id": 1234567890123456789,
      "provider": "apple",
      "external_user_id": "001234.567890abcdef.1234",
      "external_username": "John Doe",
      "external_email": "<EMAIL>",
      "external_avatar": "https://example.com/avatar.jpg",
      "status": "active",
      "bind_at": "2024-01-01T12:00:00Z"
    }
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": *************
  }
}
```

#### 错误响应示例
```json
{
  "code": 2003,
  "message": "第三方授权码无效",
  "data": null,
  "meta": {
    "request_id": "req_123456789",
    "timestamp": *************
  }
}
```

### 第三方配置接口

#### 获取第三方配置
- **路径**: `POST /api/user/auth/third-party/config`
- **描述**: 获取第三方登录配置信息
- **认证**: 不需要认证

#### 请求参数
```json
{
  "provider": "apple"
}
```

#### 响应参数
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "provider": "apple",
    "enabled": true,
    "client_id": "com.example.app",
    "redirect_uri": "https://example.com/auth/apple/callback",
    "auto_create_user": true,
    "require_email": false
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": *************
  }
}
```

### 第三方账户管理接口

#### 获取用户第三方账户列表
- **路径**: `POST /api/user/auth/third-party/list`
- **描述**: 获取当前用户的第三方账户列表
- **认证**: 需要认证

#### 请求参数
```json
{
  "page": 1,
  "size": 20
}
```

#### 响应参数
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "id": 1234567890123456789,
      "provider": "apple",
      "external_user_id": "001234.567890abcdef.1234",
      "external_username": "John Doe",
      "external_email": "<EMAIL>",
      "external_avatar": "https://example.com/avatar.jpg",
      "status": "active",
      "bind_at": "2024-01-01T12:00:00Z",
      "last_login_at": "2024-01-01T12:00:00Z"
    }
  ],
  "meta": {
    "request_id": "req_123456789",
    "timestamp": *************,
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1
    }
  }
}
```

#### 解绑第三方账户
- **路径**: `POST /api/user/auth/third-party/unbind`
- **描述**: 解绑第三方账户
- **认证**: 需要认证

#### 请求参数
```json
{
  "provider": "apple"
}
```

#### 响应参数
```json
{
  "code": 0,
  "message": "解绑成功",
  "data": null,
  "meta": {
    "request_id": "req_123456789",
    "timestamp": *************
  }
}
```

## 错误码定义

### 第三方登录相关错误码

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 0 | 成功 | 200 |
| 2001 | 第三方平台配置错误 | 500 |
| 2002 | 第三方平台服务异常 | 502 |
| 2003 | 第三方授权码无效 | 400 |
| 2004 | 第三方用户信息获取失败 | 400 |
| 2005 | 第三方账户已绑定其他用户 | 409 |
| 2006 | 第三方账户绑定失败 | 500 |
| 2007 | 第三方账户解绑失败 | 500 |
| 2008 | 第三方令牌刷新失败 | 500 |
| 2009 | 第三方平台不支持 | 400 |
| 2010 | 第三方登录已禁用 | 403 |

## 业务流程

### 第三方登录流程
```
1. 用户在前端使用第三方SDK授权
   ↓
2. 前端获得授权码和用户信息
   ↓
3. 前端发送到后端第三方登录接口
   ↓
4. 后端验证第三方Token
   ↓
5. 根据第三方用户ID查找用户
   ↓
6. 如果用户不存在，自动创建用户
   ↓
7. 创建或更新第三方账户绑定
   ↓
8. 生成系统JWT Token
   ↓
9. 返回登录结果
```

### 用户创建逻辑
```
1. 检查第三方账户是否已绑定其他用户
   ↓
2. 生成用户名（基于第三方信息）
   ↓
3. 生成随机密码
   ↓
4. 创建用户实体
   ↓
5. 保存用户到数据库
   ↓
6. 创建第三方账户绑定
   ↓
7. 返回用户信息
```

## 配置要求

### 1. Apple登录配置
```yaml
apple:
  enabled: true
  client_id: "com.example.app"
  team_id: "ABC123DEF4"
  key_id: "KEY123456"
  private_key: "-----BEGIN PRIVATE KEY-----..."
  redirect_uri: "https://example.com/auth/apple/callback"
  auto_create_user: true
  require_email: false
```

### 2. 微信登录配置
```yaml
wechat:
  enabled: true
  app_id: "wx1234567890abcdef"
  app_secret: "app_secret_xxx"
  redirect_uri: "https://example.com/auth/wechat/callback"
  auto_create_user: true
  require_email: false
```

### 3. Google登录配置
```yaml
google:
  enabled: true
  client_id: "google_client_id.apps.googleusercontent.com"
  client_secret: "google_client_secret"
  redirect_uri: "https://example.com/auth/google/callback"
  auto_create_user: true
  require_email: true
```

## 安全考虑

### 1. Token验证
- 验证第三方平台返回的Token签名
- 检查Token有效期
- 验证Token来源

### 2. 用户信息保护
- 敏感信息加密存储
- 遵循数据隐私法规
- 最小化数据收集

### 3. 防刷机制
- 登录接口限流
- IP地址限流
- 异常行为检测

## 实现建议

### 1. 第三方验证器
- 实现各平台的Token验证逻辑
- 统一验证接口
- 支持配置热更新

### 2. 用户创建策略
- 用户名生成规则
- 密码生成策略
- 角色分配规则

### 3. 错误处理
- 详细的错误分类
- 友好的错误提示
- 完整的错误日志

## 监控指标

### 1. 关键指标
- 第三方登录成功率
- 用户自动创建率
- 各平台登录分布
- 接口响应时间

### 2. 告警规则
- 登录失败率超过10%
- 第三方服务异常
- 接口响应时间超过2秒 