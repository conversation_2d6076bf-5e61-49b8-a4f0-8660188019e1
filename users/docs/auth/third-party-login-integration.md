# 第三方登录集成指南

## 概述

本文档介绍如何在用户服务中集成第三方登录功能，支持Apple、微信、Google三种主流平台。

## 功能特性

- **多平台支持**：Apple、微信、Google
- **自动用户创建**：首次登录自动创建用户账户
- **账户绑定管理**：支持查看和解绑第三方账户
- **统一接口**：所有平台使用统一的API接口
- **安全验证**：完整的Token验证和用户信息验证
- **多租户支持**：支持多租户环境

## 架构设计

### 核心组件

1. **ThirdPartyApplicationService**：第三方登录应用服务
2. **ThirdPartyValidator**：第三方验证器接口
3. **ConfigService**：第三方配置服务
4. **ThirdPartyHandler**：HTTP处理器

### 依赖关系

```
ThirdPartyHandler
    ↓
ThirdPartyApplicationService
    ↓
ThirdPartyValidator (Apple/WeChat/Google)
    ↓
ConfigService
```

## 集成步骤

### 1. 依赖注入配置

在 `main.go` 中添加第三方登录服务的依赖注入：

```go
package main

import (
    "platforms-user/internal/application/auth/service"
    "platforms-user/internal/infrastructure/thirdparty"
    "platforms-user/internal/interfaces/http/handlers"
    "platforms-user/pkg/jwt"
    "platforms-pkg/logiface"
)

func main() {
    // 初始化日志
    logger := logiface.NewLogger()
    
    // 初始化JWT服务
    jwtService := jwt.NewJWTService(jwtConfig)
    
    // 初始化用户服务
    userService := userAppService.NewUserApplicationService(...)
    
    // 初始化第三方配置服务
    configService := thirdparty.NewConfigService(logger)
    
    // 加载第三方配置
    thirdPartyConfig := map[string]interface{}{
        "apple": map[string]interface{}{
            "enabled":         true,
            "client_id":       "com.example.app",
            "team_id":         "ABC123DEF4",
            "key_id":          "KEY123456",
            "private_key":     "-----BEGIN PRIVATE KEY-----...",
            "redirect_uri":    "https://example.com/auth/apple/callback",
            "auto_create_user": true,
            "require_email":   false,
        },
        "wechat": map[string]interface{}{
            "enabled":         true,
            "app_id":          "wx1234567890abcdef",
            "app_secret":      "app_secret_xxx",
            "redirect_uri":    "https://example.com/auth/wechat/callback",
            "auto_create_user": true,
            "require_email":   false,
        },
        "google": map[string]interface{}{
            "enabled":         true,
            "client_id":       "google_client_id.apps.googleusercontent.com",
            "client_secret":   "google_client_secret",
            "redirect_uri":    "https://example.com/auth/google/callback",
            "auto_create_user": true,
            "require_email":   true,
        },
    }
    
    if err := configService.LoadConfig(thirdPartyConfig); err != nil {
        log.Fatal("Failed to load third party config:", err)
    }
    
    // 验证配置
    if err := configService.ValidateConfig(); err != nil {
        log.Fatal("Third party config validation failed:", err)
    }
    
    // 初始化第三方登录应用服务
    thirdPartyService := service.NewThirdPartyApplicationService(
        logger,
        userService,
        jwtService,
        configService,
    )
    
    // 注册第三方验证器
    appleValidator, err := thirdparty.NewAppleValidator(
        logger,
        "com.example.app",
        "ABC123DEF4",
        "KEY123456",
        "-----BEGIN PRIVATE KEY-----...",
    )
    if err != nil {
        log.Fatal("Failed to create Apple validator:", err)
    }
    thirdPartyService.RegisterValidator(appleValidator)
    
    wechatValidator := thirdparty.NewWechatValidator(
        logger,
        "wx1234567890abcdef",
        "app_secret_xxx",
    )
    thirdPartyService.RegisterValidator(wechatValidator)
    
    googleValidator := thirdparty.NewGoogleValidator(
        logger,
        "google_client_id.apps.googleusercontent.com",
        "google_client_secret",
        "https://example.com/auth/google/callback",
    )
    thirdPartyService.RegisterValidator(googleValidator)
    
    // 初始化HTTP处理器
    thirdPartyHandler := handlers.NewThirdPartyHandler(
        logger,
        thirdPartyService,
    )
    
    // 设置路由
    routes.SetupAuthRoutes(router, authHandler, registerHandler, thirdPartyHandler)
}
```

### 2. 配置管理

#### 环境变量配置

```bash
# Apple登录配置
APPLE_ENABLED=true
APPLE_CLIENT_ID=com.example.app
APPLE_TEAM_ID=ABC123DEF4
APPLE_KEY_ID=KEY123456
APPLE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----...

# 微信登录配置
WECHAT_ENABLED=true
WECHAT_APP_ID=wx1234567890abcdef
WECHAT_APP_SECRET=app_secret_xxx

# Google登录配置
GOOGLE_ENABLED=true
GOOGLE_CLIENT_ID=google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=google_client_secret
```

#### 配置文件

```yaml
# configs/app.yaml
third_party:
  apple:
    enabled: true
    client_id: "com.example.app"
    team_id: "ABC123DEF4"
    key_id: "KEY123456"
    private_key: "-----BEGIN PRIVATE KEY-----..."
    redirect_uri: "https://example.com/auth/apple/callback"
    auto_create_user: true
    require_email: false
  wechat:
    enabled: true
    app_id: "wx1234567890abcdef"
    app_secret: "app_secret_xxx"
    redirect_uri: "https://example.com/auth/wechat/callback"
    auto_create_user: true
    require_email: false
  google:
    enabled: true
    client_id: "google_client_id.apps.googleusercontent.com"
    client_secret: "google_client_secret"
    redirect_uri: "https://example.com/auth/google/callback"
    auto_create_user: true
    require_email: true
```

### 3. 数据库表结构

#### 第三方账户表

```sql
CREATE TABLE third_party_accounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    provider VARCHAR(20) NOT NULL COMMENT '第三方平台：apple, wechat, google',
    external_user_id VARCHAR(255) NOT NULL COMMENT '第三方用户ID',
    external_username VARCHAR(255) COMMENT '第三方用户名',
    external_email VARCHAR(255) COMMENT '第三方邮箱',
    external_avatar VARCHAR(500) COMMENT '第三方头像',
    status VARCHAR(20) NOT NULL DEFAULT 'active' COMMENT '状态：active, inactive',
    bind_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_provider (user_id, provider),
    UNIQUE KEY uk_provider_external_id (provider, external_user_id),
    KEY idx_external_user_id (external_user_id),
    KEY idx_user_id (user_id),
    KEY idx_provider (provider)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方账户绑定表';
```

## API接口使用

### 1. 第三方登录

```bash
curl -X POST http://localhost:8080/api/user/auth/third-party/login \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": 1,
    "provider": "apple",
    "authorization_code": "auth_code_xxx",
    "user_info": {
      "sub": "001234.567890abcdef.1234",
      "email": "<EMAIL>",
      "name": {
        "firstName": "John",
        "lastName": "Doe"
      }
    }
  }'
```

### 2. 获取第三方配置

```bash
curl -X POST http://localhost:8080/api/user/auth/third-party/config \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "apple"
  }'
```

### 3. 获取第三方账户列表

```bash
curl -X POST http://localhost:8080/api/user/auth/third-party/list \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "page": 1,
    "size": 20
  }'
```

### 4. 解绑第三方账户

```bash
curl -X POST http://localhost:8080/api/user/auth/third-party/unbind \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "provider": "apple"
  }'
```

## 前端集成示例

### React组件示例

```tsx
import React, { useState } from 'react';

interface ThirdPartyLoginProps {
  provider: 'apple' | 'wechat' | 'google';
  tenantId: number;
}

const ThirdPartyLogin: React.FC<ThirdPartyLoginProps> = ({ provider, tenantId }) => {
  const [loading, setLoading] = useState(false);

  const handleThirdPartyLogin = async (authorizationCode: string, userInfo: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/auth/third-party/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenant_id: tenantId,
          provider,
          authorization_code: authorizationCode,
          user_info: userInfo,
        }),
      });

      const result = await response.json();
      if (result.code === 0) {
        // 登录成功，保存token
        localStorage.setItem('access_token', result.data.access_token);
        localStorage.setItem('refresh_token', result.data.refresh_token);
        
        // 跳转到首页
        window.location.href = '/dashboard';
      } else {
        alert(result.message);
      }
    } catch (error) {
      console.error('Third party login failed:', error);
      alert('登录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={() => {
        // 这里需要集成具体的第三方SDK
        // 例如：Apple Sign In、微信SDK、Google Sign In
        console.log(`Login with ${provider}`);
      }}
      disabled={loading}
    >
      {loading ? '登录中...' : `使用${provider}登录`}
    </button>
  );
};

export default ThirdPartyLogin;
```

## 错误处理

### 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 2001 | 第三方平台配置错误 | 检查配置文件 |
| 2002 | 第三方平台服务异常 | 检查第三方服务状态 |
| 2003 | 第三方授权码无效 | 重新获取授权码 |
| 2004 | 第三方用户信息获取失败 | 检查用户信息格式 |
| 2005 | 第三方账户已绑定其他用户 | 提示用户账户冲突 |
| 2006 | 第三方账户绑定失败 | 重试绑定操作 |
| 2007 | 第三方账户解绑失败 | 重试解绑操作 |
| 2008 | 第三方令牌刷新失败 | 重新登录 |
| 2009 | 第三方平台不支持 | 检查平台配置 |
| 2010 | 第三方登录已禁用 | 联系管理员启用 |

### 错误处理示例

```go
func handleThirdPartyError(err error) {
    switch {
    case strings.Contains(err.Error(), "第三方平台配置错误"):
        // 记录错误日志，通知管理员
        logger.Error("Third party config error", "error", err)
        return "系统配置错误，请联系管理员"
        
    case strings.Contains(err.Error(), "第三方授权码无效"):
        // 提示用户重新授权
        return "授权失败，请重新登录"
        
    case strings.Contains(err.Error(), "第三方账户已绑定其他用户"):
        // 提示用户账户冲突
        return "该第三方账户已绑定其他用户"
        
    default:
        // 通用错误处理
        logger.Error("Third party login error", "error", err)
        return "登录失败，请重试"
    }
}
```

## 安全考虑

### 1. Token安全

- 验证第三方Token签名
- 检查Token有效期
- 验证Token来源

### 2. 用户信息保护

- 敏感信息加密存储
- 遵循数据隐私法规
- 最小化数据收集

### 3. 防刷机制

- 登录接口限流
- IP地址限流
- 异常行为检测

## 监控和日志

### 关键指标

- 第三方登录成功率
- 用户自动创建率
- 各平台登录分布
- 接口响应时间

### 日志记录

```go
// 登录尝试
logger.Info(ctx, "third party login attempt",
    logiface.String("provider", provider),
    logiface.Int64("tenant_id", tenantID),
    logiface.String("ip", ipAddress),
)

// 登录成功
logger.Info(ctx, "third party login successful",
    logiface.String("provider", provider),
    logiface.Int64("user_id", userID),
    logiface.String("external_user_id", externalUserID),
)

// 登录失败
logger.Error(ctx, "third party login failed",
    logiface.Error(err),
    logiface.String("provider", provider),
    logiface.String("reason", err.Error()),
)
```

## 测试建议

### 1. 单元测试

- 测试第三方验证器
- 测试用户创建逻辑
- 测试配置服务

### 2. 集成测试

- 测试完整的登录流程
- 测试错误处理
- 测试并发登录

### 3. 端到端测试

- 测试前端集成
- 测试用户体验
- 测试错误恢复

## 部署注意事项

### 1. 环境配置

- 确保第三方平台配置正确
- 验证回调URL配置
- 检查网络连接

### 2. 安全配置

- 配置HTTPS
- 设置CORS策略
- 配置防火墙规则

### 3. 监控配置

- 设置告警规则
- 配置日志收集
- 监控关键指标

## 常见问题

### Q: 如何处理第三方平台服务不可用？

A: 实现降级策略，当第三方服务不可用时，提示用户使用传统登录方式。

### Q: 如何处理用户取消授权？

A: 在授权流程中检测用户取消操作，并给出友好提示。

### Q: 如何处理第三方平台配置变更？

A: 实现配置热更新机制，支持运行时更新第三方平台配置。

### Q: 如何处理用户信息变更？

A: 定期同步第三方用户信息，并在用户主动更新时刷新本地数据。 