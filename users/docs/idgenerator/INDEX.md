# ID生成器文档索引

## 📚 文档目录

### 核心文档
- **[README.md](./README.md)** - 分布式ID生成器详细设计与实现文档
  - 系统架构设计
  - 数据模型定义
  - 核心算法实现
  - 性能优化策略
  - 资源不足处理机制
  - 缓存机制详解
  - 监控告警配置
  - 故障恢复与容错

### 使用指南
- **[USAGE_EXAMPLE.md](./USAGE_EXAMPLE.md)** - ID生成器使用示例
  - 基本使用方法
  - 批量ID生成
  - 错误处理示例
  - 最佳实践

## 🏗️ 系统架构

### 核心组件
1. **ID生成服务** (`IDGeneratorService`)
   - 主要ID生成逻辑
   - 缓存管理
   - 重试机制

2. **预分配器** (`PreAllocator`)
   - 自动预分配ID段
   - 定时监控
   - 批量创建

3. **仓储层** (`Repository`)
   - 序列管理
   - 分配管理
   - 数据持久化

### 数据表
1. **id_sequence** - 序列定义表
2. **id_allocation** - ID段分配表

## 🔧 主要功能

### ID生成
- 单ID生成
- 批量ID生成
- 多租户支持
- 业务类型隔离

### 性能优化
- 预分配机制
- 内存缓存
- 异步预加载
- 索引优化

### 容错机制
- 重试策略
- 故障恢复
- 资源耗尽处理
- 数据一致性保证

## 📊 监控指标

### 性能指标
- ID生成QPS
- 响应时间
- 缓存命中率
- 段分配成功率

### 告警规则
- 可用段不足
- ID生成失败率过高
- 响应时间过长
- 序列耗尽

## 🚀 快速开始

### 1. 数据库初始化
```bash
mysql -u root -p < database_schema.sql
```

### 2. 服务启动
```go
// 初始化服务
preAllocator := service.NewPreAllocator(sequenceRepo, allocationRepo, logger)
idGeneratorService := service.NewIDGeneratorService(sequenceRepo, allocationRepo, preAllocator, logger)

// 启动预分配器
preAllocator.Start()

// 注册gRPC服务
grpcService := grpc.NewIdGeneratorServiceImpl(idGeneratorService, logger)
idgeneratorpb.RegisterIdGeneratorServiceServer(grpcServer, grpcService)
```

### 3. 使用示例
```go
// 生成用户ID
userID, err := idGeneratorService.GenerateUserID(ctx, tenantID)

// 生成角色ID
roleID, err := idGeneratorService.GenerateRoleID(ctx, tenantID)

// 批量生成ID
ids, err := idGeneratorService.GenerateBatchIDs(ctx, "order", tenantID, 100)
```

## 📈 性能基准

- **单机QPS**: 100万+
- **响应时间**: P99 < 1ms
- **并发安全**: 支持多节点同时生成
- **可用性**: 99.99%

## 🔍 故障排查

### 常见问题
1. **ID生成失败**
   - 检查可用段数量
   - 查看序列配置
   - 验证数据库连接

2. **性能问题**
   - 检查缓存命中率
   - 分析索引使用情况
   - 监控预分配状态

3. **数据一致性问题**
   - 检查事务处理
   - 验证原子性操作
   - 查看并发日志

## 📞 技术支持

如有问题，请参考：
1. [README.md](./README.md) - 详细技术文档
2. [USAGE_EXAMPLE.md](./USAGE_EXAMPLE.md) - 使用示例
3. 系统日志 - 运行时信息
4. 监控面板 - 性能指标

---

*最后更新: 2024年12月* 