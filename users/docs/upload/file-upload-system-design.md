# 文件上传系统设计文档

## 1. 系统概述

### 1.1 功能需求
- 支持配置场景，不同场景可配置允许的类型、大小、是否为临时文件、是否开启文件去重
- 支持上传永久文件和临时文件（如导出excel等），临时文件支持设置过期时间
- 支持直接通过OSS直传，服务端生成临时授权信息和上传key，客户端直接上传
- 支持通过服务端接口上传
- 上传后分两步实现：先设置几十分钟级别的过期时间，用户触发提交请求后，由业务系统向文件服务发送标记永久存储的请求
- 暂不实现文件清理和监控功能
- 不支持分片设计，服务端上传文件大小会控制

### 1.2 架构原则
- 遵循DDD（领域驱动设计）架构
- 复用users模块现有的代码结构和设计模式
- 保持与现有系统的技术栈一致性
- 遵循Clean Architecture原则
- **优先使用pkg模块的公共能力**，包括错误处理、日志、中间件等
- **统一使用go-playground/validator/v10进行参数验证**
- **遵循统一的错误码规范和响应格式**

## 2. 领域模型设计

### 2.1 核心实体

#### 2.1.1 文件上传配置 (FileUploadConfig)
```go
type FileUploadConfig struct {
    ID              int64     `json:"id" gorm:"primaryKey"`
    TenantID        int64     `json:"tenant_id" gorm:"not null"`
    SceneCode       string    `json:"scene_code" gorm:"not null;size:50"`        // 场景编码
    SceneName       string    `json:"scene_name" gorm:"not null;size:100"`       // 场景名称
    AllowedTypes    string    `json:"allowed_types" gorm:"type:text"`            // 允许的文件类型，JSON格式
    MaxFileSize     int64     `json:"max_file_size" gorm:"not null"`             // 最大文件大小（字节）
    IsTemporary     bool      `json:"is_temporary" gorm:"default:false"`         // 是否为临时文件
    EnableDedup     bool      `json:"enable_dedup" gorm:"default:false"`         // 是否开启文件去重
    TempExpireTime  int       `json:"temp_expire_time" gorm:"default:30"`        // 临时文件过期时间（分钟）
    IsSystemScene   bool      `json:"is_system_scene" gorm:"default:false"`      // 是否为系统场景
    ExtraConfig     string    `json:"extra_config" gorm:"type:text"`             // 额外配置信息，JSON格式
    Status          string    `json:"status" gorm:"type:varchar(20);default:'active'"`
    CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt       time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}
```

#### 2.1.2 文件记录 (FileRecord)
```go
type FileRecord struct {
    ID              int64      `json:"id" gorm:"primaryKey"`
    TenantID        int64      `json:"tenant_id" gorm:"not null"`
    UserID          int64      `json:"user_id" gorm:"not null"`
    SceneCode       string     `json:"scene_code" gorm:"not null;size:50"`
    FileName        string     `json:"file_name" gorm:"not null;size:255"`
    FileSize        int64      `json:"file_size" gorm:"not null"`
    FileType        string     `json:"file_type" gorm:"not null;size:50"`
    FileHash        string     `json:"file_hash" gorm:"size:64"`                 // 文件MD5哈希
    StoragePath     string     `json:"storage_path" gorm:"not null;size:500"`    // 存储路径
    StorageType     string     `json:"storage_type" gorm:"not null;size:20"`     // 存储类型：oss, local
    AccessURL       string     `json:"access_url" gorm:"size:500"`               // 访问URL
    IsTemporary     bool       `json:"is_temporary" gorm:"default:true"`
    IsPermanent     bool       `json:"is_permanent" gorm:"default:false"`        // 是否已标记为永久
    ExpireAt        *time.Time `json:"expire_at"`                                // 过期时间
    Status          string     `json:"status" gorm:"type:varchar(20);default:'active'"`
    Meta            string     `json:"meta" gorm:"type:text"`                     // 元数据信息，JSON格式
    DeletedAt       *time.Time `json:"deleted_at" gorm:"index"`                   // 软删除时间
    CreatedAt       time.Time  `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt       time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}
```

#### 2.1.3 上传令牌 (UploadToken)
```go
type UploadToken struct {
    ID              int64     `json:"id" gorm:"primaryKey"`
    TenantID        int64     `json:"tenant_id" gorm:"not null"`
    UserID          int64     `json:"user_id" gorm:"not null"`
    SceneCode       string    `json:"scene_code" gorm:"not null;size:50"`
    Token           string    `json:"token" gorm:"not null;size:255;uniqueIndex"`
    OSSConfig       string    `json:"oss_config" gorm:"type:text"`               // OSS配置信息，JSON格式
    ExpireAt        time.Time `json:"expire_at" gorm:"not null"`                 // 令牌过期时间（通常30-60分钟）
    CreatedAt       time.Time `json:"created_at" gorm:"autoCreateTime"`
}
```

### 2.2 值对象

#### 2.2.1 文件类型 (FileType)
```go
type FileType struct {
    Extension string `json:"extension"`
    MimeType  string `json:"mime_type"`
    MaxSize   int64  `json:"max_size"`
}
```

#### 2.2.2 上传场景 (UploadScene)
```go
type UploadScene struct {
    Code        string     `json:"code"`
    Name        string     `json:"name"`
    Config      *FileUploadConfig `json:"config"`
}
```

#### 2.2.3 额外配置信息 (ExtraConfig)
```go
type ExtraConfig struct {
    // 存储相关配置
    StorageConfig *StorageConfig `json:"storage_config,omitempty"`
    
    // 处理相关配置
    ProcessConfig *ProcessConfig `json:"process_config,omitempty"`
    
    // 安全相关配置
    SecurityConfig *SecurityConfig `json:"security_config,omitempty"`
    
    // 业务相关配置
    BusinessConfig *BusinessConfig `json:"business_config,omitempty"`
    
    // 自定义配置
    CustomConfig map[string]interface{} `json:"custom_config,omitempty"`
}

type StorageConfig struct {
    StorageType     string `json:"storage_type"`      // 存储类型：oss, local, s3
    BucketName      string `json:"bucket_name"`       // 存储桶名称
    Region          string `json:"region"`            // 存储区域
    PathPrefix      string `json:"path_prefix"`       // 路径前缀
    EnableCDN       bool   `json:"enable_cdn"`        // 是否启用CDN
    CDNDomain       string `json:"cdn_domain"`        // CDN域名
}

type ProcessConfig struct {
    EnableCompress  bool   `json:"enable_compress"`   // 是否启用压缩
    EnableWatermark bool   `json:"enable_watermark"`  // 是否启用水印
    WatermarkText   string `json:"watermark_text"`    // 水印文字
    EnableResize    bool   `json:"enable_resize"`     // 是否启用图片缩放
    MaxWidth        int    `json:"max_width"`         // 最大宽度
    MaxHeight       int    `json:"max_height"`        // 最大高度
    Quality         int    `json:"quality"`           // 图片质量(1-100)
}

type SecurityConfig struct {
    EnableScan      bool   `json:"enable_scan"`       // 是否启用文件扫描
    ScanType        string `json:"scan_type"`         // 扫描类型：virus, content
    EnableEncrypt   bool   `json:"enable_encrypt"`    // 是否启用加密
    EncryptType     string `json:"encrypt_type"`      // 加密类型：aes, rsa
    AccessControl   string `json:"access_control"`    // 访问控制：public, private, signed
    SignedExpire    int    `json:"signed_expire"`     // 签名URL过期时间(秒)
}

type BusinessConfig struct {
    Category        string `json:"category"`          // 文件分类
    Tags            string `json:"tags"`              // 文件标签，逗号分隔
    AutoArchive     bool   `json:"auto_archive"`      // 是否自动归档
    ArchiveDays     int    `json:"archive_days"`      // 归档天数
    EnableVersion   bool   `json:"enable_version"`    // 是否启用版本控制
    MaxVersions     int    `json:"max_versions"`      // 最大版本数
}
```

## 3. 应用层设计

### 3.1 应用服务

#### 3.1.1 文件上传应用服务 (FileUploadApplicationService)
```go
type FileUploadApplicationService struct {
    *BaseService
    fileConfigRepo FileUploadConfigRepository
    fileRecordRepo FileRecordRepository
    uploadTokenRepo UploadTokenRepository
    storageService  StorageService
    logger         logiface.Logger  // 使用pkg/logiface统一日志接口
}

    // 主要方法：
- CreateUploadToken(ctx context.Context, req *dto.CreateUploadTokenRequest) (*dto.CreateUploadTokenResponse, error)
- UploadFile(ctx context.Context, sceneCode string, req *dto.UploadFileRequest) (*dto.UploadFileResponse, error)
- GetFileInfo(ctx context.Context, fileID int64) (*dto.FileInfoResponse, error)
- GetFileRecordByID(ctx context.Context, fileID int64) (*FileRecord, error)
- GetFileRecordByURL(ctx context.Context, fileURL string) (*FileRecord, error)
- MarkFilePermanent(ctx context.Context, fileID int64) error
- DeleteFile(ctx context.Context, req *dto.DeleteFileRequest) error
- CreateFileAccessToken(ctx context.Context, req *dto.CreateFileAccessTokenRequest) (*dto.CreateFileAccessTokenResponse, error)
```

#### 3.1.2 文件配置应用服务 (FileConfigApplicationService)
```go
type FileConfigApplicationService struct {
    *BaseService
    fileConfigRepo FileUploadConfigRepository
    logger         logiface.Logger  // 使用pkg/logiface统一日志接口
}

// 主要方法：
- CreateConfig(ctx context.Context, req *dto.CreateFileConfigRequest) (*FileUploadConfig, error)
- UpdateConfig(ctx context.Context, id int64, req *dto.UpdateFileConfigRequest) (*FileUploadConfig, error)
- GetConfig(ctx context.Context, id int64) (*FileUploadConfig, error)
- GetConfigByScene(ctx context.Context, tenantID int64, sceneCode string) (*FileUploadConfig, error)
- ListConfigs(ctx context.Context, req *dto.ListFileConfigsRequest) (*dto.ListFileConfigsResponse, error)
- DeleteConfig(ctx context.Context, id int64) error
```

### 3.2 DTO设计

#### 3.2.1 上传相关DTO
```go
// 创建上传令牌请求 - 用于OSS直传
type CreateUploadTokenRequest struct {
    SceneCode string `json:"scene_code" binding:"required,max=50"`     // 场景编码
}
```

// 上传令牌响应
type CreateUploadTokenResponse struct {
    Token       string                 `json:"token"`                    // 上传令牌
    OSSConfig   map[string]interface{} `json:"oss_config"`               // OSS配置信息
    ExpireAt    time.Time              `json:"expire_at"`                // 令牌过期时间
}
```
```

// 服务端上传请求 - 用于服务端上传
type UploadFileRequest struct {
    File      *multipart.FileHeader `form:"file" binding:"required"`   // 文件对象
}

// 服务端上传响应
type UploadFileResponse struct {
    FileID      int64      `json:"file_id"`                           // 文件记录ID
    FileName    string     `json:"file_name"`                         // 文件名
    FileSize    int64      `json:"file_size"`                         // 文件大小
    FileType    string     `json:"file_type"`                         // 文件类型
    AccessURL   string     `json:"access_url"`                        // 访问URL
    IsTemporary bool       `json:"is_temporary"`                      // 是否为临时文件
    ExpireAt    *time.Time `json:"expire_at,omitempty"`               // 过期时间
    CreatedAt   time.Time  `json:"created_at"`                        // 创建时间
}



// 文件信息响应
type FileInfoResponse struct {
    ID          int64      `json:"id"`                                // 文件ID
    FileName    string     `json:"file_name"`                         // 文件名
    FileSize    int64      `json:"file_size"`                         // 文件大小
    FileType    string     `json:"file_type"`                         // 文件类型
    FileHash    string     `json:"file_hash,omitempty"`               // 文件哈希
    AccessURL   string     `json:"access_url"`                        // 访问URL
    IsTemporary bool       `json:"is_temporary"`                      // 是否为临时文件
    IsPermanent bool       `json:"is_permanent"`                      // 是否已标记永久
    ExpireAt    *time.Time `json:"expire_at,omitempty"`               // 过期时间
    CreatedAt   time.Time  `json:"created_at"`                        // 创建时间
}



// 文件删除请求
type DeleteFileRequest struct {
    FileID int64 `json:"file_id" binding:"required,min=1"` // 文件ID
}

// 文件访问令牌请求
type CreateFileAccessTokenRequest struct {
    FileID    int64  `json:"file_id" binding:"required,min=1"`        // 文件ID
    ExpireIn  int    `json:"expire_in" binding:"min=60,max=86400"`    // 过期时间（秒）
}

// 文件访问令牌响应
type CreateFileAccessTokenResponse struct {
    AccessToken string    `json:"access_token"`                       // 访问令牌
    AccessURL   string    `json:"access_url"`                         // 访问URL
    ExpireAt    time.Time `json:"expire_at"`                          // 过期时间
}


```

## 4. 基础设施层设计

### 4.1 仓储接口

#### 4.1.1 文件配置仓储 (FileUploadConfigRepository)
```go
type FileUploadConfigRepository interface {
    Create(ctx context.Context, config *FileUploadConfig) error
    Update(ctx context.Context, config *FileUploadConfig) error
    FindByID(ctx context.Context, id int64) (*FileUploadConfig, error)
    FindByScene(ctx context.Context, tenantID int64, sceneCode string) (*FileUploadConfig, error)
    Find(ctx context.Context, params *QueryParams) (*QueryResult, error)
    Delete(ctx context.Context, id int64) error
}
```

#### 4.1.2 文件记录仓储 (FileRecordRepository)
```go
type FileRecordRepository interface {
    Create(ctx context.Context, record *FileRecord) error
    Update(ctx context.Context, record *FileRecord) error
    FindByID(ctx context.Context, id int64) (*FileRecord, error)
    FindByURL(ctx context.Context, tenantID int64, accessURL string) (*FileRecord, error)
    FindByHash(ctx context.Context, tenantID int64, fileHash string) (*FileRecord, error)
    Find(ctx context.Context, params *QueryParams) (*QueryResult, error)
    MarkPermanent(ctx context.Context, fileID int64) error
    Delete(ctx context.Context, id int64) error
}
```

#### 4.1.3 上传令牌仓储 (UploadTokenRepository)
```go
type UploadTokenRepository interface {
    Create(ctx context.Context, token *UploadToken) error
    FindByToken(ctx context.Context, token string) (*UploadToken, error)
    FindBySceneCode(ctx context.Context, tenantID int64, sceneCode string) (*UploadToken, error)
    DeleteExpired(ctx context.Context) error
}
```

### 4.2 存储服务

#### 4.2.1 存储服务接口 (StorageService)
```go
type StorageService interface {
    // OSS直传相关
    GenerateUploadToken(ctx context.Context, sceneCode string) (*OSSUploadInfo, error)
    
    // 服务端上传
    UploadFile(ctx context.Context, file *multipart.FileHeader, storagePath string) error
    
    // 文件管理
    GetFileURL(ctx context.Context, storagePath string) (string, error)
    DeleteFile(ctx context.Context, storagePath string) error
    FileExists(ctx context.Context, storagePath string) (bool, error)
}

type OSSUploadInfo struct {
    OSSConfig  map[string]interface{} `json:"oss_config"`  // OSS配置信息
    ExpireAt   time.Time              `json:"expire_at"`   // 过期时间
}
```

## 5. 接口层设计

### 5.1 HTTP处理器

#### 5.1.1 文件上传处理器 (FileUploadHandler)
```go
type FileUploadHandler struct {
    fileUploadService *FileUploadApplicationService
    fileConfigService *FileConfigApplicationService
    logger            logiface.Logger
}

// 主要接口：
- POST /api/file/upload-token     // 创建上传令牌（OSS直传）
- POST /api/file/upload           // 服务端上传文件
- GET  /api/file/:id              // 获取文件信息
- DELETE /api/file/:id            // 删除文件（软删除）
- POST /api/file/access-token     // 创建文件访问令牌

```

#### 5.1.2 文件配置处理器 (FileConfigHandler)
```go
type FileConfigHandler struct {
    fileConfigService *FileConfigApplicationService
    logger            logiface.Logger
}

// 主要接口：
- POST   /api/file/config         // 创建上传配置
- PUT    /api/file/config/:id     // 更新上传配置
- GET    /api/file/config/:id     // 获取上传配置
- GET    /api/file/config/scene/:sceneCode // 根据场景获取配置
- GET    /api/file/config/list    // 获取配置列表
- DELETE /api/file/config/:id     // 删除上传配置
```

### 5.2 gRPC服务

#### 5.2.1 文件上传gRPC服务 (FileUploadService)
```protobuf
service FileUploadService {
    // 批量标记文件为永久存储
    rpc MarkFilesPermanent(MarkFilesPermanentRequest) returns (MarkFilesPermanentResponse);
    
    // 检查文件是否存在
    rpc CheckFileExists(CheckFileExistsRequest) returns (CheckFileExistsResponse);
}

message MarkFilesPermanentRequest {
    repeated FileIdentifier identifiers = 1;  // 文件标识符列表
}

message FileIdentifier {
    oneof identifier {
        int64 file_id = 1;        // 文件ID
        string file_url = 2;      // 文件URL
    }
}

message MarkFilesPermanentResponse {
    bool success = 1;                    // 整体操作是否成功
    string message = 2;                  // 响应消息
    repeated FileResult results = 3;     // 每个文件的处理结果
    int32 success_count = 4;             // 成功处理的文件数量
    int32 failure_count = 5;             // 失败的文件数量
}

message FileResult {
    FileIdentifier identifier = 1;       // 文件标识符
    bool success = 2;                    // 该文件处理是否成功
    string message = 3;                  // 该文件的处理消息
    FileInfo file_info = 4;              // 文件信息（如果成功）
}

message CheckFileExistsRequest {
    string file_hash = 1;
}

message CheckFileExistsResponse {
    bool exists = 1;
    FileInfo file_info = 2;
}

message FileInfo {
    int64 id = 1;
    string file_name = 2;
    int64 file_size = 3;
    string file_type = 4;
    string access_url = 5;
}
```

#### 5.2.2 gRPC服务实现
```go
type FileUploadGRPCService struct {
    fileUploadService *FileUploadApplicationService
    logger            logiface.Logger
    pb.UnimplementedFileUploadServiceServer
}

func (s *FileUploadGRPCService) MarkFilesPermanent(ctx context.Context, req *pb.MarkFilesPermanentRequest) (*pb.MarkFilesPermanentResponse, error) {
    if len(req.Identifiers) == 0 {
        return &pb.MarkFilesPermanentResponse{
            Success: false,
            Message: "文件标识符列表不能为空",
        }, nil
    }
    
    if len(req.Identifiers) > 100 {
        return &pb.MarkFilesPermanentResponse{
            Success: false,
            Message: "单次最多处理100个文件",
        }, nil
    }
    
    var results []*pb.FileResult
    successCount := int32(0)
    failureCount := int32(0)
    
    // 批量处理文件
    for _, identifier := range req.Identifiers {
        result := &pb.FileResult{
            Identifier: identifier,
        }
        
        // 根据file_id或file_url查找文件记录
        var fileRecord *FileRecord
        var err error
        
        switch identifier.Identifier.(type) {
        case *pb.FileIdentifier_FileId:
            fileRecord, err = s.fileUploadService.GetFileRecordByID(ctx, identifier.GetFileId())
        case *pb.FileIdentifier_FileUrl:
            fileRecord, err = s.fileUploadService.GetFileRecordByURL(ctx, identifier.GetFileUrl())
        default:
            result.Success = false
            result.Message = "必须提供file_id或file_url"
            failureCount++
            results = append(results, result)
            continue
        }
        
        if err != nil {
            result.Success = false
            result.Message = fmt.Sprintf("查找文件失败: %v", err)
            failureCount++
            results = append(results, result)
            continue
        }
        
        if fileRecord == nil {
            result.Success = false
            result.Message = "文件不存在"
            failureCount++
            results = append(results, result)
            continue
        }
        
        // 标记文件为永久存储
        err = s.fileUploadService.MarkFilePermanent(ctx, fileRecord.ID)
        if err != nil {
            result.Success = false
            result.Message = fmt.Sprintf("标记永久存储失败: %v", err)
            failureCount++
            results = append(results, result)
            continue
        }
        
        // 处理成功
        result.Success = true
        result.Message = "文件已标记为永久存储"
        result.FileInfo = &pb.FileInfo{
            Id:        fileRecord.ID,
            FileName:   fileRecord.FileName,
            FileSize:   fileRecord.FileSize,
            FileType:   fileRecord.FileType,
            AccessUrl:  fileRecord.AccessURL,
        }
        successCount++
        results = append(results, result)
    }
    
    // 返回批量处理结果
    return &pb.MarkFilesPermanentResponse{
        Success:      successCount > 0,
        Message:      fmt.Sprintf("批量处理完成，成功: %d, 失败: %d", successCount, failureCount),
        Results:      results,
        SuccessCount: successCount,
        FailureCount: failureCount,
    }, nil
}

func (s *FileUploadGRPCService) CheckFileExists(ctx context.Context, req *pb.CheckFileExistsRequest) (*pb.CheckFileExistsResponse, error) {
    // 实现检查文件是否存在的逻辑
}
```

### 5.3 中间件

#### 5.3.1 文件上传中间件
```go
// 文件大小限制中间件
func FileSizeLimitMiddleware(maxSize int64) gin.HandlerFunc

// 文件类型验证中间件
func FileTypeValidationMiddleware(allowedTypes []string) gin.HandlerFunc

// 上传频率限制中间件
func UploadRateLimitMiddleware(limit int, window time.Duration) gin.HandlerFunc
```

#### 5.3.2 公共中间件集成
```go
// 使用pkg/httpmiddleware的公共中间件
import "platforms-pkg/httpmiddleware"

// 访问日志中间件
httpmiddleware.AccessLogMiddleware(logger)

// OpenTelemetry调用链追踪中间件
httpmiddleware.GinOtelMiddleware(serviceName)

// 请求ID中间件（包含在AccessLogMiddleware中）
// 自动生成和传递请求ID，用于日志关联
```

## 6. 数据库设计

### 6.1 表结构

#### 6.1.1 文件上传配置表 (file_upload_configs)
```sql
CREATE TABLE file_upload_configs (
    id BIGINT PRIMARY KEY COMMENT '分布式ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    scene_code VARCHAR(50) NOT NULL COMMENT '场景编码',
    scene_name VARCHAR(100) NOT NULL COMMENT '场景名称',
    allowed_types TEXT COMMENT '允许的文件类型，JSON格式',
    max_file_size BIGINT NOT NULL COMMENT '最大文件大小（字节）',
    is_temporary BOOLEAN DEFAULT FALSE COMMENT '是否为临时文件',
    enable_dedup BOOLEAN DEFAULT FALSE COMMENT '是否开启文件去重',
    temp_expire_time INT DEFAULT 30 COMMENT '临时文件过期时间（分钟）',
    is_system_scene BOOLEAN DEFAULT FALSE COMMENT '是否为系统场景',
    extra_config TEXT COMMENT '额外配置信息，JSON格式',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_config_tenant_scene (tenant_id, scene_code),
    INDEX idx_config_tenant_id (tenant_id),
    INDEX idx_config_scene_code (scene_code),
    INDEX idx_config_status (status),
    INDEX idx_config_system_scene (is_system_scene)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置表';
```

#### 6.1.2 文件记录表 (file_records)
```sql
CREATE TABLE file_records (
    id BIGINT PRIMARY KEY COMMENT '分布式ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '上传用户ID',
    scene_code VARCHAR(50) NOT NULL COMMENT '场景编码',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    file_hash VARCHAR(64) COMMENT '文件MD5哈希',
    storage_path VARCHAR(500) NOT NULL COMMENT '存储路径',
    storage_type VARCHAR(20) NOT NULL COMMENT '存储类型：oss, local',
    access_url VARCHAR(500) COMMENT '访问URL',
    is_temporary BOOLEAN DEFAULT TRUE COMMENT '是否为临时文件',
    is_permanent BOOLEAN DEFAULT FALSE COMMENT '是否已标记为永久',
    expire_at DATETIME COMMENT '过期时间',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    meta TEXT COMMENT '元数据信息，JSON格式',
    deleted_at DATETIME COMMENT '软删除时间',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_file_tenant_id (tenant_id),
    INDEX idx_file_user_id (user_id),
    INDEX idx_file_scene_code (scene_code),
    INDEX idx_file_hash (file_hash),
    INDEX idx_file_temporary (is_temporary),
    INDEX idx_file_permanent (is_permanent),
    INDEX idx_file_expire_at (expire_at),
    INDEX idx_file_status (status),
    INDEX idx_file_created_at (created_at),
    INDEX idx_file_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件记录表';
```

#### 6.1.3 上传令牌表 (upload_tokens)
```sql
CREATE TABLE upload_tokens (
    id BIGINT PRIMARY KEY COMMENT '分布式ID',
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    scene_code VARCHAR(50) NOT NULL COMMENT '场景编码',
    token VARCHAR(255) NOT NULL UNIQUE COMMENT '上传令牌',
    oss_config TEXT COMMENT 'OSS配置信息，JSON格式',
    expire_at DATETIME NOT NULL COMMENT '过期时间（通常30-60分钟）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_token_tenant_id (tenant_id),
    INDEX idx_token_user_id (user_id),
    INDEX idx_token_scene_code (scene_code),
    INDEX idx_token_expire_at (expire_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传令牌表';
```

## 7. 公共组件集成

### 7.1 错误处理集成
```go
import "platforms-pkg/common/errors"

// 在HTTP处理器中使用统一错误处理
func (h *FileUploadHandler) CreateUploadToken(c *gin.Context) {
    var req dto.CreateUploadTokenRequest
    
    // 使用go-playground/validator/v10进行参数验证
    if err := c.ShouldBindJSON(&req); err != nil {
        errors.GinValidationError(c, err)
        return
    }
    
    // 业务逻辑处理
    result, err := h.fileUploadService.CreateUploadToken(c.Request.Context(), &req)
    if err != nil {
        // 根据错误类型返回相应的错误码
        switch {
        case errors.Is(err, ErrConfigNotFound):
            errors.NotFound(c, "上传配置")
        case errors.Is(err, ErrFileSizeExceeded):
            errors.BadRequest(c, "文件大小超出限制")
        default:
            errors.InternalError(c, err)
        }
        return
    }
    
    // 成功响应
    errors.Success(c, result)
}
```

### 7.2 日志集成
```go
import "platforms-pkg/logiface"

// 在应用服务中使用统一日志接口
func (s *FileUploadApplicationService) CreateUploadToken(ctx context.Context, req *dto.CreateUploadTokenRequest) (*dto.CreateUploadTokenResponse, error) {
    // 结构化日志记录
    s.logger.Info(ctx, "Creating upload token", 
        logiface.String("scene_code", req.SceneCode),
        logiface.String("file_name", req.FileName),
        logiface.Int64("file_size", req.FileSize),
        logiface.String("file_type", req.FileType))
    
    // 业务逻辑...
    
    s.logger.Info(ctx, "Upload token created successfully",
        logiface.String("token", result.Token),
        logiface.Time("expire_at", result.ExpireAt))
    
    return result, nil
}
```

### 7.3 中间件集成
```go
import "platforms-pkg/httpmiddleware"
import "platforms-pkg/otel"

// 在路由设置中集成公共中间件
func SetupFileUploadRoutes(r *gin.Engine, handlers *FileUploadHandler) {
    // 初始化OpenTelemetry
    otel.InitTracerProvider("file-upload-service", "http://jaeger:14268/api/traces")
    
    // 文件上传路由组
    fileGroup := r.Group("/api/file")
    {
        // 集成访问日志中间件
        fileGroup.Use(httpmiddleware.AccessLogMiddleware(logger))
        
        // 集成调用链追踪中间件
        fileGroup.Use(httpmiddleware.GinOtelMiddleware("file-upload-service"))
        
        // 文件上传相关路由
        fileGroup.POST("/upload-token", handlers.CreateUploadToken)
        fileGroup.POST("/upload", handlers.UploadFile)
        fileGroup.POST("/mark-permanent", handlers.MarkFilePermanent)
        fileGroup.GET("/:id", handlers.GetFileInfo)
        fileGroup.GET("/list", handlers.ListFiles)
        fileGroup.DELETE("/:id", handlers.DeleteFile)
    }
}
```

### 7.4 分布式ID生成
```go
import "platforms-pkg/id"

// 在实体创建时使用分布式ID生成器
func NewFileRecord(tenantID, userID int64, sceneCode, fileName string, fileSize int64, fileType string) *FileRecord {
    return &FileRecord{
        ID:          id.GenerateID(),  // 使用雪花算法生成分布式ID
        TenantID:    tenantID,
        UserID:      userID,
        SceneCode:   sceneCode,
        FileName:    fileName,
        FileSize:    fileSize,
        FileType:    fileType,
        Status:      "active",
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }
}
```

## 8. 业务流程设计

### 8.1 OSS直传流程
1. 客户端调用 `/api/file/upload-token` 获取上传令牌
   - 请求参数：场景编码
   - 服务端验证场景配置，生成可重复使用的上传令牌（有效期30-60分钟）
   - 返回：上传令牌、OSS配置、上传路径前缀、令牌过期时间
2. 客户端使用OSS SDK直接上传到OSS
   - 使用令牌和上传路径前缀，生成具体的上传路径
   - 可以使用同一个令牌上传多个文件（在有效期内）
   - 每次使用令牌时，服务端会记录使用次数
3. 客户端调用gRPC接口批量标记文件为永久存储
   - 通过 `MarkFilesPermanent` 方法，支持批量处理
   - 可以混合使用文件ID和文件URL作为标识符

### 8.2 服务端上传流程
1. 客户端调用 `/api/file/upload` 上传文件
   - 请求参数：场景编码、文件对象
   - 服务端验证文件类型、大小等
2. 服务端保存文件到存储系统
3. 创建文件记录，设置临时过期时间
4. 客户端调用gRPC接口批量标记文件为永久存储

### 8.3 文件去重机制
1. 计算文件MD5哈希
2. 查询是否存在相同哈希的文件记录
3. 如果存在且配置开启去重，返回已存在的文件记录
4. 如果不存在，创建新的文件记录

### 8.4 重复使用令牌机制
1. **令牌获取**：客户端只需要提供场景编码，服务端返回可重复使用的上传令牌
2. **路径生成**：客户端根据OSS配置和业务需求，自行生成上传路径
   - 例如：根据场景配置生成路径 `uploads/avatar/2024/01/user123.jpg`
3. **令牌复用**：在令牌有效期内，可以用于上传多个文件
4. **无状态设计**：令牌下发后，客户端直接与OSS交互，服务端不跟踪使用情况
5. **过期控制**：令牌过期后需要重新申请

## 9. 错误码定义

### 9.1 文件上传系统专用错误码 (170000-179999)

```go
// 文件上传配置错误码 (170000-170999)
const (
    CodeFileConfigNotFound      = 170001 // 文件上传配置不存在
    CodeFileConfigExists        = 170002 // 文件上传配置已存在
    CodeFileConfigSceneExists   = 170003 // 场景配置已存在
    CodeFileConfigInvalid       = 170004 // 文件上传配置无效
    CodeFileConfigDisabled      = 170005 // 文件上传配置已禁用
    CodeSystemSceneNotModifiable = 170006 // 系统场景不允许修改
)

// 文件记录错误码 (171000-171999)
const (
    CodeFileRecordNotFound      = 171001 // 文件记录不存在
    CodeFileRecordExists        = 171002 // 文件记录已存在
    CodeFileRecordExpired       = 171003 // 文件记录已过期
    CodeFileRecordInvalid       = 171004 // 文件记录无效
    CodeFileRecordDeleted       = 171005 // 文件记录已删除
)

// 文件上传错误码 (172000-172999)
const (
    CodeFileUploadFailed        = 172001 // 文件上传失败
    CodeFileSizeExceeded        = 172002 // 文件大小超出限制
    CodeFileTypeNotAllowed      = 172003 // 文件类型不允许
    CodeFileUploadTokenExpired  = 172004 // 上传令牌已过期
    CodeFileUploadTokenInvalid  = 172005 // 上传令牌无效
    CodeFileUploadRateLimited   = 172006 // 上传频率限制
    CodeFileHashMismatch        = 172007 // 文件哈希不匹配
    CodeFileStorageError        = 172008 // 文件存储错误
    CodeSceneCodeExists         = 172009 // 场景编码已存在

)

// 文件访问错误码 (173000-173999)
const (
    CodeFileAccessDenied        = 173001 // 文件访问被拒绝
    CodeFileAccessExpired       = 173002 // 文件访问已过期
    CodeFileAccessTokenInvalid  = 173003 // 文件访问令牌无效
    CodeFileAccessForbidden     = 173004 // 文件访问被禁止
)
```

### 9.2 错误码使用示例

```go
// 在应用服务中使用错误码
func (s *FileUploadApplicationService) CreateUploadToken(ctx context.Context, req *dto.CreateUploadTokenRequest) (*dto.CreateUploadTokenResponse, error) {
    // 从上下文获取租户ID和用户ID（按照规范，不从前端传递）
    tenantID := getTenantIDFromContext(ctx)
    userID := getUserIDFromContext(ctx)
    
    // 获取场景配置
    config, err := s.fileConfigRepo.FindByScene(ctx, tenantID, req.SceneCode)
    if err != nil {
        return nil, fmt.Errorf("failed to get config: %w", err)
    }
    if config == nil {
        return nil, &errors.BusinessError{
            Code:    CodeFileConfigNotFound,
            Message: "文件上传配置不存在",
        }
    }
    
    // 检查是否已有有效的上传令牌（可重复使用）
    existingToken, err := s.uploadTokenRepo.FindBySceneCode(ctx, tenantID, req.SceneCode)
    if err == nil && existingToken != nil && time.Now().Before(existingToken.ExpireAt) {
        // 返回已存在的有效令牌
        return &dto.CreateUploadTokenResponse{
            Token:       existingToken.Token,
            OSSConfig:   unmarshalOSSConfig(existingToken.OSSConfig),
            ExpireAt:    existingToken.ExpireAt,
        }, nil
    }
    
    // 生成OSS上传信息
    ossInfo, err := s.storageService.GenerateUploadToken(ctx, req.SceneCode)
    if err != nil {
        return nil, fmt.Errorf("failed to generate upload token: %w", err)
    }
    
    // 创建可重复使用的上传令牌记录
    uploadToken := &UploadToken{
        ID:          id.GenerateID(),
        TenantID:    tenantID,
        UserID:      userID,
        SceneCode:   req.SceneCode,
        Token:       generateToken(),
        OSSConfig:   marshalOSSConfig(ossInfo.OSSConfig),
        ExpireAt:    time.Now().Add(45 * time.Minute), // 45分钟有效期
        CreatedAt:   time.Now(),
    }
    
    if err := s.uploadTokenRepo.Create(ctx, uploadToken); err != nil {
        return nil, fmt.Errorf("failed to create upload token: %w", err)
    }
    
    return &dto.CreateUploadTokenResponse{
        Token:       uploadToken.Token,
        OSSConfig:   ossInfo.OSSConfig,
        ExpireAt:    uploadToken.ExpireAt,
    }, nil
}
```



```
```

## 10. 配置管理

### 10.1 额外配置使用说明

额外配置信息（ExtraConfig）采用JSON格式存储，提供了灵活的配置扩展能力，支持以下配置类型：

#### 8.1.1 存储配置 (StorageConfig)
- **storage_type**: 存储类型，支持 oss、local、s3 等
- **bucket_name**: 存储桶名称
- **region**: 存储区域
- **path_prefix**: 文件路径前缀
- **enable_cdn**: 是否启用CDN加速
- **cdn_domain**: CDN域名

#### 8.1.2 处理配置 (ProcessConfig)
- **enable_compress**: 是否启用文件压缩
- **enable_watermark**: 是否启用水印
- **watermark_text**: 水印文字内容
- **enable_resize**: 是否启用图片缩放
- **max_width/max_height**: 图片最大宽高
- **quality**: 图片质量（1-100）

#### 8.1.3 安全配置 (SecurityConfig)
- **enable_scan**: 是否启用文件扫描
- **scan_type**: 扫描类型（virus-病毒扫描，content-内容扫描）
- **enable_encrypt**: 是否启用文件加密
- **encrypt_type**: 加密类型（aes、rsa）
- **access_control**: 访问控制（public-公开、private-私有、signed-签名访问）
- **signed_expire**: 签名URL过期时间（秒）

#### 8.1.4 业务配置 (BusinessConfig)
- **category**: 文件分类
- **tags**: 文件标签（逗号分隔）
- **auto_archive**: 是否自动归档
- **archive_days**: 归档天数
- **enable_version**: 是否启用版本控制
- **max_versions**: 最大版本数

#### 8.1.5 自定义配置 (CustomConfig)
支持任意键值对的自定义配置，满足特定业务需求。

### 10.2 默认场景配置
```json
{
    "avatar": {
        "scene_name": "用户头像",
        "allowed_types": ["image/jpeg", "image/png", "image/gif"],
        "max_file_size": 5242880,
        "is_temporary": false,
        "enable_dedup": true,
        "temp_expire_time": 30,
        "extra_config": {
            "storage_config": {
                "storage_type": "oss",
                "bucket_name": "user-avatars",
                "path_prefix": "avatars",
                "enable_cdn": true,
                "cdn_domain": "cdn.example.com"
            },
            "process_config": {
                "enable_resize": true,
                "max_width": 200,
                "max_height": 200,
                "quality": 85,
                "enable_watermark": false
            },
            "security_config": {
                "access_control": "public",
                "enable_scan": true,
                "scan_type": "virus"
            },
            "business_config": {
                "category": "avatar",
                "tags": "user,profile",
                "enable_version": false
            }
        }
    },
    "document": {
        "scene_name": "文档上传",
        "allowed_types": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
        "max_file_size": 10485760,
        "is_temporary": true,
        "enable_dedup": true,
        "temp_expire_time": 60,
        "extra_config": {
            "storage_config": {
                "storage_type": "oss",
                "bucket_name": "documents",
                "path_prefix": "docs",
                "enable_cdn": false
            },
            "security_config": {
                "access_control": "private",
                "enable_scan": true,
                "scan_type": "virus",
                "enable_encrypt": true,
                "encrypt_type": "aes"
            },
            "business_config": {
                "category": "document",
                "tags": "business,office",
                "enable_version": true,
                "max_versions": 5
            }
        }
    },
    "export": {
        "scene_name": "导出文件",
        "allowed_types": ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],
        "max_file_size": 52428800,
        "is_temporary": true,
        "enable_dedup": false,
        "temp_expire_time": 120,
        "extra_config": {
            "storage_config": {
                "storage_type": "local",
                "path_prefix": "exports"
            },
            "process_config": {
                "enable_compress": true
            },
            "security_config": {
                "access_control": "signed",
                "signed_expire": 3600
            },
            "business_config": {
                "category": "export",
                "tags": "report,data",
                "auto_archive": true,
                "archive_days": 30
            }
        }
    }
}
```

## 11. 安全考虑

### 11.1 文件安全
- 文件类型白名单验证
- 文件大小限制
- 文件内容扫描（可选）
- 访问URL签名验证

### 11.2 上传安全
- 上传频率限制
- 用户权限验证
- 租户隔离
- 临时令牌过期机制

### 11.3 存储安全
- 文件存储路径随机化
- 访问权限控制
- 敏感文件加密存储

## 12. 扩展性设计

### 12.1 存储扩展
- 支持多种存储后端（OSS、本地、S3等）
- 存储策略配置化
- 文件迁移能力

### 12.2 功能扩展
- 文件预览功能
- 文件版本管理
- 文件分享功能
- 文件标签管理

## 13. 部署和运维

### 13.1 配置要求
- OSS配置（AccessKey、SecretKey、Bucket等）
- 文件存储路径配置
- 上传限制配置

### 13.2 监控指标
- 上传成功率
- 文件存储使用量
- 临时文件清理情况
- 上传频率统计

### 13.3 日志记录
- 文件上传日志
- 文件访问日志
- 错误日志记录
- 操作审计日志 