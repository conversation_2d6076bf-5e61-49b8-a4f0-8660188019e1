# 文件上传系统 API 接口文档

## 概述

文件上传系统提供基于场景的文件上传服务，支持OSS直传和服务端上传两种模式。系统通过SceneCode区分不同的业务场景，支持文件去重、临时文件管理等功能。

## 基础信息

- **Base URL**: `/api/file`
- **认证方式**: Bearer <PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8
- **HTTP方法**: 仅支持 GET 和 POST

## 统一响应格式

```json
{
    "code": 0,
    "message": "操作成功",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000,
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 100,
            "pages": 5,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

## 错误码定义

### 文件上传系统专用错误码 (170000-179999)

```json
{
    "170001": "文件上传配置不存在",
    "170002": "文件上传配置已存在", 
    "170003": "场景编码已存在",
    "170004": "文件上传配置无效",
    "170005": "文件上传配置已禁用",
    "170006": "系统场景不允许修改",
    
    "171001": "文件记录不存在",
    "171002": "文件记录已存在",
    "171003": "文件记录已过期",
    "171004": "文件记录无效",
    "171005": "文件记录已删除",
    
    "172001": "文件上传失败",
    "172002": "文件大小超出限制",
    "172003": "文件类型不允许",
    "172004": "上传令牌已过期",
    "172005": "上传令牌无效",
    "172006": "上传频率限制",
    "172007": "文件哈希不匹配",
    "172008": "文件存储错误",
    "172009": "场景编码已存在",
    
    "173001": "文件访问被拒绝",
    "173002": "文件访问已过期",
    "173003": "文件访问令牌无效",
    "173004": "文件访问被禁止"
}
```

## 接口列表

### 1. 创建上传令牌 (OSS直传)

**接口地址**: `POST /api/file/upload-token`

**请求参数**:
```json
{
    "scene_code": "avatar"
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| scene_code | string | ✅ | min:1, max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，只能包含字母、数字、下划线和连字符 |

**错误响应示例**:
```json
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "scene_code",
            "message": "场景编码不能为空",
            "value": ""
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "token": "upload_token_123456789",
        "oss_config": {
            "endpoint": "https://oss-cn-hangzhou.aliyuncs.com",
            "bucket": "user-files",
            "access_key_id": "LTAI5tRqFHMJqKqK",
            "access_key_secret": "xxx",
            "security_token": "xxx",
            "expiration": "2024-01-01T12:00:00Z"
        },
        "expire_at": "2024-01-01T12:45:00Z"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 2. 服务端上传文件

**接口地址**: `POST /api/file/upload`

**请求参数**:
- `scene_code` (查询参数): 场景编码，如 `avatar`
- `file` (FormData): 文件对象

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| scene_code | string | ✅ | min:1, max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，只能包含字母、数字、下划线和连字符 |
| file | file | ✅ | 文件大小和类型根据场景配置验证 | 上传的文件对象 |

**常见错误响应**:
```json
// 场景编码不存在
{
    "code": 170001,
    "message": "文件上传配置不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 文件大小超出限制
{
    "code": 172002,
    "message": "文件大小超出限制",
    "data": {
        "max_size": 5242880,
        "current_size": 10485760
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 文件类型不允许
{
    "code": 172003,
    "message": "文件类型不允许",
    "data": {
        "allowed_types": ["image/jpeg", "image/png", "image/gif"],
        "current_type": "application/pdf"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 上传频率限制
{
    "code": 172006,
    "message": "上传频率限制",
    "data": {
        "limit": 10,
        "window": 60,
        "retry_after": 30
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "file_id": 123456789,
        "file_name": "user_avatar.jpg",
        "file_size": 1024000,
        "file_type": "image/jpeg",
        "access_url": "https://cdn.example.com/files/avatar/user_avatar.jpg",
        "created_at": "2024-01-01T12:00:00Z"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 3. 获取文件信息

**接口地址**: `POST /api/file/get`

**请求参数**:
```json
{
    "file_id": 123456789
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| file_id | integer | ✅ | min:1 | 文件ID，必须大于0 |

**错误响应示例**:
```json
// 文件不存在
{
    "code": 171001,
    "message": "文件记录不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 文件已删除
{
    "code": 171005,
    "message": "文件记录已删除",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 参数验证失败
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "file_id",
            "message": "文件ID必须大于0",
            "value": 0
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "id": 123456789,
        "file_name": "user_avatar.jpg",
        "file_size": 1024000,
        "file_type": "image/jpeg",
        "file_hash": "d41d8cd98f00b204e9800998ecf8427e",
        "access_url": "https://cdn.example.com/files/avatar/user_avatar.jpg",
        "is_temporary": false,
        "is_permanent": true,
        "expire_at": null,
        "created_at": "2024-01-01T12:00:00Z",
        "meta": {
            "upload_ip": "*************",
            "user_agent": "Mozilla/5.0...",
            "upload_method": "server_upload",
            "scene_code": "avatar",
            "storage_path": "files/avatar/user_avatar.jpg",
            "storage_type": "oss"
        }
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 4. 删除文件（软删除）

**接口地址**: `POST /api/file/delete`

**请求参数**:
```json
{
    "file_id": 123456789
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| file_id | integer | ✅ | min:1 | 文件ID，必须大于0 |

**错误响应示例**:
```json
// 文件不存在
{
    "code": 171001,
    "message": "文件记录不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 文件已删除
{
    "code": 171005,
    "message": "文件记录已删除",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "deleted_file_id": 123456789
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 5. 创建文件访问令牌

**接口地址**: `POST /api/file/access-token`

**请求参数**:
```json
{
    "file_id": 123456789,
    "expire_in": 3600
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| file_id | integer | ✅ | min:1 | 文件ID，必须大于0 |
| expire_in | integer | ❌ | min:60, max:86400 | 过期时间（秒），默认3600秒，最大24小时 |

**错误响应示例**:
```json
// 文件不存在
{
    "code": 171001,
    "message": "文件记录不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 文件已过期
{
    "code": 171003,
    "message": "文件记录已过期",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 过期时间超出限制
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "expire_in",
            "message": "过期时间必须在60-86400秒之间",
            "value": 100000
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "access_token": "access_token_123456789",
        "upload_url": "https://cdn.example.com/files/avatar/user_avatar.jpg?token=xxx",
        "upload_type": "oss",
        "expire_at": "2024-01-01T13:00:00Z"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 6. 获取文件列表

**接口地址**: `POST /api/file/list`

**请求参数**:
```json
{
    "scene_code": "avatar",
    "is_temporary": false,
    "is_permanent": true,
    "keyword": "avatar",
    "page": 1,
    "size": 20,
    "order_by": "created_at",
    "order_dir": "desc"
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| scene_code | string | ❌ | max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，用于筛选特定场景的文件 |
| is_temporary | boolean | ❌ | - | 是否为临时文件 |
| is_permanent | boolean | ❌ | - | 是否已标记为永久 |
| keyword | string | ❌ | max:100 | 搜索关键词，支持文件名模糊搜索 |
| page | integer | ❌ | min:1, default:1 | 页码，从1开始 |
| size | integer | ❌ | min:1, max:100, default:20 | 每页大小，最大100 |
| order_by | string | ❌ | enum:created_at,updated_at,file_size,file_name | 排序字段 |
| order_dir | string | ❌ | enum:asc,desc, default:desc | 排序方向 |

**错误响应示例**:
```json
// 参数验证失败
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "page",
            "message": "页码必须大于0",
            "value": 0
        },
        {
            "field": "size",
            "message": "每页大小必须在1-100之间",
            "value": 200
        },
        {
            "field": "order_by",
            "message": "排序字段必须是：created_at, updated_at, file_size, file_name",
            "value": "invalid_field"
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": [
        {
            "id": 123456789,
            "file_name": "user_avatar.jpg",
            "file_size": 1024000,
            "file_type": "image/jpeg",
            "access_url": "https://cdn.example.com/files/avatar/user_avatar.jpg",
            "is_temporary": false,
            "is_permanent": true,
            "created_at": "2024-01-01T12:00:00Z"
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000,
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 1,
            "pages": 1,
            "has_next": false,
            "has_prev": false
        }
    }
}
```

## 文件上传配置管理接口

### 7. 创建文件上传配置

**接口地址**: `POST /api/file/config/create`

**请求参数**:
```json
{
    "scene_code": "avatar",
    "scene_name": "用户头像",
    "allowed_types": ["image/jpeg", "image/png", "image/gif"],
    "max_file_size": 5242880,
    "is_temporary": false,
    "enable_dedup": true,
    "temp_expire_time": 30,
    "is_system_scene": false,
    "extra_config": {
        "storage_config": {
            "storage_type": "oss",
            "bucket_name": "user-avatars",
            "path_prefix": "avatars",
            "enable_cdn": true
        },
        "process_config": {
            "enable_resize": true,
            "max_width": 200,
            "max_height": 200,
            "quality": 85
        },
        "security_config": {
            "access_control": "public",
            "enable_scan": true
        }
    }
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| scene_code | string | ✅ | min:1, max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，只能包含字母、数字、下划线和连字符 |
| scene_name | string | ✅ | min:1, max:100 | 场景名称 |
| allowed_types | array | ✅ | min:1, max:20 | 允许的文件类型列表，每个元素max:50 |
| max_file_size | integer | ✅ | min:1024, max:1073741824 | 最大文件大小（字节），最小1KB，最大1GB |
| is_temporary | boolean | ❌ | default:false | 是否为临时文件 |
| enable_dedup | boolean | ❌ | default:false | 是否开启文件去重 |
| temp_expire_time | integer | ❌ | min:1, max:1440, default:30 | 临时文件过期时间（分钟），最大24小时 |
| is_system_scene | boolean | ❌ | default:false | 是否为系统场景 |
| extra_config | object | ❌ | - | 额外配置信息，JSON格式 |

**错误响应示例**:
```json
// 场景编码已存在
{
    "code": 170003,
    "message": "场景编码已存在",
    "data": {
        "existing_scene_code": "avatar"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 参数验证失败
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "scene_code",
            "message": "场景编码只能包含字母、数字、下划线和连字符",
            "value": "avatar@123"
        },
        {
            "field": "max_file_size",
            "message": "最大文件大小必须在1024-1073741824字节之间",
            "value": 500
        },
        {
            "field": "allowed_types",
            "message": "允许的文件类型列表不能为空",
            "value": []
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "id": 123456789,
        "scene_code": "avatar",
        "scene_name": "用户头像",
        "allowed_types": ["image/jpeg", "image/png", "image/gif"],
        "max_file_size": 5242880,
        "is_temporary": false,
        "enable_dedup": true,
        "temp_expire_time": 30,
        "is_system_scene": false,
        "status": "active",
        "created_at": "2024-01-01T12:00:00Z"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 8. 更新文件上传配置

**接口地址**: `POST /api/file/config/update`

**请求参数**:
```json
{
    "id": 123456789,
    "scene_code": "avatar",
    "scene_name": "用户头像",
    "allowed_types": ["image/jpeg", "image/png", "image/gif"],
    "max_file_size": 5242880,
    "is_temporary": false,
    "enable_dedup": true,
    "temp_expire_time": 30,
    "is_system_scene": false,
    "extra_config": {
        "storage_config": {
            "storage_type": "oss",
            "bucket_name": "user-avatars",
            "path_prefix": "avatars",
            "enable_cdn": true
        }
    }
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| id | integer | ✅ | min:1 | 配置ID，必须大于0 |
| scene_code | string | ✅ | min:1, max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，只能包含字母、数字、下划线和连字符 |
| scene_name | string | ✅ | min:1, max:100 | 场景名称 |
| allowed_types | array | ✅ | min:1, max:20 | 允许的文件类型列表，每个元素max:50 |
| max_file_size | integer | ✅ | min:1024, max:1073741824 | 最大文件大小（字节），最小1KB，最大1GB |
| is_temporary | boolean | ❌ | default:false | 是否为临时文件 |
| enable_dedup | boolean | ❌ | default:false | 是否开启文件去重 |
| temp_expire_time | integer | ❌ | min:1, max:1440, default:30 | 临时文件过期时间（分钟），最大24小时 |
| is_system_scene | boolean | ❌ | default:false | 是否为系统场景 |
| extra_config | object | ❌ | - | 额外配置信息，JSON格式 |

**错误响应示例**:
```json
// 配置不存在
{
    "code": 170001,
    "message": "文件上传配置不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 系统场景不允许修改
{
    "code": 170006,
    "message": "系统场景不允许修改",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 场景编码冲突
{
    "code": 170003,
    "message": "场景编码已存在",
    "data": {
        "existing_config_id": 123456790,
        "existing_scene_code": "avatar"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**: 同创建接口

### 9. 获取文件上传配置

**接口地址**: `POST /api/file/config/get`

**请求参数**:
```json
{
    "id": 123456789
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| id | integer | ✅ | min:1 | 配置ID，必须大于0 |

**错误响应示例**:
```json
// 配置不存在
{
    "code": 170001,
    "message": "文件上传配置不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "id": 123456789,
        "scene_code": "avatar",
        "scene_name": "用户头像",
        "allowed_types": ["image/jpeg", "image/png", "image/gif"],
        "max_file_size": 5242880,
        "is_temporary": false,
        "enable_dedup": true,
        "temp_expire_time": 30,
        "is_system_scene": false,
        "status": "active",
        "extra_config": {
            "storage_config": {
                "storage_type": "oss",
                "bucket_name": "user-avatars",
                "path_prefix": "avatars",
                "enable_cdn": true
            }
        },
        "created_at": "2024-01-01T12:00:00Z",
        "updated_at": "2024-01-01T12:00:00Z"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

### 10. 根据场景获取配置

**接口地址**: `POST /api/file/config/get-by-scene`

**请求参数**:
```json
{
    "scene_code": "avatar"
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| scene_code | string | ✅ | min:1, max:50, pattern:^[a-zA-Z0-9_-]+$ | 场景编码，只能包含字母、数字、下划线和连字符 |

**错误响应示例**:
```json
// 配置不存在
{
    "code": 170001,
    "message": "文件上传配置不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 参数验证失败
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "scene_code",
            "message": "场景编码不能为空",
            "value": ""
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**: 同获取配置接口

### 11. 获取配置列表

**接口地址**: `POST /api/user/file/config/list`

**请求参数**:
```json
{
    "page": 1,
    "size": 20
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| page | integer | ❌ | min:1, default:1 | 页码，从1开始 |
| size | integer | ❌ | min:1, max:100, default:20 | 每页大小，最大100 |

**错误响应示例**:
```json
// 参数验证失败
{
    "code": 10001,
    "message": "参数验证失败",
    "data": {},
    "errors": [
        {
            "field": "page",
            "message": "页码必须大于0",
            "value": 0
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": [
        {
            "id": 123456789,
            "scene_code": "avatar",
            "scene_name": "用户头像",
            "allowed_types": ["image/jpeg", "image/png", "image/gif"],
            "max_file_size": 5242880,
            "is_temporary": false,
            "enable_dedup": true,
            "temp_expire_time": 30,
            "is_system_scene": false,
            "status": "active",
            "extra_config": {
                "watermark": false,
                "compression": true
            },
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }
    ],
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000,
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 1,
            "pages": 1,
            "has_next": false,
            "has_prev": false
        }
    }
}
```

### 12. 删除文件上传配置

**接口地址**: `POST /api/file/config/delete`

**请求参数**:
```json
{
    "id": 123456789
}
```

**参数验证规则**:
| 字段 | 类型 | 必填 | 验证规则 | 说明 |
|------|------|------|----------|------|
| id | integer | ✅ | min:1 | 配置ID，必须大于0 |

**错误响应示例**:
```json
// 配置不存在
{
    "code": 170001,
    "message": "文件上传配置不存在",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 系统场景不允许删除
{
    "code": 170006,
    "message": "系统场景不允许删除",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 配置正在使用中
{
    "code": 170004,
    "message": "文件上传配置无效",
    "data": {
        "reason": "配置正在使用中，无法删除"
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}

// 权限不足
{
    "code": 11004,
    "message": "权限不足",
    "data": {},
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

**响应数据**:
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "deleted_config_id": 123456789
    },
    "meta": {
        "request_id": "req_123456789",
        "timestamp": 1640995200000
    }
}
```

## 数据模型

### FileUploadResponse HTTP上传响应
```json
{
    "file_id": "分布式ID",
    "file_name": "文件名",
    "file_size": "文件大小（字节）",
    "file_type": "文件类型",
    "access_url": "访问URL",
    "created_at": "创建时间"
}
```

### FileAccessTokenResponse 文件访问令牌响应
```json
{
    "access_token": "访问令牌",
    "upload_url": "上传URL",
    "upload_type": "上传类型：oss, server",
    "expire_at": "过期时间"
}
```

### FileRecord 文件记录
```json
{
    "id": "分布式ID",
    "tenant_id": "租户ID",
    "user_id": "上传用户ID",
    "scene_code": "场景编码",
    "file_name": "文件名",
    "file_size": "文件大小（字节）",
    "file_type": "文件类型",
    "file_hash": "文件MD5哈希",
    "storage_path": "存储路径",
    "storage_type": "存储类型：oss, local",
    "access_url": "访问URL",
    "is_temporary": "是否为临时文件",
    "is_permanent": "是否已标记为永久",
    "expire_at": "过期时间",
    "status": "状态",
    "meta": {
        "upload_ip": "上传IP",
        "user_agent": "用户代理",
        "upload_method": "上传方式：oss_upload, server_upload",
        "scene_code": "场景编码",
        "storage_path": "存储路径",
        "storage_type": "存储类型",
        "oss_bucket": "OSS桶名",
        "cdn_domain": "CDN域名",
        "file_extension": "文件扩展名",
        "mime_type": "MIME类型",
        "upload_duration": "上传耗时（毫秒）",
        "compression_ratio": "压缩比例",
        "watermark_applied": "是否应用水印",
        "virus_scan_result": "病毒扫描结果",
        "custom_fields": {}
    },
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

### FileUploadConfig 文件上传配置
```json
{
    "id": "分布式ID",
    "tenant_id": "租户ID",
    "scene_code": "场景编码",
    "scene_name": "场景名称",
    "allowed_types": "允许的文件类型，JSON格式",
    "max_file_size": "最大文件大小（字节）",
    "is_temporary": "是否为临时文件",
    "enable_dedup": "是否开启文件去重",
    "temp_expire_time": "临时文件过期时间（分钟）",
    "is_system_scene": "是否为系统场景",
    "extra_config": "额外配置信息，JSON格式",
    "status": "状态",
    "created_at": "创建时间",
    "updated_at": "更新时间"
}
```

### UploadToken 上传令牌
```json
{
    "id": "分布式ID",
    "tenant_id": "租户ID",
    "user_id": "用户ID",
    "scene_code": "场景编码",
    "token": "上传令牌",
    "oss_config": "OSS配置信息，JSON格式",
    "expire_at": "过期时间",
    "created_at": "创建时间"
}
```

## 使用示例

### 1. OSS直传流程

```javascript
// 1. 获取上传令牌
const response = await fetch('/api/file/upload-token', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
        scene_code: 'avatar'
    })
});

const { data } = await response.json();
const { token, oss_config, expire_at } = data;

// 2. 使用OSS SDK直接上传
const ossClient = new OSS({
    accessKeyId: oss_config.access_key_id,
    accessKeySecret: oss_config.access_key_secret,
    stsToken: oss_config.security_token,
    bucket: oss_config.bucket,
    endpoint: oss_config.endpoint
});

const uploadPath = `avatars/${Date.now()}_${file.name}`;
await ossClient.put(uploadPath, file);

// 3. 标记文件为永久存储（通过gRPC接口）
// 注意：HTTP接口不提供标记永久存储功能，需要通过gRPC接口
const grpcClient = new FileUploadServiceClient('localhost:50051');

// 批量标记文件
const response = await grpcClient.markFilesPermanent({
    identifiers: [
        {
            file_url: uploadPath  // 使用上传路径作为标识
        }
    ]
});

if (response.success) {
    console.log(`批量处理完成，成功: ${response.success_count}, 失败: ${response.failure_count}`);
}
```

### 2. 服务端上传流程

```javascript
// 1. 直接上传文件
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/api/file/upload?scene_code=avatar', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
});

const { data } = await response.json();
const { file_id, access_url } = data;

// 2. 标记文件为永久存储（通过gRPC接口）
// 使用gRPC客户端调用
const grpcClient = new FileUploadServiceClient('localhost:50051');

// 批量标记文件（支持混合使用file_id和file_url）
const response = await grpcClient.markFilesPermanent({
    identifiers: [
        {
            file_id: file_id
        },
        // 或者通过文件URL标记
        // {
        //     file_url: access_url
        // }
    ]
});

// 处理批量响应
if (response.success) {
    console.log(`批量处理完成，成功: ${response.success_count}, 失败: ${response.failure_count}`);
    
    // 检查每个文件的处理结果
    response.results.forEach(result => {
        if (result.success) {
            console.log(`文件标记成功: ${result.file_info.file_name}`);
        } else {
            console.log(`文件标记失败: ${result.message}`);
        }
    });
}
}
```

## gRPC接口

### 1. 批量标记文件为永久存储

**服务**: `FileUploadService`

**方法**: `MarkFilesPermanent`

**请求**:
```protobuf
message MarkFilesPermanentRequest {
    repeated FileIdentifier identifiers = 1;  // 文件标识符列表
}

message FileIdentifier {
    oneof identifier {
        int64 file_id = 1;        // 文件ID
        string file_url = 2;      // 文件URL
    }
}
```

**响应**:
```protobuf
message MarkFilesPermanentResponse {
    bool success = 1;                    // 整体操作是否成功
    string message = 2;                  // 响应消息
    repeated FileResult results = 3;     // 每个文件的处理结果
    int32 success_count = 4;             // 成功处理的文件数量
    int32 failure_count = 5;             // 失败的文件数量
}

message FileResult {
    FileIdentifier identifier = 1;       // 文件标识符
    bool success = 2;                    // 该文件处理是否成功
    string message = 3;                  // 该文件的处理消息
    FileInfo file_info = 4;              // 文件信息（如果成功）
}
```

**使用示例**:
```go
// 批量标记多个文件
request := &pb.MarkFilesPermanentRequest{
    Identifiers: []*pb.FileIdentifier{
        // 通过文件ID标记
        {
            Identifier: &pb.FileIdentifier_FileId{
                FileId: 123456789,
            },
        },
        // 通过文件URL标记
        {
            Identifier: &pb.FileIdentifier_FileUrl{
                FileUrl: "https://cdn.example.com/files/avatar/user_avatar.jpg",
            },
        },
        // 更多文件...
    },
}

// 调用gRPC服务
response, err := client.MarkFilesPermanent(ctx, request)
if err != nil {
    // 处理错误
}

// 处理响应
if response.Success {
    fmt.Printf("批量处理完成，成功: %d, 失败: %d\n", 
        response.SuccessCount, response.FailureCount)
    
    // 检查每个文件的处理结果
    for _, result := range response.Results {
        if result.Success {
            fmt.Printf("文件 %v 标记成功: %s\n", 
                result.Identifier, result.FileInfo.FileName)
        } else {
            fmt.Printf("文件 %v 标记失败: %s\n", 
                result.Identifier, result.Message)
        }
    }
}
```

### 2. 检查文件是否存在

**服务**: `FileUploadService`

**方法**: `CheckFileExists`

**请求**:
```protobuf
message CheckFileExistsRequest {
    string file_hash = 1;
}
```

**响应**:
```protobuf
message CheckFileExistsResponse {
    bool exists = 1;
    FileInfo file_info = 2;
}

message FileInfo {
    int64 id = 1;
    string file_name = 2;
    int64 file_size = 3;
    string file_type = 4;
    string access_url = 5;
}
```

## 注意事项

1. **SceneCode唯一性**: 系统会自动校验SceneCode的唯一性，重复的SceneCode将返回错误
2. **系统场景保护**: 标记为系统场景的配置不允许修改和删除
3. **文件去重**: 开启去重功能时，相同哈希的文件将返回已存在的文件记录
4. **临时文件**: 服务端上传的文件默认为临时文件，会在指定时间后自动过期，需要通过gRPC接口标记为永久存储
5. **软删除**: 删除文件采用软删除方式，设置deleted_at字段，数据不会物理删除
6. **权限控制**: 不同角色对场景的访问权限可能不同，请确保有相应权限
7. **文件大小限制**: 上传文件大小不能超过场景配置的限制
8. **文件类型限制**: 只能上传场景配置中允许的文件类型
9. **HTTP方法限制**: 所有接口仅支持GET和POST方法，不使用路径参数
10. **统一响应格式**: 所有接口都使用统一的响应结构，业务状态码在code字段中

## 通用错误处理说明

### 错误响应结构
所有错误响应都遵循统一的格式：
```json
{
    "code": 错误码,
    "message": "错误消息",
    "data": {},
    "errors": [
        {
            "field": "字段名",
            "message": "字段错误消息",
            "value": "字段值"
        }
    ],
    "meta": {
        "request_id": "请求ID",
        "timestamp": 时间戳
    }
}
```

### 常见错误码说明
| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 10001 | 参数验证失败 | 检查请求参数格式和内容 |
| 11001 | 未授权 | 检查认证信息是否正确 |
| 11004 | 权限不足 | 确认用户是否有相应权限 |
| 170001 | 文件上传配置不存在 | 检查场景编码是否正确 |
| 170003 | 场景编码已存在 | 使用不同的场景编码 |
| 170006 | 系统场景不允许修改 | 系统场景受保护，无法修改 |
| 171001 | 文件记录不存在 | 检查文件ID是否正确 |
| 171003 | 文件记录已过期 | 文件已过期，需要重新上传 |
| 171005 | 文件记录已删除 | 文件已被删除 |
| 172002 | 文件大小超出限制 | 检查文件大小是否符合要求 |
| 172003 | 文件类型不允许 | 检查文件类型是否在允许列表中 |
| 172006 | 上传频率限制 | 等待一段时间后重试 |

### 参数验证规则说明
- **必填字段**: 标记为 ✅ 的字段必须提供
- **可选字段**: 标记为 ❌ 的字段可以不提供，会使用默认值
- **字符串长度**: min:X 表示最小长度，max:X 表示最大长度
- **数值范围**: min:X 表示最小值，max:X 表示最大值
- **正则表达式**: pattern:^xxx$ 表示必须匹配的正则表达式
- **枚举值**: enum:value1,value2 表示只能是这些值中的一个
- **默认值**: default:value 表示不提供时的默认值

### 最佳实践
1. **错误处理**: 始终检查响应的 `code` 字段，非0表示有错误
2. **参数验证**: 在客户端进行基础验证，减少无效请求
3. **重试机制**: 对于网络错误和限流错误，实现适当的重试机制
4. **日志记录**: 记录请求ID，便于问题排查
5. **用户提示**: 将技术错误消息转换为用户友好的提示 