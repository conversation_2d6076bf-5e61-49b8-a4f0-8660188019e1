# 文件配置更新接口文档

## 接口概述

`file/config/update` 接口用于更新文件上传配置，允许修改场景编码、场景名称、允许的文件类型、最大文件大小等配置参数。

## 接口信息

- **接口路径**: `POST /api/user/file/config/update`
- **认证要求**: 需要JWT认证
- **租户隔离**: 是

## 请求参数

### 请求体 (JSON)

```json
{
  "id": 123,
  "scene_code": "updated_scene",
  "scene_name": "更新后的场景名称",
  "allowed_types": ["jpg", "png", "pdf", "doc"],
  "max_file_size": 20971520,
  "is_temporary": true,
  "enable_dedup": false,
  "temp_expire_time": 120,
  "is_system_scene": false,
  "extra_config": {
    "watermark": true,
    "compress": false
  }
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 配置ID |
| scene_code | string | 是 | 场景编码，1-50字符，只能包含字母、数字、下划线、连字符 |
| scene_name | string | 是 | 场景名称，1-100字符 |
| allowed_types | []string | 是 | 允许的文件类型列表，最多20个类型 |
| max_file_size | int64 | 是 | 最大文件大小（字节），1KB-1GB |
| is_temporary | bool | 否 | 是否为临时文件，默认false |
| enable_dedup | bool | 否 | 是否启用文件去重，默认false |
| temp_expire_time | int | 否 | 临时文件过期时间（分钟），1-1440分钟 |
| is_system_scene | bool | 否 | 是否为系统场景，默认false |
| extra_config | object | 否 | 额外配置信息 |

## 响应格式

### 成功响应 (200)

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 123,
    "scene_code": "updated_scene",
    "scene_name": "更新后的场景名称",
    "allowed_types": ["jpg", "png", "pdf", "doc"],
    "max_file_size": 20971520,
    "is_temporary": true,
    "enable_dedup": false,
    "temp_expire_time": 120,
    "is_system_scene": false,
    "status": "active",
    "extra_config": {
      "watermark": true,
      "compress": false
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "request_id": "req_123456",
    "timestamp": 1704067200000
  }
}
```

### 错误响应

#### 400 - 参数验证错误
```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": {
    "field_errors": {
      "scene_code": "场景编码格式不正确"
    }
  }
}
```

#### 404 - 配置不存在
```json
{
  "code": 404,
  "message": "文件上传配置不存在",
  "data": null
}
```

#### 409 - 场景编码重复
```json
{
  "code": 409,
  "message": "场景编码已存在",
  "data": null
}
```

#### 403 - 权限不足
```json
{
  "code": 403,
  "message": "无权访问此配置",
  "data": null
}
```

#### 500 - 服务器内部错误
```json
{
  "code": 500,
  "message": "内部服务器错误",
  "data": null
}
```

## 业务规则

1. **租户隔离**: 只能更新当前租户下的配置
2. **场景编码唯一性**: 同一租户下场景编码必须唯一（排除自身）
3. **权限检查**: 只能更新有权限访问的配置
4. **数据验证**: 所有输入参数都会进行格式和范围验证
5. **安全更新**: 数据库更新操作包含租户ID条件，确保跨租户安全

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8084/api/user/file/config/update" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "id": 123,
    "scene_code": "updated_scene",
    "scene_name": "更新后的场景名称",
    "allowed_types": ["jpg", "png", "pdf"],
    "max_file_size": 10485760,
    "is_temporary": false,
    "enable_dedup": true,
    "temp_expire_time": 60,
    "is_system_scene": false
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/api/user/file/config/update', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    id: 123,
    scene_code: 'updated_scene',
    scene_name: '更新后的场景名称',
    allowed_types: ['jpg', 'png', 'pdf'],
    max_file_size: 10485760,
    is_temporary: false,
    enable_dedup: true,
    temp_expire_time: 60,
    is_system_scene: false
  })
});

const result = await response.json();
```

## 注意事项

1. 更新配置后，新的上传请求将使用更新后的配置
2. 已存在的文件记录不会受到影响
3. 系统场景配置的更新需要特殊权限
4. 建议在更新配置前先备份原有配置 