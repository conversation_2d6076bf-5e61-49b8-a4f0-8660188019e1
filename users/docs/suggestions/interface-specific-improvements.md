# 用户系统接口具体改进建议

## 概述

本文档针对用户系统中的具体接口提供详细的改进建议，包括代码示例和最佳实践。

## 用户管理接口改进

### 1. CreateUser 接口优化

**当前问题：**
- 参数验证不完整
- 错误处理不统一
- 缺少业务规则验证

**改进方案：**

```go
// ✅ 优化后的CreateUser实现
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req dto.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonErrors.GinValidationError(c, err)
        return
    }

    // 1. 基础参数验证
    if err := req.Validate(); err != nil {
        commonErrors.ValidationError(c, err.(*validator.ValidationErrors).ToResponseDetails())
        return
    }

    // 2. 权限检查
    currentUserID := getUserIDFromContext(c)
    if !h.permissionService.HasPermission(currentUserID, "user:create") {
        commonErrors.Forbidden(c, "无创建用户权限")
        return
    }

    // 3. 租户隔离
    req.TenantID = getTenantIDFromContext(c)

    // 4. 业务规则验证
    if err := h.businessValidator.ValidateCreateUser(&req); err != nil {
        if fieldErr, ok := err.(*commonErrors.FieldValidationError); ok {
            commonErrors.FieldError(c, fieldErr.Field, fieldErr.Message)
            return
        }
        commonErrors.BadRequest(c, err.Error())
        return
    }

    // 5. 执行创建
    user, err := h.userService.CreateUser(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error(c.Request.Context(), "create user failed",
            logiface.Error(err),
            logiface.String("username", req.Username),
            logiface.String("email", utils.MaskEmail(req.Email)),
            logiface.Int64("tenant_id", req.TenantID),
            logiface.String("ip", c.ClientIP()),
        )

        // 处理特定错误
        if isUniqueConstraintError(err) {
            handleUniqueConstraintError(c, err)
            return
        }

        commonErrors.InternalError(c, fmt.Errorf("用户创建失败"))
        return
    }

    h.logger.Info(c.Request.Context(), "user created successfully",
        logiface.Int64("user_id", user.ID),
        logiface.String("username", user.Username),
        logiface.Int64("tenant_id", req.TenantID),
        logiface.String("ip", c.ClientIP()),
    )

    commonErrors.Created(c, user)
}
```

### 2. ListUsers 接口优化

**当前问题：**
- 缺少查询限制
- 没有缓存机制
- 敏感字段可能泄露

**改进方案：**

```go
// ✅ 优化后的ListUsers实现
func (h *UserHandler) ListUsers(c *gin.Context) {
    var req dto.ListUsersRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonErrors.GinValidationError(c, err)
        return
    }

    // 1. 参数验证和限制
    if err := req.Validate(); err != nil {
        commonErrors.ValidationError(c, err.(*validator.ValidationErrors).ToResponseDetails())
        return
    }

    // 2. 设置查询限制
    if req.Limit <= 0 {
        req.Limit = 20
    }
    if req.Limit > 1000 {
        req.Limit = 1000
    }
    if req.Offset < 0 {
        req.Offset = 0
    }

    // 3. 租户隔离
    req.TenantID = getTenantIDFromContext(c)

    // 4. 权限检查
    currentUserID := getUserIDFromContext(c)
    if !h.permissionService.HasPermission(currentUserID, "user:list") {
        commonErrors.Forbidden(c, "无查看用户列表权限")
        return
    }

    // 5. 尝试从缓存获取
    cacheKey := fmt.Sprintf("users:list:%d:%d:%d", req.TenantID, req.Offset, req.Limit)
    if cached, err := h.cache.Get(cacheKey); err == nil {
        commonErrors.Success(c, cached)
        return
    }

    // 6. 从数据库查询
    users, err := h.userService.ListUsers(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error(c.Request.Context(), "list users failed",
            logiface.Error(err),
            logiface.Int64("tenant_id", req.TenantID),
            logiface.String("ip", c.ClientIP()),
        )
        commonErrors.InternalError(c, fmt.Errorf("获取用户列表失败"))
        return
    }

    // 7. 过滤敏感字段
    filteredUsers := h.filterSensitiveFields(users.Users, currentUserID)
    result := &dto.ListUsersResponse{
        Users: filteredUsers,
        Total: users.Total,
    }

    // 8. 缓存结果
    h.cache.Set(cacheKey, result, 5*time.Minute)

    commonErrors.Paginated(c, result.Users, req.Offset/req.Limit+1, req.Limit, result.Total)
}

// 过滤敏感字段
func (h *UserHandler) filterSensitiveFields(users []*dto.UserResponse, currentUserID int64) []*dto.UserResponse {
    isAdmin := h.permissionService.HasPermission(currentUserID, "user:admin")
    
    for _, user := range users {
        if !isAdmin && user.ID != currentUserID {
            // 非管理员只能看到基本信息
            user.Email = utils.MaskEmail(user.Email)
            user.Phone = utils.MaskPhone(user.Phone)
            user.LastLoginIP = ""
        }
    }
    
    return users
}
```

### 3. DeleteUser 接口优化

**当前问题：**
- 缺少权限检查
- 没有防护措施
- 缺少审计日志

**改进方案：**

```go
// ✅ 优化后的DeleteUser实现
func (h *UserHandler) DeleteUser(c *gin.Context) {
    var req dto.DeleteUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonErrors.GinValidationError(c, err)
        return
    }

    // 1. 参数验证
    if err := req.Validate(); err != nil {
        commonErrors.ValidationError(c, err.(*validator.ValidationErrors).ToResponseDetails())
        return
    }

    currentUserID := getUserIDFromContext(c)
    tenantID := getTenantIDFromContext(c)

    // 2. 权限检查
    if !h.permissionService.HasPermission(currentUserID, "user:delete") {
        commonErrors.Forbidden(c, "无删除用户权限")
        return
    }

    // 3. 业务规则检查
    if req.UserID == currentUserID {
        commonErrors.BadRequest(c, "不能删除自己")
        return
    }

    // 4. 检查用户是否存在且属于当前租户
    user, err := h.userService.GetUser(c.Request.Context(), req.UserID)
    if err != nil {
        if errors.Is(err, domain.ErrUserNotFound) {
            commonErrors.NotFound(c, "用户")
            return
        }
        commonErrors.InternalError(c, fmt.Errorf("获取用户信息失败"))
        return
    }

    if user.TenantID != tenantID {
        commonErrors.Forbidden(c, "无权删除该用户")
        return
    }

    // 5. 检查是否为超级管理员
    if h.userService.IsSuperAdmin(req.UserID) {
        commonErrors.BadRequest(c, "不能删除超级管理员")
        return
    }

    // 6. 检查用户是否有重要数据关联
    if hasImportantData, err := h.userService.HasImportantData(req.UserID); err != nil {
        commonErrors.InternalError(c, fmt.Errorf("检查用户数据失败"))
        return
    } else if hasImportantData && !req.Force {
        commonErrors.BadRequest(c, "用户有重要数据关联，请使用强制删除")
        return
    }

    // 7. 执行删除
    err = h.userService.DeleteUser(c.Request.Context(), req.UserID, req.Force)
    if err != nil {
        h.logger.Error(c.Request.Context(), "delete user failed",
            logiface.Error(err),
            logiface.Int64("user_id", req.UserID),
            logiface.Int64("operator_id", currentUserID),
            logiface.Int64("tenant_id", tenantID),
            logiface.String("ip", c.ClientIP()),
        )
        commonErrors.InternalError(c, fmt.Errorf("删除用户失败"))
        return
    }

    // 8. 记录审计日志
    h.auditLogger.LogUserDeletion(c.Request.Context(), &audit.UserDeletionEvent{
        OperatorID:  currentUserID,
        DeletedUser: user,
        TenantID:    tenantID,
        IPAddress:   c.ClientIP(),
        UserAgent:   c.GetHeader("User-Agent"),
        Force:       req.Force,
    })

    // 9. 清理相关缓存
    h.cache.Delete(fmt.Sprintf("user:%d", req.UserID))
    h.cache.DeletePattern(fmt.Sprintf("users:list:%d:*", tenantID))

    commonErrors.Success(c, gin.H{"message": "用户删除成功"})
}
```

## 角色管理接口改进

### 1. AssignRole 接口优化

**改进方案：**

```go
// ✅ 优化后的AssignRole实现
func (h *RoleHandler) AssignRole(c *gin.Context) {
    var req dto.AssignRoleRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonErrors.GinValidationError(c, err)
        return
    }

    // 1. 参数验证
    if err := req.Validate(); err != nil {
        commonErrors.ValidationError(c, err.(*validator.ValidationErrors).ToResponseDetails())
        return
    }

    currentUserID := getUserIDFromContext(c)
    tenantID := getTenantIDFromContext(c)

    // 2. 权限检查
    if !h.permissionService.HasPermission(currentUserID, "role:assign") {
        commonErrors.Forbidden(c, "无分配角色权限")
        return
    }

    // 3. 验证角色是否存在且属于当前租户
    role, err := h.roleService.GetRole(c.Request.Context(), req.RoleID)
    if err != nil {
        if errors.Is(err, domain.ErrRoleNotFound) {
            commonErrors.NotFound(c, "角色")
            return
        }
        commonErrors.InternalError(c, fmt.Errorf("获取角色信息失败"))
        return
    }

    if role.TenantID != tenantID {
        commonErrors.Forbidden(c, "无权操作该角色")
        return
    }

    // 4. 验证用户是否都属于当前租户
    for _, userID := range req.UserIDs {
        user, err := h.userService.GetUser(c.Request.Context(), userID)
        if err != nil {
            commonErrors.BadRequest(c, fmt.Sprintf("用户 %d 不存在", userID))
            return
        }
        if user.TenantID != tenantID {
            commonErrors.BadRequest(c, fmt.Sprintf("用户 %d 不属于当前租户", userID))
            return
        }
    }

    // 5. 检查角色分配限制
    if err := h.validateRoleAssignment(role, req.UserIDs); err != nil {
        commonErrors.BadRequest(c, err.Error())
        return
    }

    // 6. 执行角色分配
    err = h.roleService.AssignRole(c.Request.Context(), req.RoleID, req.UserIDs)
    if err != nil {
        h.logger.Error(c.Request.Context(), "assign role failed",
            logiface.Error(err),
            logiface.Int64("role_id", req.RoleID),
            logiface.Any("user_ids", req.UserIDs),
            logiface.Int64("operator_id", currentUserID),
            logiface.String("ip", c.ClientIP()),
        )
        commonErrors.InternalError(c, fmt.Errorf("角色分配失败"))
        return
    }

    // 7. 记录审计日志
    h.auditLogger.LogRoleAssignment(c.Request.Context(), &audit.RoleAssignmentEvent{
        OperatorID: currentUserID,
        RoleID:     req.RoleID,
        UserIDs:    req.UserIDs,
        TenantID:   tenantID,
        IPAddress:  c.ClientIP(),
    })

    // 8. 清理相关缓存
    for _, userID := range req.UserIDs {
        h.cache.Delete(fmt.Sprintf("user:roles:%d", userID))
        h.cache.Delete(fmt.Sprintf("user:permissions:%d", userID))
    }

    commonErrors.Success(c, gin.H{"message": "角色分配成功"})
}

// 验证角色分配限制
func (h *RoleHandler) validateRoleAssignment(role *dto.RoleResponse, userIDs []int64) error {
    // 检查角色是否激活
    if !role.IsActive {
        return fmt.Errorf("不能分配未激活的角色")
    }

    // 检查角色用户数量限制
    if role.MaxUsers > 0 {
        currentCount, err := h.roleService.GetRoleUserCount(role.ID)
        if err != nil {
            return fmt.Errorf("获取角色用户数量失败")
        }
        
        if currentCount+len(userIDs) > role.MaxUsers {
            return fmt.Errorf("角色用户数量超出限制（最大 %d 个）", role.MaxUsers)
        }
    }

    return nil
}
```

## 文件上传接口改进

### 1. UploadFile 接口优化

**改进方案：**

```go
// ✅ 优化后的UploadFile实现
func (h *FileUploadHandler) UploadFile(c *gin.Context) {
    // 1. 获取上传文件
    file, header, err := c.Request.FormFile("file")
    if err != nil {
        commonErrors.BadRequest(c, "获取上传文件失败")
        return
    }
    defer file.Close()

    // 2. 基础验证
    if header.Size == 0 {
        commonErrors.BadRequest(c, "文件不能为空")
        return
    }

    // 3. 文件大小限制
    maxSize := h.getMaxFileSize(c)
    if header.Size > maxSize {
        commonErrors.BadRequest(c, fmt.Sprintf("文件大小超出限制（最大 %d MB）", maxSize/1024/1024))
        return
    }

    // 4. 文件类型验证
    allowedTypes := h.getAllowedFileTypes(c)
    if !h.isAllowedFileType(header.Filename, allowedTypes) {
        commonErrors.BadRequest(c, "不支持的文件类型")
        return
    }

    // 5. 文件内容验证（防止恶意文件）
    if err := h.validateFileContent(file); err != nil {
        commonErrors.BadRequest(c, "文件内容验证失败")
        return
    }

    // 6. 权限检查
    currentUserID := getUserIDFromContext(c)
    if !h.permissionService.HasPermission(currentUserID, "file:upload") {
        commonErrors.Forbidden(c, "无文件上传权限")
        return
    }

    // 7. 构建上传请求
    req := &dto.UploadFileRequest{
        File:     file,
        Filename: header.Filename,
        Size:     header.Size,
        UserID:   currentUserID,
        TenantID: getTenantIDFromContext(c),
        IPAddress: c.ClientIP(),
    }

    // 8. 执行上传
    result, err := h.fileUploadService.UploadFile(c.Request.Context(), req)
    if err != nil {
        h.logger.Error(c.Request.Context(), "file upload failed",
            logiface.Error(err),
            logiface.String("filename", header.Filename),
            logiface.Int64("size", header.Size),
            logiface.Int64("user_id", currentUserID),
            logiface.String("ip", c.ClientIP()),
        )
        commonErrors.InternalError(c, fmt.Errorf("文件上传失败"))
        return
    }

    // 9. 记录上传日志
    h.logger.Info(c.Request.Context(), "file uploaded successfully",
        logiface.String("file_id", result.FileID),
        logiface.String("filename", result.Filename),
        logiface.Int64("size", result.Size),
        logiface.Int64("user_id", currentUserID),
        logiface.String("ip", c.ClientIP()),
    )

    commonErrors.Created(c, result)
}

// 验证文件内容
func (h *FileUploadHandler) validateFileContent(file multipart.File) error {
    // 重置文件指针
    file.Seek(0, 0)
    
    // 读取文件头部用于类型检测
    buffer := make([]byte, 512)
    _, err := file.Read(buffer)
    if err != nil {
        return err
    }
    
    // 重置文件指针
    file.Seek(0, 0)
    
    // 检测文件类型
    contentType := http.DetectContentType(buffer)
    
    // 检查是否为可执行文件
    if strings.Contains(contentType, "application/x-executable") ||
       strings.Contains(contentType, "application/x-msdownload") {
        return fmt.Errorf("不允许上传可执行文件")
    }
    
    return nil
}
```

## 总结

通过以上改进，用户系统接口将具备：

1. **完整的参数验证** - 防止无效请求
2. **严格的权限控制** - 确保操作安全
3. **完善的错误处理** - 提供友好的错误信息
4. **详细的审计日志** - 便于问题追踪
5. **高效的缓存机制** - 提升系统性能
6. **敏感信息保护** - 防止数据泄露

这些改进将显著提升系统的安全性、稳定性和用户体验。
