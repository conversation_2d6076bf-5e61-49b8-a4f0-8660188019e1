# 用户系统接口优化实施计划

## 概述

本文档提供用户系统接口优化的详细实施计划，包括具体的修改步骤、代码示例和验证方法。

## 阶段一：安全性修复（P0优先级）

### 1.1 统一响应格式

**目标：** 移除重复的响应处理代码，统一使用 `platforms-pkg/common/errors` 包

**实施步骤：**

1. **移除自定义response包**
```bash
# 删除自定义响应包
rm -rf users/internal/interfaces/http/response/
```

2. **更新handler导入**
```go
// 在所有handler文件中替换导入
// ❌ 移除
import "platforms-user/pkg/response"

// ✅ 统一使用
import commonErrors "platforms-pkg/common/errors"
```

3. **替换响应调用**
```go
// ❌ 替换前
response.Success(c, result)
response.InternalError(c, err)
response.ValidationError(c, details)

// ✅ 替换后
commonErrors.Success(c, result)
commonErrors.InternalError(c, err)
commonErrors.ValidationError(c, details)
```

**影响文件：**
- `handlers/user_profile_handler.go`
- `handlers/role_handler.go`
- `handlers/permission_handler.go`
- `handlers/tenant_handler.go`
- `handlers/verification_handler.go`

### 1.2 完善权限检查机制

**目标：** 为所有敏感操作添加权限验证

**实施步骤：**

1. **创建权限检查中间件**
```go
// internal/interfaces/http/middleware/permission_middleware.go
func RequirePermission(permission string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := getUserIDFromContext(c)
        if !permissionService.HasPermission(userID, permission) {
            commonErrors.Forbidden(c, "权限不足")
            c.Abort()
            return
        }
        c.Next()
    }
}
```

2. **更新路由配置**
```go
// routes/user_routes.go
users.POST("/delete", 
    RequirePermission("user:delete"), 
    userHandler.DeleteUser)
users.POST("/create", 
    RequirePermission("user:create"), 
    userHandler.CreateUser)
```

3. **在handler中添加额外检查**
```go
// handlers/user_handler.go
func (h *UserHandler) DeleteUser(c *gin.Context) {
    currentUserID := getUserIDFromContext(c)
    
    // 防止删除自己
    if userID == currentUserID {
        commonErrors.BadRequest(c, "不能删除自己")
        return
    }
    
    // 检查是否为超级管理员（不能删除）
    if h.userService.IsSuperAdmin(userID) {
        commonErrors.BadRequest(c, "不能删除超级管理员")
        return
    }
}
```

### 1.3 加强租户隔离

**目标：** 确保所有操作都在正确的租户范围内

**实施步骤：**

1. **创建租户验证中间件**
```go
// middleware/tenant_middleware.go
func ValidateTenantAccess() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := getUserIDFromContext(c)
        tenantID := getTenantIDFromContext(c)
        
        if !userService.UserBelongsToTenant(userID, tenantID) {
            commonErrors.Forbidden(c, "无权访问该租户数据")
            c.Abort()
            return
        }
        c.Next()
    }
}
```

2. **更新所有handler**
```go
// 在所有需要租户隔离的操作中添加验证
func (h *UserHandler) ListUsers(c *gin.Context) {
    tenantID := getTenantIDFromContext(c)
    
    // 强制设置租户ID，防止请求体篡改
    req.TenantID = tenantID
    
    users, err := h.userService.ListUsers(c.Request.Context(), req)
}
```

## 阶段二：参数验证增强（P1优先级）

### 2.1 统一参数验证

**目标：** 建立统一的参数验证机制

**实施步骤：**

1. **创建DTO验证规则**
```go
// dto/user_dto.go
type CreateUserRequest struct {
    Username string `json:"username" validate:"required,min=3,max=20,alphanum"`
    Email    string `json:"email" validate:"required,email"`
    Password string `json:"password" validate:"required,min=8"`
    TenantID int64  `json:"-"` // 不允许前端传递
}

func (r *CreateUserRequest) Validate() error {
    v := validator.NewValidator()
    v.Required(r.Username, "username")
    v.Username(r.Username, "username")
    v.Required(r.Email, "email")
    v.Email(r.Email, "email")
    v.Required(r.Password, "password")
    v.Password(r.Password, "password")
    return v.Validate()
}
```

2. **更新handler验证逻辑**
```go
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req dto.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        commonErrors.GinValidationError(c, err)
        return
    }
    
    // 统一验证
    if err := req.Validate(); err != nil {
        commonErrors.ValidationError(c, err.(*validator.ValidationErrors).ToResponseDetails())
        return
    }
    
    // 强制设置租户ID
    req.TenantID = getTenantIDFromContext(c)
}
```

### 2.2 添加业务规则验证

**实施步骤：**

1. **创建业务验证器**
```go
// internal/application/user/validator/business_validator.go
type UserBusinessValidator struct {
    userRepo repository.UserRepository
}

func (v *UserBusinessValidator) ValidateCreateUser(req *dto.CreateUserRequest) error {
    // 检查用户名唯一性
    if exists, _ := v.userRepo.UsernameExists(req.Username, req.TenantID); exists {
        return &commonErrors.FieldValidationError{
            Field:   "username",
            Message: "用户名已存在",
        }
    }
    
    // 检查邮箱唯一性
    if exists, _ := v.userRepo.EmailExists(req.Email, req.TenantID); exists {
        return &commonErrors.FieldValidationError{
            Field:   "email",
            Message: "邮箱已存在",
        }
    }
    
    return nil
}
```

## 阶段三：错误处理优化（P1优先级）

### 3.1 统一错误码定义

**实施步骤：**

1. **创建用户系统错误码**
```go
// internal/domain/user/errors/error_codes.go
const (
    // 用户管理错误码 (100000-109999)
    CodeUserNotFound        = 100001
    CodeUserExists          = 100002
    CodeUserStatusInvalid   = 100003
    CodeUserPasswordInvalid = 100004
    
    // 角色管理错误码 (110000-119999)
    CodeRoleNotFound    = 110001
    CodeRoleExists      = 110002
    CodeRoleInUse       = 110003
)

var ErrorMessages = map[int]string{
    CodeUserNotFound:        "用户不存在",
    CodeUserExists:          "用户已存在",
    CodeUserStatusInvalid:   "用户状态无效",
    CodeUserPasswordInvalid: "密码格式错误",
}
```

2. **更新错误处理**
```go
func (h *UserHandler) GetUser(c *gin.Context) {
    user, err := h.userService.GetUser(c.Request.Context(), id)
    if err != nil {
        if errors.Is(err, domain.ErrUserNotFound) {
            commonErrors.Error(c, userErrors.CodeUserNotFound)
            return
        }
        
        h.logger.Error("get user failed", logiface.Error(err))
        commonErrors.InternalError(c, fmt.Errorf("获取用户失败"))
        return
    }
}
```

### 3.2 敏感信息保护

**实施步骤：**

1. **创建日志脱敏工具**
```go
// pkg/utils/log_utils.go
func MaskEmail(email string) string {
    parts := strings.Split(email, "@")
    if len(parts) != 2 {
        return "***"
    }
    
    username := parts[0]
    if len(username) <= 2 {
        return "***@" + parts[1]
    }
    
    return username[:1] + "***" + username[len(username)-1:] + "@" + parts[1]
}

func MaskPhone(phone string) string {
    if len(phone) < 7 {
        return "***"
    }
    return phone[:3] + "****" + phone[len(phone)-4:]
}
```

2. **更新日志记录**
```go
h.logger.Info(c.Request.Context(), "create user attempt",
    logiface.String("username", req.Username),
    logiface.String("email", utils.MaskEmail(req.Email)),
    logiface.String("ip", c.ClientIP()),
)
```

## 阶段四：性能优化（P2优先级）

### 4.1 添加查询限制

**实施步骤：**

```go
func (h *UserHandler) ListUsers(c *gin.Context) {
    // 设置默认值和限制
    if req.Limit <= 0 {
        req.Limit = 20
    }
    if req.Limit > 1000 {
        req.Limit = 1000
    }
    if req.Offset < 0 {
        req.Offset = 0
    }
}
```

### 4.2 实现查询缓存

**实施步骤：**

```go
// 为频繁查询的数据添加缓存
func (h *UserHandler) GetUserRoles(c *gin.Context) {
    cacheKey := fmt.Sprintf("user:roles:%d", userID)
    
    // 尝试从缓存获取
    if cached, err := h.cache.Get(cacheKey); err == nil {
        commonErrors.Success(c, cached)
        return
    }
    
    // 从数据库查询
    roles, err := h.userService.GetUserRoles(c.Request.Context(), userID)
    if err != nil {
        commonErrors.InternalError(c, err)
        return
    }
    
    // 缓存结果
    h.cache.Set(cacheKey, roles, 5*time.Minute)
    commonErrors.Success(c, roles)
}
```

## 验证和测试

### 单元测试
```go
func TestUserHandler_CreateUser_ValidationError(t *testing.T) {
    // 测试参数验证
    req := dto.CreateUserRequest{
        Username: "ab", // 太短
        Email:    "invalid-email",
        Password: "123", // 太简单
    }
    
    // 验证应该失败
    err := req.Validate()
    assert.Error(t, err)
}
```

### 集成测试
```go
func TestUserAPI_CreateUser_PermissionDenied(t *testing.T) {
    // 使用普通用户token测试创建用户
    token := getRegularUserToken()
    
    resp := httptest.NewRecorder()
    req := httptest.NewRequest("POST", "/api/user/create", body)
    req.Header.Set("Authorization", "Bearer "+token)
    
    router.ServeHTTP(resp, req)
    
    // 应该返回权限不足错误
    assert.Equal(t, http.StatusForbidden, resp.Code)
}
```

## 实施时间表

### 第1周：安全性修复
- 统一响应格式
- 添加权限检查
- 完善租户隔离

### 第2周：参数验证增强
- 统一参数验证
- 添加业务规则验证

### 第3周：错误处理优化
- 统一错误码
- 敏感信息保护

### 第4周：性能优化和测试
- 查询限制和缓存
- 完善测试用例

## 风险评估

### 高风险
- 权限检查可能影响现有功能
- 响应格式变更可能影响前端

### 中风险
- 参数验证加强可能导致部分请求失败
- 错误码变更需要前端适配

### 低风险
- 性能优化对现有功能影响较小
- 日志格式变更不影响业务逻辑

## 回滚计划

1. **代码回滚：** 使用Git版本控制，每个阶段创建分支
2. **数据库回滚：** 备份数据库，准备回滚脚本
3. **配置回滚：** 保留原有配置文件备份
4. **监控告警：** 设置关键指标监控，异常时及时回滚

## 总结

本实施计划按优先级分阶段进行，重点解决安全性和一致性问题。建议严格按照计划执行，并在每个阶段完成后进行充分测试，确保系统稳定性。
