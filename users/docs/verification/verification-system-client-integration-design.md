# 通用验证系统客户端集成与改造设计文档

## 一、整体流程与改造目标

### 1.1 目标
- 客户端在所有涉及安全敏感操作（注册、登录、密码重置、信息变更、MFA等）时，能根据后端返回的提示，正确发起验证码请求、输入、校验等流程。
- 客户端需适配后端统一的验证码接口与错误码，保证用户体验和安全性。
- 支持多渠道（邮箱、短信、MFA）和多场景（注册、登录、重置密码等）的验证码交互。

### 1.2 统一接口
所有验证码相关操作均通过如下接口完成（见API规范）：
- 发送验证码/链接：`POST /api/user/verification/send`
- 校验验证码：`POST /api/user/verification/verify`
- 重新发送验证码：`POST /api/user/verification/resend`
- 检查验证码状态：`POST /api/user/verification/check-status`

## 二、客户端改造点全景

### 2.1 触发验证码的业务场景梳理

| 场景         | 是否需验证码 | 触发时机         | 说明                         |
|--------------|-------------|------------------|------------------------------|
| 注册         | 是          | 注册表单提交后   | 邮箱/手机激活                |
| 登录         | 视配置      | 登录表单提交后   | 可配置是否需要二次验证       |
| 密码重置     | 是          | 提交找回请求后   | 邮箱/手机/第三方             |
| 邮箱变更     | 是          | 提交变更请求后   | 新邮箱验证                   |
| 手机变更     | 是          | 提交变更请求后   | 新手机号验证                 |
| MFA验证      | 是          | 登录后/敏感操作  | 动态码/第三方App             |

### 2.2 客户端流程全景

1. **操作触发**：用户在注册、登录、重置密码等场景提交表单。
2. **后端响应**：后端根据业务逻辑和配置，决定是否需要验证码，并通过业务响应或错误码告知客户端。
3. **客户端识别**：客户端根据后端返回的 `code` 字段和错误码，判断是否需要进入验证码流程。
4. **验证码交互**：
   - 发起验证码发送请求（`/verification/send`）。
   - 展示验证码输入框，提示用户输入。
   - 用户输入后，调用 `/verification/verify` 校验。
   - 校验通过后，继续后续业务流程。
   - 支持重新发送（`/verification/resend`）、剩余次数/冷却时间提示（根据接口返回）。
5. **错误处理**：客户端需根据错误码（如频率超限、验证码错误、过期等）做友好提示。

## 三、验证需求判定表（Verification Requirement Matrix）

| 维度      | 判定条件/示例                | 是否需要验证 | 验证级别         | 说明                         |
|-----------|------------------------------|--------------|------------------|------------------------------|
| IP        | 登录失败次数 > 5             | 是           | 高（图形验证码+短信） | 针对暴力破解防护             |
| IP        | 黑名单IP                     | 是           | 高（图形+短信）   | 已知风险IP                   |
| UID       | 账号异常（如异地登录）        | 是           | 中（图形验证码）  | 账号安全策略                 |
| UID       | 普通用户，首次登录            | 否           | 无               | 正常流程                     |
| UID       | 多次登录失败                  | 是           | 低（图形验证码）  | 防止撞库                     |
| 设备ID    | 新设备登录                    | 是           | 中（图形验证码）  | 设备指纹识别                 |
| IP+UID    | 同一IP短时间内多账号登录      | 是           | 高（图形+短信）   | 群控/批量注册防护            |
| ...       | ...                          | ...          | ...              | ...                          |

### 验证级别说明
- **无**：不需要任何验证
- **低**：仅需图形验证码
- **中**：图形验证码 + 简单二次验证（如邮箱/短信验证码二选一）
- **高**：图形验证码 + 强二次验证（如短信验证码必填）

#### 设计说明
- 判定逻辑可由后端统一实现，前端根据接口返回的“是否需要验证”与“验证级别”动态展示相应验证方式。
- 支持多维度（IP、UID、设备ID等）组合判定，便于扩展。
- 验证级别可根据业务安全策略灵活调整。

## 三、接口交互与客户端实现细节

### 3.1 如何告知客户端需要验证码

#### 3.1.1 业务接口返回码约定
- 后端业务接口（如 `/register`, `/login`, `/reset-password` 等）在需要验证码时，返回特定业务错误码（如 `100005` 验证尝试次数超限、`100100` 频率超限、`100004` 验证码无效等），或在响应体中明确标识需要验证码。
- 客户端需维护一份验证码相关错误码表，见[错误码定义](#error-codes)。

#### 3.1.2 响应结构
所有接口响应结构统一，客户端可通过 `code` 字段和 `message`、`errors` 字段判断是否进入验证码流程。

```json
{
  "code": 100005,
  "message": "需要输入验证码",
  "data": {},
  "errors": [
    {
      "field": "token",
      "message": "请输入验证码"
    }
  ],
  "meta": { ... }
}
```

#### 3.1.3 场景举例
- **注册接口**：若需邮箱激活，注册成功后返回 `code=0`，客户端自动进入邮箱验证码输入流程。
- **登录接口**：若需二次验证，登录接口返回 `code=100004`，客户端弹出验证码输入框。
- **密码重置**：找回密码接口返回 `code=0`，客户端进入验证码发送与校验流程。

### 3.2 客户端流程实现建议

#### 3.2.1 统一验证码处理组件
- 封装验证码输入、发送、校验、重发、倒计时等逻辑为独立组件，便于各业务场景复用。
- 组件需支持多渠道（邮箱、短信、MFA）、多场景（注册、登录、重置密码等）参数配置。

#### 3.2.2 发送验证码
- 调用 `/api/user/verification/send`，传递 `target`（邮箱/手机号）、`target_type`、`purpose`、`token_type`。
- 根据接口返回的 `expires_at`、`max_attempts`、`rate_limit` 等信息，展示倒计时、剩余次数等提示。

#### 3.2.3 校验验证码
- 用户输入验证码后，调用 `/api/user/verification/verify`。
- 校验通过后，继续后续业务流程（如注册激活、密码重置、登录等）。
- 校验失败，根据错误码提示用户，并根据 `remaining_attempts` 控制输入次数。

#### 3.2.4 重新发送验证码
- 支持用户主动点击“重新发送”，调用 `/api/user/verification/resend`。
- 根据接口返回的冷却时间、剩余次数等信息，控制按钮可用状态和提示。

#### 3.2.5 检查验证码状态
- 可选：调用 `/api/user/verification/check-status` 查询验证码剩余有效期、剩余尝试次数等，优化用户体验。

#### 3.2.6 错误处理与提示
- 统一处理所有验证码相关错误码，给出明确、友好的用户提示。
- 典型错误码见下表。

### 3.3 错误码与客户端处理建议

| 错误码   | 场景/含义                 | 客户端处理建议           |
|----------|--------------------------|--------------------------|
| 100000   | 验证令牌不存在            | 提示“验证码无效”         |
| 100001   | 验证令牌已过期            | 提示“验证码已过期”，可重发 |
| 100002   | 验证令牌已使用            | 提示“验证码已失效”，可重发 |
| 100003   | 验证令牌已撤销            | 提示“验证码已撤销”，可重发 |
| 100004   | 验证令牌无效              | 提示“验证码错误”         |
| 100005   | 验证尝试次数超限          | 提示“尝试次数超限”，可重发 |
| 100100   | 验证发送频率超限          | 提示“发送过于频繁”       |
| 100101   | 验证日发送量超限          | 提示“今日发送次数已达上限” |
| 100102   | IP被阻止                  | 提示“操作受限，请稍后再试” |
| 100200   | 验证配置不存在            | 提示“系统配置错误”       |
| 100201   | 验证配置已禁用            | 提示“验证码服务不可用”   |
| 100202   | 验证模板不存在            | 提示“验证码模板错误”     |
| 100300   | 验证发送失败              | 提示“验证码发送失败”     |
| 100301   | 邮件发送失败              | 提示“邮件发送失败”       |
| 100302   | 短信发送失败              | 提示“短信发送失败”       |

## 四、客户端改造清单

### 4.1 业务流程适配
- 注册、登录、密码重置、信息变更、MFA等所有涉及验证码的业务流程，需适配统一的验证码交互逻辑。
- 业务接口需能识别后端返回的“需要验证码”信号，自动切换到验证码输入流程。

### 4.2 统一组件开发
- 开发/重构验证码输入与交互组件，支持多渠道、多场景参数化。
- 组件需支持倒计时、重发、错误提示、剩余次数等功能。

### 4.3 错误码与提示适配
- 客户端需维护一份验证码相关错误码表，所有相关接口均需统一处理。
- 错误提示需简洁明了，避免暴露系统内部信息。

### 4.4 交互优化
- 支持自动填充（如短信自动读取）、粘贴、键盘优化等。
- 支持冷却时间、剩余次数等友好提示。

### 4.5 兼容性与扩展性
- 组件设计需支持未来新增渠道/场景的扩展。
- 业务流程与验证码流程解耦，便于后续维护。

## 五、数据库设计

### 5.1 verification_tokens 验证令牌表
```sql
CREATE TABLE verification_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    user_id BIGINT UNSIGNED NULL COMMENT '用户ID，可为空（如注册时）',
    token VARCHAR(255) NOT NULL COMMENT '验证令牌/验证码',
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
    target VARCHAR(255) NOT NULL COMMENT '目标地址（邮箱/手机号）',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
    purpose TINYINT UNSIGNED NOT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
    template_code VARCHAR(100) NOT NULL COMMENT '关联的模板代码',
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态：1=未使用,2=已使用,3=已过期,4=已撤销',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    ip_address VARCHAR(45) COMMENT '请求IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_target(target),
    INDEX idx_purpose(purpose),
    INDEX idx_status(status),
    INDEX idx_expires_at(expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证令牌表';
```

### 5.2 verification_policies 验证策略表
```sql
CREATE TABLE verification_policies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    scene VARCHAR(32) NOT NULL COMMENT '业务场景，如login、register',
    dimension VARCHAR(32) NOT NULL COMMENT '判定维度，如IP、UID、DEVICE_ID、IP+UID',
    condition_expr VARCHAR(255) NOT NULL COMMENT '判定条件表达式，如fail_count>5',
    need_verification BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否需要验证',
    verification_level ENUM('none','low','medium','high') NOT NULL COMMENT '验证级别',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型',
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型',
    token_length TINYINT UNSIGNED NOT NULL COMMENT '令牌长度',
    expire_minutes INT UNSIGNED NOT NULL COMMENT '过期时间(分钟)',
    max_attempts INT UNSIGNED NOT NULL COMMENT '最大尝试次数',
    rate_limit_per_minute INT UNSIGNED NOT NULL COMMENT '每分钟限制',
    rate_limit_per_hour INT UNSIGNED NOT NULL COMMENT '每小时限制',
    rate_limit_per_day INT UNSIGNED NOT NULL COMMENT '每天限制',
    template_code VARCHAR(100) NOT NULL COMMENT '模板代码',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_tenant_scene_dimension_expr (tenant_id, scene, dimension, condition_expr)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证码判定与配置策略表';
```

---

#### 合并后设计说明

1. **一体化配置**：本表将原 verification_policies（判定条件）与 verification_configs（验证码参数）合并，每条策略既定义“什么情况下需要验证码”，也直接配置验证码参数。
2. **典型字段说明**：
   - scene：业务场景（如login、register、reset_password等）
   - dimension：判定维度（IP、UID、DEVICE_ID等）
   - condition_expr：判定条件表达式（如fail_count>5）
   - verification_level：none/low/medium/high，决定验证码强度
   - target_type、token_type、token_length、expire_minutes、max_attempts、rate_limit_per_xxx、template_code：验证码参数
3. **策略优先级与冲突处理**：可扩展 priority 字段，支持多条策略命中时按优先级或最严格原则处理。
4. **页面配置建议**：
   - 策略编辑时，既可配置判定条件（可视化表达式），也可配置验证码参数。
   - 支持表达式测试、模板快速插入、策略启用/禁用、优先级调整。
5. **后端判定流程**：
   - 业务请求时，后端收集判定变量，查找所有启用策略，按优先级/最严格原则选取。
   - 若 need_verification=true，则直接读取本策略的验证码参数，返回前端。
   - 命中策略、判定结果、触发原因等写入 verification_trigger_logs。
6. **接口适配**：接口响应中直接返回验证码参数，前端据此渲染验证码交互。
7. **兼容性与迁移**：
   - 原 verification_configs 的每条配置可转为一条 verification_policies，condition_expr 设为 true。
   - 原 verification_policies 的判定条件与 verification_configs 的参数合并，形成一体化策略。
   - 后端判定逻辑统一从新表读取，接口返回合并后的参数。
   - 前端无需变更，仅需根据接口返回参数渲染。
   - 支持灰度迁移、历史版本管理、优先级调整。

---

#### 典型配置示例

| 策略名称         | scene    | dimension | condition_expr         | need_verification | verification_level | target_type | token_type | ... | template_code | 说明                 |
|------------------|----------|-----------|-----------------------|-------------------|-------------------|-------------|------------|-----|---------------|----------------------|
| 登录失败防暴力   | login    | IP        | fail_count > 5        | true              | high              | 2           | 2          | ... | login_sms     | 同一IP失败超5次      |
| 黑名单IP         | login    | IP        | is_black_ip == true   | true              | high              | 2           | 2          | ... | login_sms     | 命中黑名单           |
| 新设备登录       | login    | DEVICE_ID | is_new_device == true | true              | medium            | 1           | 2          | ... | login_email   | 首次设备登录         |
| 注册             | register | UID       | true                  | true              | low               | 1           | 2          | ... | reg_email     | 注册场景             |

---

#### 迁移与兼容性建议

- 原 verification_configs 的每条配置可转为一条 verification_policies，condition_expr 设为 true。
- 原 verification_policies 的判定条件与 verification_configs 的参数合并，形成一体化策略。
- 后端判定逻辑统一从新表读取，接口返回合并后的参数。
- 前端无需变更，仅需根据接口返回参数渲染。
- 支持灰度迁移、历史版本管理、优先级调整。

### 5.x verification_trigger_logs 验证触发记录表
```sql
CREATE TABLE verification_trigger_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NULL COMMENT '用户ID',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    device_id VARCHAR(128) NULL COMMENT '设备ID',
    policy_id BIGINT NULL COMMENT '命中的策略ID',
    verification_needed BOOLEAN NOT NULL COMMENT '是否需要验证',
    verification_level ENUM('none','low','medium','high') NOT NULL COMMENT '验证级别',
    trigger_reason VARCHAR(255) COMMENT '触发原因/命中条件',
    scene VARCHAR(32) NOT NULL COMMENT '业务场景，如login、register',
    request_id VARCHAR(64) COMMENT '请求唯一ID，便于链路追踪',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    extra JSON NULL COMMENT '其他扩展信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证触发判定记录表';
```

## condition_expr 条件表达式设计（新版）

### 一、功能目标
- 支持多维度（如 ip、uid、device_id、time）条件的灵活组合
- 条件由“维度-维度key-表达式-值”四元组组成
- 支持多条件嵌套组合，逻辑关系为 AND/OR/NOT
- 前端页面支持级联选择、输入类型自动联动

---

### 二、前端页面设计方案

#### 1. 页面结构
- 条件列表区：展示所有已配置条件（支持嵌套 AND/OR/NOT）
- 条件编辑区：每条条件为一行，包含四个下拉/输入框（维度、维度key、表达式、值），以及操作按钮（添加、删除、嵌套逻辑）
- 逻辑组合区：支持添加 AND/OR/NOT 组合块，块内可继续添加条件或嵌套组合
- 预览区（可选）：实时展示最终生成的 condition_expr JSON 或人类可读表达式

#### 2. 交互流程
- 用户点击“添加条件”，出现一行四元组选择
- 选择“维度”后，自动联动“维度key”下拉选项
- 选择“维度key”后，自动联动“表达式”下拉选项和“值”输入类型（如布尔、数字、时间段等）
- 支持添加多条条件，并通过 AND/OR/NOT 组合
- 支持嵌套组合（如 AND 内嵌 OR，OR 内嵌 NOT 等）

---

### 三、Schema 设计（前端用）

#### 1. 维度、维度key、表达式、值的 schema

```ts
// 维度定义
const DIMENSIONS = [
  {
    value: 'ip',
    label: 'IP',
    keys: [
      { value: 'fail_count', label: '失败次数', type: 'number', operators: ['>', '<', '>=', '<=', '==', '!=', 'between'] },
      { value: 'is_black_ip', label: '黑名单', type: 'boolean', operators: ['==', '!='] }
    ]
  },
  {
    value: 'uid',
    label: '用户ID',
    keys: [
      { value: 'fail_count', label: '失败次数', type: 'number', operators: ['>', '<', '>=', '<=', '==', '!=', 'between'] },
      { value: 'is_black_uid', label: '黑名单', type: 'boolean', operators: ['==', '!='] },
      { value: 'is_new_user', label: '新注册', type: 'boolean', operators: ['==', '!='] }
    ]
  },
  {
    value: 'device_id',
    label: '设备ID',
    keys: [
      { value: 'fail_count', label: '失败次数', type: 'number', operators: ['>', '<', '>=', '<=', '==', '!=', 'between'] },
      { value: 'is_black_device', label: '黑名单', type: 'boolean', operators: ['==', '!='] }
    ]
  },
  {
    value: 'time',
    label: '时间',
    keys: [
      { value: 'within_5min', label: '5分钟内', type: 'boolean', operators: ['=='] },
      { value: 'within_10min', label: '10分钟内', type: 'boolean', operators: ['=='] }
    ]
  }
];

// 表达式定义
const OPERATORS = [
  { value: '>', label: '大于' },
  { value: '<', label: '小于' },
  { value: '>=', label: '大于等于' },
  { value: '<=', label: '小于等于' },
  { value: '==', label: '等于' },
  { value: '!=', label: '不等于' },
  { value: 'between', label: '区间' }
];
```

#### 2. 条件表达式数据结构（前端/后端通用）

```ts
// 单个条件
interface Condition {
  dimension: string;      // 维度
  key: string;           // 维度key
  operator: string;      // 表达式
  value: any;            // 值
}

// 组合条件
interface ConditionGroup {
  logic: 'AND' | 'OR' | 'NOT';
  conditions: Array<Condition | ConditionGroup>;
}
```

---

### 四、前端表单联动约束逻辑
- 选择“维度”后，维度key下拉只显示该维度下的key
- 选择“维度key”后，表达式下拉只显示该key支持的操作符
- 选择“表达式”后，值输入框类型自动切换（如布尔、数字、区间、只读等）
- “值”输入支持类型校验（如布尔型只能选 true/false，区间型输入两个数字等）

---

### 五、页面UI示意（伪代码）

```jsx
<ConditionGroupEditor>
  <LogicSelector value="AND" onChange={...} />
  <ConditionRow>
    <Select value={dimension} onChange={...} options={DIMENSIONS} />
    <Select value={key} onChange={...} options={getKeysByDimension(dimension)} />
    <Select value={operator} onChange={...} options={getOperatorsByKey(dimension, key)} />
    <Input value={value} onChange={...} type={getValueType(dimension, key)} />
    <Button onClick={addCondition}>添加</Button>
    <Button onClick={removeCondition}>删除</Button>
  </ConditionRow>
  <Button onClick={addGroup}>添加 AND/OR/NOT 组合</Button>
</ConditionGroupEditor>
```

---

### 六、配置示例（JSON）

```json
{
  "logic": "AND",
  "conditions": [
    {
      "dimension": "ip",
      "key": "fail_count",
      "operator": ">=",
      "value": 5
    },
    {
      "logic": "OR",
      "conditions": [
        {
          "dimension": "uid",
          "key": "is_new_user",
          "operator": "==",
          "value": true
        },
        {
          "dimension": "time",
          "key": "within_5min",
          "operator": "==",
          "value": true
        }
      ]
    }
  ]
}
```

---

### 七、联动约束实现要点
- 维度、维度key、表达式、值的 schema 需全局唯一维护，便于联动
- 前端表单组件需根据 schema 实现级联选择和输入类型切换
- 组合条件（AND/OR/NOT）支持递归嵌套，UI 需支持树形/嵌套结构编辑

---

### 八、可选增强
- 支持条件预览、语法校验、导入导出 JSON
- 支持条件模板复用、常用条件一键插入

## 六、流程时序图

```
```
