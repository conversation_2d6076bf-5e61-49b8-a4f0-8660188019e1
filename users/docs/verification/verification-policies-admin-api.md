# 验证策略管理后台接口文档 (已废弃)

> **⚠️ 警告：此文档已废弃**
> 
> 验证策略系统已重构为统一的验证配置管理系统。
> 
> **请使用新文档：** [verification-configs-admin-api.md](./verification-configs-admin-api.md)

## 重构说明

### 系统变更
- ✅ **旧系统**: 基于 verification_policies 表的策略管理
- ✅ **新系统**: 基于 verification_configs 表的统一配置管理

### 主要变化
1. **策略 → 配置**: 将"策略"概念重构为"配置"，更加清晰准确
2. **双模式支持**: 支持静态配置和动态配置两种模式
3. **统一接口**: 所有配置通过统一的接口进行管理
4. **智能查找**: 实现动态配置 → 静态配置 → 系统默认的优先级查找

### API路径变更
- **旧路径**: `/api/user/verification/policies/*`
- **新路径**: `/api/users/verification/configs/*`

### 数据结构变更
- **旧结构**: 单一的策略表结构
- **新结构**: 统一配置表支持双模式（static/dynamic）

### 迁移指南
1. 更新前端代码中的API调用路径
2. 更新接口调用从策略相关接口到配置相关接口
3. 更新数据结构处理逻辑以支持新的配置模式
4. 执行数据迁移脚本将旧策略数据迁移到新配置表

### 相关文档
- [新配置管理API文档](./verification-configs-admin-api.md)
- [系统重构设计文档](./verification-system-redesign.md)
- [数据迁移脚本](../../migrations/0011_merge_verification_policies_to_configs.sql)

---

**如需了解新系统的使用方法，请参考新的配置管理API文档。**