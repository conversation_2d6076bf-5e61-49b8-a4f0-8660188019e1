# verification_policies 管理后台接口文档

> **本文件为管理后台接口文档，所有接口遵循统一 HTTP API 设计规范**

## 目录
- [1. 策略结构说明](#1-策略结构说明)
- [2. condition_expr 设计与管理](#2-condition_expr-设计与管理)
- [3. 策略接口](#3-策略接口)
  - [3.1 策略列表（GET）](#31-策略列表get)
  - [3.2 策略详情（GET）](#32-策略详情get)
  - [3.3 创建策略（POST）](#33-创建策略post)
  - [3.4 编辑策略（POST）](#34-编辑策略post)
  - [3.5 删除策略（POST）](#35-删除策略post)
  - [3.6 启用/禁用策略（POST）](#36-启用禁用策略post)
  - [3.7 表达式校验与测试（POST）](#37-表达式校验与测试post)
- [4. 错误码](#4-错误码)
- [5. 使用建议与安全性](#5-使用建议与安全性)

---

## 1. 策略结构说明

verification_policies 用于灵活配置验证码触发判定与参数，支持多租户、多场景、多维度。

| 字段           | 类型     | 说明                                   |
|----------------|----------|----------------------------------------|
| id             | bigint   | 策略ID                                 |
| tenant_id      | bigint   | 租户ID                                 |
| scene          | string   | 业务场景（如 login、register 等）      |
| dimension      | string   | 判定维度（如 IP、UID、DEVICE_ID）      |
| condition_expr | string   | 判定条件表达式                         |
| need_verification | bool  | 是否需要验证码                         |
| verification_level | string | 验证级别（none/low/medium/high）      |
| target_type    | int      | 验证码目标类型（1=邮箱，2=手机号，3=MFA）|
| token_type     | int      | 验证码类型（1=链接，2=数字码）         |
| token_length   | int      | 验证码长度                             |
| expire_minutes | int      | 有效期（分钟）                         |
| max_attempts   | int      | 最大尝试次数                           |
| rate_limit_per_minute | int | 每分钟限制                            |
| rate_limit_per_hour   | int | 每小时限制                            |
| rate_limit_per_day    | int | 每天限制                              |
| template_code  | string   | 模板代码                               |
| is_active      | bool     | 是否启用                               |
| priority       | int      | 优先级，数值越小优先级越高             |
| description    | string   | 策略描述                               |
| created_at     | string   | 创建时间                               |
| updated_at     | string   | 更新时间                               |

---

## 2. condition_expr 设计与管理

condition_expr 用于灵活表达验证码触发条件，支持多变量、多运算符、组合逻辑。

### 2.1 支持变量
| 变量名         | 说明                   |
|----------------|------------------------|
| fail_count     | 当前维度下失败次数     |
| ip             | 当前请求IP             |
| uid            | 当前用户ID             |
| device_id      | 当前设备ID             |
| geo_location   | 地理位置（如国家/省份）|
| hour           | 当前小时（0-23）       |
| is_black_ip    | 是否黑名单IP           |
| is_new_device  | 是否新设备             |
| ...            | 可扩展                 |

### 2.2 表达式语法
- 支持比较运算符：> < >= <= == !=
- 支持逻辑运算符：AND OR NOT
- 支持括号分组
- 支持 in、not in 集合判断
- 变量区分大小写，字符串需加引号

**示例表达式：**
- fail_count > 5
- is_black_ip == true
- fail_count > 3 AND hour >= 22
- geo_location in ("CN", "RU")
- is_new_device == true OR device_id == "abc123"

### 2.3 表达式校验与测试
- 提供表达式语法校验接口，防止配置错误
- 支持输入样例数据进行表达式测试，预览命中结果
- 支持常用模板快速插入

---

## 3. 策略接口

### 3.1 策略列表（GET）
- **接口地址**：`/api/admin/verification-policies/list`
- **方法**：GET
- **参数**（query string）：
  - page（int，默认1）
  - pageSize（int，默认20）
  - tenant_id、scene、is_active、keyword（可选筛选）
- **响应结构**：
```json
{
  "code": 0,
  "message": "Success",
  "data": {
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "list": [ { /* 策略对象 */ } ]
  },
  "errors": [],
  "meta": { "request_id": "req_xxx", "timestamp": 1640995200000 }
}
```

### 3.2 策略详情（GET）
- **接口地址**：`/api/admin/verification-policies/detail`
- **方法**：GET
- **参数**（query string）：id（必填）
- **响应结构**：同上，data 为单个策略对象

### 3.3 创建策略（POST）
- **接口地址**：`/api/admin/verification-policies/create`
- **方法**：POST
- **参数**（body）：见策略结构，condition_expr 必须通过校验
- **响应结构**：
```json
{
  "code": 0,
  "message": "Success",
  "data": { "id": 123 },
  "errors": [],
  "meta": { "request_id": "req_xxx", "timestamp": 1640995200000 }
}
```
- **校验失败示例**：
```json
{
  "code": 40001,
  "message": "表达式语法错误",
  "data": {},
  "errors": [ { "field": "condition_expr", "message": "语法错误" } ],
  "meta": { "request_id": "req_xxx", "timestamp": 1640995200000 }
}
```

### 3.4 编辑策略（POST）
- **接口地址**：`/api/admin/verification-policies/update`
- **方法**：POST
- **参数**（body）：见策略结构，id 必填，condition_expr 必须通过校验
- **响应结构**：同创建

### 3.5 删除策略（POST）
- **接口地址**：`/api/admin/verification-policies/delete`
- **方法**：POST
- **参数**（body）：id（必填）
- **响应结构**：同创建

### 3.6 启用/禁用策略（POST）
- **接口地址**：`/api/admin/verification-policies/set-status`
- **方法**：POST
- **参数**（body）：id（必填），is_active（bool，必填）
- **响应结构**：同创建

### 3.7 表达式校验与测试（POST）
- **表达式语法校验**
  - **接口地址**：`/api/admin/verification-policies/expr-validate`
  - **方法**：POST
  - **参数**（body）：{ "condition_expr": "fail_count > 5 AND hour >= 22" }
  - **响应结构**：
```json
{
  "code": 0,
  "message": "Success",
  "data": { "valid": true, "error": "" },
  "errors": [],
  "meta": { "request_id": "req_xxx", "timestamp": 1640995200000 }
}
```
- **表达式测试**
  - **接口地址**：`/api/admin/verification-policies/expr-test`
  - **方法**：POST
  - **参数**（body）：{ "condition_expr": "fail_count > 5", "sample": { "fail_count": 6 } }
  - **响应结构**：
```json
{
  "code": 0,
  "message": "Success",
  "data": { "result": true },
  "errors": [],
  "meta": { "request_id": "req_xxx", "timestamp": 1640995200000 }
}
```

---

## 4. 错误码
| code   | message                | 说明                       |
|--------|------------------------|----------------------------|
| 0      | success                | 操作成功                   |
| 40001  | invalid_expr           | 表达式语法错误             |
| 40002  | missing_required_param | 缺少必要参数               |
| 40003  | expr_test_failed       | 表达式测试失败             |
| 40004  | duplicate_policy       | 策略重复                   |
| 40401  | policy_not_found       | 策略不存在                 |
| 40301  | forbidden              | 无权限                     |
| 50001  | internal_error         | 系统内部错误               |

---

## 5. 使用建议与安全性
- condition_expr 配置需严格校验，防止语法和逻辑错误
- 建议通过表达式测试功能验证命中效果
- 支持表达式模板和历史版本管理，便于回溯和审计
- 管理接口需鉴权、审计，防止误操作
- 支持多租户、优先级、批量操作等扩展 