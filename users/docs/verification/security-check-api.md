# 通用安全校验接口（security-check）API 设计文档

> **本文件为 C端接口文档**

## 目录
- [1. security-check 接口](#1-security-check-接口)
- [2. send-code 接口](#2-send-code-接口)
- [3. verify-code 接口](#3-verify-code-接口)
- [4. 异常与错误码](#4-异常与错误码)
- [5. 使用建议](#5-使用建议)
- [6. 安全与扩展性](#6-安全与扩展性)

---

## 1. security-check 接口

用于前端在用户进入敏感页面（如登录、注册、密码重置、信息变更、MFA等）或关键操作前，实时判断当前用户/设备/场景下是否需要展示验证码组件，并获取验证码相关参数。
该接口基于后端 verification_policies 判定逻辑，支持多场景动态判定。

### 接口定义
- **接口地址**：`POST /api/user/security-check`
- **请求方式**：POST
- **请求类型**：application/json
- **鉴权方式**：可选（部分场景如注册、找回密码可匿名，登录后场景需带 token）

### 请求参数
| 字段   | 类型   | 必填 | 说明                         |
|--------|--------|------|------------------------------|
| scene  | string | 是   | 业务场景（如 login、register、reset_password、change_email、mfa 等）|

> 说明：除 scene 外，所有判定所需信息（如用户ID、IP、设备ID、失败次数等）均由服务端自动获取，无需前端传递。

**示例：**
```json
{
  "scene": "login"
}
```

### 响应参数
| 字段                | 类型     | 说明                                   |
|---------------------|----------|----------------------------------------|
| code                | int      | 0=成功，非0=失败                       |
| message             | string   | 响应消息                               |
| data                | object   | 判定结果及验证码参数                   |
| └ need_verification | boolean  | 是否需要验证码                         |
| └ target_type       | int      | 验证码目标类型（1=邮箱，2=手机号，3=MFA）|
| └ token_type        | int      | 验证码类型（1=链接，2=数字码）         |

**示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "need_verification": true,
    "target_type": 2,
    "token_type": 2
  }
}
```

**无需验证码时：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "need_verification": false,
    "target_type": null,
    "token_type": null
  }
}
```

---

## 2. send-code 接口

用于前端在需要发送验证码时（如用户点击“获取验证码”按钮），请求服务端向指定目标（邮箱、手机号等）发送验证码。

### 接口定义
- **接口地址**：`POST /api/user/send-code`
- **请求方式**：POST
- **请求类型**：application/json
- **鉴权方式**：可选（部分场景如注册、找回密码可匿名，登录后场景需带 token）

### 请求参数
| 字段      | 类型   | 必填 | 说明                         |
|-----------|--------|------|------------------------------|
| scene     | string | 是   | 业务场景（如 login、register、reset_password、change_email、mfa 等）|
| target    | string | 是   | 验证目标（如邮箱、手机号）    |

**示例：**
```json
{
  "scene": "login",
  "target": "<EMAIL>"
}
```

### 响应参数
| 字段          | 类型     | 说明                                   |
|---------------|----------|----------------------------------------|
| code          | int      | 0=发送成功，非0=失败                   |
| message       | string   | 响应消息                               |
| data          | object   | 发送结果                               |
| └ sent        | boolean  | 是否发送成功                           |
| └ expires_in  | int      | 验证码有效期（秒），可选               |
| └ remaining   | int      | 剩余可发送次数（如频控），可选         |
| └ cooldown    | int      | 冷却时间（秒），如需等待，可选         |
| └ next_send_at| string   | 下次可发送时间（ISO8601），可选        |

> 说明：当触发频控（如发送频率超限、冷却中等）时，sent=false，并返回 remaining、cooldown、next_send_at 等字段，前端可据此提示用户。

**发送成功示例：**
```json
{
  "code": 0,
  "message": "验证码已发送",
  "data": {
    "sent": true,
    "expires_in": 300,
    "remaining": 4
  }
}
```

**频控限制示例：**
```json
{
  "code": 100100,
  "message": "发送过于频繁，请稍后再试",
  "data": {
    "sent": false,
    "remaining": 0,
    "cooldown": 60,
    "next_send_at": "2024-07-16T12:01:00Z"
  }
}
```

**发送失败示例：**
```json
{
  "code": 100300,
  "message": "验证码发送失败，请稍后重试",
  "data": {
    "sent": false
  }
}
```

---

## 3. verify-code 接口

用于前端在用户输入验证码后，提交验证码进行校验，判断验证码是否正确、是否过期、是否可用。

### 接口定义
- **接口地址**：`POST /api/user/verify-code`
- **请求方式**：POST
- **请求类型**：application/json
- **鉴权方式**：可选（部分场景如注册、找回密码可匿名，登录后场景需带 token）

### 请求参数
| 字段      | 类型   | 必填 | 说明                         |
|-----------|--------|------|------------------------------|
| scene     | string | 是   | 业务场景（如 login、register、reset_password、change_email、mfa 等）|
| target    | string | 是   | 验证目标（如邮箱、手机号）    |
| code      | string | 是   | 用户输入的验证码             |

**示例：**
```json
{
  "scene": "login",
  "target": "<EMAIL>",
  "code": "123456"
}
```

### 响应参数
| 字段      | 类型     | 说明                                   |
|-----------|----------|----------------------------------------|
| code      | int      | 0=校验成功，非0=失败                   |
| message   | string   | 响应消息                               |
| data      | object   | 校验结果                               |
| └ valid   | boolean  | 校验是否通过                           |

**校验成功示例：**
```json
{
  "code": 0,
  "message": "验证码校验通过",
  "data": {
    "valid": true
  }
}
```

**校验失败示例：**
```json
{
  "code": 100004,
  "message": "验证码错误或已过期",
  "data": {
    "valid": false
  }
}
```

---

## 4. 异常与错误码

| code   | message                | 说明                       |
|--------|------------------------|----------------------------|
| 0      | success/验证码校验通过 | 判定/校验/发送成功         |
| 100100 | rate_limit_exceeded    | 发送频率超限               |
| 100101 | daily_limit_exceeded   | 当日发送次数超限           |
| 100102 | ip_blocked             | IP被封禁                   |
| 100200 | config_not_found       | 验证码配置不存在           |
| 100201 | config_disabled        | 验证码配置已禁用           |
| 100202 | template_not_found     | 验证码模板不存在           |
| 100300 | send_failed            | 验证码发送失败             |
| 100301 | email_send_failed      | 邮件发送失败               |
| 100302 | sms_send_failed        | 短信发送失败               |
| 100001 | code_expired           | 验证码已过期               |
| 100002 | code_used              | 验证码已被使用             |
| 100004 | code_invalid           | 验证码错误                 |
| 100005 | attempts_exceeded      | 尝试次数超限               |
| 40001  | invalid_scene          | 场景参数无效               |
| 40002  | missing_required_param | 缺少必要参数               |
| 50001  | internal_error         | 系统内部错误               |

---

## 5. 使用建议

- 前端在页面加载或关键操作前，调用 security-check 接口，仅需传递 scene 字段。
- 若 `need_verification=true`，则展示验证码组件，并根据返回的 target_type、token_type 渲染类型。
- 用户点击“获取验证码”时，调用 send-code 接口。
- 用户输入验证码后，调用 verify-code 接口进行校验。
- 校验通过后，继续后续业务流程；校验失败时根据错误码友好提示。
- 建议接口响应缓存短时间（如1分钟），避免频繁判定。

---

## 6. 安全与扩展性

- 服务端自动收集判定所需的所有上下文信息，前端无需关心。
- 支持多场景、灵活策略配置。
- 建议接口强制 HTTPS，敏感参数加密传输。
- 支持接口限流与审计，防止刷接口。 