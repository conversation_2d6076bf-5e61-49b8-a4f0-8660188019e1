# 模板驱动消息发送系统设计方案

## 一、设计目标

### 1.1 核心目标
- 以消息模板为中心，所有消息发送（邮件、短信等）均通过模板驱动
- 每个模板绑定一个发件账户（如邮件SMTP、短信签名等）
- 发送时只需指定模板代码、收件人和变量参数，系统自动完成账户选择、内容渲染和频控校验
- 支持多租户、频控、变量校验、模板内容渲染等能力

### 1.2 设计原则
- 遵循DDD架构设计
- 遵循Clean Architecture分层原则
- 保持与现有系统的一致性
- 考虑系统的扩展性和可维护性

## 二、数据模型设计

### 2.1 使用现有表结构

#### 2.1.1 邮件账户表（email_accounts）
使用现有表，已包含以下核心字段：
```sql
- id: BIGINT UNSIGNED (主键)
- account_id: VARCHAR(100) (业务ID)
- tenant_id: VARCHAR(50) (租户ID)
- name: VARCHAR(255) (账户名称)
- type: INT (账户类型)
- provider: VARCHAR(100) (服务提供商)
- host: VARCHAR(255) (SMTP主机)
- port: INT (端口)
- username: VARCHAR(255) (用户名)
- password: VARCHAR(500) (密码)
- from_address: VARCHAR(255) (发件地址)
- from_name: VARCHAR(255) (发件人名称)
- is_ssl: BOOLEAN (是否使用SSL)
- is_active: BOOLEAN (是否激活)
- daily_limit: INT (每日限制)
- monthly_limit: INT (每月限制)
```

#### 2.1.2 邮件模板表（email_templates）
使用现有表，已包含以下核心字段：
```sql
- id: BIGINT UNSIGNED (主键)
- tenant_id: BIGINT UNSIGNED (租户ID)
- name: VARCHAR(100) (模板名称)
- type: TINYINT UNSIGNED (模板类型)
- subject: VARCHAR(200) (邮件主题)
- html_content: TEXT (HTML内容)
- plain_text_content: TEXT (纯文本内容)
- variables: JSON (变量定义)
- is_active: BOOLEAN (是否激活)
```

#### 2.1.3 邮件消息表（email_messages）
使用现有表，已包含以下核心字段：
```sql
- id: BIGINT UNSIGNED (主键)
- email_id: VARCHAR(100) (业务ID)
- tenant_id: VARCHAR(50) (租户ID)
- template_id: VARCHAR(100) (模板ID)
- from_address: VARCHAR(200) (发件地址)
- to_addresses: JSON (收件人列表)
- cc_addresses: JSON (抄送列表)
- bcc_addresses: JSON (密送列表)
- subject: VARCHAR(500) (主题)
- html_content: TEXT (HTML内容)
- text_content: TEXT (纯文本内容)
- variables: JSON (模板变量)
- status: VARCHAR(20) (状态)
```

### 2.2 新增表结构

#### 2.2.1 验证令牌表（verification_tokens）
```sql
CREATE TABLE verification_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    user_id BIGINT UNSIGNED NULL COMMENT '用户ID，可为空（如注册时）',
    token VARCHAR(255) NOT NULL COMMENT '验证令牌/验证码',
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
    target VARCHAR(255) NOT NULL COMMENT '目标地址（邮箱/手机号）',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型：1=邮箱,2=手机号',
    template_id BIGINT UNSIGNED NOT NULL COMMENT '关联的模板ID',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    
    -- 基础字段
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态：1=未使用,2=已使用,3=已过期',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE KEY uk_token (token),
    KEY idx_target (target_type, target),
    KEY idx_user (tenant_id, user_id),
    KEY idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证令牌表';
```

## 三、接口设计

### 3.1 密码重置邮件发送接口

#### 请求接口
- 路径: `POST /api/user/auth/request-password-reset`
- 请求头:
  ```
  X-Tenant-Code: {tenant_code}
  Content-Type: application/json
  ```

#### 请求参数
```json
{
  "email": "<EMAIL>",
  "reset_type": "link",  // "link" 或 "code"
  "callback_url": "https://example.com/reset-password"  // 仅 reset_type=link 时需要
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |
| reset_type | string | 是 | 重置方式：link=重置链接, code=验证码 |
| callback_url | string | 否 | 重置链接回调地址，仅reset_type=link时必填 |

#### 响应格式

##### 成功响应
```json
{
  "code": 0,
  "message": "密码重置邮件已发送",
  "data": {
    "email": "<EMAIL>",
    "expires_at": "2024-03-01T14:30:00Z"
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1709304000000
  }
}
```

##### 错误响应
```json
{
  "code": 40001,
  "message": "邮箱地址不存在",
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1709304000000
  }
}
```

#### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 40001 | 邮箱地址不存在 |
| 40002 | 邮箱格式无效 |
| 40003 | 回调地址无效 |
| 40004 | 重置类型无效 |
| 42901 | 发送频率超限 |
| 50001 | 系统错误 |

### 3.2 验证重置令牌接口

#### 请求接口
- 路径: `POST /api/user/auth/verify-reset-token`
- 请求头:
  ```
  X-Tenant-Code: {tenant_code}
  Content-Type: application/json
  ```

#### 请求参数
```json
{
  "email": "<EMAIL>",
  "token": "123456",  // 验证码或重置令牌
  "new_password": "newPassword123"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| email | string | 是 | 用户邮箱地址 |
| token | string | 是 | 验证码或重置令牌 |
| new_password | string | 是 | 新密码 |

#### 响应格式

##### 成功响应
```json
{
  "code": 0,
  "message": "密码重置成功",
  "data": {
    "email": "<EMAIL>",
    "reset_at": "2024-03-01T14:30:00Z"
  },
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1709304000000
  }
}
```

##### 错误响应
```json
{
  "code": 40101,
  "message": "验证码无效或已过期",
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1709304000000
  }
}
```

#### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 40101 | 验证码无效或已过期 |
| 40102 | 验证码已使用 |
| 40103 | 新密码不符合要求 |
| 40104 | 邮箱与验证码不匹配 |
| 50001 | 系统错误 |

## 四、实现细节

### 4.1 密码重置流程

1. 用户请求密码重置
   - 验证邮箱是否存在
   - 检查发送频率限制
   - 生成验证令牌/验证码
   - 选择对应的邮件模板
   - 发送重置邮件

2. 用户验证重置令牌
   - 验证令牌有效性
   - 验证令牌是否过期
   - 验证令牌是否已使用
   - 更新用户密码
   - 标记令牌为已使用

### 4.2 安全考虑

1. 令牌安全
   - 使用足够长度的随机令牌
   - 设置合理的过期时间
   - 一次性使用，使用后立即失效
   - 限制单个邮箱的未使用令牌数量

2. 频率限制
   - 基于模板的发送频率限制
   - 基于账户的发送频率限制
   - 基于用户的请求频率限制

3. 密码安全
   - 强制密码复杂度要求
   - 密码加密存储
   - 禁止重复使用最近的密码

### 4.3 模板配置

1. 链接重置模板
```json
{
  "template_code": "password_reset_link",
  "template_type": 1,
  "subject": "密码重置请求",
  "content": "尊敬的用户：\n\n您请求重置密码，请点击以下链接完成重置：\n{{reset_link}}\n\n链接有效期为{{expire_minutes}}分钟。\n\n如果这不是您的操作，请忽略此邮件。",
  "variables": {
    "reset_link": {
      "type": "string",
      "required": true,
      "description": "重置链接"
    },
    "expire_minutes": {
      "type": "number",
      "required": true,
      "description": "过期时间（分钟）"
    }
  }
}
```

2. 验证码重置模板
```json
{
  "template_code": "password_reset_code",
  "template_type": 1,
  "subject": "密码重置验证码",
  "content": "尊敬的用户：\n\n您的密码重置验证码为：{{verify_code}}\n\n验证码有效期为{{expire_minutes}}分钟。\n\n如果这不是您的操作，请忽略此邮件。",
  "variables": {
    "verify_code": {
      "type": "string",
      "required": true,
      "description": "验证码"
    },
    "expire_minutes": {
      "type": "number",
      "required": true,
      "description": "过期时间（分钟）"
    }
  }
}
```

## 五、扩展性考虑

### 5.1 支持的扩展场景

1. 验证场景扩展
   - 邮箱验证
   - 手机号验证
   - 身份验证
   - 操作确认

2. 发送渠道扩展
   - 短信验证码
   - 微信消息
   - 钉钉消息
   - 其他消息渠道

3. 模板类型扩展
   - HTML模板
   - 富文本模板
   - 多语言模板
   - 自定义模板

### 5.2 后续优化方向

1. 性能优化
   - 引入消息队列
   - 异步发送处理
   - 批量发送支持
   - 发送状态追踪

2. 运营支持
   - 发送统计分析
   - 发送失败分析
   - 用户行为分析
   - 模板使用分析

3. 安全增强
   - IP限制
   - 设备指纹
   - 风险控制
   - 审计日志 