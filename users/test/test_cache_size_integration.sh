#!/bin/bash

# 测试脚本：验证 cache_size 字段的集成
# 创建时间：2025-01-27

set -e

echo "=== 开始测试 cache_size 字段集成 ==="

# 1. 检查数据库表结构
echo "1. 检查数据库表结构..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'platforms-user' 
AND TABLE_NAME = 'id_sequence' 
AND COLUMN_NAME IN ('increment_step', 'cache_size')
ORDER BY ORDINAL_POSITION;
"

# 2. 检查现有序列的 cache_size 配置
echo "2. 检查现有序列的 cache_size 配置..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
SELECT 
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    threshold,
    is_active
FROM id_sequence 
ORDER BY business_type, tenant_id;
"

# 3. 测试创建新序列（包含 cache_size）
echo "3. 测试创建新序列..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
INSERT INTO id_sequence (business_type, sequence_name, tenant_id, current_value, increment_step, cache_size, max_value, min_value, threshold, is_active, created_at, updated_at) 
VALUES ('test', 'test_sequence', 1, 0, 500, 2000, 9223372036854775807, 1, 30, TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    increment_step = VALUES(increment_step),
    cache_size = VALUES(cache_size),
    updated_at = NOW();
"

# 4. 验证新序列的配置
echo "4. 验证新序列的配置..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
SELECT 
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    threshold,
    is_active
FROM id_sequence 
WHERE business_type = 'test'
ORDER BY business_type, tenant_id;
"

# 5. 测试更新序列的 cache_size
echo "5. 测试更新序列的 cache_size..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
UPDATE id_sequence 
SET cache_size = 3000, updated_at = NOW()
WHERE business_type = 'test' AND sequence_name = 'test_sequence' AND tenant_id = 1;
"

# 6. 验证更新结果
echo "6. 验证更新结果..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
SELECT 
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    threshold,
    is_active,
    updated_at
FROM id_sequence 
WHERE business_type = 'test'
ORDER BY business_type, tenant_id;
"

# 7. 清理测试数据
echo "7. 清理测试数据..."
mysql -h 139.224.32.190 -P 3308 -u root -p'Pu0cF6KVs]7AockCKVC' platforms-user -e "
DELETE FROM id_sequence WHERE business_type = 'test';
"

echo "=== cache_size 字段集成测试完成 ==="
echo "测试结果："
echo "- 数据库表结构已正确添加 cache_size 字段"
echo "- 现有序列已正确设置 cache_size 默认值"
echo "- 新序列创建时支持 cache_size 参数"
echo "- 序列更新时支持修改 cache_size 值"
echo "- 预分配器将使用 cache_size 来配置预分配策略" 