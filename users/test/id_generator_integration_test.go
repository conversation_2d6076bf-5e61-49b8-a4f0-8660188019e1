package test

import (
	"context"
	"fmt"
	"testing"

	"platforms-pkg/id"
	"platforms-user/internal/domain/user/entity"
	"platforms-user/internal/domain/user/value_object"
)

func TestIDGeneratorIntegration(t *testing.T) {
	ctx := context.Background()

	// 初始化雪花算法ID生成器
	if err := id.InitSnowflake(1); err != nil {
		t.Fatalf("Failed to initialize snowflake: %v", err)
	}

	t.Log("=== ID生成器统一测试 ===")

	// 1. 测试雪花算法ID生成
	t.Log("\n1. 雪花算法ID生成测试:")
	sessionID := fmt.Sprintf("sess_%d", id.GenerateID())
	requestID := fmt.Sprintf("req_%d", id.GenerateID())
	t.Logf("   SessionID: %s", sessionID)
	t.Logf("   RequestID: %s", requestID)

	// 2. 测试实体创建（使用雪花算法作为fallback）
	t.Log("\n2. 实体创建测试（使用雪花算法）:")

	// 创建密码
	password, err := value_object.NewPassword("Password123!")
	if err != nil {
		t.Fatalf("Failed to create password: %v", err)
	}

	// 创建简单的ID生成器（雪花算法fallback）
	mockIDGenerator := &MockIDGenerator{}
	entityFactory := entity.NewEntityFactory(mockIDGenerator)

	// 测试用户创建
	user, err := entityFactory.NewUser(ctx, 1, "testuser", "<EMAIL>", "Test User", *password)
	if err != nil {
		t.Errorf("   ❌ 用户创建失败: %v", err)
	} else {
		t.Logf("   ✅ 用户创建成功: ID=%d, Username=%s", user.ID, user.Username)
	}

	// 测试租户创建
	tenant, err := entityFactory.NewTenant(ctx, "test", "Test Tenant")
	if err != nil {
		t.Errorf("   ❌ 租户创建失败: %v", err)
	} else {
		t.Logf("   ✅ 租户创建成功: ID=%d, Code=%s", tenant.ID, tenant.TenantCode)
	}

	// 测试角色创建
	role, err := entityFactory.NewRole(ctx, 1, "test_role", "TEST_ROLE", "Test Role", "Test role description")
	if err != nil {
		t.Errorf("   ❌ 角色创建失败: %v", err)
	} else {
		t.Logf("   ✅ 角色创建成功: ID=%d, Name=%s", role.ID, role.Name)
	}

	// 测试权限创建
	permission, err := entityFactory.NewPermission(ctx, 1, "test:permission", "TEST_PERMISSION", "Test Permission", "Test permission description", 10001, "self")
	if err != nil {
		t.Errorf("   ❌ 权限创建失败: %v", err)
	} else {
		t.Logf("   ✅ 权限创建成功: ID=%d, Name=%s", permission.ID, permission.Name)
	}

	// 测试部门创建
	department, err := entityFactory.NewDepartment(ctx, 1, "Test Department", "TEST_DEPT", "Test department description")
	if err != nil {
		t.Errorf("   ❌ 部门创建失败: %v", err)
	} else {
		t.Logf("   ✅ 部门创建成功: ID=%d, Name=%s", department.ID, department.Name)
	}

	// 测试职位创建
	position, err := entityFactory.NewPosition(ctx, 1, "Test Position", "TEST_POS", "Test position description")
	if err != nil {
		t.Errorf("   ❌ 职位创建失败: %v", err)
	} else {
		t.Logf("   ✅ 职位创建成功: ID=%d, Name=%s", position.ID, position.Name)
	}

	t.Log("\n=== 测试完成 ===")
}

// MockIDGenerator 简单的Mock ID生成器，使用雪花算法
type MockIDGenerator struct{}

func (m *MockIDGenerator) GenerateUserID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateTenantID(ctx context.Context) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateRoleID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GeneratePermissionID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateDepartmentID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GeneratePositionID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateResourceID(ctx context.Context, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateID(ctx context.Context, businessType string, tenantID int64) (int64, error) {
	return id.GenerateID(), nil
}

func (m *MockIDGenerator) GenerateSessionID() string {
	return fmt.Sprintf("sess_%d", id.GenerateID())
}

func (m *MockIDGenerator) GenerateRequestID() string {
	return fmt.Sprintf("req_%d", id.GenerateID())
}