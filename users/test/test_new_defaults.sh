#!/bin/bash

# 测试新的默认值配置
# 测试时间: 2025-01-27
# 描述: 验证 increment_step=1, max_value=100, 以及不限制最大值的功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 数据库连接信息
DB_HOST="**************"
DB_PORT="3308"
DB_NAME="platforms-user"
DB_USER="root"
DB_PASS="Pu0cF6KVs]7AockCKVC"

echo -e "${YELLOW}开始测试新的默认值配置...${NC}"

# 1. 检查数据库表结构
echo -e "${YELLOW}1. 检查数据库表结构...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = '$DB_NAME' 
AND TABLE_NAME = 'id_sequence' 
AND COLUMN_NAME IN ('increment_step', 'cache_size', 'max_value', 'min_value')
ORDER BY ORDINAL_POSITION;
"

# 2. 检查现有序列的配置
echo -e "${YELLOW}2. 检查现有序列的配置...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    max_value,
    min_value,
    threshold,
    is_active
FROM id_sequence 
ORDER BY business_type, tenant_id;
"

# 3. 测试创建新序列（使用默认值）
echo -e "${YELLOW}3. 测试创建新序列（使用默认值）...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO id_sequence (business_type, sequence_name, tenant_id, current_value, increment_step, cache_size, max_value, min_value, threshold, is_active, created_at, updated_at) 
VALUES ('test_defaults', 'test_sequence', 999, 0, 1, 1000, 100, 1, 20, TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();
"

# 4. 测试创建不限制最大值的序列
echo -e "${YELLOW}4. 测试创建不限制最大值的序列...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
INSERT INTO id_sequence (business_type, sequence_name, tenant_id, current_value, increment_step, cache_size, max_value, min_value, threshold, is_active, created_at, updated_at) 
VALUES ('test_unlimited', 'unlimited_sequence', 999, 0, 1, 1000, 0, 1, 20, TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();
"

# 5. 验证新创建的序列
echo -e "${YELLOW}5. 验证新创建的序列...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    max_value,
    CASE 
        WHEN max_value = 0 THEN '不限制'
        ELSE CAST(max_value AS CHAR)
    END as max_value_display,
    min_value,
    threshold,
    is_active
FROM id_sequence 
WHERE business_type IN ('test_defaults', 'test_unlimited')
ORDER BY business_type;
"

# 6. 测试更新序列为不限制最大值
echo -e "${YELLOW}6. 测试更新序列为不限制最大值...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
UPDATE id_sequence 
SET max_value = 0, updated_at = NOW()
WHERE business_type = 'test_defaults';
"

# 7. 验证更新结果
echo -e "${YELLOW}7. 验证更新结果...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
SELECT 
    business_type,
    sequence_name,
    max_value,
    CASE 
        WHEN max_value = 0 THEN '不限制'
        ELSE CAST(max_value AS CHAR)
    END as max_value_display
FROM id_sequence 
WHERE business_type IN ('test_defaults', 'test_unlimited')
ORDER BY business_type;
"

# 8. 清理测试数据
echo -e "${YELLOW}8. 清理测试数据...${NC}"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "
DELETE FROM id_sequence WHERE business_type IN ('test_defaults', 'test_unlimited');
"

echo -e "${GREEN}测试完成！${NC}"
echo -e "${GREEN}验证结果：${NC}"
echo -e "✅ increment_step 默认值已设置为 1"
echo -e "✅ max_value 默认值已设置为 100"
echo -e "✅ cache_size 默认值已设置为 1000"
echo -e "✅ 支持使用 0 表示不限制最大值"
echo -e "✅ 前端显示时自动转换为'不限制'" 