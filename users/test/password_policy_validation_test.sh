#!/bin/bash

# 密码策略验证测试脚本
BASE_URL="http://localhost:8080/api/user"
TENANT_ID=1

echo "=== 密码策略验证测试 ==="

# 1. 设置租户密码策略
echo "1. 设置租户密码策略..."
curl -X POST "${BASE_URL}/tenant-config/password-policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "tenant_id": 1,
    "policy": {
      "min_length": 8,
      "max_length": 32,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_digits": true,
      "require_special_chars": false,
      "forbidden_patterns": ["123456", "password", "admin"],
      "password_history_count": 5,
      "expire_days": 90
    }
  }' | jq '.'

echo -e "\n"

# 2. 测试符合策略的密码
echo "2. 测试符合策略的密码..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser1",
    "email": "<EMAIL>",
    "password": "Password123",
    "real_name": "Test User 1",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 3. 测试密码太短
echo "3. 测试密码太短..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser2",
    "email": "<EMAIL>",
    "password": "Pass1",
    "real_name": "Test User 2",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 4. 测试缺少大写字母
echo "4. 测试缺少大写字母..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser3",
    "email": "<EMAIL>",
    "password": "password123",
    "real_name": "Test User 3",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 5. 测试缺少数字
echo "5. 测试缺少数字..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser4",
    "email": "<EMAIL>",
    "password": "PasswordABC",
    "real_name": "Test User 4",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 6. 测试包含禁止模式
echo "6. 测试包含禁止模式..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser5",
    "email": "<EMAIL>",
    "password": "Password123456",
    "real_name": "Test User 5",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 7. 更新密码策略，要求特殊字符
echo "7. 更新密码策略，要求特殊字符..."
curl -X POST "${BASE_URL}/tenant-config/password-policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "tenant_id": 1,
    "policy": {
      "min_length": 10,
      "max_length": 32,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_digits": true,
      "require_special_chars": true,
      "forbidden_patterns": ["123456", "password", "admin"],
      "password_history_count": 5,
      "expire_days": 90
    }
  }' | jq '.'

echo -e "\n"

# 8. 测试符合新策略的密码
echo "8. 测试符合新策略的密码..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser6",
    "email": "<EMAIL>",
    "password": "Password123!",
    "real_name": "Test User 6",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n"

# 9. 测试缺少特殊字符
echo "9. 测试缺少特殊字符..."
curl -X POST "${BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser7",
    "email": "<EMAIL>",
    "password": "Password123",
    "real_name": "Test User 7",
    "tenant_id": 1
  }' | jq '.'

echo -e "\n=== 测试完成 ==="
echo "测试说明："
echo "1. 测试了密码长度验证"
echo "2. 测试了大小写字母要求"
echo "3. 测试了数字要求"
echo "4. 测试了特殊字符要求"
echo "5. 测试了禁止模式验证"
echo "6. 测试了策略动态更新" 