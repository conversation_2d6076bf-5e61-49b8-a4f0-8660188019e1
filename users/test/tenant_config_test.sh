#!/bin/bash

# 租户配置功能测试脚本
BASE_URL="http://localhost:8080/api/user"
TENANT_ID=1

echo "=== 租户配置功能测试 ==="

# 1. 获取密码策略
echo "1. 获取密码策略..."
curl -X GET "${BASE_URL}/tenant-config/password-policy?tenant_id=${TENANT_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

echo -e "\n"

# 2. 更新密码策略
echo "2. 更新密码策略..."
curl -X POST "${BASE_URL}/tenant-config/password-policy" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "tenant_id": 1,
    "policy": {
      "min_length": 10,
      "max_length": 50,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_digits": true,
      "require_special_chars": true,
      "forbidden_patterns": ["123456", "password", "admin", "qwerty"],
      "password_history_count": 8,
      "expire_days": 120
    }
  }' | jq '.'

echo -e "\n"

# 3. 获取注册方式配置
echo "3. 获取注册方式配置..."
curl -X GET "${BASE_URL}/tenant-config/registration-methods?tenant_id=${TENANT_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

echo -e "\n"

# 4. 更新注册方式配置
echo "4. 更新注册方式配置..."
curl -X POST "${BASE_URL}/tenant-config/registration-methods" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "tenant_id": 1,
    "methods": {
      "email": {
        "enabled": true,
        "require_verification": true,
        "auto_activate": false
      },
      "phone": {
        "enabled": false,
        "require_verification": true,
        "auto_activate": false
      },
      "oauth": {
        "enabled": true,
        "auto_activate": true
      },
      "admin_creation": {
        "enabled": true,
        "require_approval": true
      }
    }
  }' | jq '.'

echo -e "\n"

# 5. 获取租户所有配置
echo "5. 获取租户所有配置..."
curl -X GET "${BASE_URL}/tenant-config/configs?tenant_id=${TENANT_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

echo -e "\n"

# 6. 复制系统配置到租户
echo "6. 复制系统配置到租户..."
curl -X POST "${BASE_URL}/tenant-config/copy-system?tenant_id=${TENANT_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" | jq '.'

echo -e "\n=== 测试完成 ===" 