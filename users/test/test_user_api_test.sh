#!/bin/bash

# 测试用户功能API测试脚本
# 使用方法: ./test_user_api_test.sh [base_url]
# 示例: ./test_user_api_test.sh http://localhost:8084

BASE_URL=${1:-"http://localhost:8084"}
API_PREFIX="/api"

echo "=== 测试用户功能API测试 ==="
echo "Base URL: $BASE_URL"
echo "测试环境变量 APP_ENV 应设置为: dev, development 或 local"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local endpoint="$3"
    local headers="$4"
    local data="$5"
    
    echo -e "${BLUE}测试: $name${NC}"
    echo "请求: $method $BASE_URL$endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" "$BASE_URL$endpoint" $headers -d "$data")
    else
        response=$(curl -s -w "\nHTTP_CODE:%{http_code}" -X "$method" "$BASE_URL$endpoint" $headers)
    fi
    
    http_code=$(echo "$response" | tail -n1 | cut -d: -f2)
    body=$(echo "$response" | sed '$d')
    
    echo "响应码: $http_code"
    echo "响应体:"
    echo "$body" | jq . 2>/dev/null || echo "$body"
    
    if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
        echo -e "${GREEN}✓ 测试通过${NC}"
    else
        echo -e "${RED}✗ 测试失败${NC}"
    fi
    echo "----------------------------------------"
}

# 1. 健康检查
echo -e "${YELLOW}1. 健康检查${NC}"
test_api "健康检查" "GET" "/health" ""

# 2. 无认证访问受保护资源（应该失败）
echo -e "${YELLOW}2. 无认证访问受保护资源${NC}"
test_api "无认证获取用户信息" "GET" "$API_PREFIX/user/profile" ""

# 3. 使用测试用户访问受保护资源
echo -e "${YELLOW}3. 使用测试用户访问受保护资源${NC}"
test_api "测试用户获取用户信息" "GET" "$API_PREFIX/user/profile" "-H 'X-User-ID: 1'"

# 4. 使用测试用户获取租户信息
echo -e "${YELLOW}4. 使用测试用户获取租户信息${NC}"
test_api "测试用户获取租户信息" "GET" "$API_PREFIX/tenant/info" "-H 'X-User-ID: 1'"

# 5. 使用测试用户创建验证策略
echo -e "${YELLOW}5. 使用测试用户创建验证策略${NC}"
policy_data='{
  "scene": "login",
  "dimension": "ip",
  "condition_expr": "fail_count > 5",
  "need_verification": true,
  "verification_level": "high",
  "target_type": 2,
  "token_type": 1,
  "token_length": 6,
  "expire_minutes": 5,
  "max_attempts": 3,
  "rate_limit_per_minute": 1,
  "rate_limit_per_hour": 10,
  "rate_limit_per_day": 100,
  "template_code": "login_sms",
  "priority": 1,
  "description": "登录失败防护策略"
}'
test_api "测试用户创建验证策略" "POST" "$API_PREFIX/verification/policy" "-H 'X-User-ID: 1' -H 'Content-Type: application/json'" "$policy_data"

# 6. 使用测试用户获取验证策略列表
echo -e "${YELLOW}6. 使用测试用户获取验证策略列表${NC}"
test_api "测试用户获取策略列表" "GET" "$API_PREFIX/verification/policies" "-H 'X-User-ID: 1'"

# 7. 使用测试用户更新个人信息
echo -e "${YELLOW}7. 使用测试用户更新个人信息${NC}"
profile_data='{
  "real_name": "更新的测试用户",
  "email": "<EMAIL>",
  "phone": "13900139000"
}'
test_api "测试用户更新个人信息" "PUT" "$API_PREFIX/user/profile" "-H 'X-User-ID: 1' -H 'Content-Type: application/json'" "$profile_data"

# 8. 使用非测试用户ID（应该执行正常认证流程）
echo -e "${YELLOW}8. 使用非测试用户ID${NC}"
test_api "非测试用户ID访问" "GET" "$API_PREFIX/user/profile" "-H 'X-User-ID: 2'"

# 9. 测试灵活mock功能（开发环境）
echo -e "${YELLOW}9. 测试灵活mock功能${NC}"
test_api "灵活mock用户" "GET" "$API_PREFIX/user/profile" "-H 'X-User-ID: 100' -H 'X-Username: mock_user' -H 'X-Real-Name: Mock User' -H 'X-Email: <EMAIL>' -H 'X-Tenant-ID: 2' -H 'X-Tenant-Code: mock_tenant' -H 'X-Tenant-Name: Mock Tenant'"

echo ""
echo -e "${GREEN}=== 测试完成 ===${NC}"
echo ""
echo "注意事项:"
echo "1. 确保服务运行在开发环境 (APP_ENV=dev/development/local)"
echo "2. 只有 X-User-ID: 1 会触发固定测试用户功能"
echo "3. 其他用户ID会使用灵活mock功能或正常认证流程"
echo "4. 生产环境会自动禁用所有测试用户功能"
echo "5. 查看响应头中的 X-Test-User 字段确认是否使用了测试用户"