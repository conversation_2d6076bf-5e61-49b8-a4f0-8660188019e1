package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"platforms-pkg/grpcmiddleware"
	"platforms-pkg/grpcregistry"
	"platforms-pkg/httpmiddleware"
	"platforms-pkg/id"
	"platforms-pkg/logiface"
	"platforms-pkg/otel"
	"platforms-user/api/idgeneratorpb"
	"platforms-user/api/userpb"
	"platforms-user/internal/infrastructure/container"
	grpcserver "platforms-user/internal/interfaces/grpc"
	"platforms-user/internal/interfaces/http/routes"
	"platforms-user/pkg/config"
	"platforms-user/pkg/logtypes"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
)

const ServiceName = "platforms-user"

func main() {
	// 创建应用实例
	app := NewApplication()

	// 初始化应用
	if err := app.Initialize(); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// 启动应用
	if err := app.Start(); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}

	// 等待关闭信号
	app.WaitForShutdown()

	// 优雅关闭
	if err := app.Shutdown(); err != nil {
		log.Printf("Error during shutdown: %v", err)
		os.Exit(1)
	}
}

// Application 应用主结构
type Application struct {
	config       *config.AppConfig
	logger       logiface.Logger
	accessLogger logiface.Logger
	container    *container.DependencyContainer
	httpServer   *http.Server
	grpcServer   *grpc.Server
	grpcRegistry *grpcregistry.ServiceRegistry
	otelShutdown func(context.Context) error
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewApplication 创建应用实例
func NewApplication() *Application {
	ctx, cancel := context.WithCancel(context.Background())
	return &Application{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Initialize 初始化应用
func (app *Application) Initialize() error {
	// 1. 解析命令行参数
	flag.Parse()

	// 2. 加载配置
	appConfig := config.LoadAppConfig(ServiceName)
	if appConfig == nil {
		return fmt.Errorf("failed to load config")
	}
	app.config = appConfig

	// 3. 初始化snowflake ID生成器
	if err := id.InitSnowflake(1); err != nil {
		return fmt.Errorf("failed to init snowflake: %w", err)
	}

	// 4. 初始化日志
	if err := app.initLogger(); err != nil {
		return fmt.Errorf("failed to init logger: %w", err)
	}

	// 5. 启动配置热更新监听
	app.startConfigWatcher()

	// 6. 初始化 OpenTelemetry
	if err := app.initOpenTelemetry(); err != nil {
		return fmt.Errorf("failed to init OpenTelemetry: %w", err)
	}

	// 7. 初始化依赖注入容器
	if err := app.initDependencyContainer(); err != nil {
		return fmt.Errorf("failed to init dependency container: %w", err)
	}

	// 8. 构建 HTTP 服务器
	if err := app.buildHTTPServer(); err != nil {
		return fmt.Errorf("failed to build HTTP server: %w", err)
	}

	// 9. 构建 gRPC 服务器
	if err := app.buildGRPCServer(); err != nil {
		return fmt.Errorf("failed to build gRPC server: %w", err)
	}

	// 10. 注册 gRPC 服务到 Nacos
	if err := app.registerGRPCService(); err != nil {
		return fmt.Errorf("failed to register gRPC service: %w", err)
	}

	// 11. 初始化 gRPC 客户端管理器
	if err := app.initGRPCManager(); err != nil {
		return fmt.Errorf("failed to init gRPC manager: %w", err)
	}

	app.logger.Info(app.ctx, "Application initialized successfully",
		logiface.String("service", ServiceName),
		logiface.String("env", app.config.Server.Env))

	return nil
}

// initLogger 初始化日志系统
func (app *Application) initLogger() error {
	logCfg := convertLogConfig(app.config.Log)
	logCfg.ServiceName = ServiceName
	logCfg.EnableTrace = true
	logCfg.Caller = true
	logCfg.Stacktrace = true

	multiCfg := logiface.MultiLogConfig{
		App:    logCfg,
		Access: logCfg,
		Error:  logCfg,
	}
	logiface.InitLogger(multiCfg)
	app.logger = logiface.GetLogger()
	app.accessLogger = logiface.GetAccessLogger()

	// 设置访问日志
	httpmiddleware.SetAccessLogger(app.accessLogger)

	app.logger.Info(app.ctx, "User platform starting...", logiface.String("service", ServiceName))
	return nil
}

// startConfigWatcher 启动配置监听
func (app *Application) startConfigWatcher() {
	go func() {
		if err := config.ListenNacosConfigChange(ServiceName); err != nil {
			app.logger.Warn(app.ctx, "Failed to start nacos config change listener", logiface.Error(err))
		}
	}()
}

// initOpenTelemetry 初始化 OpenTelemetry
func (app *Application) initOpenTelemetry() error {
	// 如果端点为空，跳过 OpenTelemetry 初始化
	if app.config.Otel.Endpoint == "" {
		app.logger.Info(app.ctx, "OpenTelemetry endpoint is empty, skipping initialization")
		app.otelShutdown = func(context.Context) error { return nil }
		return nil
	}

	shutdown, err := otel.InitTracerProvider(ServiceName, app.config.Otel.Endpoint)
	if err != nil {
		app.logger.Error(app.ctx, "Failed to init OpenTelemetry", logiface.Error(err))
		return err
	}
	app.otelShutdown = shutdown
	return nil
}

// initDependencyContainer 初始化依赖注入容器
func (app *Application) initDependencyContainer() error {
	app.container = container.NewDependencyContainer(app.config, app.logger)
	return app.container.Initialize(app.ctx)
}

// buildHTTPServer 构建 HTTP 服务器
func (app *Application) buildHTTPServer() error {
	// 设置 Gin 模式
	if app.config.Server.Env == "prod" || app.config.Server.Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	engine := gin.New()

	// 设置路由
	routes.SetupRoutes(engine, app.container, app.container.GetJWTService(), app.container.GetTenantLookupService(), ServiceName)
	// 创建 HTTP 服务器
	app.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", app.config.Server.Port),
		Handler:      engine,
		ReadTimeout:  app.config.GetServerReadTimeout(),
		WriteTimeout: app.config.GetServerWriteTimeout(),
		IdleTimeout:  app.config.GetServerIdleTimeout(),
	}

	return nil
}

// buildGRPCServer 构建 gRPC 服务器
func (app *Application) buildGRPCServer() error {
	app.grpcServer = grpc.NewServer(
		grpcmiddleware.GRPCOtelServerOption(),
		grpc.ChainUnaryInterceptor(
			grpcmiddleware.AccessLogInterceptor(app.accessLogger, []string{"password", "token"}),
		),
	)

	// 注册服务
	userpb.RegisterUserServiceServer(app.grpcServer, grpcserver.NewUserServiceServer(
		app.container.GetUserService(),
		app.container.GetTenantService(),
		app.container.GetJWTService(),
	))

	// 注册ID生成器服务
	idgeneratorpb.RegisterIdGeneratorServiceServer(app.grpcServer, grpcserver.NewIdGeneratorServiceImpl(
		app.container.GetIDGeneratorService(),
		app.container.GetLogger(),
	))
	return nil
}

// registerGRPCService 注册 gRPC 服务到 Nacos
func (app *Application) registerGRPCService() error {
	grpcRegistry, err := grpcregistry.NewServiceRegistry(&grpcregistry.GRPCServiceConfig{
		Port:           app.config.GRPC.Port,
		ServiceName:    ServiceName,
		Group:          "DEFAULT_GROUP",
		Namespace:      app.config.GRPC.Namespace,
		Weight:         100,
		EnableRegister: true,
		Metadata:       map[string]string{"env": app.config.Server.Env},
		LocalIP:        app.config.GRPC.LocalIP,
	}, app.logger)
	if err != nil {
		return fmt.Errorf("failed to init gRPC registry: %w", err)
	}

	app.grpcRegistry = grpcRegistry

	if err := grpcRegistry.Register(app.ctx); err != nil {
		return fmt.Errorf("gRPC service register failed: %w", err)
	}

	app.logger.Info(app.ctx, "gRPC service registered successfully")
	return nil
}

// initGRPCManager 初始化 gRPC 客户端管理器
func (app *Application) initGRPCManager() error {
	// 初始化全局 gRPC 客户端管理器
	grpcregistry.InitGlobalManager(app.logger)

	// 批量订阅 gRPC 服务
	if err := grpcregistry.BatchSubscribeServices(app.config.GRPCSubscriptions, app.logger); err != nil {
		return fmt.Errorf("failed to batch subscribe grpc services: %w", err)
	}

	// 清理 gRPC 连接池，确保所有连接都使用新的 OpenTelemetry 配置
	grpcregistry.ClearAllConnectionPoolsGlobal()
	app.logger.Info(app.ctx, "Cleared gRPC connection pools after OpenTelemetry initialization")

	app.logger.Info(app.ctx, "gRPC client manager initialized successfully")
	return nil
}

// Start 启动应用
func (app *Application) Start() error {
	// 启动 HTTP 服务器
	go func() {
		app.logger.Info(app.ctx, "Starting HTTP server", logiface.String("address", app.httpServer.Addr))
		if err := app.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			app.logger.Error(app.ctx, "HTTP server failed to start", logiface.Error(err))
			app.cancel() // 触发优雅关闭
		}
	}()

	// 启动 gRPC 服务器
	go func() {
		grpcPort := app.config.GRPC.Port
		if grpcPort == 0 {
			grpcPort = 50051
		}
		grpcAddr := fmt.Sprintf(":%d", grpcPort)

		l, err := net.Listen("tcp", grpcAddr)
		if err != nil {
			app.logger.Error(app.ctx, "Failed to listen gRPC", logiface.Error(err))
			app.cancel() // 触发优雅关闭
			return
		}

		app.logger.Info(app.ctx, "Starting gRPC server", logiface.String("address", grpcAddr))
		if err := app.grpcServer.Serve(l); err != nil {
			app.logger.Error(app.ctx, "gRPC server failed to start", logiface.Error(err))
			app.cancel() // 触发优雅关闭
		}
	}()

	app.logger.Info(app.ctx, "Application started successfully")
	return nil
}

// WaitForShutdown 等待关闭信号
func (app *Application) WaitForShutdown() {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		app.logger.Info(app.ctx, "Received shutdown signal")
	case <-app.ctx.Done():
		app.logger.Info(app.ctx, "Application context cancelled")
	}
}

// Shutdown 优雅关闭应用
func (app *Application) Shutdown() error {
	app.logger.Info(app.ctx, "Shutting down application...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 注销 gRPC 服务
	if app.grpcRegistry != nil {
		if err := app.grpcRegistry.Deregister(ctx); err != nil {
			app.logger.Warn(ctx, "gRPC service deregister failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "gRPC service deregistered")
		}
	}

	// 2. 关闭 gRPC 服务器
	if app.grpcServer != nil {
		app.grpcServer.GracefulStop()
		app.logger.Info(ctx, "gRPC server shutdown completed")
	}

	// 3. 关闭 HTTP 服务器
	if app.httpServer != nil {
		if err := app.httpServer.Shutdown(ctx); err != nil {
			app.logger.Error(ctx, "HTTP server shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "HTTP server shutdown completed")
		}
	}

	// 4. 关闭依赖注入容器
	if app.container != nil {
		if err := app.container.Close(ctx); err != nil {
			app.logger.Error(ctx, "Dependency container close failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "Dependency container closed")
		}
	}

	// 5. 关闭 OpenTelemetry
	if app.otelShutdown != nil {
		if err := app.otelShutdown(ctx); err != nil {
			app.logger.Error(ctx, "OpenTelemetry shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "OpenTelemetry shutdown completed")
		}
	}

	app.logger.Info(ctx, "Application shutdown completed")
	return nil
}

// convertLogConfig converts logtypes.LogConfig to logiface.LogConfig
func convertLogConfig(cfg logtypes.LogConfig) logiface.LogConfig {
	return logiface.LogConfig{
		Level:  cfg.Level,
		Format: cfg.Format,
		Output: cfg.Output,
		File: struct {
			Path       string `yaml:"path" json:"path" toml:"path"`
			MaxSize    int    `yaml:"maxSize" json:"maxSize" toml:"maxSize"`
			MaxBackups int    `yaml:"maxBackups" json:"maxBackups" toml:"maxBackups"`
			MaxAge     int    `yaml:"maxAge" json:"maxAge" toml:"maxAge"`
			Compress   bool   `yaml:"compress" json:"compress" toml:"compress"`
		}{
			Path:       cfg.Output,
			MaxSize:    cfg.MaxSize,
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge,
			Compress:   cfg.Compress,
		},
	}
}
