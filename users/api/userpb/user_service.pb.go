// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/user_service.proto

package userpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求：token 查询用户信息
type GetUserInfoByTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // JWT 或 access token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByTokenRequest) Reset() {
	*x = GetUserInfoByTokenRequest{}
	mi := &file_api_user_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByTokenRequest) ProtoMessage() {}

func (x *GetUserInfoByTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByTokenRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoByTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserInfoByTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 响应：用户信息
type GetUserInfoByTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *UserInfo              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoByTokenResponse) Reset() {
	*x = GetUserInfoByTokenResponse{}
	mi := &file_api_user_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoByTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByTokenResponse) ProtoMessage() {}

func (x *GetUserInfoByTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByTokenResponse.ProtoReflect.Descriptor instead.
func (*GetUserInfoByTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserInfoByTokenResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserInfoByTokenResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetUserInfoByTokenResponse) GetData() *UserInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 用户基础信息（不返回 roles/status）
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	RealName      string                 `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	TenantId      int64                  `protobuf:"varint,5,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_api_user_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfo) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInfo) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

// 批量权限检查请求
type CheckUserPermissionsRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	PermissionCodes []string               `protobuf:"bytes,2,rep,name=permission_codes,json=permissionCodes,proto3" json:"permission_codes,omitempty"` // 支持单个或多个权限
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CheckUserPermissionsRequest) Reset() {
	*x = CheckUserPermissionsRequest{}
	mi := &file_api_user_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPermissionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPermissionsRequest) ProtoMessage() {}

func (x *CheckUserPermissionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPermissionsRequest.ProtoReflect.Descriptor instead.
func (*CheckUserPermissionsRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{3}
}

func (x *CheckUserPermissionsRequest) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckUserPermissionsRequest) GetPermissionCodes() []string {
	if x != nil {
		return x.PermissionCodes
	}
	return nil
}

// 批量权限检查响应
type CheckUserPermissionsResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Results       []*PermissionCheckResult `protobuf:"bytes,3,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserPermissionsResponse) Reset() {
	*x = CheckUserPermissionsResponse{}
	mi := &file_api_user_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPermissionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPermissionsResponse) ProtoMessage() {}

func (x *CheckUserPermissionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPermissionsResponse.ProtoReflect.Descriptor instead.
func (*CheckUserPermissionsResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckUserPermissionsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUserPermissionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckUserPermissionsResponse) GetResults() []*PermissionCheckResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// 权限检查结果
type PermissionCheckResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	PermissionCode string                 `protobuf:"bytes,1,opt,name=permission_code,json=permissionCode,proto3" json:"permission_code,omitempty"`
	HasPermission  bool                   `protobuf:"varint,2,opt,name=has_permission,json=hasPermission,proto3" json:"has_permission,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PermissionCheckResult) Reset() {
	*x = PermissionCheckResult{}
	mi := &file_api_user_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PermissionCheckResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PermissionCheckResult) ProtoMessage() {}

func (x *PermissionCheckResult) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PermissionCheckResult.ProtoReflect.Descriptor instead.
func (*PermissionCheckResult) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{5}
}

func (x *PermissionCheckResult) GetPermissionCode() string {
	if x != nil {
		return x.PermissionCode
	}
	return ""
}

func (x *PermissionCheckResult) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

// 根据租户代码查询租户信息请求
type GetTenantInfoByCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantCode    string                 `protobuf:"bytes,1,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"` // 租户代码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantInfoByCodeRequest) Reset() {
	*x = GetTenantInfoByCodeRequest{}
	mi := &file_api_user_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantInfoByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantInfoByCodeRequest) ProtoMessage() {}

func (x *GetTenantInfoByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantInfoByCodeRequest.ProtoReflect.Descriptor instead.
func (*GetTenantInfoByCodeRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetTenantInfoByCodeRequest) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

// 根据租户代码查询租户信息响应
type GetTenantInfoByCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TenantInfo            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantInfoByCodeResponse) Reset() {
	*x = GetTenantInfoByCodeResponse{}
	mi := &file_api_user_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantInfoByCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantInfoByCodeResponse) ProtoMessage() {}

func (x *GetTenantInfoByCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantInfoByCodeResponse.ProtoReflect.Descriptor instead.
func (*GetTenantInfoByCodeResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetTenantInfoByCodeResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetTenantInfoByCodeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetTenantInfoByCodeResponse) GetData() *TenantInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 租户基本信息
type TenantInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	TenantCode    string                 `protobuf:"bytes,2,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`
	TenantName    string                 `protobuf:"bytes,3,opt,name=tenant_name,json=tenantName,proto3" json:"tenant_name,omitempty"`
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TenantInfo) Reset() {
	*x = TenantInfo{}
	mi := &file_api_user_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TenantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantInfo) ProtoMessage() {}

func (x *TenantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantInfo.ProtoReflect.Descriptor instead.
func (*TenantInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{8}
}

func (x *TenantInfo) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *TenantInfo) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *TenantInfo) GetTenantName() string {
	if x != nil {
		return x.TenantName
	}
	return ""
}

func (x *TenantInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// 获取租户详细信息请求
type GetTenantDetailInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      int64                  `protobuf:"varint,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantDetailInfoRequest) Reset() {
	*x = GetTenantDetailInfoRequest{}
	mi := &file_api_user_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantDetailInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantDetailInfoRequest) ProtoMessage() {}

func (x *GetTenantDetailInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantDetailInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTenantDetailInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetTenantDetailInfoRequest) GetTenantId() int64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

// 获取租户详细信息响应
type GetTenantDetailInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *TenantDetailInfo      `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantDetailInfoResponse) Reset() {
	*x = GetTenantDetailInfoResponse{}
	mi := &file_api_user_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantDetailInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantDetailInfoResponse) ProtoMessage() {}

func (x *GetTenantDetailInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantDetailInfoResponse.ProtoReflect.Descriptor instead.
func (*GetTenantDetailInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetTenantDetailInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetTenantDetailInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetTenantDetailInfoResponse) GetData() *TenantDetailInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

// 租户详细信息配置
type TenantDetailInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Type            string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                               // 企业或个人：enterprise/personal
	SystemName      string                 `protobuf:"bytes,2,opt,name=system_name,json=systemName,proto3" json:"system_name,omitempty"`                 // 系统名称
	ServiceEmail    string                 `protobuf:"bytes,3,opt,name=service_email,json=serviceEmail,proto3" json:"service_email,omitempty"`           // 客服邮箱
	Address         string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`                                         // 地址
	ContactPerson   string                 `protobuf:"bytes,5,opt,name=contact_person,json=contactPerson,proto3" json:"contact_person,omitempty"`        // 联系人
	ContactPhone    string                 `protobuf:"bytes,6,opt,name=contact_phone,json=contactPhone,proto3" json:"contact_phone,omitempty"`           // 联系电话
	Website         string                 `protobuf:"bytes,7,opt,name=website,proto3" json:"website,omitempty"`                                         // 官网地址
	Logo            string                 `protobuf:"bytes,8,opt,name=logo,proto3" json:"logo,omitempty"`                                               // 企业/个人标志
	Description     string                 `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`                                 // 描述
	BusinessLicense string                 `protobuf:"bytes,10,opt,name=business_license,json=businessLicense,proto3" json:"business_license,omitempty"` // 营业执照（企业）
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TenantDetailInfo) Reset() {
	*x = TenantDetailInfo{}
	mi := &file_api_user_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TenantDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TenantDetailInfo) ProtoMessage() {}

func (x *TenantDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TenantDetailInfo.ProtoReflect.Descriptor instead.
func (*TenantDetailInfo) Descriptor() ([]byte, []int) {
	return file_api_user_service_proto_rawDescGZIP(), []int{11}
}

func (x *TenantDetailInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TenantDetailInfo) GetSystemName() string {
	if x != nil {
		return x.SystemName
	}
	return ""
}

func (x *TenantDetailInfo) GetServiceEmail() string {
	if x != nil {
		return x.ServiceEmail
	}
	return ""
}

func (x *TenantDetailInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TenantDetailInfo) GetContactPerson() string {
	if x != nil {
		return x.ContactPerson
	}
	return ""
}

func (x *TenantDetailInfo) GetContactPhone() string {
	if x != nil {
		return x.ContactPhone
	}
	return ""
}

func (x *TenantDetailInfo) GetWebsite() string {
	if x != nil {
		return x.Website
	}
	return ""
}

func (x *TenantDetailInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *TenantDetailInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TenantDetailInfo) GetBusinessLicense() string {
	if x != nil {
		return x.BusinessLicense
	}
	return ""
}

var File_api_user_service_proto protoreflect.FileDescriptor

const file_api_user_service_proto_rawDesc = "" +
	"\n" +
	"\x16api/user_service.proto\x12\x04user\"1\n" +
	"\x19GetUserInfoByTokenRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"n\n" +
	"\x1aGetUserInfoByTokenResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04data\"\x8f\x01\n" +
	"\bUserInfo\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1b\n" +
	"\treal_name\x18\x03 \x01(\tR\brealName\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x1b\n" +
	"\ttenant_id\x18\x05 \x01(\x03R\btenantId\"a\n" +
	"\x1bCheckUserPermissionsRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x03R\x06userId\x12)\n" +
	"\x10permission_codes\x18\x02 \x03(\tR\x0fpermissionCodes\"\x83\x01\n" +
	"\x1cCheckUserPermissionsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x125\n" +
	"\aresults\x18\x03 \x03(\v2\x1b.user.PermissionCheckResultR\aresults\"g\n" +
	"\x15PermissionCheckResult\x12'\n" +
	"\x0fpermission_code\x18\x01 \x01(\tR\x0epermissionCode\x12%\n" +
	"\x0ehas_permission\x18\x02 \x01(\bR\rhasPermission\"=\n" +
	"\x1aGetTenantInfoByCodeRequest\x12\x1f\n" +
	"\vtenant_code\x18\x01 \x01(\tR\n" +
	"tenantCode\"q\n" +
	"\x1bGetTenantInfoByCodeResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12$\n" +
	"\x04data\x18\x03 \x01(\v2\x10.user.TenantInfoR\x04data\"\x83\x01\n" +
	"\n" +
	"TenantInfo\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\x12\x1f\n" +
	"\vtenant_code\x18\x02 \x01(\tR\n" +
	"tenantCode\x12\x1f\n" +
	"\vtenant_name\x18\x03 \x01(\tR\n" +
	"tenantName\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"9\n" +
	"\x1aGetTenantDetailInfoRequest\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\x03R\btenantId\"w\n" +
	"\x1bGetTenantDetailInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04data\x18\x03 \x01(\v2\x16.user.TenantDetailInfoR\x04data\"\xcd\x02\n" +
	"\x10TenantDetailInfo\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x1f\n" +
	"\vsystem_name\x18\x02 \x01(\tR\n" +
	"systemName\x12#\n" +
	"\rservice_email\x18\x03 \x01(\tR\fserviceEmail\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\x12%\n" +
	"\x0econtact_person\x18\x05 \x01(\tR\rcontactPerson\x12#\n" +
	"\rcontact_phone\x18\x06 \x01(\tR\fcontactPhone\x12\x18\n" +
	"\awebsite\x18\a \x01(\tR\awebsite\x12\x12\n" +
	"\x04logo\x18\b \x01(\tR\x04logo\x12 \n" +
	"\vdescription\x18\t \x01(\tR\vdescription\x12)\n" +
	"\x10business_license\x18\n" +
	" \x01(\tR\x0fbusinessLicense2\xfd\x02\n" +
	"\vUserService\x12W\n" +
	"\x12GetUserInfoByToken\x12\x1f.user.GetUserInfoByTokenRequest\x1a .user.GetUserInfoByTokenResponse\x12]\n" +
	"\x14CheckUserPermissions\x12!.user.CheckUserPermissionsRequest\x1a\".user.CheckUserPermissionsResponse\x12Z\n" +
	"\x13GetTenantInfoByCode\x12 .user.GetTenantInfoByCodeRequest\x1a!.user.GetTenantInfoByCodeResponse\x12Z\n" +
	"\x13GetTenantDetailInfo\x12 .user.GetTenantDetailInfoRequest\x1a!.user.GetTenantDetailInfoResponseB\x1bZ\x19platforms-user/api/userpbb\x06proto3"

var (
	file_api_user_service_proto_rawDescOnce sync.Once
	file_api_user_service_proto_rawDescData []byte
)

func file_api_user_service_proto_rawDescGZIP() []byte {
	file_api_user_service_proto_rawDescOnce.Do(func() {
		file_api_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_user_service_proto_rawDesc), len(file_api_user_service_proto_rawDesc)))
	})
	return file_api_user_service_proto_rawDescData
}

var file_api_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_user_service_proto_goTypes = []any{
	(*GetUserInfoByTokenRequest)(nil),    // 0: user.GetUserInfoByTokenRequest
	(*GetUserInfoByTokenResponse)(nil),   // 1: user.GetUserInfoByTokenResponse
	(*UserInfo)(nil),                     // 2: user.UserInfo
	(*CheckUserPermissionsRequest)(nil),  // 3: user.CheckUserPermissionsRequest
	(*CheckUserPermissionsResponse)(nil), // 4: user.CheckUserPermissionsResponse
	(*PermissionCheckResult)(nil),        // 5: user.PermissionCheckResult
	(*GetTenantInfoByCodeRequest)(nil),   // 6: user.GetTenantInfoByCodeRequest
	(*GetTenantInfoByCodeResponse)(nil),  // 7: user.GetTenantInfoByCodeResponse
	(*TenantInfo)(nil),                   // 8: user.TenantInfo
	(*GetTenantDetailInfoRequest)(nil),   // 9: user.GetTenantDetailInfoRequest
	(*GetTenantDetailInfoResponse)(nil),  // 10: user.GetTenantDetailInfoResponse
	(*TenantDetailInfo)(nil),             // 11: user.TenantDetailInfo
}
var file_api_user_service_proto_depIdxs = []int32{
	2,  // 0: user.GetUserInfoByTokenResponse.data:type_name -> user.UserInfo
	5,  // 1: user.CheckUserPermissionsResponse.results:type_name -> user.PermissionCheckResult
	8,  // 2: user.GetTenantInfoByCodeResponse.data:type_name -> user.TenantInfo
	11, // 3: user.GetTenantDetailInfoResponse.data:type_name -> user.TenantDetailInfo
	0,  // 4: user.UserService.GetUserInfoByToken:input_type -> user.GetUserInfoByTokenRequest
	3,  // 5: user.UserService.CheckUserPermissions:input_type -> user.CheckUserPermissionsRequest
	6,  // 6: user.UserService.GetTenantInfoByCode:input_type -> user.GetTenantInfoByCodeRequest
	9,  // 7: user.UserService.GetTenantDetailInfo:input_type -> user.GetTenantDetailInfoRequest
	1,  // 8: user.UserService.GetUserInfoByToken:output_type -> user.GetUserInfoByTokenResponse
	4,  // 9: user.UserService.CheckUserPermissions:output_type -> user.CheckUserPermissionsResponse
	7,  // 10: user.UserService.GetTenantInfoByCode:output_type -> user.GetTenantInfoByCodeResponse
	10, // 11: user.UserService.GetTenantDetailInfo:output_type -> user.GetTenantDetailInfoResponse
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_api_user_service_proto_init() }
func file_api_user_service_proto_init() {
	if File_api_user_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_user_service_proto_rawDesc), len(file_api_user_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_user_service_proto_goTypes,
		DependencyIndexes: file_api_user_service_proto_depIdxs,
		MessageInfos:      file_api_user_service_proto_msgTypes,
	}.Build()
	File_api_user_service_proto = out.File
	file_api_user_service_proto_goTypes = nil
	file_api_user_service_proto_depIdxs = nil
}
