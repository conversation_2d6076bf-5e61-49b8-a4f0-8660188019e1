syntax = "proto3";

package idgeneratorpb;

option go_package = "users/api/idgeneratorpb";

// 生成ID请求
message GenerateIdRequest {
  string business_type = 1; // 业务类型
  int64 tenant_id = 2;      // 租户ID
}

// 生成ID响应
message GenerateIdResponse {
  int32 code = 1;
  string message = 2;
  int64 id = 3;
}

// 批量生成ID请求
message GenerateBatchIdsRequest {
  string business_type = 1;
  int64 tenant_id = 2;
  int32 count = 3;
}

// 批量生成ID响应
message GenerateBatchIdsResponse {
  int32 code = 1;
  string message = 2;
  repeated int64 ids = 3;
}

// ID生成器服务
service IdGeneratorService {
  // 生成单个ID
  rpc GenerateId(GenerateIdRequest) returns (GenerateIdResponse);
  // 批量生成ID
  rpc GenerateBatchIds(GenerateBatchIdsRequest) returns (GenerateBatchIdsResponse);
} 