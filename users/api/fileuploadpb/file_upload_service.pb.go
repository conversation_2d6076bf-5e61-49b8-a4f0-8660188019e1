// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/file_upload_service.proto

package fileuploadpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 标记文件为永久存储请求
type MarkFilePermanentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileId        int64                  `protobuf:"varint,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"` // 文件ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkFilePermanentRequest) Reset() {
	*x = MarkFilePermanentRequest{}
	mi := &file_api_file_upload_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkFilePermanentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkFilePermanentRequest) ProtoMessage() {}

func (x *MarkFilePermanentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_file_upload_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkFilePermanentRequest.ProtoReflect.Descriptor instead.
func (*MarkFilePermanentRequest) Descriptor() ([]byte, []int) {
	return file_api_file_upload_service_proto_rawDescGZIP(), []int{0}
}

func (x *MarkFilePermanentRequest) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// 标记文件为永久存储响应
type MarkFilePermanentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                   // 响应码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`              // 响应消息
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`             // 是否成功
	FileId        int64                  `protobuf:"varint,4,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"` // 文件ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkFilePermanentResponse) Reset() {
	*x = MarkFilePermanentResponse{}
	mi := &file_api_file_upload_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkFilePermanentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkFilePermanentResponse) ProtoMessage() {}

func (x *MarkFilePermanentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_file_upload_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkFilePermanentResponse.ProtoReflect.Descriptor instead.
func (*MarkFilePermanentResponse) Descriptor() ([]byte, []int) {
	return file_api_file_upload_service_proto_rawDescGZIP(), []int{1}
}

func (x *MarkFilePermanentResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *MarkFilePermanentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *MarkFilePermanentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkFilePermanentResponse) GetFileId() int64 {
	if x != nil {
		return x.FileId
	}
	return 0
}

// 批量标记文件为永久存储请求
type BatchMarkFilesPermanentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FileIds       []int64                `protobuf:"varint,1,rep,packed,name=file_ids,json=fileIds,proto3" json:"file_ids,omitempty"` // 文件ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMarkFilesPermanentRequest) Reset() {
	*x = BatchMarkFilesPermanentRequest{}
	mi := &file_api_file_upload_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMarkFilesPermanentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMarkFilesPermanentRequest) ProtoMessage() {}

func (x *BatchMarkFilesPermanentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_file_upload_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMarkFilesPermanentRequest.ProtoReflect.Descriptor instead.
func (*BatchMarkFilesPermanentRequest) Descriptor() ([]byte, []int) {
	return file_api_file_upload_service_proto_rawDescGZIP(), []int{2}
}

func (x *BatchMarkFilesPermanentRequest) GetFileIds() []int64 {
	if x != nil {
		return x.FileIds
	}
	return nil
}

// 批量标记文件为永久存储响应
type BatchMarkFilesPermanentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                                 // 响应码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                                            // 响应消息
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`                                           // 是否成功
	SuccessCount  int64                  `protobuf:"varint,4,opt,name=success_count,json=successCount,proto3" json:"success_count,omitempty"`             // 成功数量
	FailureCount  int64                  `protobuf:"varint,5,opt,name=failure_count,json=failureCount,proto3" json:"failure_count,omitempty"`             // 失败数量
	FailedFileIds []int64                `protobuf:"varint,6,rep,packed,name=failed_file_ids,json=failedFileIds,proto3" json:"failed_file_ids,omitempty"` // 失败的文件ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchMarkFilesPermanentResponse) Reset() {
	*x = BatchMarkFilesPermanentResponse{}
	mi := &file_api_file_upload_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchMarkFilesPermanentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchMarkFilesPermanentResponse) ProtoMessage() {}

func (x *BatchMarkFilesPermanentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_file_upload_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchMarkFilesPermanentResponse.ProtoReflect.Descriptor instead.
func (*BatchMarkFilesPermanentResponse) Descriptor() ([]byte, []int) {
	return file_api_file_upload_service_proto_rawDescGZIP(), []int{3}
}

func (x *BatchMarkFilesPermanentResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BatchMarkFilesPermanentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BatchMarkFilesPermanentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *BatchMarkFilesPermanentResponse) GetSuccessCount() int64 {
	if x != nil {
		return x.SuccessCount
	}
	return 0
}

func (x *BatchMarkFilesPermanentResponse) GetFailureCount() int64 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *BatchMarkFilesPermanentResponse) GetFailedFileIds() []int64 {
	if x != nil {
		return x.FailedFileIds
	}
	return nil
}

var File_api_file_upload_service_proto protoreflect.FileDescriptor

const file_api_file_upload_service_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/file_upload_service.proto\x12\n" +
	"fileupload\x1a\x1fgoogle/protobuf/timestamp.proto\"3\n" +
	"\x18MarkFilePermanentRequest\x12\x17\n" +
	"\afile_id\x18\x01 \x01(\x03R\x06fileId\"|\n" +
	"\x19MarkFilePermanentResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12\x17\n" +
	"\afile_id\x18\x04 \x01(\x03R\x06fileId\";\n" +
	"\x1eBatchMarkFilesPermanentRequest\x12\x19\n" +
	"\bfile_ids\x18\x01 \x03(\x03R\afileIds\"\xdb\x01\n" +
	"\x1fBatchMarkFilesPermanentResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\x12#\n" +
	"\rsuccess_count\x18\x04 \x01(\x03R\fsuccessCount\x12#\n" +
	"\rfailure_count\x18\x05 \x01(\x03R\ffailureCount\x12&\n" +
	"\x0ffailed_file_ids\x18\x06 \x03(\x03R\rfailedFileIds2\xe9\x01\n" +
	"\x11FileUploadService\x12`\n" +
	"\x11MarkFilePermanent\x12$.fileupload.MarkFilePermanentRequest\x1a%.fileupload.MarkFilePermanentResponse\x12r\n" +
	"\x17BatchMarkFilesPermanent\x12*.fileupload.BatchMarkFilesPermanentRequest\x1a+.fileupload.BatchMarkFilesPermanentResponseB!Z\x1fplatforms-user/api/fileuploadpbb\x06proto3"

var (
	file_api_file_upload_service_proto_rawDescOnce sync.Once
	file_api_file_upload_service_proto_rawDescData []byte
)

func file_api_file_upload_service_proto_rawDescGZIP() []byte {
	file_api_file_upload_service_proto_rawDescOnce.Do(func() {
		file_api_file_upload_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_file_upload_service_proto_rawDesc), len(file_api_file_upload_service_proto_rawDesc)))
	})
	return file_api_file_upload_service_proto_rawDescData
}

var file_api_file_upload_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_file_upload_service_proto_goTypes = []any{
	(*MarkFilePermanentRequest)(nil),        // 0: fileupload.MarkFilePermanentRequest
	(*MarkFilePermanentResponse)(nil),       // 1: fileupload.MarkFilePermanentResponse
	(*BatchMarkFilesPermanentRequest)(nil),  // 2: fileupload.BatchMarkFilesPermanentRequest
	(*BatchMarkFilesPermanentResponse)(nil), // 3: fileupload.BatchMarkFilesPermanentResponse
}
var file_api_file_upload_service_proto_depIdxs = []int32{
	0, // 0: fileupload.FileUploadService.MarkFilePermanent:input_type -> fileupload.MarkFilePermanentRequest
	2, // 1: fileupload.FileUploadService.BatchMarkFilesPermanent:input_type -> fileupload.BatchMarkFilesPermanentRequest
	1, // 2: fileupload.FileUploadService.MarkFilePermanent:output_type -> fileupload.MarkFilePermanentResponse
	3, // 3: fileupload.FileUploadService.BatchMarkFilesPermanent:output_type -> fileupload.BatchMarkFilesPermanentResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_file_upload_service_proto_init() }
func file_api_file_upload_service_proto_init() {
	if File_api_file_upload_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_file_upload_service_proto_rawDesc), len(file_api_file_upload_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_file_upload_service_proto_goTypes,
		DependencyIndexes: file_api_file_upload_service_proto_depIdxs,
		MessageInfos:      file_api_file_upload_service_proto_msgTypes,
	}.Build()
	File_api_file_upload_service_proto = out.File
	file_api_file_upload_service_proto_goTypes = nil
	file_api_file_upload_service_proto_depIdxs = nil
}
