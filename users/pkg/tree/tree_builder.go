package tree

import (
	"fmt"
	"reflect"
)

// TreeNode 树节点接口
type TreeNode interface {
	GetID() int64
	GetParentID() *int64
	GetChildren() []TreeNode
	SetChildren(children []TreeNode)
}

// TreeBuilder 树形结构构建器
type TreeBuilder struct{}

// NewTreeBuilder 创建新的树构建器
func NewTreeBuilder() *TreeBuilder {
	return &TreeBuilder{}
}

// BuildTree 构建树形结构
func (tb *TreeBuilder) BuildTree(nodes []TreeNode) ([]TreeNode, error) {
	if len(nodes) == 0 {
		return []TreeNode{}, nil
	}

	// 创建ID到节点的映射
	nodeMap := make(map[int64]TreeNode)
	var rootNodes []TreeNode

	// 初始化节点映射
	for _, node := range nodes {
		nodeMap[node.GetID()] = node
	}

	// 构建父子关系
	for _, node := range nodes {
		parentID := node.GetParentID()
		if parentID == nil {
			// 根节点
			rootNodes = append(rootNodes, node)
		} else {
			// 子节点
			if parent, exists := nodeMap[*parentID]; exists {
				children := parent.GetChildren()
				children = append(children, node)
				parent.SetChildren(children)
			} else {
				// 父节点不存在，作为根节点处理
				rootNodes = append(rootNodes, node)
			}
		}
	}

	return rootNodes, nil
}

// BuildTreeWithLevel 构建带层级的树形结构
func (tb *TreeBuilder) BuildTreeWithLevel(nodes []TreeNode, startLevel int) ([]TreeNode, error) {
	rootNodes, err := tb.BuildTree(nodes)
	if err != nil {
		return nil, err
	}

	// 设置层级
	tb.setLevel(rootNodes, startLevel)

	return rootNodes, nil
}

// setLevel 递归设置节点层级
func (tb *TreeBuilder) setLevel(nodes []TreeNode, level int) {
	for _, node := range nodes {
		// 使用反射设置层级字段（如果存在）
		tb.setFieldValue(node, "Level", level)

		// 递归设置子节点层级
		children := node.GetChildren()
		if len(children) > 0 {
			tb.setLevel(children, level+1)
		}
	}
}

// setFieldValue 使用反射设置字段值
func (tb *TreeBuilder) setFieldValue(node TreeNode, fieldName string, value interface{}) {
	v := reflect.ValueOf(node)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() == reflect.Struct {
		field := v.FieldByName(fieldName)
		if field.IsValid() && field.CanSet() {
			fieldValue := reflect.ValueOf(value)
			if field.Type() == fieldValue.Type() {
				field.Set(fieldValue)
			}
		}
	}
}

// FlattenTree 将树形结构扁平化
func (tb *TreeBuilder) FlattenTree(nodes []TreeNode) []TreeNode {
	var result []TreeNode
	for _, node := range nodes {
		result = append(result, node)
		children := node.GetChildren()
		if len(children) > 0 {
			result = append(result, tb.FattenTree(children)...)
		}
	}
	return result
}

// FattenTree 递归扁平化（内部方法）
func (tb *TreeBuilder) FattenTree(nodes []TreeNode) []TreeNode {
	var result []TreeNode
	for _, node := range nodes {
		result = append(result, node)
		children := node.GetChildren()
		if len(children) > 0 {
			result = append(result, tb.FattenTree(children)...)
		}
	}
	return result
}

// FindNodeByID 在树中查找指定ID的节点
func (tb *TreeBuilder) FindNodeByID(nodes []TreeNode, id int64) (TreeNode, bool) {
	for _, node := range nodes {
		if node.GetID() == id {
			return node, true
		}
		children := node.GetChildren()
		if len(children) > 0 {
			if found, exists := tb.FindNodeByID(children, id); exists {
				return found, true
			}
		}
	}
	return nil, false
}

// GetNodePath 获取节点路径
func (tb *TreeBuilder) GetNodePath(nodes []TreeNode, targetID int64) ([]TreeNode, bool) {
	for _, node := range nodes {
		if node.GetID() == targetID {
			return []TreeNode{node}, true
		}
		children := node.GetChildren()
		if len(children) > 0 {
			if path, found := tb.GetNodePath(children, targetID); found {
				return append([]TreeNode{node}, path...), true
			}
		}
	}
	return nil, false
}

// ValidateTree 验证树形结构的完整性
func (tb *TreeBuilder) ValidateTree(nodes []TreeNode) error {
	visited := make(map[int64]bool)
	return tb.validateTreeRecursive(nodes, visited)
}

// validateTreeRecursive 递归验证树形结构
func (tb *TreeBuilder) validateTreeRecursive(nodes []TreeNode, visited map[int64]bool) error {
	for _, node := range nodes {
		if visited[node.GetID()] {
			return fmt.Errorf("circular reference detected at node ID: %d", node.GetID())
		}
		visited[node.GetID()] = true

		children := node.GetChildren()
		if len(children) > 0 {
			if err := tb.validateTreeRecursive(children, visited); err != nil {
				return err
			}
		}
	}
	return nil
}
