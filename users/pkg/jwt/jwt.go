package jwt

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"platforms-pkg/logiface"
	userErrors "platforms-user/internal/domain/errors"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTService JWT服务
type JWTService struct {
	logger        logiface.Logger
	secretKey     string
	accessExpiry  time.Duration
	refreshExpiry time.Duration
}

// Claims JWT声明
type Claims struct {
	UserID   int64  `json:"user_id"`
	TenantID int64  `json:"tenant_id"`
	Username string `json:"username"`
	Type     string `json:"type"` // access 或 refresh
	jwt.RegisteredClaims
}

// TokenInfo 令牌信息
type TokenInfo struct {
	Token     string    `json:"token"`
	JTI       string    `json:"jti"`
	TokenHash string    `json:"token_hash"`
	ExpiresAt time.Time `json:"expires_at"`
}

// NewJWTService 创建JWT服务
func NewJWTService(secretKey string, accessExpiry, refreshExpiry time.Duration) *JWTService {
	return &JWTService{
		logger:        logiface.GetLogger(),
		secretKey:     secretKey,
		accessExpiry:  accessExpiry,
		refreshExpiry: refreshExpiry,
	}
}

// NewJWTServiceWithLogger 创建带自定义日志器的JWT服务
func NewJWTServiceWithLogger(logger logiface.Logger, secretKey string, accessExpiry, refreshExpiry time.Duration) *JWTService {
	return &JWTService{
		logger:        logger,
		secretKey:     secretKey,
		accessExpiry:  accessExpiry,
		refreshExpiry: refreshExpiry,
	}
}

// GenerateAccessToken 生成访问令牌
func (j *JWTService) GenerateAccessToken(userID, tenantID int64, username string) (string, error) {
	ctx := context.Background()

	j.logger.Info(ctx, "generating access token",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("username", username),
		logiface.Duration("expiry", j.accessExpiry),
	)

	// 生成JTI - 确保所有token都包含JTI
	jti := uuid.New().String()

	claims := Claims{
		UserID:   userID,
		TenantID: tenantID,
		Username: username,
		Type:     "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti, // 使用JTI作为令牌ID
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.accessExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-center",
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"user-center-api"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(j.secretKey))
	if err != nil {
		j.logger.Error(ctx, "failed to generate access token",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("username", username),
		)
		return "", userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("access token signing failed: %v", err))
	}

	j.logger.Info(ctx, "access token generated successfully",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("username", username),
		logiface.String("token_type", "access"),
		logiface.String("jti", jti),
	)

	return tokenString, nil
}

// GenerateAccessTokenWithInfo 生成访问令牌并返回完整信息
func (j *JWTService) GenerateAccessTokenWithInfo(userID, tenantID int64, username string) (*TokenInfo, error) {
	ctx := context.Background()

	j.logger.Info(ctx, "generating access token with info",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("username", username),
		logiface.Duration("expiry", j.accessExpiry),
	)

	// 生成JTI - 确保所有token都包含JTI
	jti := uuid.New().String()

	claims := Claims{
		UserID:   userID,
		TenantID: tenantID,
		Username: username,
		Type:     "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti,
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.accessExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-center",
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"user-center-api"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(j.secretKey))
	if err != nil {
		j.logger.Error(ctx, "failed to generate access token with info",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("username", username),
		)
		return nil, userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("access token signing failed: %v", err))
	}

	// 计算令牌哈希
	tokenHash := j.CalculateTokenHash(tokenString)

	tokenInfo := &TokenInfo{
		Token:     tokenString,
		JTI:       jti,
		TokenHash: tokenHash,
		ExpiresAt: time.Now().Add(j.accessExpiry),
	}

	j.logger.Info(ctx, "access token with info generated successfully",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("username", username),
		logiface.String("token_type", "access"),
		logiface.String("jti", jti),
		logiface.String("token_hash", tokenHash),
	)

	return tokenInfo, nil
}

// GenerateRefreshToken 生成刷新令牌
func (j *JWTService) GenerateRefreshToken(userID, tenantID int64) (string, error) {
	ctx := context.Background()

	j.logger.Info(ctx, "generating refresh token",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.Duration("expiry", j.refreshExpiry),
	)

	// 生成JTI - 确保所有token都包含JTI
	jti := uuid.New().String()

	claims := Claims{
		UserID:   userID,
		TenantID: tenantID,
		Type:     "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti,
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.refreshExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-center",
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"user-center-api"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(j.secretKey))
	if err != nil {
		j.logger.Error(ctx, "failed to generate refresh token",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("tenant_id", tenantID),
		)
		return "", userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("refresh token signing failed: %v", err))
	}

	j.logger.Info(ctx, "refresh token generated successfully",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("token_type", "refresh"),
		logiface.String("jti", jti),
	)

	return tokenString, nil
}

// GenerateRefreshTokenWithInfo 生成刷新令牌并返回完整信息
func (j *JWTService) GenerateRefreshTokenWithInfo(userID, tenantID int64) (*TokenInfo, error) {
	ctx := context.Background()

	j.logger.Info(ctx, "generating refresh token with info",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.Duration("expiry", j.refreshExpiry),
	)

	// 生成JTI - 确保所有token都包含JTI
	jti := uuid.New().String()

	claims := Claims{
		UserID:   userID,
		TenantID: tenantID,
		Type:     "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti,
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.refreshExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "user-center",
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"user-center-api"},
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(j.secretKey))
	if err != nil {
		j.logger.Error(ctx, "failed to generate refresh token with info",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.Int64("tenant_id", tenantID),
		)
		return nil, userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("refresh token signing failed: %v", err))
	}

	// 计算令牌哈希
	tokenHash := j.CalculateTokenHash(tokenString)

	tokenInfo := &TokenInfo{
		Token:     tokenString,
		JTI:       jti,
		TokenHash: tokenHash,
		ExpiresAt: time.Now().Add(j.refreshExpiry),
	}

	j.logger.Info(ctx, "refresh token with info generated successfully",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("token_type", "refresh"),
		logiface.String("jti", jti),
		logiface.String("token_hash", tokenHash),
	)

	return tokenInfo, nil
}

// CalculateTokenHash 计算令牌哈希
func (j *JWTService) CalculateTokenHash(token string) string {
	if token == "" {
		return ""
	}
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// ValidateToken 验证令牌 - 兼容性方法，返回map[string]interface{}
func (j *JWTService) ValidateToken(tokenString string) (map[string]interface{}, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "validating token",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	claims, err := j.ParseToken(tokenString)
	if err != nil {
		j.logger.Error(ctx, "token validation failed",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return nil, err
	}

	// 转换为map[string]interface{}
	result := map[string]interface{}{
		"user_id":   claims.UserID,
		"tenant_id": claims.TenantID,
		"username":  claims.Username,
		"type":      claims.Type,
		"jti":       claims.ID,
		"exp":       claims.ExpiresAt.Time.Unix(),
		"iat":       claims.IssuedAt.Time.Unix(),
		"nbf":       claims.NotBefore.Time.Unix(),
		"iss":       claims.Issuer,
		"sub":       claims.Subject,
		"aud":       claims.Audience,
	}

	j.logger.Debug(ctx, "token validation successful",
		logiface.Int64("user_id", claims.UserID),
		logiface.Int64("tenant_id", claims.TenantID),
		logiface.String("username", claims.Username),
		logiface.String("token_type", claims.Type),
		logiface.String("jti", claims.ID),
	)

	return result, nil
}

// ParseToken 解析JWT令牌
// 注意：当前实现暂时不做JTI验证，仅验证签名、过期时间等基本信息
// TODO: 后续可以添加JTI验证逻辑，如检查JTI是否在黑名单中
func (j *JWTService) ParseToken(tokenString string) (*Claims, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "parsing token",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			j.logger.Warn(ctx, "unexpected signing method",
				logiface.String("method", token.Method.Alg()),
			)
			return nil, fmt.Errorf("unexpected signing method: %v", token.Method.Alg())
		}
		return []byte(j.secretKey), nil
	})

	if err != nil {
		j.logger.Error(ctx, "token parsing failed",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return nil, userErrors.NewBusinessError(userErrors.CodeTokenInvalid, fmt.Sprintf("token parsing failed: %v", err))
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		// 暂时不做JTI验证，仅记录JTI信息用于调试
		j.logger.Debug(ctx, "token parsed successfully",
			logiface.Int64("user_id", claims.UserID),
			logiface.Int64("tenant_id", claims.TenantID),
			logiface.String("username", claims.Username),
			logiface.String("token_type", claims.Type),
			logiface.String("jti", claims.ID),
			logiface.String("note", "JTI validation is temporarily disabled"),
		)
		return claims, nil
	}

	j.logger.Warn(ctx, "invalid token claims",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)
	return nil, userErrors.NewBusinessError(userErrors.CodeTokenInvalid, "invalid token claims")
}

// IsTokenExpired 检查令牌是否过期
func (j *JWTService) IsTokenExpired(tokenString string) (bool, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "checking token expiration",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	claims, err := j.ParseToken(tokenString)
	if err != nil {
		j.logger.Error(ctx, "failed to parse token for expiration check",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return false, err
	}

	isExpired := time.Now().After(claims.ExpiresAt.Time)
	remainingTime := claims.ExpiresAt.Time.Sub(time.Now())

	j.logger.Debug(ctx, "token expiration check completed",
		logiface.Bool("is_expired", isExpired),
		logiface.Duration("remaining_time", remainingTime),
		logiface.String("expires_at", claims.ExpiresAt.Time.Format(time.RFC3339)),
	)

	return isExpired, nil
}

// GetTokenExpiry 获取令牌过期时间
func (j *JWTService) GetTokenExpiry(tokenString string) (time.Time, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "getting token expiry",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	claims, err := j.ParseToken(tokenString)
	if err != nil {
		j.logger.Error(ctx, "failed to parse token for expiry check",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return time.Time{}, err
	}

	j.logger.Debug(ctx, "token expiry retrieved successfully",
		logiface.String("expires_at", claims.ExpiresAt.Time.Format(time.RFC3339)),
	)

	return claims.ExpiresAt.Time, nil
}

// GetTokenJTI 获取令牌的JTI
func (j *JWTService) GetTokenJTI(tokenString string) (string, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "getting token JTI",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	claims, err := j.ParseToken(tokenString)
	if err != nil {
		j.logger.Error(ctx, "failed to parse token for JTI extraction",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return "", err
	}

	j.logger.Debug(ctx, "token JTI retrieved successfully",
		logiface.String("jti", claims.ID),
	)

	return claims.ID, nil
}

// RefreshToken 刷新令牌
func (j *JWTService) RefreshToken(ctx context.Context, refreshToken string) (string, string, error) {
	j.logger.Info(ctx, "refreshing token",
		logiface.String("refresh_token_length", fmt.Sprintf("%d", len(refreshToken))),
	)

	// 解析刷新令牌
	claims, err := j.ParseToken(refreshToken)
	if err != nil {
		j.logger.Error(ctx, "failed to parse refresh token",
			logiface.Error(err),
			logiface.String("refresh_token_length", fmt.Sprintf("%d", len(refreshToken))),
		)
		return "", "", userErrors.NewBusinessError(userErrors.CodeTokenInvalid, fmt.Sprintf("refresh token parsing failed: %v", err))
	}

	// 检查令牌类型
	if claims.Type != "refresh" {
		j.logger.Warn(ctx, "invalid token type for refresh",
			logiface.String("expected_type", "refresh"),
			logiface.String("actual_type", claims.Type),
			logiface.Int64("user_id", claims.UserID),
		)
		return "", "", userErrors.NewBusinessError(userErrors.CodeTokenInvalid, "invalid token type for refresh")
	}

	// 检查是否过期
	if time.Now().After(claims.ExpiresAt.Time) {
		j.logger.Warn(ctx, "refresh token expired",
			logiface.Int64("user_id", claims.UserID),
			logiface.String("expires_at", claims.ExpiresAt.Time.Format(time.RFC3339)),
		)
		return "", "", userErrors.NewBusinessError(userErrors.CodeTokenExpired, "refresh token expired")
	}

	// 生成新的访问令牌
	accessToken, err := j.GenerateAccessToken(claims.UserID, claims.TenantID, claims.Username)
	if err != nil {
		j.logger.Error(ctx, "failed to generate new access token",
			logiface.Error(err),
			logiface.Int64("user_id", claims.UserID),
			logiface.Int64("tenant_id", claims.TenantID),
		)
		return "", "", userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("new access token generation failed: %v", err))
	}

	// 生成新的刷新令牌
	newRefreshToken, err := j.GenerateRefreshToken(claims.UserID, claims.TenantID)
	if err != nil {
		j.logger.Error(ctx, "failed to generate new refresh token",
			logiface.Error(err),
			logiface.Int64("user_id", claims.UserID),
			logiface.Int64("tenant_id", claims.TenantID),
		)
		return "", "", userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("new refresh token generation failed: %v", err))
	}

	j.logger.Info(ctx, "token refresh completed successfully",
		logiface.Int64("user_id", claims.UserID),
		logiface.Int64("tenant_id", claims.TenantID),
		logiface.String("old_jti", claims.ID),
	)

	return accessToken, newRefreshToken, nil
}

// RevokeToken 撤销令牌（标记JTI为无效）
func (j *JWTService) RevokeToken(ctx context.Context, tokenString string) error {
	j.logger.Info(ctx, "revoking token",
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	// 解析令牌获取JTI
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		j.logger.Error(ctx, "failed to parse token for revocation",
			logiface.Error(err),
			logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
		)
		return userErrors.NewBusinessError(userErrors.CodeTokenInvalid, fmt.Sprintf("token parsing failed for revocation: %v", err))
	}

	// TODO: 实现JTI黑名单机制
	// 当前暂时只记录日志，不进行实际的撤销操作
	j.logger.Info(ctx, "token revocation requested",
		logiface.String("jti", claims.ID),
		logiface.Int64("user_id", claims.UserID),
		logiface.Int64("tenant_id", claims.TenantID),
		logiface.String("note", "JTI blacklist mechanism not implemented yet"),
	)

	return nil
}

// GenerateCustomJWT 生成自定义JWT令牌（支持RSA签名）
func (j *JWTService) GenerateCustomJWT(claims map[string]interface{}, headers map[string]interface{}, privateKey interface{}, signingMethod jwt.SigningMethod) (string, error) {
	ctx := context.Background()

	j.logger.Debug(ctx, "generating custom JWT token",
		logiface.String("signing_method", signingMethod.Alg()),
	)

	// 创建自定义声明
	customClaims := jwt.MapClaims(claims)

	// 创建令牌
	token := jwt.NewWithClaims(signingMethod, customClaims)

	// 设置自定义头部
	if headers != nil {
		for key, value := range headers {
			token.Header[key] = value
		}
	}

	// 签名令牌
	tokenString, err := token.SignedString(privateKey)
	if err != nil {
		j.logger.Error(ctx, "failed to sign custom JWT token",
			logiface.Error(err),
			logiface.String("signing_method", signingMethod.Alg()),
		)
		return "", userErrors.NewBusinessError(userErrors.CodeTokenGenerationFailed, fmt.Sprintf("custom JWT token signing failed: %v", err))
	}

	j.logger.Debug(ctx, "custom JWT token generated successfully",
		logiface.String("signing_method", signingMethod.Alg()),
		logiface.String("token_length", fmt.Sprintf("%d", len(tokenString))),
	)

	return tokenString, nil
}
