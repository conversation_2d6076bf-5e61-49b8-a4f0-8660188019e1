# JWT Service with Logging

这个包提供了一个带有完整日志功能的JWT服务，支持访问令牌和刷新令牌的生成、验证、刷新和撤销操作。

## 功能特性

- ✅ **完整的日志记录**: 所有JWT操作都有详细的日志记录
- ✅ **结构化日志**: 使用logiface包提供结构化日志输出
- ✅ **错误追踪**: 详细的错误日志，便于问题排查
- ✅ **安全审计**: 记录所有令牌操作，便于安全审计
- ✅ **性能监控**: 记录操作耗时和关键指标
- ✅ **测试友好**: 支持Mock Logger，便于单元测试
- ✅ **JTI支持**: 所有令牌都包含JTI（JWT ID），便于令牌管理

## JTI设计策略

### 当前实现
- **生成时全部使用JTI**: 每个JWT令牌都包含唯一的JTI标识符
- **验证时暂时不做JTI验证**: 当前阶段不验证JTI的有效性，仅记录JTI信息
- **渐进式设计**: 为后续JTI验证机制预留接口

### 优势
- **唯一性保证**: 每个令牌都有全局唯一的JTI
- **调试友好**: JTI信息记录在日志中，便于问题追踪
- **扩展性**: 可以根据需要随时启用JTI验证
- **向后兼容**: 保持与现有系统的兼容性

## 快速开始

### 基本使用

```go
package main

import (
    "platforms-pkg/logiface"
    "platforms-user/pkg/jwt"
    "time"
)

func main() {
    // 1. 初始化日志系统
    logConfig := logiface.MultiLogConfig{
        App: logiface.LogConfig{
            Level:       "info",
            Format:      "json",
            Output:      "stdout",
            ServiceName: "my-service",
            EnableTrace: true,
        },
    }
    logiface.InitLogger(logConfig)

    // 2. 创建JWT服务
    logger := logiface.GetLogger()
    jwtService := jwt.NewJWTServiceWithLogger(
        logger,
        "your-secret-key",
        24*time.Hour,  // 访问令牌过期时间
        168*time.Hour, // 刷新令牌过期时间
    )

    // 3. 生成令牌（包含JTI）
    accessToken, err := jwtService.GenerateAccessToken(123, 1, "john.doe")
    if err != nil {
        panic(err)
    }

    // 4. 解析令牌（暂时不做JTI验证）
    claims, err := jwtService.ParseToken(accessToken)
    if err != nil {
        panic(err)
    }

    fmt.Printf("User ID: %d, Username: %s, JTI: %s\n", 
        claims.UserID, claims.Username, claims.ID)
}
```

### 使用默认日志器

```go
// 使用默认的全局日志器
jwtService := jwt.NewJWTService(
    "your-secret-key",
    24*time.Hour,
    168*time.Hour,
)
```

## API 参考

### 构造函数

#### `NewJWTService(secretKey string, accessExpiry, refreshExpiry time.Duration) *JWTService`

使用默认日志器创建JWT服务。

#### `NewJWTServiceWithLogger(logger logiface.Logger, secretKey string, accessExpiry, refreshExpiry time.Duration) *JWTService`

使用自定义日志器创建JWT服务。

### 主要方法

#### `GenerateAccessToken(userID, tenantID int64, username string) (string, error)`

生成访问令牌，包含JTI。

**日志记录:**
- Info级别: 开始生成令牌
- Info级别: 令牌生成成功（包含JTI）
- Error级别: 令牌生成失败

#### `GenerateRefreshToken(userID, tenantID int64) (string, error)`

生成刷新令牌，包含JTI。

#### `ParseToken(tokenString string) (*Claims, error)`

解析JWT令牌，暂时不做JTI验证。

**注意**: 当前实现仅验证签名、过期时间等基本信息，不验证JTI的有效性。

#### `GetTokenJTI(tokenString string) (string, error)`

获取令牌的JTI标识符。

#### `RevokeToken(ctx context.Context, tokenString string) error`

撤销令牌（标记JTI为无效）。

**注意**: 当前实现仅记录JTI信息，不进行实际的撤销操作。

## 日志示例

### 令牌生成
```
INFO generating access token user_id=123 tenant_id=1 username=john.doe expiry=24h0m0s
INFO access token generated successfully user_id=123 tenant_id=1 username=john.doe token_type=access jti=550e8400-e29b-41d4-a716-446655440000
```

### 令牌验证
```
DEBUG parsing token token_length=1234
DEBUG token parsed successfully user_id=123 tenant_id=1 username=john.doe token_type=access jti=550e8400-e29b-41d4-a716-446655440000 note="JTI validation is temporarily disabled"
```

### 令牌撤销
```
INFO revoking token token_length=1234
INFO token revocation requested jti=550e8400-e29b-41d4-a716-446655440000 user_id=123 tenant_id=1 note="JTI blacklist mechanism not implemented yet"
```

## 设计文档

详细的JWT重新设计说明请参考：[JWT重新设计实现说明](docs/auth/jwt-redesign-implementation.md)

## 注意事项

1. **JTI验证暂时禁用**: 当前实现不验证JTI的有效性，仅记录JTI信息
2. **向后兼容**: 保持与现有系统的兼容性
3. **日志记录**: 所有JTI相关操作都有详细的日志记录
4. **扩展性**: 为后续JTI验证机制预留了接口

## 后续计划

- 实现JTI黑名单机制
- 支持JTI白名单验证
- 添加JTI有效期检查
- 基于JTI的会话管理
- 安全审计日志增强 