package jwt

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/logiface"
)

// ExampleJTIUsage 演示JTI的使用
func ExampleJTIUsage() {
	// 1. 初始化日志系统
	logConfig := logiface.MultiLogConfig{
		App: logiface.LogConfig{
			Level:       "info",
			Format:      "json",
			Output:      "stdout",
			ServiceName: "jwt-jti-example",
			EnableTrace: true,
		},
	}
	logiface.InitLogger(logConfig)

	// 2. 创建JWT服务
	logger := logiface.GetLogger()
	jwtService := NewJWTServiceWithLogger(
		logger,
		"your-secret-key-here",
		24*time.Hour,  // 访问令牌24小时过期
		168*time.Hour, // 刷新令牌7天过期
	)

	// 3. 生成访问令牌（包含JTI）
	userID := int64(12345)
	tenantID := int64(1)
	username := "john.doe"

	fmt.Println("=== 生成访问令牌 ===")
	accessToken, err := jwtService.GenerateAccessToken(userID, tenantID, username)
	if err != nil {
		fmt.Printf("Failed to generate access token: %v\n", err)
		return
	}
	fmt.Printf("Generated access token: %s\n", accessToken)

	// 4. 生成刷新令牌（包含JTI）
	fmt.Println("\n=== 生成刷新令牌 ===")
	refreshToken, err := jwtService.GenerateRefreshToken(userID, tenantID)
	if err != nil {
		fmt.Printf("Failed to generate refresh token: %v\n", err)
		return
	}
	fmt.Printf("Generated refresh token: %s\n", refreshToken)

	// 5. 解析访问令牌（暂时不做JTI验证）
	fmt.Println("\n=== 解析访问令牌 ===")
	claims, err := jwtService.ParseToken(accessToken)
	if err != nil {
		fmt.Printf("Failed to parse access token: %v\n", err)
		return
	}
	fmt.Printf("Token claims: UserID=%d, TenantID=%d, Username=%s, Type=%s, JTI=%s\n",
		claims.UserID, claims.TenantID, claims.Username, claims.Type, claims.ID)

	// 6. 获取令牌的JTI
	fmt.Println("\n=== 获取令牌JTI ===")
	jti, err := jwtService.GetTokenJTI(accessToken)
	if err != nil {
		fmt.Printf("Failed to get token JTI: %v\n", err)
		return
	}
	fmt.Printf("Token JTI: %s\n", jti)

	// 7. 检查令牌是否过期
	fmt.Println("\n=== 检查令牌过期 ===")
	isExpired, err := jwtService.IsTokenExpired(accessToken)
	if err != nil {
		fmt.Printf("Failed to check token expiration: %v\n", err)
		return
	}
	fmt.Printf("Token expired: %t\n", isExpired)

	// 8. 获取令牌过期时间
	fmt.Println("\n=== 获取令牌过期时间 ===")
	expiry, err := jwtService.GetTokenExpiry(accessToken)
	if err != nil {
		fmt.Printf("Failed to get token expiry: %v\n", err)
		return
	}
	fmt.Printf("Token expires at: %s\n", expiry.Format(time.RFC3339))

	// 9. 刷新令牌
	fmt.Println("\n=== 刷新令牌 ===")
	newAccessToken, newRefreshToken, err := jwtService.RefreshToken(context.Background(), refreshToken)
	if err != nil {
		fmt.Printf("Failed to refresh token: %v\n", err)
		return
	}
	fmt.Printf("New access token: %s\n", newAccessToken)
	fmt.Printf("New refresh token: %s\n", newRefreshToken)

	// 10. 撤销令牌（标记JTI为无效）
	fmt.Println("\n=== 撤销令牌 ===")
	err = jwtService.RevokeToken(context.Background(), accessToken)
	if err != nil {
		fmt.Printf("Failed to revoke token: %v\n", err)
		return
	}
	fmt.Println("Token revocation requested (JTI blacklist mechanism not implemented yet)")

	// 11. 生成带完整信息的令牌
	fmt.Println("\n=== 生成带完整信息的令牌 ===")
	tokenInfo, err := jwtService.GenerateAccessTokenWithInfo(userID, tenantID, username)
	if err != nil {
		fmt.Printf("Failed to generate access token with info: %v\n", err)
		return
	}
	fmt.Printf("Token Info: Token=%s, JTI=%s, TokenHash=%s, ExpiresAt=%s\n",
		tokenInfo.Token[:50]+"...", // 只显示前50个字符
		tokenInfo.JTI,
		tokenInfo.TokenHash,
		tokenInfo.ExpiresAt.Format(time.RFC3339))

	fmt.Println("\n=== JTI使用示例完成 ===")
	fmt.Println("注意：当前实现暂时不做JTI验证，仅记录JTI信息用于调试和后续扩展")
}
