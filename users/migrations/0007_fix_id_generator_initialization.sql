-- 修复ID生成器初始化冲突问题
-- 迁移时间: 2025-01-27
-- 问题描述: ID生成器与数据库初始化数据冲突，需要统一序列名称和修复冲突

-- 1. 清理可能存在的冲突序列
DELETE FROM id_sequence WHERE business_type IN ('user', 'tenant', 'role', 'permission', 'department', 'position', 'resource') AND sequence_name = '';

-- 2. 创建统一的ID序列配置
INSERT INTO id_sequence (business_type, sequence_name, tenant_id, current_value, increment_step, max_value, min_value, threshold, is_active, created_at, updated_at) VALUES
-- 系统级序列（tenant_id = 0）
('tenant', 'tenant', 0, 1, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
-- 租户级序列（tenant_id = 1，演示租户）
('user', 'user', 1, 1001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
('role', 'role', 1, 2001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
('permission', 'permission', 1, 3001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
('department', 'department', 1, 4001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
('position', 'position', 1, 5001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW()),
('resource', 'resource', 1, 10001, 1000, 9223372036854775807, 1, 20, TRUE, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
current_value = VALUES(current_value),
increment_step = VALUES(increment_step),
updated_at = NOW();

-- 3. 清理旧的ID分配段
DELETE FROM id_allocation WHERE sequence_id IN (
    SELECT id FROM id_sequence WHERE business_type IN ('user', 'tenant', 'role', 'permission', 'department', 'position', 'resource')
);

-- 4. 为每个序列创建初始ID分配段
INSERT INTO id_allocation (sequence_id, tenant_id, start_value, end_value, segment_size, status, allocated_at, created_at, updated_at) 
SELECT 
    s.id,
    s.tenant_id,
    s.current_value + 1,
    s.current_value + s.increment_step,
    s.increment_step,
    'AVAILABLE',
    NULL,
    NOW(),
    NOW()
FROM id_sequence s
WHERE s.business_type IN ('user', 'tenant', 'role', 'permission', 'department', 'position', 'resource');

-- 5. 验证修复结果
-- 检查序列配置
SELECT 'ID sequences after fix:' as info, business_type, sequence_name, tenant_id, current_value, increment_step FROM id_sequence 
WHERE business_type IN ('user', 'tenant', 'role', 'permission', 'department', 'position', 'resource')
ORDER BY business_type, tenant_id;

-- 检查ID分配段
SELECT 'ID allocation segments after fix:' as info, 
       s.business_type, 
       a.start_value, 
       a.end_value, 
       a.status 
FROM id_allocation a
JOIN id_sequence s ON a.sequence_id = s.id
WHERE s.business_type IN ('user', 'tenant', 'role', 'permission', 'department', 'position', 'resource')
ORDER BY s.business_type, a.start_value; 