-- 合并验证策略表到配置表
-- 迁移时间: 2025-01-XX
-- 问题描述: 将verification_policies数据迁移到verification_configs表，统一验证配置管理

-- 1. 首先备份原表数据
CREATE TABLE verification_configs_backup_20250127 AS SELECT * FROM verification_configs;
CREATE TABLE verification_policies_backup_20250127 AS SELECT * FROM verification_policies;

-- 2. 修改verification_configs表结构
ALTER TABLE verification_configs 
ADD COLUMN config_mode ENUM('static', 'dynamic') NOT NULL DEFAULT 'static' COMMENT '配置模式：static=静态配置，dynamic=动态策略' AFTER tenant_id,
ADD COLUMN business_scene VARCHAR(100) NULL COMMENT '业务场景：user_register, password_reset, email_change等' AFTER target_type,
ADD COLUMN judgment_dimension VARCHAR(100) NULL COMMENT '判定维度：ip_location, user_behavior, device_info等' AFTER business_scene,
ADD COLUMN condition_expr TEXT NULL COMMENT '条件表达式，支持复杂逻辑判断' AFTER judgment_dimension,
ADD COLUMN require_verification BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否需要验证' AFTER template_code,
ADD COLUMN verification_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '验证级别：1=低,2=中,3=高' AFTER require_verification,
ADD COLUMN priority INT NOT NULL DEFAULT 0 COMMENT '优先级，数值越大优先级越高' AFTER verification_level,
ADD COLUMN description TEXT COMMENT '配置描述' AFTER is_active,
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER description;

-- 3. 修改purpose字段为可为空（动态配置时为空）
ALTER TABLE verification_configs MODIFY COLUMN purpose TINYINT UNSIGNED NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证';

-- 4. 删除原有的唯一索引
DROP INDEX uk_tenant_purpose_target ON verification_configs;

-- 5. 创建新的唯一索引
ALTER TABLE verification_configs 
ADD UNIQUE KEY uk_static_config (tenant_id, purpose, target_type, deleted_at) COMMENT '静态配置唯一索引',
ADD UNIQUE KEY uk_dynamic_config (tenant_id, business_scene, judgment_dimension, condition_expr(255), deleted_at) COMMENT '动态配置唯一索引',
ADD KEY idx_config_mode (config_mode),
ADD KEY idx_business_scene (business_scene),
ADD KEY idx_priority (priority),
ADD KEY idx_deleted_at (deleted_at);

-- 6. 更新现有静态配置数据
UPDATE verification_configs SET 
    config_mode = 'static',
    require_verification = TRUE,
    verification_level = 1,
    priority = 0;

-- 7. 迁移动态策略数据
INSERT INTO verification_configs (
    tenant_id, config_mode, business_scene, judgment_dimension, condition_expr,
    target_type, token_type, token_length, expire_minutes, max_attempts,
    rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day, template_code,
    require_verification, verification_level, priority, is_active, description,
    created_at, updated_at
)
SELECT 
    tenant_id, 'dynamic' as config_mode, business_scene, judgment_dimension, condition_expr,
    target_type, token_type, token_length, expire_minutes, max_attempts,
    rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day, template_code,
    require_verification, verification_level, priority, is_enabled as is_active, description,
    created_at, updated_at
FROM verification_policies
WHERE deleted_at IS NULL;

-- 8. 验证迁移结果
SELECT 
    config_mode,
    COUNT(*) as count,
    COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_count
FROM verification_configs
WHERE deleted_at IS NULL
GROUP BY config_mode;

-- 9. 检查数据一致性
SELECT 
    'Static configs' as type,
    COUNT(*) as count
FROM verification_configs 
WHERE config_mode = 'static' AND deleted_at IS NULL

UNION ALL

SELECT 
    'Dynamic configs' as type,
    COUNT(*) as count
FROM verification_configs 
WHERE config_mode = 'dynamic' AND deleted_at IS NULL

UNION ALL

SELECT 
    'Original configs' as type,
    COUNT(*) as count
FROM verification_configs_backup_20250127

UNION ALL

SELECT 
    'Original policies' as type,
    COUNT(*) as count
FROM verification_policies_backup_20250127
WHERE deleted_at IS NULL;

-- 10. 验证必要字段的完整性
SELECT 
    'Static config validation' as check_type,
    COUNT(*) as total,
    COUNT(CASE WHEN purpose IS NOT NULL THEN 1 END) as has_purpose,
    COUNT(CASE WHEN business_scene IS NULL THEN 1 END) as no_business_scene
FROM verification_configs 
WHERE config_mode = 'static' AND deleted_at IS NULL

UNION ALL

SELECT 
    'Dynamic config validation' as check_type,
    COUNT(*) as total,
    COUNT(CASE WHEN business_scene IS NOT NULL THEN 1 END) as has_business_scene,
    COUNT(CASE WHEN purpose IS NULL THEN 1 END) as no_purpose
FROM verification_configs 
WHERE config_mode = 'dynamic' AND deleted_at IS NULL;

SELECT 'Verification configs migration completed successfully' as info;