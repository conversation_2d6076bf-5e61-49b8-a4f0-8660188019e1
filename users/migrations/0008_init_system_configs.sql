-- 初始化系统配置
-- 密码策略默认配置
INSERT INTO system_config (tenant_id, config_key, config_value, config_type, created_at, updated_at) VALUES 
(0, 'password_policy', '{
  "min_length": 8,
  "max_length": 32,
  "require_uppercase": true,
  "require_lowercase": true,
  "require_digits": true,
  "require_special_chars": false,
  "forbidden_patterns": ["123456", "password", "admin"],
  "password_history_count": 5,
  "expire_days": 90
}', 'json', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = NOW();

-- 注册方式默认配置
INSERT INTO system_config (tenant_id, config_key, config_value, config_type, created_at, updated_at) VALUES 
(0, 'registration_methods', '{
  "email": {
    "enabled": true,
    "require_verification": true,
    "auto_activate": false
  },
  "phone": {
    "enabled": true,
    "require_verification": true,
    "auto_activate": false
  },
  "oauth": {
    "enabled": true,
    "auto_activate": true
  },
  "admin_creation": {
    "enabled": true,
    "require_approval": false
  }
}', 'json', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_at = NOW(); 