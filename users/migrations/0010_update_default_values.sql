-- 迁移脚本：更新ID生成器默认值
-- 创建时间：2025-01-27
-- 描述：更新 increment_step 和 max_value 的默认值，支持不限制最大值

-- 1. 更新表结构默认值
ALTER TABLE id_sequence 
MODIFY COLUMN increment_step INT NOT NULL DEFAULT 1 COMMENT '步长',
MODIFY COLUMN max_value BIGINT NOT NULL DEFAULT 100 COMMENT '最大值';

-- 2. 更新现有记录的默认值（只更新使用旧默认值的记录）
UPDATE id_sequence SET 
    increment_step = 1,
    max_value = 100
WHERE increment_step = 1000 OR max_value = 9223372036854775807;

-- 3. 验证更新结果
SELECT 
    'Updated sequence configurations:' as info,
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    max_value,
    CASE 
        WHEN max_value = 0 THEN '不限制'
        ELSE CAST(max_value AS CHAR)
    END as max_value_display,
    min_value,
    threshold,
    is_active
FROM id_sequence 
ORDER BY business_type, tenant_id;

-- 4. 显示表结构
SELECT 
    'Table structure after update:' as info,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'platforms-user' 
AND TABLE_NAME = 'id_sequence' 
AND COLUMN_NAME IN ('increment_step', 'cache_size', 'max_value', 'min_value')
ORDER BY ORDINAL_POSITION; 