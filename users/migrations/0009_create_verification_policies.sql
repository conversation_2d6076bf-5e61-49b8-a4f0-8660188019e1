-- 创建验证策略表
-- 迁移时间: 2025-01-XX
-- 问题描述: 添加验证策略表，支持基于条件表达式的动态验证策略配置

-- 创建验证策略表
CREATE TABLE verification_policies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    
    -- 策略基本信息
    business_scene VARCHAR(100) NOT NULL COMMENT '业务场景：user_register, password_reset, email_change等',
    judgment_dimension VARCHAR(100) NOT NULL COMMENT '判定维度：ip_location, user_behavior, device_info等',
    condition_expr TEXT NOT NULL COMMENT '条件表达式，支持复杂逻辑判断',
    
    -- 验证配置
    require_verification BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否需要验证码',
    verification_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '验证级别：1=低,2=中,3=高',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
    token_length INT UNSIGNED NOT NULL DEFAULT 6 COMMENT '验证码长度',
    expire_minutes INT UNSIGNED NOT NULL DEFAULT 10 COMMENT '过期时间（分钟）',
    max_attempts INT UNSIGNED NOT NULL DEFAULT 3 COMMENT '最大尝试次数',
    
    -- 限流配置
    rate_limit_per_minute INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '每分钟限制次数',
    rate_limit_per_hour INT UNSIGNED NOT NULL DEFAULT 5 COMMENT '每小时限制次数',
    rate_limit_per_day INT UNSIGNED NOT NULL DEFAULT 20 COMMENT '每天限制次数',
    
    -- 模板配置
    template_code VARCHAR(100) NOT NULL COMMENT '关联的模板代码',
    
    -- 策略状态
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    priority INT NOT NULL DEFAULT 0 COMMENT '优先级，数值越大优先级越高',
    description TEXT COMMENT '策略描述',
    
    -- 审计字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间（软删除）',
    
    -- 索引
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_business_scene (business_scene),
    INDEX idx_judgment_dimension (judgment_dimension),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at),
    INDEX idx_tenant_scene_enabled (tenant_id, business_scene, is_enabled),
    INDEX idx_tenant_dimension_enabled (tenant_id, judgment_dimension, is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='验证策略表';

-- 插入默认策略示例
INSERT INTO verification_policies (
    tenant_id, business_scene, judgment_dimension, condition_expr, 
    require_verification, verification_level, target_type, token_type, 
    token_length, expire_minutes, max_attempts, 
    rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day, 
    template_code, is_enabled, priority, description
) VALUES 
-- 用户注册默认策略
(0, 'user_register', 'default', 'true', TRUE, 1, 1, 2, 6, 30, 5, 3, 10, 50, 'email_activation', TRUE, 0, '用户注册邮箱激活默认策略'),
(0, 'user_register', 'default', 'true', TRUE, 1, 2, 2, 6, 10, 3, 1, 5, 20, 'sms_activation', TRUE, 0, '用户注册手机激活默认策略'),

-- 密码重置默认策略
(0, 'password_reset', 'default', 'true', TRUE, 2, 1, 1, 32, 60, 3, 1, 3, 10, 'password_reset', TRUE, 0, '密码重置邮箱验证默认策略'),
(0, 'password_reset', 'default', 'true', TRUE, 2, 2, 2, 6, 10, 3, 1, 3, 10, 'password_reset_sms', TRUE, 0, '密码重置手机验证默认策略'),

-- 邮箱变更默认策略
(0, 'email_change', 'default', 'true', TRUE, 2, 1, 2, 6, 15, 3, 2, 6, 30, 'email_change', TRUE, 0, '邮箱变更验证默认策略'),

-- 手机变更默认策略
(0, 'phone_change', 'default', 'true', TRUE, 2, 2, 2, 6, 10, 3, 1, 5, 20, 'phone_change', TRUE, 0, '手机变更验证默认策略'),

-- 登录验证默认策略
(0, 'login_verification', 'default', 'true', TRUE, 1, 2, 2, 6, 5, 3, 3, 15, 100, 'login_verification', TRUE, 0, '登录验证默认策略'),

-- MFA验证默认策略
(0, 'mfa_verification', 'default', 'true', TRUE, 3, 3, 2, 6, 5, 3, 5, 20, 200, 'mfa_verification', TRUE, 0, 'MFA验证默认策略');

-- 验证策略表创建成功
SELECT 'Verification policies table created successfully' as info;

-- 检查策略初始化
SELECT 
    tenant_id,
    business_scene,
    judgment_dimension,
    target_type,
    template_code,
    is_enabled
FROM verification_policies 
ORDER BY tenant_id, business_scene, priority DESC;