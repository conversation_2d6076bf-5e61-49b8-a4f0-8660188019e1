-- 创建通用验证系统表结构
-- 迁移时间: 2025-01-XX
-- 问题描述: 支持邮件激活、手机验证码、MFA等多种验证渠道的统一验证系统

-- 1. 创建统一验证令牌表
CREATE TABLE verification_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    user_id BIGINT UNSIGNED NULL COMMENT '用户ID，可为空（如注册时）',
    
    -- 验证信息
    token VARCHAR(255) NOT NULL COMMENT '验证令牌/验证码',
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
    target VARCHAR(255) NOT NULL COMMENT '目标地址（邮箱/手机号）',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
    
    -- 业务信息
    purpose TINYINT UNSIGNED NOT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
    template_code VARCHAR(100) NOT NULL COMMENT '关联的模板代码',
    
    -- 状态管理
    status TINYINT UNSIGNED DEFAULT 1 COMMENT '状态：1=未使用,2=已使用,3=已过期,4=已撤销',
    expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    revoked_at TIMESTAMP NULL COMMENT '撤销时间',
    
    -- 安全信息
    ip_address VARCHAR(45) COMMENT '请求IP地址',
    user_agent TEXT COMMENT '用户代理',
    attempt_count INT DEFAULT 0 COMMENT '尝试次数',
    max_attempts INT DEFAULT 5 COMMENT '最大尝试次数',
    
    -- 基础字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE KEY uk_token (token),
    KEY idx_target (target_type, target),
    KEY idx_user (tenant_id, user_id),
    KEY idx_purpose (purpose),
    KEY idx_expires (expires_at),
    KEY idx_status (status),
    KEY idx_tenant_purpose (tenant_id, purpose)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一验证令牌表';

-- 2. 创建验证配置表
CREATE TABLE verification_configs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    
    -- 配置信息
    purpose TINYINT UNSIGNED NOT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
    target_type TINYINT UNSIGNED NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
    
    -- 验证策略
    token_type TINYINT UNSIGNED NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
    token_length INT NOT NULL DEFAULT 6 COMMENT '验证码长度',
    expire_minutes INT NOT NULL DEFAULT 30 COMMENT '过期时间（分钟）',
    max_attempts INT NOT NULL DEFAULT 5 COMMENT '最大尝试次数',
    
    -- 频率限制
    rate_limit_per_minute INT NOT NULL DEFAULT 3 COMMENT '每分钟限制',
    rate_limit_per_hour INT NOT NULL DEFAULT 10 COMMENT '每小时限制',
    rate_limit_per_day INT NOT NULL DEFAULT 50 COMMENT '每日限制',
    
    -- 模板配置
    template_code VARCHAR(100) NOT NULL COMMENT '模板代码',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    -- 基础字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE KEY uk_tenant_purpose_target (tenant_id, purpose, target_type),
    KEY idx_tenant_id (tenant_id),
    KEY idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证配置表';

-- 3. 创建验证日志表
CREATE TABLE verification_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tenant_id BIGINT UNSIGNED NOT NULL COMMENT '租户ID',
    token_id BIGINT UNSIGNED NOT NULL COMMENT '验证令牌ID',
    
    -- 操作信息
    action TINYINT UNSIGNED NOT NULL COMMENT '操作：1=发送,2=验证成功,3=验证失败,4=过期,5=撤销',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    -- 结果信息
    success BOOLEAN NOT NULL COMMENT '是否成功',
    error_message VARCHAR(500) COMMENT '错误信息',
    
    -- 基础字段
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    KEY idx_token_id (token_id),
    KEY idx_tenant_id (tenant_id),
    KEY idx_action (action),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='验证日志表';

-- 4. 初始化默认验证配置
INSERT INTO verification_configs (tenant_id, purpose, target_type, token_type, token_length, expire_minutes, max_attempts, rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day, template_code, is_active) VALUES
-- 注册激活配置
(1, 1, 1, 1, 32, 30, 5, 3, 10, 50, 'account_verification', TRUE),      -- 邮箱激活使用链接类型
(1, 1, 2, 2, 6, 10, 3, 1, 5, 20, 'sms_activation', TRUE),         -- 手机激活

-- 密码重置配置
(1, 2, 1, 1, 32, 60, 3, 1, 3, 10, 'password_reset', TRUE),        -- 邮箱重置
(1, 2, 2, 2, 6, 10, 3, 1, 3, 10, 'password_reset_sms', TRUE),     -- 手机重置

-- 邮箱变更配置
(1, 3, 1, 2, 6, 30, 5, 3, 10, 50, 'email_change', TRUE),          -- 邮箱变更

-- 手机变更配置
(1, 4, 2, 2, 6, 10, 3, 1, 5, 20, 'phone_change', TRUE),           -- 手机变更

-- 登录验证配置
(1, 5, 2, 2, 6, 5, 3, 1, 10, 100, 'login_verification', TRUE),    -- 手机登录验证
(1, 5, 3, 2, 6, 5, 3, 1, 20, 200, 'mfa_verification', TRUE);      -- MFA验证

-- 5. 验证表创建成功
SELECT 'Verification system tables created successfully' as info;

-- 6. 检查配置初始化
SELECT 
    tenant_id,
    purpose,
    target_type,
    template_code,
    is_active
FROM verification_configs 
ORDER BY tenant_id, purpose, target_type; 