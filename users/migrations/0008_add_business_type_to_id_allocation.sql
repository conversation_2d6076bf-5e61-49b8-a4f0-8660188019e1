-- 迁移脚本：为id_allocation表添加business_type字段
-- 创建时间：2024-01-01
-- 描述：为ID分配表添加业务类型字段，优化查询性能

-- 1. 添加business_type字段
ALTER TABLE id_allocation 
ADD COLUMN business_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '业务类型' AFTER tenant_id;

-- 2. 添加索引
ALTER TABLE id_allocation 
ADD INDEX idx_business_tenant_status (business_type, tenant_id, status),
ADD INDEX idx_business_type (business_type);

-- 3. 更新现有数据的business_type字段
-- 通过sequence_id关联id_sequence表获取business_type
UPDATE id_allocation a 
INNER JOIN id_sequence s ON a.sequence_id = s.id 
SET a.business_type = s.business_type 
WHERE a.business_type = '';

-- 4. 验证数据更新
SELECT 
    'Allocation records with business_type' as info,
    COUNT(*) as total_count,
    COUNT(CASE WHEN business_type != '' THEN 1 END) as with_business_type,
    COUNT(CASE WHEN business_type = '' THEN 1 END) as without_business_type
FROM id_allocation;

-- 5. 显示各业务类型的分配段数量
SELECT 
    business_type,
    tenant_id,
    status,
    COUNT(*) as segment_count
FROM id_allocation 
GROUP BY business_type, tenant_id, status 
ORDER BY business_type, tenant_id, status; 