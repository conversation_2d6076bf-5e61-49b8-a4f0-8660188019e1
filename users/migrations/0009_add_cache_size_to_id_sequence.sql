-- 迁移脚本：为id_sequence表添加cache_size字段
-- 创建时间：2025-01-27
-- 描述：为ID序列表添加缓存大小字段，用于控制内存缓存大小

-- 1. 添加cache_size字段
ALTER TABLE id_sequence 
ADD COLUMN cache_size INT NOT NULL DEFAULT 100 COMMENT '缓存大小' AFTER increment_step;

-- 2. 更新现有记录的默认值
UPDATE id_sequence SET 
    increment_step = 1,
    max_value = 100,
    cache_size = 1000
WHERE increment_step = 1000 OR max_value = 9223372036854775807;

-- 2. 验证字段添加
SELECT 
    'id_sequence table structure after adding cache_size:' as info,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'platforms-user' 
AND TABLE_NAME = 'id_sequence' 
AND COLUMN_NAME IN ('increment_step', 'cache_size')
ORDER BY ORDINAL_POSITION;

-- 3. 显示现有序列的配置
SELECT 
    'Current sequence configurations:' as info,
    business_type,
    sequence_name,
    tenant_id,
    current_value,
    increment_step,
    cache_size,
    threshold,
    is_active
FROM id_sequence 
ORDER BY business_type, tenant_id; 