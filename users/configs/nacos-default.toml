# 服务配置
[server]
port = 8084
read_timeout = "30s"
write_timeout = "30s"
idle_timeout = "60s"

# 数据库配置
[database.mysql]
host = "**************"
port = 3308
database = "platforms-user"
username = "root"
password = "Pu0cF6KVs]7AockCKVC"
charset = "utf8mb4"
parse_time = true
loc = "Local"
max_open_conns = 50
max_idle_conns = 10
conn_max_lifetime = "300s"
params = {}

[database.mysql.params]
parseTime = "true"
loc = "UTC"

# JWT配置
[jwt]
secret = "your_jwt_secret"
access_token_ttl = "1h"
refresh_token_ttl = "24h"
issuer = "platforms"
audience = "platforms-users"

# Redis配置
[redis]
host = "127.0.0.1"
port = 6379
password = ""
database = 0
pool_size = 10

# 日志配置
[log.app]
file = "logs/app.log"
level = "info"
format = "json"
max_size = 512
max_backups = 7
max_age = 30
compress = true

[log.access]
file = "logs/access.log"
level = "info"
format = "json"
max_size = 1024
max_backups = 3
max_age = 7
compress = true

[log.error]
file = "logs/error.log"
level = "error"
format = "json"
max_size = 256
max_backups = 10
max_age = 60
compress = true

# gRPC配置
[grpc]
port = 50051
service_name = "platforms-user-grpc"
group = "DEFAULT_GROUP"
namespace = ""
weight = 1
enable_register = true
metadata = { version = "1.0.0" }

# Nacos配置
[nacos]
app_name = "platforms-user"
group = "DEFAULT_GROUP"
server_configs = [ { host = "127.0.0.1", port = 8848 } ]
client_config = { namespace_id = "", timeout_ms = 5000 }

# OpenTelemetry配置（暂时禁用）
[otel]
endpoint = ""

# 测试用户配置（仅开发环境）
[test_user]
enabled = true  # 是否启用测试用户功能，生产环境应设为false

# 邮件服务配置
[email]
default_provider = "smtp"
retry_count = 3
retry_interval = "5s"
timeout = "30s"
enable_failover = true
default_from = "<EMAIL>"
default_reply_to = "<EMAIL>"

# 短信服务配置
[sms]
default_provider = "aliyun"
retry_count = 3
retry_interval = "5s"
timeout = "30s"
enable_failover = true
rate_limit_per_min = 60
max_concurrent = 10

# MFA服务配置
[mfa]
default_provider = "totp"
code_length = 6
code_expiry = "5m"
enable_failover = true

# gRPC客户端订阅配置
[[grpcSubscriptions]]
serviceName = "platforms-email"
group = "DEFAULT_GROUP"
namespace = ""
strategy = "weighted"  # 支持 random, round_robin, weighted
subscribe = true
retry = { enabled = true, maxRetries = 3, initialDelay = "1s", maxDelay = "10s", backoffFactor = 2.0 }
healthCheck = { enabled = true, interval = "30s", timeout = "5s" }
circuitBreaker = { enabled = true, failureThreshold = 5, successThreshold = 2, timeout = "60s" }

# 基础URL配置
base_url = "http://localhost:8084"
