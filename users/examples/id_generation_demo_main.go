package main

import (
	"context"
	"fmt"
	"log"

	"platforms-pkg/id"
)

func main() {
	_ = context.Background() // 声明但不使用，避免编译错误

	// 初始化雪花算法ID生成器
	if err := id.InitSnowflake(1); err != nil {
		log.Fatalf("Failed to initialize snowflake: %v", err)
	}

	// 示例：不同类型的ID生成
	fmt.Println("=== ID生成示例 ===")

	// 1. 雪花算法ID（Session、Request等）
	fmt.Println("\n1. 雪花算法ID（Session、Request等）:")
	sessionID := fmt.Sprintf("sess_%d", id.GenerateID())
	requestID := fmt.Sprintf("req_%d", id.GenerateID())
	fmt.Printf("   SessionID: %s\n", sessionID)
	fmt.Printf("   RequestID: %s\n", requestID)

	// 2. 实体创建示例（使用 EntityFactory）
	fmt.Println("\n2. 实体创建示例（使用 EntityFactory）:")

	// 注意：这些构造函数已被废弃，请使用 EntityFactory
	// 这里仅作为示例展示，实际使用时应该使用依赖注入的 EntityFactory

	fmt.Println("   注意：这些构造函数已被废弃，请使用 EntityFactory")
	fmt.Println("   实际使用时应该通过依赖注入获取 EntityFactory 实例")

	// 示例：使用 EntityFactory 的方式（需要依赖注入）
	// entityFactory := container.GetEntityFactory()
	// user, err := entityFactory.NewUser(ctx, 1, "testuser", "<EMAIL>", "Test User", *password)
	// tenant, err := entityFactory.NewTenant(ctx, "test_tenant", "Test Tenant")
	// role, err := entityFactory.NewRole(ctx, 1, "admin", "ADMIN", "管理员", "系统管理员角色")

	fmt.Println("\n=== 示例完成 ===")
	fmt.Println("\n注意：")
	fmt.Println("- 所有实体都使用 EntityFactory 构造函数")
	fmt.Println("- 使用雪花算法ID作为回退方案")
	fmt.Println("- 支持依赖注入和错误处理")
	fmt.Println("- 废弃的构造函数已移除，请使用 EntityFactory")
}
