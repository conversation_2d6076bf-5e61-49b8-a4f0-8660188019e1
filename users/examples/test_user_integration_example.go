package main

import (
	"fmt"
	"log"
	"net/http"
	"platforms-pkg/usercontext"
	"platforms-user/internal/infrastructure/config"
	appConfig "platforms-user/pkg/config"

	"github.com/gin-gonic/gin"
)

// 示例：如何在主应用中集成测试用户功能
func TestUserIntegrationExample() {
	// 1. 加载应用配置
	cfg := appConfig.LoadAppConfig("platforms-user-config")
	if cfg == nil {
		log.Fatal("Failed to load app config")
	}

	// 2. 创建测试用户集成服务
	testUserIntegration := config.NewTestUserIntegration(cfg)

	// 3. 创建 Gin 引擎
	engine := gin.New()

	// 4. 添加测试用户中间件（在其他中间件之前）
	engine.Use(testUserIntegration.CreateTestUserMiddleware())

	// 5. 添加其他中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())

	// 6. 设置路由
	setupRoutes(engine, testUserIntegration)

	// 7. 启动服务器
	fmt.Printf("Server starting on port %d\n", cfg.Server.Port)
	fmt.Printf("Test user feature enabled: %v\n", testUserIntegration.IsEnabled())
	if testUserIntegration.IsEnabled() {
		fmt.Println("You can use 'X-User-ID: 1' header to test with mock user")
	}

	log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", cfg.Server.Port), engine))
}

// setupRoutes 设置路由
func setupRoutes(engine *gin.Engine, testUserIntegration *config.TestUserIntegration) {
	// 健康检查（无需认证）
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":            "ok",
			"test_user_enabled": testUserIntegration.IsEnabled(),
		})
	})

	// API 路由组
	api := engine.Group("/api")

	// 无需认证的路由
	api.POST("/auth/login", handleLogin)
	api.POST("/auth/register", handleRegister)

	// 需要认证的路由
	authRequired := api.Group("")
	authRequired.Use(testUserIntegration.CreateTestUserAuthMiddleware())
	{
		// 用户相关
		authRequired.GET("/user/profile", handleGetProfile)
		authRequired.PUT("/user/profile", handleUpdateProfile)

		// 验证策略相关
		authRequired.GET("/verification/policies", handleGetPolicies)
		authRequired.POST("/verification/policy", handleCreatePolicy)
		authRequired.PUT("/verification/policy/:id", handleUpdatePolicy)
		authRequired.DELETE("/verification/policy/:id", handleDeletePolicy)

		// 租户相关
		authRequired.GET("/tenant/info", handleGetTenantInfo)
	}
}

// 处理器示例

func handleLogin(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Login endpoint - no auth required",
		"tip":     "Use 'X-User-ID: 1' header to test with mock user in dev environment",
	})
}

func handleRegister(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "Register endpoint - no auth required",
	})
}

func handleGetProfile(c *gin.Context) {
	// 获取用户信息
	userInfo, exists := usercontext.GetUserInfo(c.Request.Context())
	if !exists {
		c.JSON(401, gin.H{"error": "User not authenticated"})
		return
	}

	// 检查是否为测试用户
	isTestUser := false
	if testUserFlag, exists := c.Get("is_test_user"); exists {
		isTestUser = testUserFlag.(bool)
	}

	c.JSON(200, gin.H{
		"user_id":      userInfo.UserID,
		"username":     userInfo.Username,
		"real_name":    userInfo.RealName,
		"email":        userInfo.Email,
		"phone":        userInfo.Phone,
		"tenant_id":    userInfo.TenantID,
		"tenant_code":  userInfo.TenantCode,
		"tenant_name":  userInfo.TenantName,
		"is_test_user": isTestUser,
	})
}

func handleUpdateProfile(c *gin.Context) {
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	var req struct {
		RealName string `json:"real_name"`
		Email    string `json:"email"`
		Phone    string `json:"phone"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	c.JSON(200, gin.H{
		"message":   "Profile updated successfully",
		"user_id":   userInfo.UserID,
		"real_name": req.RealName,
		"email":     req.Email,
		"phone":     req.Phone,
	})
}

func handleGetPolicies(c *gin.Context) {
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())
	tenantInfo, _ := usercontext.GetTenantInfo(c.Request.Context())

	c.JSON(200, gin.H{
		"message":     "Get verification policies",
		"user_id":     userInfo.UserID,
		"tenant_id":   tenantInfo.TenantID,
		"tenant_code": tenantInfo.TenantCode,
		"policies": []gin.H{
			{
				"id":                 1,
				"scene":              "login",
				"dimension":          "ip",
				"condition_expr":     "fail_count > 5",
				"need_verification":  true,
				"verification_level": "high",
			},
		},
	})
}

func handleCreatePolicy(c *gin.Context) {
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())
	tenantInfo, _ := usercontext.GetTenantInfo(c.Request.Context())

	var req struct {
		Scene             string `json:"scene"`
		Dimension         string `json:"dimension"`
		ConditionExpr     string `json:"condition_expr"`
		NeedVerification  bool   `json:"need_verification"`
		VerificationLevel string `json:"verification_level"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request body"})
		return
	}

	c.JSON(201, gin.H{
		"message":            "Policy created successfully",
		"user_id":            userInfo.UserID,
		"tenant_id":          tenantInfo.TenantID,
		"scene":              req.Scene,
		"dimension":          req.Dimension,
		"condition_expr":     req.ConditionExpr,
		"need_verification":  req.NeedVerification,
		"verification_level": req.VerificationLevel,
	})
}

func handleUpdatePolicy(c *gin.Context) {
	policyID := c.Param("id")
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	c.JSON(200, gin.H{
		"message":   "Policy updated successfully",
		"policy_id": policyID,
		"user_id":   userInfo.UserID,
	})
}

func handleDeletePolicy(c *gin.Context) {
	policyID := c.Param("id")
	userInfo, _ := usercontext.GetUserInfo(c.Request.Context())

	c.JSON(200, gin.H{
		"message":   "Policy deleted successfully",
		"policy_id": policyID,
		"user_id":   userInfo.UserID,
	})
}

func handleGetTenantInfo(c *gin.Context) {
	tenantInfo, exists := usercontext.GetTenantInfo(c.Request.Context())
	if !exists {
		c.JSON(404, gin.H{"error": "Tenant not found"})
		return
	}

	c.JSON(200, gin.H{
		"tenant_id":   tenantInfo.TenantID,
		"tenant_code": tenantInfo.TenantCode,
		"tenant_name": tenantInfo.TenantName,
	})
}
