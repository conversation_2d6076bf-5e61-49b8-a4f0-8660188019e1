package grpc

import (
	"context"
	"regexp"

	"platforms-pkg/logiface"
	idgeneratorpb "platforms-user/api/idgeneratorpb"
	"platforms-user/internal/application/idgenerator/service"
)

// IdGeneratorServiceImpl ID生成器gRPC服务实现
type IdGeneratorServiceImpl struct {
	idgeneratorpb.UnimplementedIdGeneratorServiceServer
	idGeneratorService *service.IDGeneratorService
	logger             logiface.Logger
}

// NewIdGeneratorServiceImpl 创建ID生成器gRPC服务实现
func NewIdGeneratorServiceImpl(
	idGeneratorService *service.IDGeneratorService,
	logger logiface.Logger,
) *IdGeneratorServiceImpl {
	return &IdGeneratorServiceImpl{
		idGeneratorService: idGeneratorService,
		logger:             logger,
	}
}

// GenerateId 生成单个ID
func (s *IdGeneratorServiceImpl) GenerateId(ctx context.Context, req *idgeneratorpb.GenerateIdRequest) (*idgeneratorpb.GenerateIdResponse, error) {
	// 参数验证
	if req.BusinessType == "" {
		return &idgeneratorpb.GenerateIdResponse{
			Code:    400,
			Message: "business_type is required",
		}, nil
	}
	if req.TenantId <= 0 {
		return &idgeneratorpb.GenerateIdResponse{
			Code:    400,
			Message: "tenant_id must be positive",
		}, nil
	}

	// 验证业务类型格式
	if !s.isValidBusinessType(req.BusinessType) {
		return &idgeneratorpb.GenerateIdResponse{
			Code:    400,
			Message: "business_type format is invalid",
		}, nil
	}

	// 生成ID
	id, err := s.idGeneratorService.GenerateID(ctx, req.BusinessType, req.TenantId)
	if err != nil {
		s.logger.Error(ctx, "failed to generate id",
			logiface.Error(err),
			logiface.String("business_type", req.BusinessType),
			logiface.Int64("tenant_id", req.TenantId))
		return &idgeneratorpb.GenerateIdResponse{
			Code:    500,
			Message: "internal server error",
		}, nil
	}
	return &idgeneratorpb.GenerateIdResponse{
		Code:    200,
		Message: "success",
		Id:      id,
	}, nil
}

// GenerateBatchIds 批量生成ID
func (s *IdGeneratorServiceImpl) GenerateBatchIds(ctx context.Context, req *idgeneratorpb.GenerateBatchIdsRequest) (*idgeneratorpb.GenerateBatchIdsResponse, error) {
	// 参数验证
	if req.BusinessType == "" {
		return &idgeneratorpb.GenerateBatchIdsResponse{
			Code:    400,
			Message: "business_type is required",
		}, nil
	}
	if req.TenantId <= 0 {
		return &idgeneratorpb.GenerateBatchIdsResponse{
			Code:    400,
			Message: "tenant_id must be positive",
		}, nil
	}
	if req.Count <= 0 {
		return &idgeneratorpb.GenerateBatchIdsResponse{
			Code:    400,
			Message: "count must be positive",
		}, nil
	}
	if req.Count > 10000 {
		return &idgeneratorpb.GenerateBatchIdsResponse{
			Code:    400,
			Message: "count too large, max 10000",
		}, nil
	}
	// 批量生成ID
	ids, err := s.idGeneratorService.GenerateBatchIDs(ctx, req.BusinessType, req.TenantId, int(req.Count))
	if err != nil {
		s.logger.Error(ctx, "failed to generate batch ids",
			logiface.Error(err),
			logiface.String("business_type", req.BusinessType),
			logiface.Int64("tenant_id", req.TenantId),
			logiface.Int("count", int(req.Count)))
		return &idgeneratorpb.GenerateBatchIdsResponse{
			Code:    500,
			Message: "internal server error",
		}, nil
	}
	return &idgeneratorpb.GenerateBatchIdsResponse{
		Code:    200,
		Message: "success",
		Ids:     ids,
	}, nil
}

// isValidBusinessType 验证业务类型格式
func (s *IdGeneratorServiceImpl) isValidBusinessType(businessType string) bool {
	if businessType == "" {
		return false
	}

	// 支持 system:user 等格式的正则表达式
	// 必须以字母开头，只能包含字母、数字和下划线，支持冒号分隔但不能在开头或结尾
	pattern := `^[a-zA-Z][a-zA-Z0-9_]*(:[a-zA-Z][a-zA-Z0-9_]*)*$`
	matched, err := regexp.MatchString(pattern, businessType)
	if err != nil {
		return false
	}
	return matched
}
