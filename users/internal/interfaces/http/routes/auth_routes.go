package routes

import (
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"
	"platforms-user/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes 设置认证路由
func SetupAuthRoutes(router *gin.Engine, authHandler *handlers.AuthHandler, registerHandler *handlers.RegisterHandler, thirdPartyHandler *handlers.ThirdPartyHandler, jwtService *jwt.JWTService) {
	// 认证路由（不需要认证）
	auth := router.Group(config.AuthAPIPrefix)
	{
		auth.POST("/register", registerHandler.Register)             // 用户注册
		auth.GET("/activate", registerHandler.ActivateAccount)       // 激活账户
		auth.POST("/captcha", registerHandler.GenerateCaptcha)       // 生成验证码
		auth.POST("/login", authHandler.Login)                       // 用户登录
		auth.POST("/mfa-login", authHandler.MFALogin)                // MFA登录
		auth.POST("/refresh", authHandler.RefreshToken)              // 刷新令牌
		auth.POST("/forgot-password", authHandler.ForgotPassword)    // 忘记密码
		auth.POST("/reset-password", authHandler.ResetPassword)      // 重置密码
		auth.GET("/reset-password", authHandler.ResetPasswordPage)   // 密码重置页面验证
		auth.POST("/send-mfa-code", authHandler.SendMFACode)         // 发送MFA验证码
		auth.POST("/verify-mfa-code", authHandler.VerifyMFACode)     // 验证MFA验证码

		// 第三方登录路由（不需要认证）
		auth.POST("/third-party/login", thirdPartyHandler.ThirdPartyLogin)      // 第三方登录
		auth.POST("/third-party/config", thirdPartyHandler.GetThirdPartyConfig) // 获取第三方配置
	}

	// 需要认证的认证路由
	authProtected := router.Group(config.AuthAPIPrefix)
	authProtected.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		authProtected.POST("/logout", authHandler.Logout)                // 用户登出
		authProtected.POST("/sessions", authHandler.GetSessions)         // 获取会话列表
		authProtected.POST("/revoke-session", authHandler.RevokeSession) // 撤销会话

		// 第三方账户管理路由（需要认证）三方登录管理功能
		authProtected.POST("/third-party/list", thirdPartyHandler.GetThirdPartyAccounts)     // 获取第三方账户列表
		authProtected.POST("/third-party/unbind", thirdPartyHandler.UnbindThirdPartyAccount) // 解绑第三方账户
	}
}
