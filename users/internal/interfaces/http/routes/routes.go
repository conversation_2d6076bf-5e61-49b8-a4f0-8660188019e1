package routes

import (
	"context"
	common_response "platforms-pkg/common/response"
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/application/auth/service"
	"platforms-user/internal/infrastructure/container"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"
	"platforms-user/pkg/jwt"
	"time"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置所有路由
func SetupRoutes(engine *gin.Engine, container *container.DependencyContainer, jwtService *jwt.JWTService, tenantLookupService *service.TenantLookupService, serviceName string) {
	// 创建适配器
	userInfoProvider := &UserInfoProviderAdapter{container: container}
	tenantInfoProvider := &TenantInfoProviderAdapter{container: container}
	httpmiddleware.SetupCommonMiddleware(engine, &httpmiddleware.MiddlewareConfig{
		ServiceName:           serviceName,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      false,
		EnableTimeout:         true,
		RequestTimeout:        10 * time.Second,
		EnableTraceID:         true,
		Logger:                container.GetLogger(),
		EnableUserInfo:        true,
		UserInfoProvider:      userInfoProvider,
		TenantInfoProvider:    tenantInfoProvider,
		EnableAccessLog:       true,
	})
	// 基础路由
	engine.GET("/health", func(c *gin.Context) {
		common_response.Success(c, gin.H{"status": "ok"})
	})

	// 设置各模块路由
	SetupAuthRoutes(engine, container.GetAuthHandler(), container.GetRegisterHandler(), container.GetThirdPartyHandler(), jwtService)
	SetupUserRoutes(engine, container.GetUserHandler(), container.GetUserProfileHandler(), jwtService)
	SetupRoleRoutes(engine, container.GetRoleHandler(), jwtService)
	SetupPermissionRoutes(engine, container.GetPermissionHandler(), jwtService)
	SetupResourceRoutes(engine, container.GetResourceHandler(), jwtService)
	SetupDepartmentRoutes(engine, container.GetDepartmentHandler(), jwtService)
	SetupPositionRoutes(engine, container.GetPositionHandler(), jwtService)
	SetupTenantRoutes(engine, container.GetTenantHandler(), jwtService)
	SetupTenantConfigRoutes(engine, container.GetTenantConfigHandler(), jwtService)
	SetupMenuRoutes(engine, container.GetMenuHandler(), jwtService)
	SetupCaptchaRoutes(engine, container.GetRegisterHandler())
	SetupPasswordRoutes(engine, container.GetPasswordHandler(), jwtService)
	SetupIDGeneratorRoutes(engine, container.GetIDGeneratorHandler())
	SetupFileUploadRoutes(engine, container.GetFileUploadHandler(), jwtService)
	SetupOAuthChannelRoutes(engine, container.GetOAuthChannelHandler(), tenantLookupService)
	SetupVerificationRoutes(engine, container.GetVerificationHandler())
}

// SetupPasswordRoutes 设置密码相关路由
func SetupPasswordRoutes(engine *gin.Engine, passwordHandler *handlers.PasswordHandler, jwtService *jwt.JWTService) {
	passwordGroup := engine.Group(config.GlobalAPIPrefix + "/password")
	passwordGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		passwordGroup.POST("/change", passwordHandler.ChangePassword)
		passwordGroup.POST("/reset", passwordHandler.ResetPassword)
		passwordGroup.POST("/history", passwordHandler.GetPasswordHistory)
	}
}

// SetupFileUploadRoutes 设置文件上传相关路由
func SetupFileUploadRoutes(engine *gin.Engine, fileUploadHandler *handlers.FileUploadHandler, jwtService *jwt.JWTService) {
	fileGroup := engine.Group(config.GlobalAPIPrefix + "/file")
	fileGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		RegisterFileUploadRoutes(fileGroup, fileUploadHandler)
	}
}

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string) *httpmiddleware.AuthedUser {
	user, err := a.container.GetUserAppService().GetUserByToken(ctx, token)
	if err != nil {
		return nil
	}
	return &httpmiddleware.AuthedUser{
		UserId:   user.ID,
		Username: user.Username,
		RealName: user.RealName,
		Email:    user.Email,
		TenantId: user.TenantID,
	}
}

// TenantInfoProviderAdapter 租户信息提供者适配器
type TenantInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetTenantInfo 实现 TenantInfoProvider 接口
func (a *TenantInfoProviderAdapter) GetTenantInfo(ctx context.Context, tenantCode string) *httpmiddleware.TenantInfo {
	// 直接使用 TenantLookupService 查找完整的租户信息
	tenant, err := a.container.GetTenantLookupService().FindTenantByCode(ctx, tenantCode)
	if err != nil || tenant == nil {
		return nil
	}

	return &httpmiddleware.TenantInfo{
		TenantId:   tenant.ID,
		TenantCode: tenant.TenantCode,
		TenantName: tenant.TenantName,
	}
}
