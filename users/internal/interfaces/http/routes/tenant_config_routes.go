package routes

import (
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"
	"platforms-user/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupTenantConfigRoutes 设置租户配置路由
func SetupTenantConfigRoutes(engine *gin.Engine, tenantConfigHandler *handlers.TenantConfigHandler, jwtService *jwt.JWTService) {
	// 租户配置API路由组
	tenantConfigGroup := engine.Group(config.GlobalAPIPrefix + "/tenant-config")
	tenantConfigGroup.Use(httpmiddleware.RequireAuthedMiddleware())

	// 密码策略配置
	tenantConfigGroup.GET("/password-policy", tenantConfigHandler.GetPasswordPolicy)     // 获取密码策略
	tenantConfigGroup.POST("/password-policy", tenantConfigHandler.UpdatePasswordPolicy) // 更新密码策略

	// 注册方式配置
	tenantConfigGroup.GET("/registration-methods", tenantConfigHandler.GetRegistrationMethods)     // 获取注册方式
	tenantConfigGroup.POST("/registration-methods", tenantConfigHandler.UpdateRegistrationMethods) // 更新注册方式

	// 通用配置管理
	tenantConfigGroup.GET("/configs", tenantConfigHandler.GetTenantConfigs)               // 获取租户所有配置
	tenantConfigGroup.POST("/config", tenantConfigHandler.UpdateTenantConfig)             // 更新租户配置
	tenantConfigGroup.POST("/copy-system", tenantConfigHandler.CopySystemConfigsToTenant) // 复制系统配置到租户
}
