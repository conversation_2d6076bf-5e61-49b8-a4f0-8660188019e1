package routes

import (
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"

	"github.com/gin-gonic/gin"
)

// SetupPolicyRoutes 设置策略路由
func SetupPolicyRoutes(router *gin.Engine, policyHandler *handlers.PolicyHandler) {
	// 如果处理器未初始化，跳过路由设置
	if policyHandler == nil {
		return
	}

	// 策略管理路由组
	policyGroup := router.Group(config.GlobalAPIPrefix + "/verification/policies")
	policyGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 策略CRUD操作 - 统一使用POST方法
		policyGroup.POST("/create", policyHandler.CreatePolicy)        // 创建策略
		policyGroup.POST("/update", policyHandler.UpdatePolicy)        // 更新策略
		policyGroup.POST("/delete", policyHandler.DeletePolicy)        // 删除策略
		policyGroup.POST("/set-status", policyHandler.SetPolicyStatus) // 设置策略状态

		// 策略查询操作 - 统一使用POST方法
		policyGroup.POST("/list", policyHandler.GetPolicyList)  // 获取策略列表
		policyGroup.POST("/get", policyHandler.GetPolicyDetail) // 获取策略详情

		// 表达式相关操作
		policyGroup.POST("/expression/validate", policyHandler.ValidateExpression) // 验证表达式
		policyGroup.POST("/expression/test", policyHandler.TestExpression)         // 测试表达式
	}
}
