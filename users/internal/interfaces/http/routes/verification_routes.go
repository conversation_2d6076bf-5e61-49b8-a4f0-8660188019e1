package routes

import (
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/interfaces/http/handlers"

	"github.com/gin-gonic/gin"
)

// SetupVerificationRoutes 设置验证系统路由
func SetupVerificationRoutes(router *gin.Engine, verificationHandler *handlers.VerificationHandler) {
	// 如果处理器未初始化，跳过路由设置
	if verificationHandler == nil {
		return
	}

	// 验证系统路由（需要认证） - 按照规范要求修改路径结构
	// 修改为 /api/users/verification 以符合模块化路径规范
	verification := router.Group("/api/users/verification")

	// 按照规范要求实现标准中间件顺序：CORS → 限流 → 租户 → 认证 → 权限
	// CORS 中间件通常在全局路由层面设置
	// 这里添加租户和认证中间件
	verification.Use(httpmiddleware.RequireAuthedMiddleware()) // 认证中间件
	{
		// 操作类接口使用 POST 方法
		verification.POST("/send", verificationHandler.SendVerification)     // 发送验证
		verification.POST("/verify", verificationHandler.VerifyToken)        // 验证令牌
		verification.POST("/resend", verificationHandler.ResendVerification) // 重新发送验证

		// 查询类接口使用 GET 方法（符合HTTP方法语义化规范）
		verification.GET("/status", verificationHandler.CheckTokenStatus)  // 检查令牌状态
		verification.GET("/statistics", verificationHandler.GetStatistics) // 获取验证统计
	}
}
