package routes

import (
	"platforms-pkg/httpmiddleware"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"

	"github.com/gin-gonic/gin"
)

// SetupVerificationRoutes 设置验证系统路由
func SetupVerificationRoutes(router *gin.Engine, verificationHandler *handlers.VerificationHandler) {
	// 如果处理器未初始化，跳过路由设置
	if verificationHandler == nil {
		return
	}

	// 验证系统路由（需要认证）- 按照项目规范使用 /api/user/admin/verification 前缀
	verification := router.Group(config.GlobalAPIPrefix + "/admin/verification")
	verification.Use(httpmiddleware.RequireAuthedMiddleware()) // 认证中间件
	{
		// 验证操作接口 - 全部使用POST方法
		verification.POST("/send", verificationHandler.SendVerification)     // 发送验证
		verification.POST("/verify", verificationHandler.VerifyToken)        // 验证令牌
		verification.POST("/resend", verificationHandler.ResendVerification) // 重新发送验证
		verification.POST("/status", verificationHandler.CheckTokenStatus)   // 检查令牌状态
		verification.POST("/statistics", verificationHandler.GetStatistics)  // 获取验证统计

		// 配置管理接口 - 每个URL保持唯一性
		verification.GET("/configs", verificationHandler.ListConfigs)                   // 查询配置列表
		verification.POST("/configs/create", verificationHandler.ManageConfigs)         // 创建配置
		verification.POST("/configs/update", verificationHandler.ManageConfigs)         // 更新配置
		verification.POST("/configs/delete", verificationHandler.ManageConfigs)         // 删除配置
		verification.POST("/configs/copy", verificationHandler.CopyConfigs)             // 复制配置
		verification.POST("/configs/effective", verificationHandler.GetEffectiveConfig) // 获取有效配置
	}
}
