package middleware

import (
	"context"
	"os"
	"platforms-pkg/usercontext"
	"platforms-user/internal/infrastructure/config"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// TestUserMiddleware 测试用户中间件
// 在开发环境中，当请求头包含 X-User-ID: 1 时，自动注入测试用户信息
func TestUserMiddleware(testUserService *config.TestUserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只在开发环境启用
		if !isDevEnvironment() {
			c.Next()
			return
		}

		// 检查是否有测试用户ID头部
		userIDHeader := c.GetHeader("X-User-ID")
		if userIDHeader == "" {
			c.Next()
			return
		}

		// 解析用户ID
		userID, err := strconv.ParseInt(userIDHeader, 10, 64)
		if err != nil {
			c.Next()
			return
		}

		// 只有当用户ID为1时才启用测试功能
		if userID != 1 {
			c.Next()
			return
		}

		// 获取测试用户信息
		testUserInfo := testUserService.GetTestUserInfo(c.Request.Context(), userID)
		if testUserInfo == nil {
			c.Next()
			return
		}

		// 获取测试租户信息
		testTenantInfo := testUserService.GetTestTenantInfo(c.Request.Context(), testUserInfo.TenantID)

		// 注入用户和租户信息到上下文
		usercontext.InjectUserAndTenant(c, testUserInfo, testTenantInfo)

		// 设置额外的测试标识
		c.Set("is_test_user", true)
		c.Set("test_user_id", userID)

		// 在响应头中添加测试标识（便于调试）
		c.Header("X-Test-User", "true")
		c.Header("X-Test-User-ID", userIDHeader)

		c.Next()
	}
}

// EnhancedUserInfoMiddleware 增强的用户信息中间件
// 集成了测试用户功能的用户信息中间件
func EnhancedUserInfoMiddleware(
	getUserInfo func(ctx context.Context, token string) *usercontext.UserInfo,
	getTenantInfo func(ctx context.Context, tenantCode string) *usercontext.TenantInfo,
	testUserService *config.TestUserService,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 首先检查测试用户
		if isDevEnvironment() && testUserService != nil {
			userIDHeader := c.GetHeader("X-User-ID")
			if userIDHeader != "" {
				if userID, err := strconv.ParseInt(userIDHeader, 10, 64); err == nil && userID == 1 {
					testUserInfo := testUserService.GetTestUserInfo(c.Request.Context(), userID)
					if testUserInfo != nil {
						testTenantInfo := testUserService.GetTestTenantInfo(c.Request.Context(), testUserInfo.TenantID)
						usercontext.InjectUserAndTenant(c, testUserInfo, testTenantInfo)
						
						// 设置测试标识
						c.Set("is_test_user", true)
						c.Header("X-Test-User", "true")
						c.Header("X-Test-User-ID", userIDHeader)
						
						c.Next()
						return
					}
				}
			}
		}

		// 如果不是测试用户，执行正常的用户信息获取逻辑
		var userInfo *usercontext.UserInfo
		var tenantInfo *usercontext.TenantInfo

		// 获取用户信息（含租户信息）
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" && strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if token != "" {
				userInfo = getUserInfo(c.Request.Context(), token)
			}
		}

		if userInfo != nil {
			// 用户已登录，直接注入用户和租户信息
			if userInfo.TenantID != 0 || userInfo.TenantCode != "" {
				tenantInfo = &usercontext.TenantInfo{
					TenantID:   userInfo.TenantID,
					TenantCode: userInfo.TenantCode,
					TenantName: userInfo.TenantName,
				}
			}
			usercontext.InjectUserAndTenant(c, userInfo, tenantInfo)
			c.Next()
			return
		}

		// 未登录，尝试header获取tenantCode再查租户
		tenantCode := c.GetHeader("X-Tenant-Code")
		if tenantCode != "" {
			tenantInfo = getTenantInfo(c.Request.Context(), tenantCode)
			if tenantInfo != nil {
				usercontext.InjectUserAndTenant(c, nil, tenantInfo)
			}
		}
		c.Next()
	}
}

// isDevEnvironment 检查是否为开发环境
func isDevEnvironment() bool {
	env := os.Getenv("APP_ENV")
	return env == "dev" || env == "development" || env == "local"
}

// TestUserAuthMiddleware 测试用户认证中间件
// 为测试用户提供特殊的认证逻辑
func TestUserAuthMiddleware(testUserService *config.TestUserService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为测试用户
		if isTestUser, exists := c.Get("is_test_user"); exists && isTestUser.(bool) {
			// 测试用户自动通过认证
			c.Next()
			return
		}

		// 非测试用户，执行正常认证逻辑
		userInfo, exists := usercontext.GetUserInfo(c.Request.Context())
		if !exists || userInfo == nil {
			c.JSON(403, gin.H{
				"success": false,
				"code":    "UNAUTHORIZED",
				"message": "用户未认证",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}