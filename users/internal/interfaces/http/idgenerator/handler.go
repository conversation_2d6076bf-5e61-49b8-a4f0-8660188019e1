package idgenerator

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/usercontext"
	"platforms-user/internal/application/idgenerator/service"
	userErrors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/idgenerator/entity"
	"platforms-user/internal/domain/idgenerator/repository"
	"platforms-user/internal/interfaces/http/handlers"
	"platforms-user/pkg/config"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// Handler ID生成器HTTP处理器
type Handler struct {
	idGeneratorService *service.IDGeneratorService
	sequenceRepo       repository.SequenceRepository
	validator          *validator.Validate
}

// NewHandler 创建处理器
func NewHandler(idGeneratorService *service.IDGeneratorService, sequenceRepo repository.SequenceRepository) *Handler {
	return &Handler{
		idGeneratorService: idGeneratorService,
		sequenceRepo:       sequenceRepo,
		validator:          validator.New(),
	}
}

// ==================== 序列管理 ====================

// SequenceListRequest 序列列表请求
type SequenceListRequest struct {
	Page         int    `json:"page"`
	Size         int    `json:"size"`
	Keyword      string `json:"keyword"`
	Status       string `json:"status"`
	BusinessType string `json:"businessType"`
	SortBy       string `json:"sortBy"`
	SortOrder    string `json:"sortOrder"`
}

// SequenceResponse 序列响应
type SequenceResponse struct {
	ID            int64       `json:"id"`
	BusinessType  string      `json:"businessType"`
	SequenceName  string      `json:"sequenceName"`
	CurrentValue  int64       `json:"currentValue"`
	IncrementStep int         `json:"incrementStep"`
	CacheSize     int         `json:"cacheSize"`
	MaxValue      interface{} `json:"maxValue"` // 支持"不限制"字符串
	MinValue      int64       `json:"minValue"`
	IsActive      bool        `json:"isActive"`
	Threshold     int         `json:"threshold"`
	Remarks       string      `json:"remarks"`
	CreatedAt     time.Time   `json:"createdAt"`
	UpdatedAt     time.Time   `json:"updatedAt"`
}

// GetSequenceList 获取序列列表
func (h *Handler) GetSequenceList(c *gin.Context) {
	var req SequenceListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// 设置默认值
	if req.Page < 1 {
		req.Page = 1
	}
	if req.Size < 1 || req.Size > 100 {
		req.Size = 20
	}

	// 查询数据库获取序列列表
	sequences, total, err := h.getSequenceListFromDB(c.Request.Context(), req)
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}

	// 使用标准分页响应，符合项目规范
	commonResponse.Paginated(c, sequences, req.Page, req.Size, total)
}

// CreateSequenceRequest 创建序列请求
type CreateSequenceRequest struct {
	BusinessType  string `json:"businessType" binding:"required"`
	SequenceName  string `json:"sequenceName" binding:"required"`
	InitialValue  int64  `json:"initialValue"`
	IncrementStep int    `json:"incrementStep"`
	CacheSize     int    `json:"cacheSize"`
	MaxValue      int64  `json:"maxValue"`
	Threshold     int    `json:"threshold"`
	Remarks       string `json:"remarks"`
}

// CreateSequence 创建序列
func (h *Handler) CreateSequence(c *gin.Context) {
	var req CreateSequenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	if req.InitialValue == 0 {
		req.InitialValue = 1
	}
	if req.IncrementStep == 0 {
		req.IncrementStep = config.DefaultIncrementStep
	}
	if req.CacheSize == 0 {
		req.CacheSize = config.DefaultCacheSize
	}
	if req.Threshold == 0 {
		req.Threshold = config.DefaultThreshold
	}

	ctx := context.Background()
	sequence, err := h.createSequenceViaService(ctx, req)
	if err != nil {
		commonResponse.InternalError(c, fmt.Errorf("failed to create sequence"))
		return
	}

	commonResponse.Created(c, toSequenceResponse(sequence))
}

// UpdateSequenceRequest 更新序列请求
type UpdateSequenceRequest struct {
	SequenceName  string `json:"sequenceName"`
	IncrementStep int    `json:"incrementStep"`
	CacheSize     int    `json:"cacheSize"`
	MaxValue      int64  `json:"maxValue"`
	Threshold     int    `json:"threshold"`
	IsActive      bool   `json:"isActive"`
	Remarks       string `json:"remarks"`
}

// UpdateSequence 更新序列
func (h *Handler) UpdateSequence(c *gin.Context) {
	// 从请求体中获取ID和更新数据
	type UpdateSequenceWithID struct {
		ID            int64  `json:"id" binding:"required"`
		SequenceName  string `json:"sequenceName"`
		IncrementStep int    `json:"incrementStep"`
		CacheSize     int    `json:"cacheSize"`
		MaxValue      int64  `json:"maxValue"`
		Threshold     int    `json:"threshold"`
		IsActive      bool   `json:"isActive"`
		Remarks       string `json:"remarks"`
	}

	var req UpdateSequenceWithID
	if err := c.ShouldBindJSON(&req); err != nil {
		// 使用统一的错误处理函数，提供更友好的错误消息
		commonResponse.GinValidationError(c, err)
		return
	}

	ctx := context.Background()

	// 构建更新请求对象
	updateReq := service.UpdateSequenceRequest{}

	// 只设置提供的字段
	if req.SequenceName != "" {
		updateReq.SequenceName = &req.SequenceName
	}
	if req.IncrementStep > 0 {
		updateReq.IncrementStep = &req.IncrementStep
	}
	if req.CacheSize >= 0 { // 允许 0 值
		updateReq.CacheSize = &req.CacheSize
	}
	if req.MaxValue >= 0 { // 允许 0 表示不限制
		updateReq.MaxValue = &req.MaxValue
	}
	if req.Threshold > 0 {
		updateReq.Threshold = &req.Threshold
	}
	updateReq.IsActive = &req.IsActive
	if req.Remarks != "" {
		updateReq.Remarks = &req.Remarks
	}

	sequence, err := h.idGeneratorService.UpdateSequence(ctx, req.ID, updateReq)
	if err != nil {
		commonResponse.InternalError(c, fmt.Errorf("failed to update sequence"))
		return
	}

	commonResponse.Success(c, toSequenceResponse(sequence))
}

// DeleteSequenceRequest 删除序列请求
type DeleteSequenceRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// DeleteSequence 删除序列
func (h *Handler) DeleteSequence(c *gin.Context) {
	var req DeleteSequenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务删除序列
	// err := h.idGeneratorService.DeleteSequence(c.Request.Context(), req.ID)

	commonResponse.Deleted(c)
}

// PauseSequenceRequest 暂停序列请求
type PauseSequenceRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// PauseSequence 暂停序列
func (h *Handler) PauseSequence(c *gin.Context) {
	var req PauseSequenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务暂停序列
	// err := h.idGeneratorService.PauseSequence(c.Request.Context(), req.ID)

	commonResponse.Success(c, gin.H{"message": "序列已暂停"})
}

// ResumeSequenceRequest 恢复序列请求
type ResumeSequenceRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// ResumeSequence 恢复序列
func (h *Handler) ResumeSequence(c *gin.Context) {
	var req ResumeSequenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务恢复序列
	// err := h.idGeneratorService.ResumeSequence(c.Request.Context(), req.ID)

	commonResponse.Success(c, gin.H{"message": "序列已恢复"})
}

// AllocateSequenceRequest 手动预分配请求
type AllocateSequenceRequest struct {
	SequenceID int64  `json:"sequenceId" binding:"required"`
	Size       int    `json:"size"`
	Reason     string `json:"reason"`
}

// AllocateSequence 手动预分配ID段
func (h *Handler) AllocateSequence(c *gin.Context) {
	var req AllocateSequenceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	if req.Size < 1 {
		req.Size = 1000 // 默认分配大小
	}

	// TODO: 调用应用服务预分配
	// err := h.idGeneratorService.AllocateSequence(c.Request.Context(), req)

	commonResponse.Success(c, gin.H{"message": "预分配成功"})
}

// ==================== ID生成 ====================

// GenerateIDRequest 生成ID请求
type GenerateIDRequest struct {
	BusinessType string `json:"businessType" binding:"required"`
}

// GenerateIDResponse 生成ID响应
type GenerateIDResponse struct {
	ID           int64  `json:"id"`
	BusinessType string `json:"businessType"`
	GeneratedAt  string `json:"generatedAt"`
}

// GenerateID 生成单个ID
func (h *Handler) GenerateID(c *gin.Context) {
	var req GenerateIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// 从上下文获取租户ID
	tenantID := getTenantIDFromContext(c)
	if tenantID == 0 {
		commonResponse.Unauthorized(c, "tenant not specified")
		return
	}

	// 调用应用服务生成ID
	id, err := h.idGeneratorService.GenerateID(c.Request.Context(), req.BusinessType, tenantID)
	if err != nil {
		// 使用error_handler.go处理错误
		handlers.HandleUserError(c, err)
		return
	}

	resp := GenerateIDResponse{
		ID:           id,
		BusinessType: req.BusinessType,
		GeneratedAt:  time.Now().Format(time.RFC3339),
	}

	commonResponse.Success(c, resp)
}

// GenerateBatchIDRequest 批量生成ID请求
type GenerateBatchIDRequest struct {
	BusinessType string `json:"businessType" binding:"required"`
	Count        int    `json:"count" binding:"required,min=1,max=10000"`
}

// GenerateBatchIDResponse 批量生成ID响应
type GenerateBatchIDResponse struct {
	IDs          []int64 `json:"ids"`
	BusinessType string  `json:"businessType"`
	Count        int     `json:"count"`
	GeneratedAt  string  `json:"generatedAt"`
}

// GenerateBatchIDs 批量生成ID
func (h *Handler) GenerateBatchIDs(c *gin.Context) {
	var req GenerateBatchIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// 从上下文获取租户ID
	tenantID := getTenantIDFromContext(c)
	if tenantID == 0 {
		commonResponse.Unauthorized(c, "tenant not specified")
		return
	}

	// 调用应用服务批量生成ID
	ids, err := h.idGeneratorService.GenerateBatchIDs(c.Request.Context(), req.BusinessType, tenantID, req.Count)
	if err != nil {
		// 使用error_handler.go处理错误
		handlers.HandleUserError(c, err)
		return
	}

	resp := GenerateBatchIDResponse{
		IDs:          ids,
		BusinessType: req.BusinessType,
		Count:        len(ids),
		GeneratedAt:  time.Now().Format(time.RFC3339),
	}

	commonResponse.Success(c, resp)
}

// ==================== 业务类型管理 ====================

// GetBusinessTypesResponse 业务类型列表响应
// GetBusinessTypes 获取所有业务类型
func (h *Handler) GetBusinessTypes(c *gin.Context) {
	// 从数据库获取业务类型列表
	businessTypes, err := h.getBusinessTypesFromDB(c.Request.Context())
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}

	// 直接返回业务类型数组，符合项目规范
	commonResponse.Success(c, businessTypes)
}

// BusinessTypeApplicationRequest 业务类型申请请求
type BusinessTypeApplicationRequest struct {
	BusinessType string `json:"businessType" binding:"required"`
	Description  string `json:"description" binding:"required"`
	ExpectedQps  int    `json:"expectedQps"`
	Department   string `json:"department"`
	Contact      string `json:"contact"`
	Reason       string `json:"reason" binding:"required"`
}

// BusinessTypeApplicationResponse 业务类型申请响应
type BusinessTypeApplicationResponse struct {
	ID           int64  `json:"id"`
	BusinessType string `json:"businessType"`
	Description  string `json:"description"`
	ExpectedQps  int    `json:"expectedQps"`
	Department   string `json:"department"`
	Contact      string `json:"contact"`
	Reason       string `json:"reason"`
	Status       string `json:"status"`
	Applicant    string `json:"applicant"`
	Approver     string `json:"approver"`
	AppliedAt    string `json:"appliedAt"`
	ApprovedAt   string `json:"approvedAt"`
	RejectReason string `json:"rejectReason"`
}

// ApplyBusinessType 申请新的业务类型
func (h *Handler) ApplyBusinessType(c *gin.Context) {
	var req BusinessTypeApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务创建申请
	// application, err := h.idGeneratorService.ApplyBusinessType(c.Request.Context(), req)

	// 模拟响应
	resp := BusinessTypeApplicationResponse{
		ID:           time.Now().Unix(),
		BusinessType: req.BusinessType,
		Description:  req.Description,
		ExpectedQps:  req.ExpectedQps,
		Department:   req.Department,
		Contact:      req.Contact,
		Reason:       req.Reason,
		Status:       "pending",
		Applicant:    "current_user", // TODO: 从上下文获取当前用户
		AppliedAt:    time.Now().Format(time.RFC3339),
	}

	commonResponse.Created(c, resp)
}

// GetBusinessTypeApplications 获取业务类型申请记录
func (h *Handler) GetBusinessTypeApplications(c *gin.Context) {
	// TODO: 调用应用服务获取申请记录
	// applications, err := h.idGeneratorService.GetBusinessTypeApplications(c.Request.Context())

	// 模拟数据 - 直接返回申请列表数组
	applications := []BusinessTypeApplicationResponse{}

	commonResponse.Success(c, applications)
}

// ApproveBusinessTypeRequest 审批业务类型请求
type ApproveBusinessTypeRequest struct {
	ID       int64  `json:"id" binding:"required"`
	Approved bool   `json:"approved"`
	Reason   string `json:"reason"`
}

// ApproveBusinessType 审批业务类型申请
func (h *Handler) ApproveBusinessType(c *gin.Context) {
	var req ApproveBusinessTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务审批申请
	// err := h.idGeneratorService.ApproveBusinessType(c.Request.Context(), req)

	message := "申请已批准"
	if !req.Approved {
		message = "申请已拒绝"
	}

	commonResponse.Success(c, gin.H{"message": message})
}

// ==================== 系统监控 ====================

// SystemStatsResponse 系统统计响应
type SystemStatsResponse struct {
	TotalSequences       int    `json:"totalSequences"`
	ActiveSequences      int    `json:"activeSequences"`
	TodayGenerated       int    `json:"todayGenerated"`
	TotalGenerated       int    `json:"totalGenerated"`
	AverageQps           int    `json:"averageQps"`
	PeakQps              int    `json:"peakQps"`
	LowCapacitySequences int    `json:"lowCapacitySequences"`
	SystemUptime         string `json:"systemUptime"`
}

// GetSystemStats 获取系统统计信息
func (h *Handler) GetSystemStats(c *gin.Context) {
	// 从数据库获取系统统计信息
	stats, err := h.getSystemStatsFromDB(c.Request.Context())
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, stats)
}

// GetSequenceMetricsRequest 获取序列性能数据请求
type GetSequenceMetricsRequest struct {
	BusinessType string `json:"businessType" binding:"required"`
	Hours        int    `json:"hours"`
}

// GetSequenceMetrics 获取序列性能数据
func (h *Handler) GetSequenceMetrics(c *gin.Context) {
	var req GetSequenceMetricsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	if req.Hours < 1 {
		req.Hours = 24
	}

	// TODO: 调用应用服务获取性能数据
	// metrics, err := h.idGeneratorService.GetSequenceMetrics(c.Request.Context(), req)

	// 模拟数据
	resp := gin.H{
		"businessType": req.BusinessType,
		"metrics": []gin.H{
			{
				"timestamp": time.Now().Unix(),
				"qps":       50,
				"generated": 100,
			},
		},
	}

	commonResponse.Success(c, resp)
}

// GetHealthStatus 获取系统健康状态
func (h *Handler) GetHealthStatus(c *gin.Context) {
	// TODO: 调用应用服务获取健康状态
	// status, err := h.idGeneratorService.GetHealthStatus(c.Request.Context())

	// 模拟数据
	resp := gin.H{
		"status":  "healthy",
		"version": "1.0.0",
		"uptime":  "7天12小时",
		"checks": gin.H{
			"database": "ok",
			"cache":    "ok",
			"service":  "ok",
		},
	}

	commonResponse.Success(c, resp)
}

// ==================== 实用工具 ====================

// ValidateBusinessTypeRequest 验证业务类型请求
type ValidateBusinessTypeRequest struct {
	BusinessType string `json:"businessType" binding:"required"`
}

// ValidateBusinessTypeResponse 验证业务类型响应
type ValidateBusinessTypeResponse struct {
	Available bool   `json:"available"`
	Message   string `json:"message"`
}

// ValidateBusinessType 验证业务类型名称是否可用
func (h *Handler) ValidateBusinessType(c *gin.Context) {
	var req ValidateBusinessTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// 验证业务类型格式
	if !isValidBusinessType(req.BusinessType) {
		resp := ValidateBusinessTypeResponse{
			Available: false,
			Message:   "业务类型格式不正确，必须以字母开头，只能包含字母、数字和下划线，支持冒号分隔但不能在开头或结尾",
		}
		commonResponse.Success(c, resp)
		return
	}

	// TODO: 调用应用服务验证业务类型
	// result, err := h.idGeneratorService.ValidateBusinessType(c.Request.Context(), req.BusinessType)

	// 检查业务类型是否可用
	available := h.isBusinessTypeAvailable(c.Request.Context(), req.BusinessType)
	message := "业务类型可用"
	if !available {
		message = "业务类型已存在"
	}

	resp := ValidateBusinessTypeResponse{
		Available: available,
		Message:   message,
	}

	commonResponse.Success(c, resp)
}

// BatchOperateRequest 批量操作请求
type BatchOperateRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}

// BatchOperateResponse 批量操作响应
type BatchOperateResponse struct {
	Success []int64 `json:"success"`
	Failed  []struct {
		ID     int64  `json:"id"`
		Reason string `json:"reason"`
	} `json:"failed"`
}

// BatchPauseSequences 批量暂停序列
func (h *Handler) BatchPauseSequences(c *gin.Context) {
	var req BatchOperateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务批量暂停
	// result, err := h.idGeneratorService.BatchPauseSequences(c.Request.Context(), req.IDs)

	// 模拟响应
	resp := BatchOperateResponse{
		Success: req.IDs,
		Failed: []struct {
			ID     int64  `json:"id"`
			Reason string `json:"reason"`
		}{},
	}

	commonResponse.Success(c, resp)
}

// BatchResumeSequences 批量恢复序列
func (h *Handler) BatchResumeSequences(c *gin.Context) {
	var req BatchOperateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务批量恢复
	// result, err := h.idGeneratorService.BatchResumeSequences(c.Request.Context(), req.IDs)

	// 模拟响应
	resp := BatchOperateResponse{
		Success: req.IDs,
		Failed: []struct {
			ID     int64  `json:"id"`
			Reason string `json:"reason"`
		}{},
	}

	commonResponse.Success(c, resp)
}

// BatchDeleteSequences 批量删除序列
func (h *Handler) BatchDeleteSequences(c *gin.Context) {
	var req BatchOperateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.ValidationErrorResponse(c, []commonResponse.ValidationError{{Field: "request", Message: err.Error(), Value: ""}})
		return
	}

	// TODO: 调用应用服务批量删除
	// result, err := h.idGeneratorService.BatchDeleteSequences(c.Request.Context(), req.IDs)

	// 模拟响应
	resp := BatchOperateResponse{
		Success: req.IDs,
		Failed: []struct {
			ID     int64  `json:"id"`
			Reason string `json:"reason"`
		}{},
	}

	commonResponse.Success(c, resp)
}

// ==================== 辅助函数 ====================

// isValidBusinessType 验证业务类型格式
func isValidBusinessType(businessType string) bool {
	if businessType == "" {
		return false
	}

	// 支持 system:user 等格式的正则表达式
	// 必须以字母开头，只能包含字母、数字和下划线，支持冒号分隔但不能在开头或结尾
	pattern := `^[a-zA-Z][a-zA-Z0-9_]*(:[a-zA-Z][a-zA-Z0-9_]*)*$`
	matched, err := regexp.MatchString(pattern, businessType)
	if err != nil {
		return false
	}
	return matched
}

// isBusinessTypeAvailable 检查业务类型是否可用（唯一性检查）
func (h *Handler) isBusinessTypeAvailable(ctx context.Context, businessType string) bool {
	// 查询数据库检查业务类型是否已存在
	_, err := h.sequenceRepo.FindByBusinessAndName(ctx, businessType, "default", 0)
	if err == entity.ErrSequenceNotFound {
		return true // 不存在，可用
	}
	return false // 存在或查询错误，不可用
}

// createSequenceViaService 通过应用服务创建序列
func (h *Handler) createSequenceViaService(ctx context.Context, req CreateSequenceRequest) (*entity.Sequence, error) {
	// 参数验证
	if req.BusinessType == "" {
		return nil, userErrors.NewParameterValidationFailedError("business_type", "业务类型不能为空")
	}
	if req.IncrementStep <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("increment_step", "步长必须大于0")
	}
	if req.IncrementStep > 1000000 {
		return nil, userErrors.NewParameterValidationFailedError("increment_step", "步长不能超过1000000")
	}
	if req.InitialValue < 0 {
		return nil, userErrors.NewParameterValidationFailedError("initial_value", "初始值不能小于0")
	}
	if req.MaxValue > 0 && req.MaxValue <= req.InitialValue {
		return nil, userErrors.NewParameterValidationFailedError("max_value", "最大值必须大于初始值")
	}
	if req.Threshold <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("threshold", "阈值必须大于0")
	}
	if req.Threshold > 100 {
		return nil, userErrors.NewParameterValidationFailedError("threshold", "阈值不能超过100")
	}

	// 验证业务类型格式
	if !isValidBusinessType(req.BusinessType) {
		return nil, userErrors.NewParameterValidationFailedError("business_type", "业务类型格式无效")
	}

	// 从context中获取租户ID，默认为0（系统级）
	tenantID := int64(0)

	// 创建序列实体
	sequence := entity.NewSequence(req.BusinessType, req.SequenceName, tenantID, req.IncrementStep)

	// 设置序列属性
	sequence.CurrentValue = req.InitialValue
	if req.MaxValue > 0 {
		sequence.MaxValue = req.MaxValue
	}
	if req.Threshold > 0 {
		sequence.Threshold = req.Threshold
	}

	// 验证业务类型
	if err := sequence.ValidateBusinessType(); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := h.sequenceRepo.Create(ctx, sequence); err != nil {
		return nil, err
	}

	return sequence, nil
}

// getSequenceListFromDB 从数据库获取序列列表
func (h *Handler) getSequenceListFromDB(ctx context.Context, req SequenceListRequest) ([]SequenceResponse, int64, error) {
	// 查询活跃的序列
	sequences, err := h.sequenceRepo.FindActiveSequences(ctx)
	if err != nil {
		return nil, 0, err
	}

	// 过滤和搜索
	var filteredSequences []*entity.Sequence
	for _, seq := range sequences {
		// 业务类型过滤
		if req.BusinessType != "" && seq.BusinessType != req.BusinessType {
			continue
		}

		// 状态过滤
		if req.Status != "" {
			if req.Status == "true" && !seq.IsActive {
				continue
			}
			if req.Status == "false" && seq.IsActive {
				continue
			}
		}

		// 关键词搜索
		if req.Keyword != "" {
			if !h.matchKeyword(seq, req.Keyword) {
				continue
			}
		}

		filteredSequences = append(filteredSequences, seq)
	}

	total := int64(len(filteredSequences))

	// 分页
	start := (req.Page - 1) * req.Size
	end := start + req.Size
	if start >= len(filteredSequences) {
		return []SequenceResponse{}, total, nil
	}
	if end > len(filteredSequences) {
		end = len(filteredSequences)
	}

	pagedSequences := filteredSequences[start:end]

	// 转换为响应格式
	var responses []SequenceResponse
	for _, seq := range pagedSequences {
		responses = append(responses, toSequenceResponse(seq))
	}

	return responses, total, nil
}

// matchKeyword 检查序列是否匹配关键词
func (h *Handler) matchKeyword(seq *entity.Sequence, keyword string) bool {
	keyword = strings.ToLower(keyword)
	return strings.Contains(strings.ToLower(seq.BusinessType), keyword) ||
		strings.Contains(strings.ToLower(seq.SequenceName), keyword)
}

// getSequenceStatus 获取序列状态
func (h *Handler) getSequenceStatus(seq *entity.Sequence) string {
	if !seq.IsActive {
		return "disabled"
	}
	if seq.CurrentValue >= seq.MaxValue {
		return "exhausted"
	}
	return "active"
}

// getBusinessTypesFromDB 从数据库获取业务类型列表
func (h *Handler) getBusinessTypesFromDB(ctx context.Context) ([]string, error) {
	sequences, err := h.sequenceRepo.FindActiveSequences(ctx)
	if err != nil {
		return nil, err
	}

	// 去重业务类型
	typeSet := make(map[string]bool)
	for _, seq := range sequences {
		typeSet[seq.BusinessType] = true
	}

	var businessTypes []string
	for businessType := range typeSet {
		businessTypes = append(businessTypes, businessType)
	}

	return businessTypes, nil
}

// getSystemStatsFromDB 从数据库获取系统统计信息
func (h *Handler) getSystemStatsFromDB(ctx context.Context) (SystemStatsResponse, error) {
	// 查询所有序列
	sequences, err := h.sequenceRepo.FindActiveSequences(ctx)
	if err != nil {
		return SystemStatsResponse{}, err
	}

	totalSequences := len(sequences)
	activeSequences := 0
	totalGenerated := int64(0)
	lowCapacitySequences := 0

	for _, seq := range sequences {
		if seq.IsActive {
			activeSequences++
		}
		totalGenerated += seq.CurrentValue

		// 检查是否为低容量序列（接近最大值）
		if seq.CurrentValue >= seq.MaxValue*8/10 { // 超过80%认为是低容量
			lowCapacitySequences++
		}
	}

	stats := SystemStatsResponse{
		TotalSequences:       totalSequences,
		ActiveSequences:      activeSequences,
		TodayGenerated:       0, // TODO: 实现今日生成数量的统计
		TotalGenerated:       int(totalGenerated),
		AverageQps:           0, // TODO: 实现平均QPS统计
		PeakQps:              0, // TODO: 实现峰值QPS统计
		LowCapacitySequences: lowCapacitySequences,
		SystemUptime:         "运行中", // TODO: 实现系统运行时间统计
	}

	return stats, nil
}

func toSequenceResponse(sequence *entity.Sequence) SequenceResponse {
	return SequenceResponse{
		ID:            sequence.ID,
		BusinessType:  sequence.BusinessType,
		SequenceName:  sequence.SequenceName,
		CurrentValue:  sequence.CurrentValue,
		IncrementStep: sequence.IncrementStep,
		CacheSize:     sequence.CacheSize,
		MaxValue:      sequence.GetDisplayMaxValue(),
		MinValue:      sequence.MinValue,
		IsActive:      sequence.IsActive,
		Threshold:     sequence.Threshold,
		Remarks:       sequence.Remarks,
		CreatedAt:     sequence.CreatedAt,
		UpdatedAt:     sequence.UpdatedAt,
	}
}

// getTenantIDFromContext 从上下文中获取租户ID
func getTenantIDFromContext(c *gin.Context) int64 {
	if tenantID, exists := usercontext.GetTenantID(c.Request.Context()); exists {
		return tenantID
	}
	return 0
}
