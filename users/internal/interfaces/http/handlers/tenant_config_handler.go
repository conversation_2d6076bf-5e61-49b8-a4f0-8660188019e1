package handlers

import (
	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/user/dto"
	"platforms-user/internal/application/user/service"
	"platforms-user/pkg/validator"

	"github.com/gin-gonic/gin"
)

// TenantConfigHandler 租户配置处理器
type TenantConfigHandler struct {
	logger              logiface.Logger
	tenantConfigService *service.TenantConfigService
}

// NewTenantConfigHandler 创建租户配置处理器
func NewTenantConfigHandler(logger logiface.Logger, tenantConfigService *service.TenantConfigService) *TenantConfigHandler {
	return &TenantConfigHandler{
		logger:              logger,
		tenantConfigService: tenantConfigService,
	}
}

// GetPasswordPolicy 获取密码策略
func (h *TenantConfigHandler) GetPasswordPolicy(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	req := &dto.GetPasswordPolicyRequest{
		TenantID: tenantID,
	}

	policy, err := h.tenantConfigService.GetPasswordPolicy(c.Request.Context(), req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get password policy", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, policy)
}

// UpdatePasswordPolicy 更新密码策略
func (h *TenantConfigHandler) UpdatePasswordPolicy(c *gin.Context) {
	var req dto.UpdatePasswordPolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	if req.Policy == nil {
		commonResponse.BadRequest(c, "密码策略不能为空")
		return
	}

	if err := h.tenantConfigService.UpdatePasswordPolicy(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update password policy", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "密码策略更新成功"})
}

// GetRegistrationMethods 获取注册方式配置
func (h *TenantConfigHandler) GetRegistrationMethods(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	req := &dto.GetRegistrationMethodsRequest{
		TenantID: tenantID,
	}

	methods, err := h.tenantConfigService.GetRegistrationMethods(c.Request.Context(), req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get registration methods", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, methods)
}

// UpdateRegistrationMethods 更新注册方式配置
func (h *TenantConfigHandler) UpdateRegistrationMethods(c *gin.Context) {
	var req dto.UpdateRegistrationMethodsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	if req.Methods == nil {
		commonResponse.BadRequest(c, "注册方式配置不能为空")
		return
	}

	if err := h.tenantConfigService.UpdateRegistrationMethods(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update registration methods", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "注册方式配置更新成功"})
}

// GetTenantConfigs 获取租户所有配置
func (h *TenantConfigHandler) GetTenantConfigs(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	configs, err := h.tenantConfigService.GetTenantConfigs(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get tenant configs", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, configs)
}

// UpdateTenantConfig 更新租户配置
func (h *TenantConfigHandler) UpdateTenantConfig(c *gin.Context) {
	var req dto.UpdateTenantConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	v := validator.NewValidator()
	v.Required(req.ConfigKey, "config_key")
	v.Required(req.ConfigValue, "config_value")

	if err := v.Validate(); err != nil {
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	if err := h.tenantConfigService.UpdateTenantConfig(c.Request.Context(), &req); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to update tenant config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "租户配置更新成功"})
}

// CopySystemConfigsToTenant 复制系统配置到租户
func (h *TenantConfigHandler) CopySystemConfigsToTenant(c *gin.Context) {
	tenantID, err := parseIDFromQuery(c, "tenant_id")
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	if err := h.tenantConfigService.CopySystemConfigsToTenant(c.Request.Context(), tenantID); err != nil {
		h.logger.Error(c.Request.Context(), "Failed to copy system configs to tenant", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "系统配置复制成功"})
}
