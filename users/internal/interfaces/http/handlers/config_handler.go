package handlers

import (
	"fmt"
	"net/http"
	commonResponse "platforms-pkg/common/response"
	"platforms-user/internal/application/verification/dto"
	"platforms-user/internal/application/verification/service"
	"strconv"

	"platforms-pkg/logiface"

	"github.com/gin-gonic/gin"
)

// ConfigHandler 配置管理处理器
type ConfigHandler struct {
	configService *service.ConfigApplicationService
	logger        logiface.Logger
}

// NewConfigHandler 创建配置管理处理器
func NewConfigHandler(
	configService *service.ConfigApplicationService,
	logger logiface.Logger,
) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
		logger:        logger,
	}
}

// CreateConfig 创建验证配置
// @Summary 创建验证配置
// @Description 创建新的验证配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CreateConfigRequest true "创建配置请求"
// @Success 201 {object} commonResponse.Response{data=dto.ConfigResponse} "创建成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 409 {object} commonResponse.Response "配置已存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs [post]
func (h *ConfigHandler) CreateConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.CreateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	result, err := h.configService.CreateConfig(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", result.ID))
	commonResponse.Created(c, result)
}

// ListConfigs 获取配置列表
// @Summary 获取配置列表
// @Description 获取验证配置列表
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param purpose query int false "验证用途"
// @Param target_type query int false "目标类型"
// @Param token_type query int false "令牌类型"
// @Param is_active query bool false "是否激活"
// @Param page query int false "页码" default(1)
// @Param size query int false "页大小" default(20)
// @Param order_by query string false "排序字段" default("created_at")
// @Param order_desc query bool false "是否降序" default(true)
// @Success 200 {object} commonResponse.Response{data=dto.ConfigListResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs [get]
func (h *ConfigHandler) ListConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析查询参数
	var req dto.ConfigListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid query parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	configs, total, err := h.configService.ListConfigs(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	commonResponse.Paginated(c, configs, req.Page, req.Size, total)
}

// GetConfig 获取配置详情
// @Summary 获取配置详情
// @Description 根据ID获取验证配置详情
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/{id} [get]
func (h *ConfigHandler) GetConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取配置ID
	configID, err := h.getConfigID(c)
	if err != nil {
		commonResponse.BadRequest(c, "配置ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	result, err := h.configService.GetConfig(c.Request.Context(), tenantID, configID)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// UpdateConfig 更新配置
// @Summary 更新配置
// @Description 更新验证配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Param request body dto.UpdateConfigRequest true "更新配置请求"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "更新成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/{id} [put]
func (h *ConfigHandler) UpdateConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取配置ID
	configID, err := h.getConfigID(c)
	if err != nil {
		commonResponse.BadRequest(c, "配置ID无效: "+err.Error())
		return
	}

	// 解析请求参数
	var req dto.UpdateConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置配置ID
	req.ID = configID

	// 调用应用服务
	result, err := h.configService.UpdateConfig(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Config updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	commonResponse.Success(c, result)
}

// DeleteConfig 删除配置
// @Summary 删除配置
// @Description 删除验证配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 204 "删除成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/{id} [delete]
func (h *ConfigHandler) DeleteConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取配置ID
	configID, err := h.getConfigID(c)
	if err != nil {
		commonResponse.BadRequest(c, "配置ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.configService.DeleteConfig(c.Request.Context(), tenantID, configID)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Config deleted successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	c.Status(http.StatusNoContent)
}

// EnableConfig 启用配置
// @Summary 启用配置
// @Description 启用验证配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 200 {object} commonResponse.Response "启用成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/{id}/enable [put]
func (h *ConfigHandler) EnableConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取配置ID
	configID, err := h.getConfigID(c)
	if err != nil {
		commonResponse.BadRequest(c, "配置ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.configService.EnableConfig(c.Request.Context(), tenantID, configID)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Config enabled successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	commonResponse.Success(c, gin.H{"message": "Config enabled successfully"})
}

// DisableConfig 禁用配置
// @Summary 禁用配置
// @Description 禁用验证配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 200 {object} commonResponse.Response "禁用成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/{id}/disable [put]
func (h *ConfigHandler) DisableConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取配置ID
	configID, err := h.getConfigID(c)
	if err != nil {
		commonResponse.BadRequest(c, "配置ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.configService.DisableConfig(c.Request.Context(), tenantID, configID)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Config disabled successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	commonResponse.Success(c, gin.H{"message": "Config disabled successfully"})
}

// BatchUpdateConfigs 批量更新配置
// @Summary 批量更新配置
// @Description 批量启用、禁用或删除配置
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.BatchConfigRequest true "批量操作请求"
// @Success 200 {object} commonResponse.Response{data=dto.BatchConfigResponse} "操作成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/batch [post]
func (h *ConfigHandler) BatchUpdateConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.BatchConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	result, err := h.configService.BatchUpdateConfigs(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Batch config operation completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("operation", req.Operation),
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("failure_count", result.FailureCount))

	commonResponse.Success(c, result)
}

// CopySystemConfigs 复制系统配置
// @Summary 复制系统配置
// @Description 复制系统默认配置到当前租户
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Success 200 {object} commonResponse.Response "复制成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/copy-system [post]
func (h *ConfigHandler) CopySystemConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 调用应用服务
	err := h.configService.CopySystemConfigs(c.Request.Context(), tenantID)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "System configs copied successfully",
		logiface.Int64("tenant_id", tenantID))

	commonResponse.Success(c, gin.H{"message": "System configs copied successfully"})
}

// GetConfigStatistics 获取配置统计
// @Summary 获取配置统计
// @Description 获取验证配置的统计信息
// @Tags config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigStatisticsResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/configs/statistics [get]
func (h *ConfigHandler) GetConfigStatistics(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 调用应用服务
	// result, err := h.configService.GetConfigStatistics(c.Request.Context(), tenantID)
	// if err != nil {
	//     HandleUserError(c, err)
	//     return
	// }

	// 临时返回空统计
	result := &dto.ConfigStatisticsResponse{
		TotalConfigs:    0,
		ActiveConfigs:   0,
		InactiveConfigs: 0,
	}

	h.logger.Info(c.Request.Context(), "Config statistics retrieved",
		logiface.Int64("tenant_id", tenantID))

	commonResponse.Success(c, result)
}

// getConfigID 获取配置ID
func (h *ConfigHandler) getConfigID(c *gin.Context) (int64, error) {
	configIDStr := c.Param("id")
	if configIDStr == "" {
		return 0, fmt.Errorf("config ID is required")
	}

	configID, err := strconv.ParseInt(configIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid config ID format")
	}

	if configID <= 0 {
		return 0, fmt.Errorf("config ID must be positive")
	}

	return configID, nil
}
