package handlers

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/verification/dto"
	"platforms-user/internal/application/verification/service"
)

// VerificationHandler 验证处理器
type VerificationHandler struct {
	verificationService *service.VerificationApplicationService
	logger              logiface.Logger
}

// NewVerificationHandler 创建验证处理器
func NewVerificationHandler(
	verificationService *service.VerificationApplicationService,
	logger logiface.Logger,
) *VerificationHandler {
	return &VerificationHandler{
		verificationService: verificationService,
		logger:              logger,
	}
}

// SendVerification 发送验证
// @Summary 发送验证
// @Description 发送验证码或验证链接到指定目标
// @Tags verification
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.SendVerificationRequest true "发送验证请求"
// @Success 200 {object} commonResponse.Response{data=dto.SendVerificationResponse} "发送成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 429 {object} commonResponse.Response "频率限制"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/send [post]
func (h *VerificationHandler) SendVerification(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.SendVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Send verification request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.String("user_agent", c.GetHeader("User-Agent")))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置IP地址和User-Agent
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")

	// 记录操作尝试
	h.logger.Info(c.Request.Context(), "Send verification attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.String("ip", req.IPAddress),
		logiface.String("user_agent", req.UserAgent))

	// 调用应用服务
	result, err := h.verificationService.SendVerification(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Send verification failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int("target_type", int(req.TargetType)),
			logiface.Int("purpose", int(req.Purpose)),
			logiface.String("ip", req.IPAddress))
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Verification sent successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.String("ip", req.IPAddress))

	commonResponse.Success(c, result)
}

// VerifyToken 验证令牌
// @Summary 验证令牌
// @Description 验证验证码或验证链接
// @Tags verification
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.VerifyTokenRequest true "验证令牌请求"
// @Success 200 {object} commonResponse.Response{data=dto.VerifyTokenResponse} "验证成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "验证失败"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/verify [post]
func (h *VerificationHandler) VerifyToken(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.VerifyTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Verify token request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.String("user_agent", c.GetHeader("User-Agent")))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置IP地址和User-Agent
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")

	// 记录操作尝试
	h.logger.Info(c.Request.Context(), "Token verification attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", req.Target), // 暂时不脱敏，因为不知道类型
		logiface.String("token_masked", dto.MaskToken(req.Token)),
		logiface.String("ip", req.IPAddress),
		logiface.String("user_agent", req.UserAgent))

	// 调用应用服务
	result, err := h.verificationService.VerifyToken(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Token verification failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", req.Target), // 暂时不脱敏，因为不知道类型
			logiface.String("token_masked", dto.MaskToken(req.Token)),
			logiface.String("ip", req.IPAddress))
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Token verified successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", dto.MaskTarget(req.Target, result.TargetType)),
		logiface.Int("target_type", int(result.TargetType)),
		logiface.String("token_masked", dto.MaskToken(req.Token)),
		logiface.String("ip", req.IPAddress))

	commonResponse.Success(c, result)
}

// ResendVerification 重新发送验证
// @Summary 重新发送验证
// @Description 重新发送验证码或验证链接
// @Tags verification
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.ResendVerificationRequest true "重新发送验证请求"
// @Success 200 {object} commonResponse.Response{data=dto.SendVerificationResponse} "发送成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 429 {object} commonResponse.Response "频率限制"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/resend [post]
func (h *VerificationHandler) ResendVerification(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.ResendVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Resend verification request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.String("user_agent", c.GetHeader("User-Agent")))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置IP地址和User-Agent
	req.IPAddress = c.ClientIP()
	req.UserAgent = c.GetHeader("User-Agent")

	// 记录操作尝试
	h.logger.Info(c.Request.Context(), "Resend verification attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.String("ip", req.IPAddress),
		logiface.String("user_agent", req.UserAgent))

	// 调用应用服务
	result, err := h.verificationService.ResendVerification(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Resend verification failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int("target_type", int(req.TargetType)),
			logiface.String("ip", req.IPAddress))
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Verification resent successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.String("ip", req.IPAddress))

	commonResponse.Success(c, result)
}

// CheckTokenStatus 检查令牌状态
// @Summary 检查令牌状态
// @Description 检查验证令牌的状态信息，使用POST方法和JSON请求体
// @Tags verification
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CheckTokenStatusRequest true "检查令牌状态请求"
// @Success 200 {object} commonResponse.Response{data=dto.CheckTokenStatusResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "令牌不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/admin/verification/status [post]
func (h *VerificationHandler) CheckTokenStatus(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析JSON请求体
	var req dto.CheckTokenStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Check token status request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	if req.Token == "" {
		commonResponse.BadRequest(c, "token参数不能为空")
		return
	}

	h.logger.Info(c.Request.Context(), "Check token status attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("token_masked", dto.MaskToken(req.Token)),
		logiface.String("ip", c.ClientIP()))

	// 调用应用服务
	result, err := h.verificationService.CheckTokenStatus(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// GetStatistics 获取验证统计
// @Summary 获取验证统计
// @Description 获取验证系统的统计信息，使用POST方法和JSON请求体
// @Tags verification
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.VerificationStatisticsRequest true "统计请求参数"
// @Success 200 {object} commonResponse.Response{data=dto.VerificationStatisticsResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/admin/verification/statistics [post]
func (h *VerificationHandler) GetStatistics(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析JSON请求体
	var req dto.VerificationStatisticsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Get statistics request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.String("user_agent", c.GetHeader("User-Agent")))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 记录操作尝试
	var startTimeStr, endTimeStr string
	var purposeInt, targetTypeInt int

	if req.StartTime != nil {
		startTimeStr = req.StartTime.Format(time.RFC3339)
	}
	if req.EndTime != nil {
		endTimeStr = req.EndTime.Format(time.RFC3339)
	}
	if req.Purpose != nil {
		purposeInt = int(*req.Purpose)
	}
	if req.TargetType != nil {
		targetTypeInt = int(*req.TargetType)
	}

	h.logger.Info(c.Request.Context(), "Get verification statistics attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("start_time", startTimeStr),
		logiface.String("end_time", endTimeStr),
		logiface.Int("purpose", purposeInt),
		logiface.Int("target_type", targetTypeInt),
		logiface.String("ip", c.ClientIP()),
		logiface.String("user_agent", c.GetHeader("User-Agent")))

	// 调用应用服务（这里需要实现统计功能）
	// result, err := h.verificationService.GetStatistics(c.Request.Context(), tenantID, req)
	// if err != nil {
	//     h.logger.Error(c.Request.Context(), "Get verification statistics failed",
	//         logiface.Error(err),
	//         logiface.Int64("tenant_id", tenantID),
	//         logiface.String("ip", c.ClientIP()))
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回空统计
	result := &dto.VerificationStatisticsResponse{
		TotalSent:     0,
		TotalVerified: 0,
		SuccessRate:   0,
		FailedCount:   0,
		ExpiredCount:  0,
	}

	h.logger.Info(c.Request.Context(), "Get verification statistics successful",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("total_sent", result.TotalSent),
		logiface.Int64("total_verified", result.TotalVerified),
		logiface.Float64("success_rate", result.SuccessRate),
		logiface.String("ip", c.ClientIP()))

	commonResponse.Success(c, result)
}

// CreateStaticConfig 创建静态验证配置
// @Summary 创建静态验证配置
// @Description 创建静态验证配置，基于用途和目标类型
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CreateStaticConfigRequest true "创建静态配置请求"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "创建成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 409 {object} commonResponse.Response "配置已存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/static [post]
func (h *VerificationHandler) CreateStaticConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析请求参数
	var req dto.CreateStaticConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Create static config request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "Create static config request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Create static config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.String("template_code", req.TemplateCode),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务创建静态配置
	// result, err := h.verificationService.CreateStaticConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回成功（待实现）
	result := &dto.ConfigResponse{
		ID:                 1,
		TenantID:           tenantID,
		ConfigMode:         "static",
		Purpose:            &req.Purpose,
		TargetType:         req.TargetType,
		TokenType:          req.TokenType,
		TokenLength:        req.TokenLength,
		ExpireMinutes:      req.ExpireMinutes,
		MaxAttempts:        req.MaxAttempts,
		RateLimitPerMinute: req.RateLimitPerMinute,
		RateLimitPerHour:   req.RateLimitPerHour,
		RateLimitPerDay:    req.RateLimitPerDay,
		TemplateCode:       req.TemplateCode,
		Priority:           req.Priority,
		IsActive:           true,
	}

	h.logger.Info(c.Request.Context(), "Static config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", result.ID))

	commonResponse.Success(c, result)
}

// CreateDynamicConfig 创建动态验证配置
// @Summary 创建动态验证配置
// @Description 创建动态验证配置，基于业务场景和条件表达式
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CreateDynamicConfigRequest true "创建动态配置请求"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "创建成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 409 {object} commonResponse.Response "配置已存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/dynamic [post]
func (h *VerificationHandler) CreateDynamicConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析请求参数
	var req dto.CreateDynamicConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Create dynamic config request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "Create dynamic config request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Create dynamic config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("business_scene", req.BusinessScene),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.String("judgment_dimension", req.JudgmentDimension),
		logiface.String("template_code", req.TemplateCode),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务创建动态配置
	// result, err := h.verificationService.CreateDynamicConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回成功（待实现）
	result := &dto.ConfigResponse{
		ID:                  2,
		TenantID:            tenantID,
		ConfigMode:          "dynamic",
		BusinessScene:       &req.BusinessScene,
		JudgmentDimension:   &req.JudgmentDimension,
		ConditionExpr:       &req.ConditionExpr,
		TargetType:          req.TargetType,
		TokenType:           req.TokenType,
		TokenLength:         req.TokenLength,
		ExpireMinutes:       req.ExpireMinutes,
		MaxAttempts:         req.MaxAttempts,
		RateLimitPerMinute:  req.RateLimitPerMinute,
		RateLimitPerHour:    req.RateLimitPerHour,
		RateLimitPerDay:     req.RateLimitPerDay,
		TemplateCode:        req.TemplateCode,
		Priority:            req.Priority,
		VerificationLevel:   req.VerificationLevel,
		RequireVerification: req.RequireVerification,
		IsActive:            true,
	}

	h.logger.Info(c.Request.Context(), "Dynamic config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", result.ID))

	commonResponse.Success(c, result)
}

// UpdateStaticConfig 更新静态验证配置
// @Summary 更新静态验证配置
// @Description 更新静态验证配置
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Param request body dto.UpdateStaticConfigRequest true "更新静态配置请求"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "更新成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/static/{id} [put]
func (h *VerificationHandler) UpdateStaticConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析请求参数
	var req dto.UpdateStaticConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Update static config request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "Update static config request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Update static config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", req.ID),
		logiface.String("template_code", req.TemplateCode),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务更新静态配置
	// result, err := h.verificationService.UpdateStaticConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回成功（待实现）
	result := &dto.ConfigResponse{
		ID:       req.ID,
		TenantID: tenantID,
		IsActive: req.IsActive,
	}

	h.logger.Info(c.Request.Context(), "Static config updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", req.ID))

	commonResponse.Success(c, result)
}

// UpdateDynamicConfig 更新动态验证配置
// @Summary 更新动态验证配置
// @Description 更新动态验证配置
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Param request body dto.UpdateDynamicConfigRequest true "更新动态配置请求"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "更新成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/dynamic/{id} [put]
func (h *VerificationHandler) UpdateDynamicConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析请求参数
	var req dto.UpdateDynamicConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Update dynamic config request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "Update dynamic config request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Update dynamic config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", req.ID),
		logiface.String("judgment_dimension", req.JudgmentDimension),
		logiface.String("template_code", req.TemplateCode),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务更新动态配置
	// result, err := h.verificationService.UpdateDynamicConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回成功（待实现）
	result := &dto.ConfigResponse{
		ID:       req.ID,
		TenantID: tenantID,
		IsActive: req.IsActive,
	}

	h.logger.Info(c.Request.Context(), "Dynamic config updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", req.ID))

	commonResponse.Success(c, result)
}

// DeleteStaticConfig 删除静态验证配置
// @Summary 删除静态验证配置
// @Description 软删除静态验证配置
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 200 {object} commonResponse.Response "删除成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/static/{id} [delete]
func (h *VerificationHandler) DeleteStaticConfig(c *gin.Context) {
	h.deleteConfig(c, "static")
}

// DeleteDynamicConfig 删除动态验证配置
// @Summary 删除动态验证配置
// @Description 软删除动态验证配置
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "配置ID"
// @Success 200 {object} commonResponse.Response "删除成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/dynamic/{id} [delete]
func (h *VerificationHandler) DeleteDynamicConfig(c *gin.Context) {
	h.deleteConfig(c, "dynamic")
}

// deleteConfig 通用删除配置方法
func (h *VerificationHandler) deleteConfig(c *gin.Context, configType string) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 获取配置ID
	idStr := c.Param("id")
	if idStr == "" {
		commonResponse.BadRequest(c, "配置ID不能为空")
		return
	}

	var req dto.DeleteConfigRequest
	// 这里简化处理，实际应该解析路径参数
	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Delete "+configType+" config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("config_id", idStr),
		logiface.String("config_type", configType),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务删除配置
	// err := h.verificationService.DeleteConfig(c.Request.Context(), tenantID, configID)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	h.logger.Info(c.Request.Context(), "Config deleted successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("config_id", idStr),
		logiface.String("config_type", configType))

	commonResponse.Success(c, gin.H{"message": configType + " config deleted successfully"})
}

// GetEffectiveConfig 获取有效验证配置
// @Summary 获取有效验证配置
// @Description 根据业务场景和规则获取最适用的验证配置
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param purpose query int true "验证用途"
// @Param target_type query int true "目标类型"
// @Param business_scene query string false "业务场景"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 404 {object} commonResponse.Response "配置不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/users/verification/configs/effective [get]
func (h *VerificationHandler) GetEffectiveConfig(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析查询参数
	var req dto.GetEffectiveConfigRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Get effective config query parameters binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Get effective config attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.Int("target_type", int(req.TargetType)),
		logiface.String("business_scene", func() string {
			if req.BusinessScene != nil {
				return *req.BusinessScene
			}
			return ""
		}()),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务获取有效配置
	// result, err := h.verificationService.GetEffectiveConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回模拟数据
	result := &dto.ConfigResponse{
		ID:                 1,
		TenantID:           tenantID,
		ConfigMode:         "static",
		Purpose:            &req.Purpose,
		TargetType:         req.TargetType,
		TokenType:          1,
		TokenLength:        6,
		ExpireMinutes:      5,
		MaxAttempts:        3,
		RateLimitPerMinute: 1,
		RateLimitPerHour:   10,
		RateLimitPerDay:    100,
		TemplateCode:       "default_template",
		Priority:           10,
		IsActive:           true,
	}

	h.logger.Info(c.Request.Context(), "Effective config found",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", result.ID),
		logiface.String("config_mode", string(result.ConfigMode)))

	commonResponse.Success(c, result)
}

// ListConfigs 查询验证配置列表 (GET /api/user/admin/verification/configs)
// @Summary 查询验证配置列表
// @Description 分页查询验证配置列表，支持多种过滤条件，使用GET方法和query参数
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param config_mode query string false "配置模式: static, dynamic"
// @Param purpose query int false "验证用途"
// @Param business_scene query string false "业务场景"
// @Param target_type query int false "目标类型"
// @Param token_type query int false "令牌类型"
// @Param is_active query bool false "是否激活"
// @Param keyword query string false "搜索关键词"
// @Param page query int false "页码，默认1"
// @Param size query int false "页大小，默认20"
// @Param order_by query string false "排序字段，默认created_at"
// @Param order_desc query bool false "是否降序，默认true"
// @Success 200 {object} commonResponse.Response{data=[]dto.ConfigResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/admin/verification/configs [get]
func (h *VerificationHandler) ListConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析查询参数
	var req dto.ConfigListRequestBody
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "List configs request parsing failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 转换为应用层请求
	appReq := req.ToConfigListRequest(tenantID)

	h.logger.Info(c.Request.Context(), "List configs attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("keyword", req.Keyword),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size),
		logiface.String("ip", c.ClientIP()))

	// 调用应用服务
	configs, total, err := h.verificationService.ListConfigs(c.Request.Context(), tenantID, appReq)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to list configs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Configs listed successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("total", total),
		logiface.Int("page", req.Page))

	commonResponse.Paginated(c, configs, req.Page, req.Size, total)
}

// ManageConfigs 统一配置管理 (POST /api/user/admin/verification/configs)
// @Summary 统一配置管理
// @Description 统一处理验证配置的创建、更新、删除操作，通过operation字段区分操作类型
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body object true "配置管理请求，包含operation字段指定操作类型"
// @Success 200 {object} commonResponse.Response{data=dto.ConfigResponse} "操作成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/admin/verification/configs [post]
func (h *VerificationHandler) ManageConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 读取原始请求体
	var rawReq map[string]interface{}
	if err := c.ShouldBindJSON(&rawReq); err != nil {
		h.logger.Warn(c.Request.Context(), "Manage configs request parsing failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 获取操作类型
	operation, ok := rawReq["operation"].(string)
	if !ok || operation == "" {
		h.logger.Warn(c.Request.Context(), "Missing or invalid operation field",
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.BadRequest(c, "operation字段为必填项")
		return
	}

	h.logger.Info(c.Request.Context(), "Manage configs attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("operation", operation),
		logiface.String("ip", c.ClientIP()))

	// 将原始请求体存储到上下文中，供子函数使用
	c.Set("raw_request", rawReq)

	// 根据操作类型分发处理
	switch operation {
	case "create_static":
		h.handleCreateStaticConfig(c, tenantID)
	case "create_dynamic":
		h.handleCreateDynamicConfig(c, tenantID)
	case "update_static":
		h.handleUpdateStaticConfig(c, tenantID)
	case "update_dynamic":
		h.handleUpdateDynamicConfig(c, tenantID)
	case "delete":
		h.handleDeleteConfig(c, tenantID)
	default:
		h.logger.Warn(c.Request.Context(), "Invalid operation type",
			logiface.String("operation", operation),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, "无效的操作类型: "+operation)
	}
}

// handleCreateStaticConfig 处理创建静态配置
func (h *VerificationHandler) handleCreateStaticConfig(c *gin.Context, tenantID int64) {
	// 从上下文中获取原始请求体
	rawReqInterface, exists := c.Get("raw_request")
	if !exists {
		commonResponse.BadRequest(c, "请求数据丢失")
		return
	}

	rawReq, ok := rawReqInterface.(map[string]interface{})
	if !ok {
		commonResponse.BadRequest(c, "请求数据格式错误")
		return
	}

	// 移除不需要的字段，避免与CreateStaticConfigRequest冲突
	delete(rawReq, "operation")
	delete(rawReq, "is_active")
	delete(rawReq, "config_mode")

	// 将过滤后的数据重新序列化为JSON
	filteredJSON, err := json.Marshal(rawReq)
	if err != nil {
		commonResponse.BadRequest(c, "参数序列化失败: "+err.Error())
		return
	}

	// 绑定到DTO
	var req dto.CreateStaticConfigRequest
	if err := json.Unmarshal(filteredJSON, &req); err != nil {
		commonResponse.BadRequest(c, "参数解析失败: "+err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}
	// 调用应用服务创建静态配置
	result, err := h.verificationService.CreateStaticConfig(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// handleCreateDynamicConfig 处理创建动态配置
func (h *VerificationHandler) handleCreateDynamicConfig(c *gin.Context, tenantID int64) {
	// 从上下文中获取原始请求体
	rawReqInterface, exists := c.Get("raw_request")
	if !exists {
		commonResponse.BadRequest(c, "请求数据丢失")
		return
	}

	rawReq, ok := rawReqInterface.(map[string]interface{})
	if !ok {
		commonResponse.BadRequest(c, "请求数据格式错误")
		return
	}

	// 移除不需要的字段，避免与CreateDynamicConfigRequest冲突
	delete(rawReq, "operation")
	delete(rawReq, "is_active")
	delete(rawReq, "config_mode")

	// 将过滤后的数据重新序列化为JSON
	filteredJSON, err := json.Marshal(rawReq)
	if err != nil {
		commonResponse.BadRequest(c, "参数序列化失败: "+err.Error())
		return
	}

	// 绑定到DTO
	var req dto.CreateDynamicConfigRequest
	if err := json.Unmarshal(filteredJSON, &req); err != nil {
		commonResponse.BadRequest(c, "参数解析失败: "+err.Error())
		return
	}

	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	// 检查验证服务是否初始化
	if h.verificationService == nil {
		h.logger.Error(c.Request.Context(), "Verification service is nil")
		commonResponse.InternalError(c, fmt.Errorf("verification service not initialized"))
		return
	}

	// 调用应用服务创建动态配置
	result, err := h.verificationService.CreateDynamicConfig(c.Request.Context(), tenantID, &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// handleUpdateStaticConfig 处理更新静态配置
func (h *VerificationHandler) handleUpdateStaticConfig(c *gin.Context, tenantID int64) {
	var req dto.UpdateStaticConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	// TODO: 调用应用服务更新静态配置
	// result, err := h.verificationService.UpdateStaticConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     HandleUserError(c, err)
	//     return
	// }

	// 临时返回模拟数据
	result := &dto.ConfigResponse{
		ID:       req.ID,
		TenantID: tenantID,
		IsActive: req.IsActive,
	}

	commonResponse.Success(c, result)
}

// handleUpdateDynamicConfig 处理更新动态配置
func (h *VerificationHandler) handleUpdateDynamicConfig(c *gin.Context, tenantID int64) {
	var req dto.UpdateDynamicConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	// TODO: 调用应用服务更新动态配置
	// result, err := h.verificationService.UpdateDynamicConfig(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     HandleUserError(c, err)
	//     return
	// }

	// 临时返回模拟数据
	result := &dto.ConfigResponse{
		ID:       req.ID,
		TenantID: tenantID,
		IsActive: req.IsActive,
	}

	commonResponse.Success(c, result)
}

// handleDeleteConfig 处理删除配置
func (h *VerificationHandler) handleDeleteConfig(c *gin.Context, tenantID int64) {
	var req dto.DeleteConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	if err := req.Validate(); err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	// TODO: 调用应用服务删除配置
	// err := h.verificationService.DeleteConfig(c.Request.Context(), tenantID, req.ID)
	// if err != nil {
	//     HandleUserError(c, err)
	//     return
	// }

	commonResponse.Success(c, gin.H{"message": "配置删除成功"})
}

// CopyConfigs 复制系统配置到租户 (POST /api/user/admin/verification/configs/copy)
// @Summary 复制系统配置到租户
// @Description 复制系统默认配置到指定租户
// @Tags verification-config
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CopyConfigRequest true "复制配置请求"
// @Success 200 {object} commonResponse.Response "复制成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/admin/verification/configs/copy [post]
func (h *VerificationHandler) CopyConfigs(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)

	// 解析请求参数
	var req dto.CopyConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Copy system configs request binding failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证请求参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "Copy system configs request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "Copy system configs attempt",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("source_tenant_id", req.SourceTenantID),
		logiface.Int64("target_tenant_id", req.TargetTenantID),
		logiface.Bool("overwrite", req.Overwrite),
		logiface.String("ip", c.ClientIP()))

	// TODO: 调用应用服务复制系统配置
	// err := h.verificationService.CopySystemConfigs(c.Request.Context(), &req)
	// if err != nil {
	//     HandleUserError(c, err)
	//     return
	// }

	h.logger.Info(c.Request.Context(), "System configs copied successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("target_tenant_id", req.TargetTenantID))

	commonResponse.Success(c, gin.H{"message": "System configs copied successfully"})
}
