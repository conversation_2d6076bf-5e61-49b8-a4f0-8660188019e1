package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"

	"github.com/gin-gonic/gin"

	"platforms-user/internal/application/verification/dto"
	"platforms-user/internal/application/verification/service"
	user_errors "platforms-user/internal/domain/errors"
)

// TemplateHandler 模板管理处理器
type TemplateHandler struct {
	templateService *service.TemplateApplicationService
	logger          logiface.Logger
}

// NewTemplateHandler 创建模板管理处理器
func NewTemplateHandler(
	templateService *service.TemplateApplicationService,
	logger logiface.Logger,
) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
		logger:          logger,
	}
}

// CreateTemplate 创建短信模板
// @Summary 创建短信模板
// @Description 创建新的短信模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.CreateTemplateRequest true "创建模板请求"
// @Success 200 {object} commonResponse.Response{data=dto.TemplateResponse} "创建成功"
// @Failure 200 {object} commonResponse.Response "请求参数错误"
// @Router /api/v1/verification/management/templates/create [post]
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.CreateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	result, err := h.templateService.CreateTemplate(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Template created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", result.ID),
		logiface.String("code", req.Code))

	commonResponse.Created(c, result)
}

// ListTemplates 获取模板列表
// @Summary 获取模板列表
// @Description 获取短信模板列表
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param keyword query string false "关键词搜索"
// @Param is_active query bool false "是否激活"
// @Param is_system query bool false "是否系统模板"
// @Param page query int false "页码" default(1)
// @Param size query int false "页大小" default(20)
// @Param order_by query string false "排序字段" default("created_at")
// @Param order_desc query bool false "是否降序" default(true)
// @Success 200 {object} commonResponse.Response{data=dto.TemplateListResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates [get]
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析查询参数
	var req dto.TemplateListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid query parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	result, err := h.templateService.ListTemplates(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// GetTemplate 获取模板详情
// @Summary 获取模板详情
// @Description 根据ID获取短信模板详情
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Success 200 {object} commonResponse.Response{data=dto.TemplateResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id} [get]
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	result, err := h.templateService.GetTemplate(c.Request.Context(), tenantID, templateID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// UpdateTemplate 更新模板
// @Summary 更新模板
// @Description 更新短信模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Param request body dto.UpdateTemplateRequest true "更新模板请求"
// @Success 200 {object} commonResponse.Response{data=dto.TemplateResponse} "更新成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id} [put]
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 解析请求参数
	var req dto.UpdateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置模板ID
	req.ID = templateID

	// 调用应用服务
	result, err := h.templateService.UpdateTemplate(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Template updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	commonResponse.Success(c, result)
}

// DeleteTemplate 删除模板
// @Summary 删除模板
// @Description 删除短信模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Success 204 "删除成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id} [delete]
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.templateService.DeleteTemplate(c.Request.Context(), tenantID, templateID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Template deleted successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	c.Status(http.StatusNoContent)
}

// EnableTemplate 启用模板
// @Summary 启用模板
// @Description 启用短信模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Success 200 {object} commonResponse.Response "启用成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id}/enable [put]
func (h *TemplateHandler) EnableTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.templateService.EnableTemplate(c.Request.Context(), tenantID, templateID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Template enabled successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	commonResponse.Success(c, gin.H{"message": "Template enabled successfully"})
}

// DisableTemplate 禁用模板
// @Summary 禁用模板
// @Description 禁用短信模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Success 200 {object} commonResponse.Response "禁用成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id}/disable [put]
func (h *TemplateHandler) DisableTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 调用应用服务
	err = h.templateService.DisableTemplate(c.Request.Context(), tenantID, templateID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Template disabled successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	commonResponse.Success(c, gin.H{"message": "Template disabled successfully"})
}

// BatchUpdateTemplates 批量更新模板
// @Summary 批量更新模板
// @Description 批量启用、禁用或删除模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.BatchTemplateRequest true "批量操作请求"
// @Success 200 {object} commonResponse.Response{data=dto.BatchTemplateResponse} "操作成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/batch [post]
func (h *TemplateHandler) BatchUpdateTemplates(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.BatchTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 调用应用服务
	result, err := h.templateService.BatchUpdateTemplates(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "Batch template operation completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("operation", req.Operation),
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("failure_count", result.FailureCount))

	commonResponse.Success(c, result)
}

// CloneTemplate 克隆模板
// @Summary 克隆模板
// @Description 克隆现有模板到指定租户
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "源模板ID"
// @Param request body dto.CloneTemplateRequest true "克隆模板请求"
// @Success 201 {object} commonResponse.Response{data=dto.TemplateResponse} "克隆成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 409 {object} commonResponse.Response "模板代码已存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id}/clone [post]
func (h *TemplateHandler) CloneTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取源模板ID
	sourceID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 解析请求参数
	var req dto.CloneTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置源模板ID
	req.SourceID = sourceID

	// 调用应用服务
	// result, err := h.templateService.CloneTemplate(c.Request.Context(), tenantID, &req)
	// if err != nil {
	//     h.handleError(c, err)
	//     return
	// }

	// 临时返回模拟结果
	result := &dto.TemplateResponse{
		ID:      999, // 模拟新模板ID
		Code:    req.NewCode,
		Name:    req.NewName,
		Content: "Cloned template content",
	}

	h.logger.Info(c.Request.Context(), "Template cloned successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("source_id", sourceID),
		logiface.Int64("new_template_id", result.ID))

	commonResponse.Created(c, result)
}

// CopySystemTemplates 复制系统模板
// @Summary 复制系统模板
// @Description 复制系统默认模板到当前租户
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Success 200 {object} commonResponse.Response "复制成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/copy-system [post]
func (h *TemplateHandler) CopySystemTemplates(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 调用应用服务
	err := h.templateService.CopySystemTemplates(c.Request.Context(), tenantID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "System templates copied successfully",
		logiface.Int64("tenant_id", tenantID))

	commonResponse.Success(c, gin.H{"message": "System templates copied successfully"})
}

// ValidateTemplate 验证模板
// @Summary 验证模板
// @Description 验证模板内容和语法
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param request body dto.ValidateTemplateRequest true "验证模板请求"
// @Success 200 {object} commonResponse.Response{data=dto.ValidateTemplateResponse} "验证成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/validate [post]
func (h *TemplateHandler) ValidateTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 解析请求参数
	var req dto.ValidateTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 简化的模板验证实现
	result := &dto.ValidateTemplateResponse{
		Valid:          true,
		ErrorMessages:  []string{},
		Warnings:       []string{},
		SyntaxValid:    true,
		VariablesValid: true,
		LengthValid:    len(req.Content) <= 500,
		ContentPreview: req.Content,
		DetectedVars:   []string{}, // 这里应该解析模板中的变量
	}

	// 基本验证
	if len(req.Content) > 500 {
		result.Valid = false
		result.LengthValid = false
		result.ErrorMessages = append(result.ErrorMessages, "Template content too long (max 500 characters)")
	}

	if req.Content == "" {
		result.Valid = false
		result.SyntaxValid = false
		result.ErrorMessages = append(result.ErrorMessages, "Template content cannot be empty")
	}

	h.logger.Info(c.Request.Context(), "Template validation completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.Bool("valid", result.Valid))

	commonResponse.Success(c, result)
}

// TestTemplate 测试模板
// @Summary 测试模板
// @Description 使用测试数据渲染模板
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Param id path int64 true "模板ID"
// @Param request body dto.TestTemplateRequest true "测试模板请求"
// @Success 200 {object} commonResponse.Response{data=dto.TestTemplateResponse} "测试成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "模板不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/{id}/test [post]
func (h *TemplateHandler) TestTemplate(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 获取模板ID
	templateID, err := h.getTemplateID(c)
	if err != nil {
		commonResponse.BadRequest(c, "模板ID无效: "+err.Error())
		return
	}

	// 解析请求参数
	var req dto.TestTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "Invalid request parameters",
			logiface.Error(err))
		commonResponse.GinValidationError(c, err)
		return
	}

	// 设置模板ID
	req.TemplateID = templateID

	// 获取模板
	template, err := h.templateService.GetTemplate(c.Request.Context(), tenantID, templateID)
	if err != nil {
		h.handleError(c, err)
		return
	}

	// 简化的模板渲染实现
	renderedContent := template.Content
	for key, value := range req.TestData {
		if strValue, ok := value.(string); ok {
			// 简单的字符串替换（实际应该使用模板引擎）
			placeholder := fmt.Sprintf("{{%s}}", key)
			renderedContent = strings.ReplaceAll(renderedContent, placeholder, strValue)
		}
	}

	result := &dto.TestTemplateResponse{
		Success:         true,
		RenderedContent: renderedContent,
		ContentLength:   len(renderedContent),
		TestData:        req.TestData,
		RenderTime:      10, // 模拟渲染时间
	}

	h.logger.Info(c.Request.Context(), "Template test completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.Bool("success", result.Success))

	commonResponse.Success(c, result)
}

// GetTemplateStatistics 获取模板统计
// @Summary 获取模板统计
// @Description 获取短信模板的统计信息
// @Tags template
// @Accept json
// @Produce json
// @Param X-Tenant-ID header int64 true "租户ID"
// @Success 200 {object} commonResponse.Response{data=dto.TemplateStatisticsResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "请求参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/v1/verification/management/templates/statistics [get]
func (h *TemplateHandler) GetTemplateStatistics(c *gin.Context) {
	// 获取租户ID
	tenantID := getTenantIDFromContext(c)
	// 简化的统计实现
	result := &dto.TemplateStatisticsResponse{
		TotalTemplates:    0,
		ActiveTemplates:   0,
		InactiveTemplates: 0,
		SystemTemplates:   0,
		CustomTemplates:   0,
		UsageCount:        make(map[string]int64),
		AverageLength:     0,
		LongestTemplate:   0,
		ShortestTemplate:  0,
	}

	h.logger.Info(c.Request.Context(), "Template statistics retrieved",
		logiface.Int64("tenant_id", tenantID))

	commonResponse.Success(c, result)
}

// getTemplateID 获取模板ID
func (h *TemplateHandler) getTemplateID(c *gin.Context) (int64, error) {
	templateIDStr := c.Param("id")
	if templateIDStr == "" {
		return 0, fmt.Errorf("template ID is required")
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid template ID format")
	}

	if templateID <= 0 {
		return 0, fmt.Errorf("template ID must be positive")
	}

	return templateID, nil
}

// handleError 处理错误
func (h *TemplateHandler) handleError(c *gin.Context, err error) {
	if userErr, ok := err.(*user_errors.UserError); ok {
		// 用户模块特定错误 - 根据错误类型区分日志级别
		switch userErr.Code {
		case user_errors.CodeParameterValidationFailed:
			h.logger.Warn(c.Request.Context(), "template validation failed",
				logiface.String("error_message", userErr.Message))
			commonResponse.BadRequest(c, userErr.Message)
		case user_errors.CodeSystemError:
			h.logger.Warn(c.Request.Context(), "template not found",
				logiface.String("error_message", userErr.Message))
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "模板不存在")
		default:
			h.logger.Warn(c.Request.Context(), "template operation failed",
				logiface.String("error_message", userErr.Message))
			commonResponse.Error(c, commonResponse.CodeBusinessLogicError, userErr.Message)
		}
	} else {
		// 系统错误使用Error级别
		h.logger.Error(c.Request.Context(), "Template operation failed",
			logiface.Error(err))
		commonResponse.InternalError(c, err)
	}
}
