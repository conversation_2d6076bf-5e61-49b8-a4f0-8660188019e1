package handlers

import (
	"net/http"

	commonResponse "platforms-pkg/common/response"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/verification/dto"
	"platforms-user/internal/application/verification/service"

	"github.com/gin-gonic/gin"
)

// PolicyHandler 策略处理器
type PolicyHandler struct {
	policyService *service.PolicyApplicationService
	logger        logiface.Logger
}

// NewPolicyHandler 创建策略处理器
func NewPolicyHandler(
	policyService *service.PolicyApplicationService,
	logger logiface.Logger,
) *PolicyHandler {
	return &PolicyHandler{
		policyService: policyService,
		logger:        logger,
	}
}

// CreatePolicy 创建策略
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理创建策略请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定请求参数
	var req dto.CreatePolicyRequest
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 设置租户ID
	req.TenantID = tenantID

	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	resp, err := h.policyService.CreatePolicy(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "策略创建失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略创建成功", logiface.Int64("policy_id", resp.ID))
	commonResponse.Created(c, resp)
}

// UpdatePolicy 更新策略
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理更新策略请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定请求参数
	var req dto.UpdatePolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}
	// 设置租户ID
	req.TenantID = tenantID
	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	err := h.policyService.UpdatePolicy(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "策略更新失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略更新成功", logiface.Int64("policy_id", req.PolicyID))
	commonResponse.Updated(c, nil)
}

// DeletePolicy 删除策略
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理删除策略请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定请求参数
	var req dto.DeletePolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 设置租户ID
	req.TenantID = tenantID

	// 调用服务
	if err := h.policyService.DeletePolicy(ctx, &req); err != nil {
		h.logger.Error(ctx, "策略删除失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略删除成功", logiface.Int64("policy_id", req.PolicyID))
	commonResponse.Success(c, nil)
}

// SetPolicyStatus 设置策略状态
func (h *PolicyHandler) SetPolicyStatus(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理设置策略状态请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定请求参数
	var req dto.SetPolicyStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 设置租户ID
	req.TenantID = tenantID

	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	if err := h.policyService.SetPolicyStatus(ctx, &req); err != nil {
		h.logger.Error(ctx, "策略状态设置失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略状态设置成功", logiface.Int64("policy_id", req.PolicyID), logiface.Bool("enabled", req.Enabled))
	commonResponse.Updated(c, nil)
}

// GetPolicyList 获取策略列表
func (h *PolicyHandler) GetPolicyList(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理获取策略列表请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定查询参数
	var req dto.PolicyListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 设置租户ID
	req.TenantID = tenantID

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	resp, err := h.policyService.GetPolicyList(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "策略列表获取失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略列表获取成功", logiface.Int("count", len(resp.List)))
	commonResponse.Success(c, resp)
}

// GetPolicyDetail 获取策略详情
func (h *PolicyHandler) GetPolicyDetail(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理获取策略详情请求")

	tenantID := getTenantIDFromContext(c)

	// 绑定查询参数
	var req dto.PolicyDetailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 设置租户ID
	req.TenantID = tenantID

	// 调用服务
	resp, err := h.policyService.GetPolicyDetail(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "策略详情获取失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "策略详情获取成功", logiface.Int64("policy_id", req.PolicyID))
	commonResponse.Success(c, resp)
}

// ValidateExpression 验证表达式
func (h *PolicyHandler) ValidateExpression(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理验证表达式请求")

	// 绑定请求参数
	var req dto.ExprValidateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	resp, err := h.policyService.ValidateExpression(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "表达式验证失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "表达式验证完成", logiface.Bool("valid", resp.Valid))
	commonResponse.Success(c, resp)
}

// TestExpression 测试表达式
func (h *PolicyHandler) TestExpression(c *gin.Context) {
	ctx := c.Request.Context()
	h.logger.Info(ctx, "处理测试表达式请求")

	// 绑定请求参数
	var req dto.ExprTestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(ctx, "参数绑定失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数格式错误", err.Error())
		return
	}

	// 验证参数
	if err := req.Validate(); err != nil {
		h.logger.Warn(ctx, "参数验证失败", logiface.Any("error", err))
		commonResponse.Error(c, http.StatusBadRequest, "参数验证失败", err.Error())
		return
	}

	// 调用服务
	resp, err := h.policyService.TestExpression(ctx, &req)
	if err != nil {
		h.logger.Error(ctx, "表达式测试失败", logiface.Error(err))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(ctx, "表达式测试完成", logiface.Any("result", resp.Result))
	commonResponse.Success(c, resp)
}
