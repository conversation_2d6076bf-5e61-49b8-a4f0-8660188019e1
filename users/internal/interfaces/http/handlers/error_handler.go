package handlers

import (
	"errors"
	commonResponse "platforms-pkg/common/response"
	userErrors "platforms-user/internal/domain/errors"

	"github.com/gin-gonic/gin"
)

// HandleUserError 处理用户模块自定义错误
func HandleUserError(c *gin.Context, err error) {
	var userErr *userErrors.UserError
	if errors.As(err, &userErr) {
		// 根据错误码返回相应的HTTP响应
		switch userErr.Code {
		case userErrors.CodeParameterValidationFailed:
			commonResponse.FieldError(c, userErr.Field, userErr.Message)
		// 用户认证相关错误 (110000-110099)
		case userErrors.CodeUserNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "用户不存在")
		case userErrors.CodeUserAlreadyExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeUserLocked:
			commonResponse.Error(c, commonResponse.CodeAccountLocked, userErr.Message)
		case userErrors.CodeUserDisabled:
			commonResponse.Error(c, commonResponse.CodeAccountDisabled, userErr.Message)
		case userErrors.CodeUserInactive:
			commonResponse.Error(c, commonResponse.CodeAccountDisabled, userErr.Message)
		case userErrors.CodeUserExpired:
			commonResponse.Error(c, commonResponse.CodeAccountExpired, userErr.Message)
		case userErrors.CodeUserSuspended:
			commonResponse.Error(c, commonResponse.CodeAccountSuspended, userErr.Message)
		case userErrors.CodeInvalidCredentials:
			commonResponse.FieldError(c, "username", "用户名或者密码错误")
		case userErrors.CodePasswordIncorrect:
			commonResponse.FieldError(c, "password", "密码错误")
		case userErrors.CodePasswordExpired:
			commonResponse.Error(c, commonResponse.CodePasswordExpired, userErr.Message)
		case userErrors.CodePasswordTooWeak:
			commonResponse.FieldError(c, "password", "密码强度不足")
		case userErrors.CodePasswordHistoryLimit:
			commonResponse.FieldError(c, "password", "密码不能与历史密码相同")
		case userErrors.CodePasswordPolicyViolated:
			// 使用用户错误中的详细信息，如果没有则使用默认消息
			message := userErr.Message
			if userErr.Details != "" {
				message = userErr.Details
			}
			commonResponse.FieldError(c, "password", message)
		case userErrors.CodeUserAccountLocked:
			commonResponse.Error(c, commonResponse.CodeAccountLocked, userErr.Message)
		case userErrors.CodeUserAccountDisabled:
			commonResponse.Error(c, commonResponse.CodeAccountDisabled, userErr.Message)
		case userErrors.CodeLoginAttemptsExceeded:
			commonResponse.Error(c, commonResponse.CodeRateLimitExceeded, userErr.Message)
		case userErrors.CodeIPBlocked:
			commonResponse.Error(c, commonResponse.CodeIPRestricted, userErr.Message)
		case userErrors.CodeDeviceNotTrusted:
			commonResponse.Error(c, commonResponse.CodeDeviceNotTrusted, userErr.Message)
		case userErrors.CodeLocationRestricted:
			commonResponse.Error(c, commonResponse.CodeLocationRestricted, userErr.Message)
		case userErrors.CodeTimeRestricted:
			commonResponse.Error(c, commonResponse.CodeTimeRestricted, userErr.Message)
		case userErrors.CodeConcurrentLoginLimit:
			commonResponse.Error(c, commonResponse.CodeConcurrentLogin, userErr.Message)
		case userErrors.CodeSessionExpired:
			commonResponse.Error(c, commonResponse.CodeSessionExpired, userErr.Message)
		case userErrors.CodeSessionInvalid:
			commonResponse.Error(c, commonResponse.CodeSessionInvalid, userErr.Message)
		case userErrors.CodeSessionNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "会话不存在")
		case userErrors.CodeSessionRevoked:
			commonResponse.Error(c, commonResponse.CodeTokenRevoked, userErr.Message)
		case userErrors.CodeTokenExpired:
			commonResponse.Error(c, commonResponse.CodeTokenExpired, userErr.Message)
		case userErrors.CodeTokenInvalid:
			commonResponse.Error(c, commonResponse.CodeTokenInvalid, userErr.Message)
		case userErrors.CodeTokenRevoked:
			commonResponse.Error(c, commonResponse.CodeTokenRevoked, userErr.Message)
		case userErrors.CodeTokenNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "Token不存在")
		case userErrors.CodeMFARequired:
			commonResponse.Error(c, commonResponse.CodeMFARequired, userErr.Message)
		case userErrors.CodeMFAInvalid:
			commonResponse.Error(c, commonResponse.CodeMFAInvalid, userErr.Message)
		case userErrors.CodeMFAExpired:
			commonResponse.Error(c, commonResponse.CodeMFAExpired, userErr.Message)
		case userErrors.CodeCaptchaRequired:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeCaptchaInvalid:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeCaptchaExpired:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)

		// 用户注册相关错误 (110100-110199)
		case userErrors.CodeRegistrationDisabled:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, userErr.Message)
		case userErrors.CodeInvitationRequired:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeInvitationInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeInvitationExpired:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeInvitationUsed:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeInvitationLimitReached:
			commonResponse.Error(c, commonResponse.CodeRateLimitExceeded, userErr.Message)
		case userErrors.CodeEmailVerificationRequired:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodePhoneVerificationRequired:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeUsernameExists:
			commonResponse.FieldError(c, "username", "用户名已存在")
		case userErrors.CodeEmailExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodePhoneExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeUsernameInvalid:
			commonResponse.FieldError(c, "username", "用户名格式无效")
		case userErrors.CodeEmailInvalid:
			commonResponse.FieldError(c, "email", "邮箱格式无效")
		case userErrors.CodePhoneInvalid:
			commonResponse.FieldError(c, "phone", "手机号格式无效")
		case userErrors.CodeRealNameRequired:
			commonResponse.FieldError(c, "real_name", "真实姓名必填")
		case userErrors.CodeRealNameInvalid:
			commonResponse.FieldError(c, "real_name", "真实姓名格式无效")
		case userErrors.CodeAgeRestriction:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeRegionRestriction:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeDomainRestriction:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeRegistrationRateLimit:
			commonResponse.Error(c, commonResponse.CodeRateLimitExceeded, userErr.Message)

		// 用户信息相关错误 (110200-110299)
		case userErrors.CodeProfileUpdateFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeAvatarUploadFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeAvatarFormatInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidFormat, userErr.Message)
		case userErrors.CodeAvatarSizeExceeded:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, userErr.Message)
		case userErrors.CodeEmailUpdateFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodePhoneUpdateFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeRealNameUpdateFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeDepartmentNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "部门不存在")
		case userErrors.CodePositionNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "职位不存在")
		case userErrors.CodeEmployeeIDExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeEmployeeIDInvalid:
			commonResponse.FieldError(c, "employee_id", "员工编号格式无效")
		case userErrors.CodeHireDateInvalid:
			commonResponse.FieldError(c, "hire_date", "入职日期无效")
		case userErrors.CodeProfileIncomplete:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)

		// 用户权限相关错误 (110300-110399)
		case userErrors.CodePermissionDenied:
			commonResponse.Error(c, commonResponse.CodePermissionDenied, userErr.Message)
		case userErrors.CodeRoleNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "角色不存在")
		case userErrors.CodeRoleAlreadyAssigned:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeRoleNotAssigned:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, userErr.Message)
		case userErrors.CodeRoleLimitExceeded:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, userErr.Message)
		case userErrors.CodePermissionNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "权限不存在")
		case userErrors.CodePermissionAlreadyGranted:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodePermissionNotGranted:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, userErr.Message)
		case userErrors.CodeUserResourceNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "用户资源不存在")
		case userErrors.CodeResourceAccessDenied:
			commonResponse.Error(c, commonResponse.CodeAccessForbidden, userErr.Message)
		case userErrors.CodeUserOperationNotAllowed:
			commonResponse.Error(c, commonResponse.CodeOperationNotAllowed, userErr.Message)
		case userErrors.CodeSelfOperationNotAllowed:
			commonResponse.BadRequest(c, userErr.Message)
		case userErrors.CodeAdminOperationRequired:
			commonResponse.Error(c, commonResponse.CodePermissionDenied, userErr.Message)
		case userErrors.CodeSuperAdminRequired:
			commonResponse.Error(c, commonResponse.CodePermissionDenied, userErr.Message)

		// 租户相关错误 (110400-110499)
		case userErrors.CodeTenantNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "租户不存在")
		case userErrors.CodeTenantDisabled:
			commonResponse.Error(c, commonResponse.CodeAccountDisabled, userErr.Message)
		case userErrors.CodeTenantExpired:
			commonResponse.Error(c, commonResponse.CodeAccountExpired, userErr.Message)
		case userErrors.CodeTenantSuspended:
			commonResponse.Error(c, commonResponse.CodeAccountSuspended, userErr.Message)
		case userErrors.CodeTenantCodeInvalid:
			commonResponse.FieldError(c, "tenant_code", "租户代码无效")
		case userErrors.CodeTenantNameExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeTenantCodeExists:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeTenantLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, userErr.Message)
		case userErrors.CodeTenantUserLimitReached:
			commonResponse.Error(c, commonResponse.CodeCountLimitExceeded, userErr.Message)
		case userErrors.CodeTenantStorageLimitReached:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, userErr.Message)
		case userErrors.CodeTenantQuotaExceeded:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, userErr.Message)
		case userErrors.CodeTenantConfigInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeTenantConfigNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "租户配置不存在")

		// 第三方认证相关错误 (110500-110599)
		case userErrors.CodeOAuthProviderNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "OAuth提供商不存在")
		case userErrors.CodeOAuthProviderDisabled:
			commonResponse.Error(c, commonResponse.CodeServiceUnavailable, userErr.Message)
		case userErrors.CodeOAuthProviderError:
			commonResponse.Error(c, commonResponse.CodeThirdPartyError, userErr.Message)
		case userErrors.CodeOAuthTokenInvalid:
			commonResponse.Error(c, commonResponse.CodeTokenInvalid, userErr.Message)
		case userErrors.CodeOAuthTokenExpired:
			commonResponse.Error(c, commonResponse.CodeTokenExpired, userErr.Message)
		case userErrors.CodeOAuthUserNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "OAuth用户不存在")
		case userErrors.CodeOAuthAccountLinked:
			commonResponse.Error(c, commonResponse.CodeResourceExists, userErr.Message)
		case userErrors.CodeOAuthAccountUnlinked:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, userErr.Message)
		case userErrors.CodeOAuthScopeInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthCallbackInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthStateInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthCodeInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthCodeExpired:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthCodeUsed:
			commonResponse.Error(c, commonResponse.CodeInvalidRequest, userErr.Message)
		case userErrors.CodeOAuthRefreshTokenInvalid:
			commonResponse.Error(c, commonResponse.CodeTokenInvalid, userErr.Message)
		case userErrors.CodeOAuthRefreshTokenExpired:
			commonResponse.Error(c, commonResponse.CodeTokenExpired, userErr.Message)
		case userErrors.CodeOAuthUserInfoFailed:
			commonResponse.Error(c, commonResponse.CodeThirdPartyError, userErr.Message)
		case userErrors.CodeOAuthEmailMismatch:
			commonResponse.Error(c, commonResponse.CodeValidationError, userErr.Message)
		case userErrors.CodeOAuthAccountConflict:
			commonResponse.Error(c, commonResponse.CodeResourceConflict, userErr.Message)

		// 文件上传相关错误 (110600-110699)
		case userErrors.CodeFileUploadFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeFileFormatInvalid:
			commonResponse.Error(c, commonResponse.CodeInvalidFormat, userErr.Message)
		case userErrors.CodeFileSizeExceeded:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, userErr.Message)
		case userErrors.CodeFileTypeNotAllowed:
			commonResponse.Error(c, commonResponse.CodeInvalidFormat, userErr.Message)
		case userErrors.CodeFileVirusDetected:
			commonResponse.Error(c, commonResponse.CodeInvalidFormat, userErr.Message)
		case userErrors.CodeFileCorrupted:
			commonResponse.Error(c, commonResponse.CodeInvalidFormat, userErr.Message)
		case userErrors.CodeFileNotFound:
			commonResponse.Error(c, commonResponse.CodeResourceNotFound, "文件不存在")
		case userErrors.CodeFileAccessDenied:
			commonResponse.Error(c, commonResponse.CodeAccessForbidden, userErr.Message)
		case userErrors.CodeFileDeleteFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeFileDownloadFailed:
			commonResponse.Error(c, commonResponse.CodeOperationFailed, userErr.Message)
		case userErrors.CodeFileStorageError:
			commonResponse.Error(c, commonResponse.CodeSystemError, userErr.Message)
		case userErrors.CodeFileQuotaExceeded:
			commonResponse.Error(c, commonResponse.CodeSizeLimitExceeded, userErr.Message)

		// 系统错误 (110900-110999)
		case userErrors.CodeSystemError:
			commonResponse.Error(c, commonResponse.CodeSystemError, userErr.Message)
		case userErrors.CodeDatabaseError:
			commonResponse.Error(c, commonResponse.CodeDatabaseError, userErr.Message)
		case userErrors.CodeCacheError:
			commonResponse.Error(c, commonResponse.CodeCacheError, userErr.Message)
		case userErrors.CodeNetworkError:
			commonResponse.Error(c, commonResponse.CodeServiceUnavailable, userErr.Message)
		case userErrors.CodeServiceUnavailable:
			commonResponse.ServiceUnavailable(c, userErr.Message)
		case userErrors.CodeServiceTimeout:
			commonResponse.Error(c, commonResponse.CodeServiceTimeout, userErr.Message)
		case userErrors.CodeServiceOverload:
			commonResponse.Error(c, commonResponse.CodeServiceOverload, userErr.Message)
		case userErrors.CodeThirdPartyError:
			commonResponse.Error(c, commonResponse.CodeThirdPartyError, userErr.Message)
		case userErrors.CodeThirdPartyTimeout:
			commonResponse.Error(c, commonResponse.CodeThirdPartyTimeout, userErr.Message)
		case userErrors.CodeThirdPartyUnavailable:
			commonResponse.Error(c, commonResponse.CodeThirdPartyUnavailable, userErr.Message)
		case userErrors.CodeInternalError:
			commonResponse.InternalError(c, err)
		case userErrors.CodeUnexpectedError:
			commonResponse.Error(c, commonResponse.CodeSystemError, userErr.Message)

		// 默认处理
		default:
			commonResponse.Error(c, commonResponse.CodeBusinessLogicError, userErr.Message)
		}
	} else {
		commonResponse.Error(c, commonResponse.CodeInternalError, err.Error())
	}
}

// IsUserError 检查是否为用户模块自定义错误
func IsUserError(err error) bool {
	_, ok := err.(*userErrors.UserError)
	return ok
}

// GetUserErrorCode 获取用户错误码
func GetUserErrorCode(err error) int {
	if userErr, ok := err.(*userErrors.UserError); ok {
		return userErr.Code
	}
	return 0
}

// GetUserErrorMessage 获取用户错误消息
func GetUserErrorMessage(err error) string {
	if userErr, ok := err.(*userErrors.UserError); ok {
		return userErr.Message
	}
	return err.Error()
}
