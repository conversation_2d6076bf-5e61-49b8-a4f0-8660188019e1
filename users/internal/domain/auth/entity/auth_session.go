package entity

import (
	"platforms-user/internal/domain/auth/value_object"
	"time"

	"github.com/google/uuid"
)

// AuthSession 认证会话实体
type AuthSession struct {
	ID               string                     `json:"id" gorm:"primaryKey;type:varchar(36)"`
	UserID           int64                      `json:"user_id" gorm:"not null;index"`
	TenantID         int64                      `json:"tenant_id" gorm:"not null;index"`
	JTI              string                     `json:"jti" gorm:"type:varchar(64);not null;uniqueIndex"` // JWT ID，用于令牌撤销
	AccessTokenHash  string                     `json:"access_token_hash" gorm:"type:varchar(64);index"`  // 访问令牌哈希
	RefreshTokenHash string                     `json:"refresh_token_hash" gorm:"type:varchar(64);index"` // 刷新令牌哈希
	Status           value_object.SessionStatus `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
	DeviceType       string                     `json:"device_type" gorm:"type:varchar(20)"`
	OS               string                     `json:"os" gorm:"type:varchar(50)"`
	OSVersion        string                     `json:"os_version" gorm:"type:varchar(20)"`
	Browser          string                     `json:"browser" gorm:"type:varchar(50)"`
	BrowserVersion   string                     `json:"browser_version" gorm:"type:varchar(20)"`
	DeviceModel      string                     `json:"device_model" gorm:"type:varchar(100)"`
	ScreenSize       string                     `json:"screen_size" gorm:"type:varchar(20)"`
	Language         string                     `json:"language" gorm:"type:varchar(10)"`
	Timezone         string                     `json:"timezone" gorm:"type:varchar(50)"`
	Fingerprint      string                     `json:"fingerprint" gorm:"type:varchar(255)"`
	ExpiresAt        time.Time                  `json:"expires_at" gorm:"not null"`
	LastUsedAt       time.Time                  `json:"last_used_at" gorm:"not null"`
	CreatedAt        time.Time                  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time                  `json:"updated_at" gorm:"autoUpdateTime"`
}

// NewAuthSessionWithJTI 创建新的认证会话（使用JTI和令牌哈希）
func NewAuthSessionWithJTI(userID, tenantID int64, jti, accessTokenHash, refreshTokenHash string, deviceInfo *value_object.DeviceInfo, expiresAt time.Time) *AuthSession {
	return &AuthSession{
		ID:               uuid.New().String(),
		UserID:           userID,
		TenantID:         tenantID,
		JTI:              jti,
		AccessTokenHash:  accessTokenHash,
		RefreshTokenHash: refreshTokenHash,
		Status:           value_object.SessionStatusActive,
		DeviceType:       deviceInfo.DeviceType,
		OS:               deviceInfo.OS,
		OSVersion:        deviceInfo.OSVersion,
		Browser:          deviceInfo.Browser,
		BrowserVersion:   deviceInfo.BrowserVersion,
		DeviceModel:      deviceInfo.DeviceModel,
		ScreenSize:       deviceInfo.ScreenSize,
		Language:         deviceInfo.Language,
		Timezone:         deviceInfo.Timezone,
		Fingerprint:      deviceInfo.Fingerprint,
		ExpiresAt:        expiresAt,
		LastUsedAt:       time.Now(),
	}
}

// NewAuthSession 创建新的认证会话（向后兼容，使用JTI方案）
func NewAuthSession(userID, tenantID int64, accessToken, refreshToken string, deviceInfo *value_object.DeviceInfo, expiresAt time.Time) *AuthSession {
	// 生成JTI（使用访问令牌的哈希作为JTI的基础）
	jti := uuid.New().String()

	return &AuthSession{
		ID:               uuid.New().String(),
		UserID:           userID,
		TenantID:         tenantID,
		JTI:              jti,
		AccessTokenHash:  "", // 将在应用层设置
		RefreshTokenHash: "", // 将在应用层设置
		Status:           value_object.SessionStatusActive,
		DeviceType:       deviceInfo.DeviceType,
		OS:               deviceInfo.OS,
		OSVersion:        deviceInfo.OSVersion,
		Browser:          deviceInfo.Browser,
		BrowserVersion:   deviceInfo.BrowserVersion,
		DeviceModel:      deviceInfo.DeviceModel,
		ScreenSize:       deviceInfo.ScreenSize,
		Language:         deviceInfo.Language,
		Timezone:         deviceInfo.Timezone,
		Fingerprint:      deviceInfo.Fingerprint,
		ExpiresAt:        expiresAt,
		LastUsedAt:       time.Now(),
	}
}

// IsActive 检查会话是否活跃
func (s *AuthSession) IsActive() bool {
	return s.Status == value_object.SessionStatusActive && time.Now().Before(s.ExpiresAt)
}

// IsExpired 检查会话是否过期
func (s *AuthSession) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// RefreshWithJTI 刷新会话（使用JTI和令牌哈希）
func (s *AuthSession) RefreshWithJTI(jti, accessTokenHash, refreshTokenHash string, expiresAt time.Time) {
	s.JTI = jti
	s.AccessTokenHash = accessTokenHash
	s.RefreshTokenHash = refreshTokenHash
	s.ExpiresAt = expiresAt
	s.LastUsedAt = time.Now()
	s.Status = value_object.SessionStatusActive
}

// Revoke 撤销会话
func (s *AuthSession) Revoke() {
	s.Status = value_object.SessionStatusRevoked
	s.UpdatedAt = time.Now()
}

// UpdateLastUsed 更新最后使用时间
func (s *AuthSession) UpdateLastUsed() {
	s.LastUsedAt = time.Now()
}

// GetRemainingTime 获取剩余时间
func (s *AuthSession) GetRemainingTime() time.Duration {
	return s.ExpiresAt.Sub(time.Now())
}

// GetJTI 获取JTI
func (s *AuthSession) GetJTI() string {
	return s.JTI
}

// GetAccessTokenHash 获取访问令牌哈希
func (s *AuthSession) GetAccessTokenHash() string {
	return s.AccessTokenHash
}

// GetRefreshTokenHash 获取刷新令牌哈希
func (s *AuthSession) GetRefreshTokenHash() string {
	return s.RefreshTokenHash
}

// SetAccessTokenHash 设置访问令牌哈希
func (s *AuthSession) SetAccessTokenHash(hash string) {
	s.AccessTokenHash = hash
}

// SetRefreshTokenHash 设置刷新令牌哈希
func (s *AuthSession) SetRefreshTokenHash(hash string) {
	s.RefreshTokenHash = hash
}

// SetJTI 设置JTI
func (s *AuthSession) SetJTI(jti string) {
	s.JTI = jti
}

// TableName 指定表名
func (AuthSession) TableName() string {
	return "auth_sessions"
}
