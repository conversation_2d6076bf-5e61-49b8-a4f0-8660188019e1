package repository

import (
	"context"
	"platforms-user/internal/domain/auth/entity"
	"time"
)

// AuthRepository 认证仓储接口
type AuthRepository interface {
	// 会话管理
	CreateSession(ctx context.Context, session *entity.AuthSession) error
	GetSession(ctx context.Context, sessionID string) (*entity.AuthSession, error)
	UpdateSession(ctx context.Context, session *entity.AuthSession) error
	DeleteSession(ctx context.Context, sessionID string) error
	RevokeSession(ctx context.Context, sessionID string) error
	RevokeSessionsByUserIDExcept(ctx context.Context, userID int64, exceptSessionID string) error
	RevokeAllSessionsByUserID(ctx context.Context, userID int64) error
	FindSessionByID(ctx context.Context, sessionID string) (*entity.AuthSession, error)
	FindSessionsByUserID(ctx context.Context, userID int64, offset, limit int) ([]*entity.AuthSession, int64, error)
	FindActiveSessionsByUserID(ctx context.Context, userID int64) ([]*entity.AuthSession, error)

	// JTI相关查询方法
	FindSessionByJTI(ctx context.Context, jti string) (*entity.AuthSession, error)
	FindSessionByAccessTokenHash(ctx context.Context, accessTokenHash string) (*entity.AuthSession, error)
	FindSessionByRefreshTokenHash(ctx context.Context, refreshTokenHash string) (*entity.AuthSession, error)
	RevokeSessionByJTI(ctx context.Context, jti string) error

	// 登录尝试管理
	CreateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error
	GetLoginAttempts(ctx context.Context, identifier string, since time.Time) ([]entity.LoginAttempt, error)
	GetFailedLoginAttempts(ctx context.Context, identifier string, since time.Time) (int64, error)
	UpdateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error
	FindLoginAttemptsByUsername(ctx context.Context, tenantID int64, username string, since time.Time) ([]*entity.LoginAttempt, error)
	FindLoginAttemptsByIP(ctx context.Context, tenantID int64, ipAddress string, since time.Time) ([]*entity.LoginAttempt, error)
	CleanOldLoginAttempts(ctx context.Context, before time.Time) error

	// 统计信息
	GetLoginAttemptStats(ctx context.Context, tenantID int64) (*LoginAttemptStats, error)
	GetSessionStats(ctx context.Context, tenantID int64) (*SessionStats, error)
}

// LoginAttemptStats 登录尝试统计
type LoginAttemptStats struct {
	TotalAttempts      int64 `json:"total_attempts"`
	SuccessfulAttempts int64 `json:"successful_attempts"`
	FailedAttempts     int64 `json:"failed_attempts"`
	BlockedIPs         int64 `json:"blocked_ips"`
}

// SessionStats 会话统计
type SessionStats struct {
	TotalSessions     int64 `json:"total_sessions"`
	ActiveSessions    int64 `json:"active_sessions"`
	ExpiredSessions   int64 `json:"expired_sessions"`
	RevokedSessions   int64 `json:"revoked_sessions"`
	LoggedOutSessions int64 `json:"logged_out_sessions"`
}
