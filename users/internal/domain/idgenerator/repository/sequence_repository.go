package repository

import (
	"context"
	"platforms-user/internal/domain/idgenerator/entity"
)

// SequenceRepository 序列仓储接口
type SequenceRepository interface {
	// Create 创建序列
	Create(ctx context.Context, sequence *entity.Sequence) error

	// FindByBusinessAndName 根据业务类型和名称查找序列
	FindByBusinessAndName(ctx context.Context, businessType, sequenceName string, tenantID int64) (*entity.Sequence, error)

	// FindByID 根据ID查找序列
	FindByID(ctx context.Context, id int64) (*entity.Sequence, error)

	// Update 更新序列
	Update(ctx context.Context, sequence *entity.Sequence) error

	// UpdateCurrentValue 更新当前值
	UpdateCurrentValue(ctx context.Context, sequenceID int64, currentValue int64) error

	// FindActiveSequences 查找活跃的序列
	FindActiveSequences(ctx context.Context) ([]*entity.Sequence, error)

	// ExistsByBusinessTypeAndTenant 检查business_type和tenant_id组合是否已存在
	ExistsByBusinessTypeAndTenant(ctx context.Context, businessType string, tenantID int64) (bool, error)
}
