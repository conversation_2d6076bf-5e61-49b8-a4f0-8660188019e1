package entity

import (
	"platforms-user/pkg/config"
	"time"
)

// Sequence ID序列实体
type Sequence struct {
	ID            int64
	BusinessType  string
	SequenceName  string
	TenantID      int64
	CurrentValue  int64
	IncrementStep int
	CacheSize     int
	MaxValue      int64
	MinValue      int64
	Threshold     int
	IsActive      bool
	Remarks       string
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// NewSequence 创建新的序列
func NewSequence(businessType, sequenceName string, tenantID int64, incrementStep int) *Sequence {
	return &Sequence{
		BusinessType:  businessType,
		SequenceName:  sequenceName,
		TenantID:      tenantID,
		CurrentValue:  0,
		IncrementStep: incrementStep,
		CacheSize:     config.DefaultCacheSize, // 使用配置常量
		MaxValue:      config.DefaultMaxValue,
		MinValue:      config.DefaultMinValue,
		Threshold:     config.DefaultThreshold,
		IsActive:      true,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}
}

// ValidateBusinessType 验证业务类型
func (s *Sequence) ValidateBusinessType() error {
	if s.BusinessType == "" {
		return ErrInvalidBusinessType
	}
	return nil
}

// ValidateSequenceName 验证序列名称
func (s *Sequence) ValidateSequenceName() error {
	if s.SequenceName == "" {
		return ErrInvalidSequenceName
	}
	return nil
}

// IsReachMaxValue 检查是否达到最大值
func (s *Sequence) IsReachMaxValue() bool {
	// 如果 MaxValue 为 0，表示不限制
	if s.MaxValue == config.UnlimitedMaxValue {
		return false
	}
	return s.CurrentValue >= s.MaxValue
}

// CanAllocateSegments 检查是否可以分配新段
func (s *Sequence) CanAllocateSegments(count int) bool {
	// 如果 MaxValue 为 0，表示不限制
	if s.MaxValue == config.UnlimitedMaxValue {
		return true
	}
	nextValue := s.CurrentValue + int64(count*s.IncrementStep)
	return nextValue <= s.MaxValue
}

// GetSequenceKey 获取序列唯一键
func (s *Sequence) GetSequenceKey() string {
	return s.BusinessType + ":" + s.SequenceName
}

// IsMaxValueUnlimited 检查最大值是否不限制
func (s *Sequence) IsMaxValueUnlimited() bool {
	return s.MaxValue == config.UnlimitedMaxValue
}

// GetDisplayMaxValue 获取显示用的最大值
func (s *Sequence) GetDisplayMaxValue() interface{} {
	if s.IsMaxValueUnlimited() {
		return "不限制"
	}
	return s.MaxValue
}

// SetMaxValue 设置最大值，支持不限制
func (s *Sequence) SetMaxValue(maxValue int64) {
	if maxValue == 0 {
		s.MaxValue = config.UnlimitedMaxValue
	} else {
		s.MaxValue = maxValue
	}
}
