package entity

import (
	"encoding/json"
	"time"
)

// TenantConfig 租户配置实体
type TenantConfig struct {
	ID          int64     `json:"id" gorm:"primaryKey"`
	TenantID    int64     `json:"tenant_id" gorm:"not null;index"`
	ConfigKey   string    `json:"config_key" gorm:"not null;size:100"`
	ConfigValue string    `json:"config_value" gorm:"type:text"`
	ConfigType  string    `json:"config_type" gorm:"type:varchar(20);default:'json'"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (TenantConfig) TableName() string {
	return "system_config"
}

// PasswordPolicy 密码策略配置
type PasswordPolicy struct {
	MinLength            int      `json:"min_length"`
	MaxLength            int      `json:"max_length"`
	RequireUppercase     bool     `json:"require_uppercase"`
	RequireLowercase     bool     `json:"require_lowercase"`
	RequireDigits        bool     `json:"require_digits"`
	RequireSpecialChars  bool     `json:"require_special_chars"`
	ForbiddenPatterns    []string `json:"forbidden_patterns"`
	PasswordHistoryCount int      `json:"password_history_count"`
	ExpireDays           int      `json:"expire_days"`
}

// RegistrationMethods 注册方式配置
type RegistrationMethods struct {
	Email struct {
		Enabled             bool `json:"enabled"`
		RequireVerification bool `json:"require_verification"`
		ManualActivation    bool `json:"manual_activation"` // 手动激活
		VerificationTemplateCode string `json:"verification_template_code,omitempty"` // 邮箱验证模板代码
	} `json:"email"`
	Phone struct {
		Enabled             bool `json:"enabled"`
		RequireVerification bool `json:"require_verification"`
		ManualActivation    bool `json:"manual_activation"` // 手动激活
		VerificationTemplateCode string `json:"verification_template_code,omitempty"` // 短信验证模板代码
	} `json:"phone"`
	OAuth struct {
		Enabled         bool `json:"enabled"`
		ManualActivation bool `json:"manual_activation"` // 手动激活
	} `json:"oauth"`
	AdminCreation struct {
		Enabled         bool `json:"enabled"`
		RequireApproval bool `json:"require_approval"`
	} `json:"admin_creation"`
}

// TenantInfo 租户信息配置
type TenantInfo struct {
	Type           string `json:"type"`            // 企业或个人：enterprise/personal
	SystemName     string `json:"system_name"`     // 系统名称
	ServiceEmail   string `json:"service_email"`   // 客服邮箱
	Address        string `json:"address"`         // 地址
	ContactPerson  string `json:"contact_person"`  // 联系人
	ContactPhone   string `json:"contact_phone"`   // 联系电话
	Website        string `json:"website"`         // 官网地址
	Logo           string `json:"logo"`            // 企业/个人标志
	Description    string `json:"description"`     // 描述
	BusinessLicense string `json:"business_license"` // 营业执照（企业）
}

// 配置键常量
const (
	ConfigKeyPasswordPolicy      = "password_policy"
	ConfigKeyRegistrationMethods = "registration_methods"
	ConfigKeyTenantInfo          = "tenant_info"
)

// NewDefaultPasswordPolicy 创建默认密码策略
func NewDefaultPasswordPolicy() *PasswordPolicy {
	return &PasswordPolicy{
		MinLength:            8,
		MaxLength:            32,
		RequireUppercase:     true,
		RequireLowercase:     true,
		RequireDigits:        true,
		RequireSpecialChars:  false,
		ForbiddenPatterns:    []string{"123456", "password", "admin"},
		PasswordHistoryCount: 5,
		ExpireDays:           90,
	}
}

// NewDefaultRegistrationMethods 创建默认注册方式配置
func NewDefaultRegistrationMethods() *RegistrationMethods {
	methods := &RegistrationMethods{}
	methods.Email.Enabled = true
	methods.Email.RequireVerification = true
	methods.Email.ManualActivation = false
	methods.Phone.Enabled = true
	methods.Phone.RequireVerification = true
	methods.Phone.ManualActivation = false
	methods.OAuth.Enabled = true
	methods.OAuth.ManualActivation = false
	methods.AdminCreation.Enabled = true
	methods.AdminCreation.RequireApproval = false
	return methods
}

// NewDefaultTenantInfo 创建默认租户信息配置
func NewDefaultTenantInfo() *TenantInfo {
	return &TenantInfo{
		Type:         "personal",
		SystemName:   "我的系统",
		ServiceEmail: "",
		Address:      "",
		ContactPerson: "",
		ContactPhone: "",
		Website:      "",
		Logo:         "",
		Description:  "",
		BusinessLicense: "",
	}
}

// ToJSON 转换为JSON字符串
func (p *PasswordPolicy) ToJSON() (string, error) {
	data, err := json.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (p *PasswordPolicy) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), p)
}

// ToJSON 转换为JSON字符串
func (r *RegistrationMethods) ToJSON() (string, error) {
	data, err := json.Marshal(r)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (r *RegistrationMethods) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), r)
}

// ToJSON 转换为JSON字符串
func (t *TenantInfo) ToJSON() (string, error) {
	data, err := json.Marshal(t)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (t *TenantInfo) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), t)
}
