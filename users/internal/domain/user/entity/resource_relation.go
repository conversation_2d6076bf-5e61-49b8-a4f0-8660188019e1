package entity

import (
	"time"
)

// ResourceRelation 资源关联关系实体
type ResourceRelation struct {
	ID               int64     `json:"id" gorm:"primaryKey"`
	TenantID         int64     `json:"tenant_id" gorm:"not null;index"`
	SourceResourceID int64     `json:"source_resource_id" gorm:"not null;index"`
	TargetResourceID int64     `json:"target_resource_id" gorm:"not null;index"`
	Description      string    `json:"description" gorm:"size:500"`
	CreatedAt        time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	SourceResource *Resource `json:"source_resource,omitempty" gorm:"foreignKey:SourceResourceID"`
	TargetResource *Resource `json:"target_resource,omitempty" gorm:"foreignKey:TargetResourceID"`
}

// TableName 指定表名
func (ResourceRelation) TableName() string {
	return "resource_relations"
}

// IsValid 检查资源关系是否有效
func (rr *ResourceRelation) IsValid() bool {
	return rr.SourceResourceID > 0 && rr.TargetResourceID > 0 && rr.SourceResourceID != rr.TargetResourceID
}

// GetDescription 获取关联描述
func (rr *ResourceRelation) GetDescription() string {
	if rr.Description != "" {
		return rr.Description
	}
	return "资源关联关系"
}
