package repository

import (
	"context"

	"platforms-user/internal/domain/user/entity"
)

// ResourceRelationRepository 资源关系仓储接口
type ResourceRelationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, relation *entity.ResourceRelation) error
	FindByID(ctx context.Context, id int64) (*entity.ResourceRelation, error)
	Update(ctx context.Context, relation *entity.ResourceRelation) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	FindBySourceResource(ctx context.Context, sourceResourceID int64, tenantID int64) ([]entity.ResourceRelation, error)
	FindByTargetResource(ctx context.Context, targetResourceID int64, tenantID int64) ([]entity.ResourceRelation, error)
	FindByResources(ctx context.Context, sourceResourceID, targetResourceID int64, tenantID int64) (*entity.ResourceRelation, error)

	// 批量操作
	BatchCreate(ctx context.Context, relations []entity.ResourceRelation) error
	BatchDelete(ctx context.Context, ids []int64) error
	DeleteBySourceResource(ctx context.Context, sourceResourceID int64, tenantID int64) error
	DeleteByTargetResource(ctx context.Context, targetResourceID int64, tenantID int64) error

	// 关联查询
	GetRelatedResources(ctx context.Context, resourceID int64, tenantID int64) ([]entity.Resource, error)
	GetSourceResources(ctx context.Context, targetResourceID int64, tenantID int64) ([]entity.Resource, error)
	GetTargetResources(ctx context.Context, sourceResourceID int64, tenantID int64) ([]entity.Resource, error)

	// 检查操作
	Exists(ctx context.Context, sourceResourceID, targetResourceID int64, tenantID int64) (bool, error)
	CountBySourceResource(ctx context.Context, sourceResourceID int64, tenantID int64) (int64, error)
	CountByTargetResource(ctx context.Context, targetResourceID int64, tenantID int64) (int64, error)
}
