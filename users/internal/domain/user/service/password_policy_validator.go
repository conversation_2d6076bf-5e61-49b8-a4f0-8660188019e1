package service

import (
	"fmt"
	"platforms-user/internal/domain/user/entity"
	"regexp"
	"strings"
)

// PasswordPolicyValidator 密码策略验证器
type PasswordPolicyValidator struct{}

// NewPasswordPolicyValidator 创建密码策略验证器
func NewPasswordPolicyValidator() *PasswordPolicyValidator {
	return &PasswordPolicyValidator{}
}

// ValidatePassword 验证密码是否符合策略
func (v *PasswordPolicyValidator) ValidatePassword(password string, policy *entity.PasswordPolicy) error {
	if policy == nil {
		// 如果没有策略，使用默认策略
		policy = entity.NewDefaultPasswordPolicy()
	}

	var errors []string

	// 1. 检查密码长度
	if len(password) < policy.MinLength {
		errors = append(errors, fmt.Sprintf("密码长度不能少于%d位", policy.MinLength))
	}
	if len(password) > policy.MaxLength {
		errors = append(errors, fmt.Sprintf("密码长度不能超过%d位", policy.MaxLength))
	}

	// 2. 检查大写字母要求
	if policy.RequireUppercase {
		if !containsUppercase(password) {
			errors = append(errors, "密码必须包含大写字母")
		}
	}

	// 3. 检查小写字母要求
	if policy.RequireLowercase {
		if !containsLowercase(password) {
			errors = append(errors, "密码必须包含小写字母")
		}
	}

	// 4. 检查数字要求
	if policy.RequireDigits {
		if !containsDigits(password) {
			errors = append(errors, "密码必须包含数字")
		}
	}

	// 5. 检查特殊字符要求
	if policy.RequireSpecialChars {
		if !containsSpecialChars(password) {
			errors = append(errors, "密码必须包含特殊字符")
		}
	}

	// 6. 检查禁止的模式
	for _, pattern := range policy.ForbiddenPatterns {
		if strings.Contains(strings.ToLower(password), strings.ToLower(pattern)) {
			errors = append(errors, fmt.Sprintf("密码不能包含禁止的模式: %s", pattern))
		}
	}

	// 如果有错误，返回详细的错误信息
	if len(errors) > 0 {
		policyDescription := v.GeneratePolicyDescription(policy)
		errorMessage := fmt.Sprintf("%s 具体问题：%s", policyDescription, strings.Join(errors, "；"))
		return fmt.Errorf(errorMessage)
	}

	return nil
}

// GeneratePolicyDescription 生成密码策略要求描述
func (v *PasswordPolicyValidator) GeneratePolicyDescription(policy *entity.PasswordPolicy) string {
	if policy == nil {
		policy = entity.NewDefaultPasswordPolicy()
	}

	var requirements []string

	// 长度要求
	requirements = append(requirements, fmt.Sprintf("长度%d-%d位", policy.MinLength, policy.MaxLength))

	// 字符类型要求
	if policy.RequireUppercase {
		requirements = append(requirements, "包含大写字母")
	}
	if policy.RequireLowercase {
		requirements = append(requirements, "包含小写字母")
	}
	if policy.RequireDigits {
		requirements = append(requirements, "包含数字")
	}
	if policy.RequireSpecialChars {
		requirements = append(requirements, "包含特殊字符")
	}

	// 禁止模式
	if len(policy.ForbiddenPatterns) > 0 {
		requirements = append(requirements, fmt.Sprintf("不能包含：%s", strings.Join(policy.ForbiddenPatterns, "、")))
	}

	return fmt.Sprintf("密码策略要求：%s", strings.Join(requirements, "，"))
}

// GetPolicyRequirements 获取密码策略要求列表
func (v *PasswordPolicyValidator) GetPolicyRequirements(policy *entity.PasswordPolicy) []string {
	if policy == nil {
		policy = entity.NewDefaultPasswordPolicy()
	}

	var requirements []string

	// 长度要求
	requirements = append(requirements, fmt.Sprintf("密码长度必须在%d-%d位之间", policy.MinLength, policy.MaxLength))

	// 字符类型要求
	if policy.RequireUppercase {
		requirements = append(requirements, "必须包含至少一个大写字母(A-Z)")
	}
	if policy.RequireLowercase {
		requirements = append(requirements, "必须包含至少一个小写字母(a-z)")
	}
	if policy.RequireDigits {
		requirements = append(requirements, "必须包含至少一个数字(0-9)")
	}
	if policy.RequireSpecialChars {
		requirements = append(requirements, "必须包含至少一个特殊字符(!@#$%^&*()_+-=[]{}|;:,.<>?)")
	}

	// 禁止模式
	if len(policy.ForbiddenPatterns) > 0 {
		requirements = append(requirements, fmt.Sprintf("不能包含以下模式：%s", strings.Join(policy.ForbiddenPatterns, "、")))
	}

	return requirements
}

// containsUppercase 检查是否包含大写字母
func containsUppercase(s string) bool {
	return regexp.MustCompile(`[A-Z]`).MatchString(s)
}

// containsLowercase 检查是否包含小写字母
func containsLowercase(s string) bool {
	return regexp.MustCompile(`[a-z]`).MatchString(s)
}

// containsDigits 检查是否包含数字
func containsDigits(s string) bool {
	return regexp.MustCompile(`[0-9]`).MatchString(s)
}

// containsSpecialChars 检查是否包含特殊字符
func containsSpecialChars(s string) bool {
	return regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(s)
}
