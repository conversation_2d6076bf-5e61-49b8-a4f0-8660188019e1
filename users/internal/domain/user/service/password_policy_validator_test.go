package service

import (
	"platforms-user/internal/domain/user/entity"
	"strings"
	"testing"
)

func TestPasswordPolicyValidator_ValidatePassword(t *testing.T) {
	validator := NewPasswordPolicyValidator()

	tests := []struct {
		name     string
		password string
		policy   *entity.PasswordPolicy
		wantErr  bool
	}{
		{
			name:     "valid password with default policy",
			password: "TestSecure789",
			policy:   entity.NewDefaultPasswordPolicy(),
			wantErr:  false,
		},
		{
			name:     "password too short",
			password: "Test1",
			policy: &entity.PasswordPolicy{
				MinLength: 8,
				MaxLength: 32,
			},
			wantErr: true,
		},
		{
			name:     "password too long",
			password: strings.Repeat("a", 33),
			policy: &entity.PasswordPolicy{
				MinLength: 8,
				MaxLength: 32,
			},
			wantErr: true,
		},
		{
			name:     "missing uppercase",
			password: "test123456",
			policy: &entity.PasswordPolicy{
				MinLength:        8,
				MaxLength:        32,
				RequireUppercase: true,
			},
			wantErr: true,
		},
		{
			name:     "missing lowercase",
			password: "TEST123456",
			policy: &entity.PasswordPolicy{
				MinLength:        8,
				MaxLength:        32,
				RequireLowercase: true,
			},
			wantErr: true,
		},
		{
			name:     "missing digits",
			password: "TestPassword",
			policy: &entity.PasswordPolicy{
				MinLength:     8,
				MaxLength:     32,
				RequireDigits: true,
			},
			wantErr: true,
		},
		{
			name:     "missing special chars",
			password: "Test123456",
			policy: &entity.PasswordPolicy{
				MinLength:           8,
				MaxLength:           32,
				RequireSpecialChars: true,
			},
			wantErr: true,
		},
		{
			name:     "contains forbidden pattern",
			password: "Test123456password",
			policy: &entity.PasswordPolicy{
				MinLength:         8,
				MaxLength:         32,
				ForbiddenPatterns: []string{"password"},
			},
			wantErr: true,
		},
		{
			name:     "valid password with all requirements",
			password: "Test123!@#",
			policy: &entity.PasswordPolicy{
				MinLength:           8,
				MaxLength:           32,
				RequireUppercase:    true,
				RequireLowercase:    true,
				RequireDigits:       true,
				RequireSpecialChars: true,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validator.ValidatePassword(tt.password, tt.policy)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidatePassword() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err != nil && tt.wantErr {
				// 检查错误消息是否包含策略要求描述
				if !strings.Contains(err.Error(), "密码策略要求：") {
					t.Errorf("Error message should contain policy description, got: %v", err.Error())
				}
			}
		})
	}
}

func TestPasswordPolicyValidator_GeneratePolicyDescription(t *testing.T) {
	validator := NewPasswordPolicyValidator()

	tests := []struct {
		name     string
		policy   *entity.PasswordPolicy
		expected string
	}{
		{
			name:     "default policy",
			policy:   entity.NewDefaultPasswordPolicy(),
			expected: "密码策略要求：长度8-32位，包含大写字母，包含小写字母，包含数字，不能包含：123456、password、admin",
		},
		{
			name: "custom policy",
			policy: &entity.PasswordPolicy{
				MinLength:           10,
				MaxLength:           20,
				RequireUppercase:    true,
				RequireLowercase:    false,
				RequireDigits:       true,
				RequireSpecialChars: true,
				ForbiddenPatterns:   []string{"test"},
			},
			expected: "密码策略要求：长度10-20位，包含大写字母，包含数字，包含特殊字符，不能包含：test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.GeneratePolicyDescription(tt.policy)
			if result != tt.expected {
				t.Errorf("GeneratePolicyDescription() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestPasswordPolicyValidator_GetPolicyRequirements(t *testing.T) {
	validator := NewPasswordPolicyValidator()

	policy := entity.NewDefaultPasswordPolicy()
	requirements := validator.GetPolicyRequirements(policy)

	expectedRequirements := []string{
		"密码长度必须在8-32位之间",
		"必须包含至少一个大写字母(A-Z)",
		"必须包含至少一个小写字母(a-z)",
		"必须包含至少一个数字(0-9)",
		"不能包含以下模式：123456、password、admin",
	}

	if len(requirements) != len(expectedRequirements) {
		t.Errorf("Expected %d requirements, got %d", len(expectedRequirements), len(requirements))
	}

	for i, requirement := range requirements {
		if requirement != expectedRequirements[i] {
			t.Errorf("Requirement %d: expected %s, got %s", i, expectedRequirements[i], requirement)
		}
	}
}

func TestPasswordPolicyValidator_HelperFunctions(t *testing.T) {
	// 测试辅助函数
	if !containsUppercase("Test") {
		t.Error("containsUppercase should return true for 'Test'")
	}
	if containsUppercase("test") {
		t.Error("containsUppercase should return false for 'test'")
	}

	if !containsLowercase("test") {
		t.Error("containsLowercase should return true for 'test'")
	}
	if containsLowercase("TEST") {
		t.Error("containsLowercase should return false for 'TEST'")
	}

	if !containsDigits("test123") {
		t.Error("containsDigits should return true for 'test123'")
	}
	if containsDigits("test") {
		t.Error("containsDigits should return false for 'test'")
	}

	if !containsSpecialChars("test!@#") {
		t.Error("containsSpecialChars should return true for 'test!@#'")
	}
	if containsSpecialChars("test") {
		t.Error("containsSpecialChars should return false for 'test'")
	}
}
