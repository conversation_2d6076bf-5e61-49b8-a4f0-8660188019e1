package service

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"reflect"
	"strconv"
	"strings"
)

// ExpressionService 表达式服务
type ExpressionService struct{}

// NewExpressionService 创建表达式服务
func NewExpressionService() *ExpressionService {
	return &ExpressionService{}
}

// ValidateExpression 验证表达式语法
func (s *ExpressionService) ValidateExpression(expr string) error {
	if strings.TrimSpace(expr) == "" {
		return fmt.Errorf("表达式不能为空")
	}

	// 预处理表达式，将常见的逻辑运算符转换为Go语法
	processedExpr := s.preprocessExpression(expr)

	// 尝试解析表达式
	_, err := parser.ParseExpr(processedExpr)
	if err != nil {
		return fmt.Errorf("表达式语法错误: %w", err)
	}

	// 检查是否包含支持的变量
	if err := s.validateVariables(expr); err != nil {
		return err
	}

	return nil
}

// TestExpression 测试表达式
func (s *ExpressionService) TestExpression(expr string, sample map[string]interface{}) (bool, error) {
	// 首先验证表达式
	if err := s.ValidateExpression(expr); err != nil {
		return false, err
	}

	// 执行表达式计算
	result, err := s.evaluateExpression(expr, sample)
	if err != nil {
		return false, fmt.Errorf("表达式执行失败: %w", err)
	}

	return result, nil
}

// preprocessExpression 预处理表达式
func (s *ExpressionService) preprocessExpression(expr string) string {
	// 替换逻辑运算符
	expr = strings.ReplaceAll(expr, " AND ", " && ")
	expr = strings.ReplaceAll(expr, " and ", " && ")
	expr = strings.ReplaceAll(expr, " OR ", " || ")
	expr = strings.ReplaceAll(expr, " or ", " || ")
	expr = strings.ReplaceAll(expr, " NOT ", " ! ")
	expr = strings.ReplaceAll(expr, " not ", " ! ")

	// 处理 in 和 not in 操作符（简化处理）
	// 这里可以根据需要实现更复杂的 in 操作符处理

	return expr
}

// validateVariables 验证变量
func (s *ExpressionService) validateVariables(expr string) error {
	supportedVars := map[string]bool{
		"fail_count":    true,
		"ip":            true,
		"uid":           true,
		"device_id":     true,
		"geo_location":  true,
		"hour":          true,
		"is_black_ip":   true,
		"is_new_device": true,
	}

	// 简单的变量提取（可以改进为更精确的解析）
	processedExpr := s.preprocessExpression(expr)
	node, err := parser.ParseExpr(processedExpr)
	if err != nil {
		return nil // 语法错误会在 ValidateExpression 中处理
	}

	// 遍历AST查找标识符
	var unsupportedVars []string
	ast.Inspect(node, func(n ast.Node) bool {
		if ident, ok := n.(*ast.Ident); ok {
			// 跳过布尔值和其他关键字
			if ident.Name != "true" && ident.Name != "false" && !supportedVars[ident.Name] {
				unsupportedVars = append(unsupportedVars, ident.Name)
			}
		}
		return true
	})

	if len(unsupportedVars) > 0 {
		return fmt.Errorf("不支持的变量: %s", strings.Join(unsupportedVars, ", "))
	}

	return nil
}

// evaluateExpression 计算表达式
func (s *ExpressionService) evaluateExpression(expr string, sample map[string]interface{}) (bool, error) {
	// 这是一个简化的表达式计算实现
	// 在生产环境中，建议使用更成熟的表达式引擎，如 govaluate 或 expr

	// 替换变量值
	evaluatedExpr := expr
	for key, value := range sample {
		var valueStr string
		switch v := value.(type) {
		case string:
			valueStr = fmt.Sprintf("\"%s\"", v)
		case bool:
			valueStr = strconv.FormatBool(v)
		case int:
			valueStr = strconv.Itoa(v)
		case int64:
			valueStr = strconv.FormatInt(v, 10)
		case float64:
			valueStr = strconv.FormatFloat(v, 'f', -1, 64)
		default:
			valueStr = fmt.Sprintf("%v", v)
		}
		evaluatedExpr = strings.ReplaceAll(evaluatedExpr, key, valueStr)
	}

	// 预处理逻辑运算符
	evaluatedExpr = s.preprocessExpression(evaluatedExpr)

	// 简单的表达式计算（这里可以集成更强大的表达式引擎）
	result, err := s.simpleEvaluate(evaluatedExpr)
	if err != nil {
		return false, err
	}

	return result, nil
}

// simpleEvaluate 简单的表达式计算
func (s *ExpressionService) simpleEvaluate(expr string) (bool, error) {
	// 这是一个非常简化的实现，仅用于演示
	// 在实际项目中应该使用专业的表达式引擎

	// 尝试解析为Go表达式
	node, err := parser.ParseExpr(expr)
	if err != nil {
		return false, fmt.Errorf("表达式解析失败: %w", err)
	}

	// 简单的求值（仅支持基本的比较和逻辑运算）
	result, err := s.evalNode(node)
	if err != nil {
		return false, err
	}

	// 确保结果是布尔值
	if boolResult, ok := result.(bool); ok {
		return boolResult, nil
	}

	return false, fmt.Errorf("表达式结果不是布尔值")
}

// evalNode 计算AST节点
func (s *ExpressionService) evalNode(node ast.Node) (interface{}, error) {
	switch n := node.(type) {
	case *ast.BasicLit:
		switch n.Kind {
		case token.INT:
			return strconv.Atoi(n.Value)
		case token.FLOAT:
			return strconv.ParseFloat(n.Value, 64)
		case token.STRING:
			return strconv.Unquote(n.Value)
		}
	case *ast.Ident:
		if n.Name == "true" {
			return true, nil
		}
		if n.Name == "false" {
			return false, nil
		}
		return nil, fmt.Errorf("未知标识符: %s", n.Name)
	case *ast.BinaryExpr:
		left, err := s.evalNode(n.X)
		if err != nil {
			return nil, err
		}
		right, err := s.evalNode(n.Y)
		if err != nil {
			return nil, err
		}
		return s.evalBinaryOp(left, n.Op, right)
	case *ast.UnaryExpr:
		operand, err := s.evalNode(n.X)
		if err != nil {
			return nil, err
		}
		return s.evalUnaryOp(n.Op, operand)
	case *ast.ParenExpr:
		return s.evalNode(n.X)
	}
	return nil, fmt.Errorf("不支持的表达式类型")
}

// evalBinaryOp 计算二元运算
func (s *ExpressionService) evalBinaryOp(left interface{}, op token.Token, right interface{}) (interface{}, error) {
	switch op {
	case token.LAND: // &&
		lBool, lOk := left.(bool)
		rBool, rOk := right.(bool)
		if !lOk || !rOk {
			return nil, fmt.Errorf("逻辑运算符要求布尔操作数")
		}
		return lBool && rBool, nil
	case token.LOR: // ||
		lBool, lOk := left.(bool)
		rBool, rOk := right.(bool)
		if !lOk || !rOk {
			return nil, fmt.Errorf("逻辑运算符要求布尔操作数")
		}
		return lBool || rBool, nil
	case token.EQL: // ==
		return reflect.DeepEqual(left, right), nil
	case token.NEQ: // !=
		return !reflect.DeepEqual(left, right), nil
	case token.LSS: // <
		return s.compareNumbers(left, right, func(a, b float64) bool { return a < b })
	case token.LEQ: // <=
		return s.compareNumbers(left, right, func(a, b float64) bool { return a <= b })
	case token.GTR: // >
		return s.compareNumbers(left, right, func(a, b float64) bool { return a > b })
	case token.GEQ: // >=
		return s.compareNumbers(left, right, func(a, b float64) bool { return a >= b })
	}
	return nil, fmt.Errorf("不支持的二元运算符: %s", op)
}

// evalUnaryOp 计算一元运算
func (s *ExpressionService) evalUnaryOp(op token.Token, operand interface{}) (interface{}, error) {
	switch op {
	case token.NOT: // !
		if boolVal, ok := operand.(bool); ok {
			return !boolVal, nil
		}
		return nil, fmt.Errorf("逻辑非运算符要求布尔操作数")
	}
	return nil, fmt.Errorf("不支持的一元运算符: %s", op)
}

// compareNumbers 比较数字
func (s *ExpressionService) compareNumbers(left, right interface{}, cmp func(float64, float64) bool) (bool, error) {
	lNum, err := s.toFloat64(left)
	if err != nil {
		return false, err
	}
	rNum, err := s.toFloat64(right)
	if err != nil {
		return false, err
	}
	return cmp(lNum, rNum), nil
}

// toFloat64 转换为float64
func (s *ExpressionService) toFloat64(val interface{}) (float64, error) {
	switch v := val.(type) {
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case float64:
		return v, nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("无法转换为数字: %v", val)
	}
}
