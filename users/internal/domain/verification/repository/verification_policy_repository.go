package repository

import (
	"context"
	"platforms-user/internal/domain/verification/entity"
)

// ListPolicyParams 策略列表查询参数
type ListPolicyParams struct {
	TenantID int64  `json:"tenant_id"`
	Scene    string `json:"scene"`
	IsActive *bool  `json:"is_active"`
	Keyword  string `json:"keyword"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

// ExistsConditionPolicyParams 检查条件策略存在性参数
type ExistsConditionPolicyParams struct {
	TenantID            int64  `json:"tenant_id"`
	BusinessScene       string `json:"business_scene"`
	Dimension           string `json:"dimension"`
	ConditionExpression string `json:"condition_expression"`
}

// VerificationPolicyRepository 验证策略仓储接口
type VerificationPolicyRepository interface {
	// Create 创建策略
	Create(ctx context.Context, policy *entity.VerificationPolicy) (*entity.VerificationPolicy, error)

	// Update 更新策略
	Update(ctx context.Context, policy *entity.VerificationPolicy) error

	// Delete 删除策略
	Delete(ctx context.Context, id int64, tenantID int64) error

	// GetByID 根据ID获取策略
	GetByID(ctx context.Context, id int64, tenantID int64) (*entity.VerificationPolicy, error)

	// List 获取策略列表
	List(ctx context.Context, params *ListPolicyParams) ([]*entity.VerificationPolicy, int64, error)

	// SetStatus 设置策略状态
	SetStatus(ctx context.Context, id int64, tenantID int64, enabled bool) error

	// GetActivePoliciesByScene 根据场景获取激活的策略列表（按优先级排序）
	GetActivePoliciesByScene(ctx context.Context, tenantID int64, businessScene string) ([]*entity.VerificationPolicy, error)

	// ExistsConditionPolicy 检查是否存在满足条件的策略
	ExistsConditionPolicy(ctx context.Context, params *ExistsConditionPolicyParams) (bool, error)
}
