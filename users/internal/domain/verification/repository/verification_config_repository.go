package repository

import (
	"context"
	"fmt"

	"platforms-user/internal/domain/verification/entity"
)

// VerificationConfigRepository 验证配置仓储接口
type VerificationConfigRepository interface {
	// 统一配置查找（支持静态和动态）
	FindEffectiveConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType, businessScene *string) (*entity.VerificationConfig, error)
	FindStaticConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error)
	FindDynamicConfig(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) (*entity.VerificationConfig, error)
	FindSystemDefaultConfig(ctx context.Context, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error)

	// 动态配置管理
	FindByBusinessScene(ctx context.Context, tenantID int64, businessScene string) ([]*entity.VerificationConfig, error)
	FindByJudgmentDimension(ctx context.Context, tenantID int64, dimension string) ([]*entity.VerificationConfig, error)
	FindDynamicConfigsByPriority(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) ([]*entity.VerificationConfig, error)

	// 基础CRUD操作
	Create(ctx context.Context, config *entity.VerificationConfig) error
	Update(ctx context.Context, config *entity.VerificationConfig) error
	Delete(ctx context.Context, id int64) error
	SoftDelete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*entity.VerificationConfig, error)

	// 查询操作
	FindByTenantID(ctx context.Context, tenantID int64) ([]*entity.VerificationConfig, error)
	FindByPurposeAndTarget(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error)
	FindActiveConfigs(ctx context.Context, tenantID int64) ([]*entity.VerificationConfig, error)
	FindByTemplateCode(ctx context.Context, tenantID int64, templateCode string) ([]*entity.VerificationConfig, error)

	// 状态管理
	EnableConfig(ctx context.Context, id int64) error
	DisableConfig(ctx context.Context, id int64) error
	BatchUpdateStatus(ctx context.Context, ids []int64, isActive bool) error

	// 配置检查
	ExistsByPurposeAndTarget(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (bool, error)
	ExistsByBusinessScene(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) (bool, error)
	IsConfigActive(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (bool, error)

	// 租户配置管理
	CopySystemConfigsToTenant(ctx context.Context, tenantID int64) error
	GetEffectiveConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error)

	// 分页查询
	FindWithPagination(ctx context.Context, filter *VerificationConfigFilter) ([]*entity.VerificationConfig, int64, error)
}

// VerificationConfigFilter 验证配置查询过滤器
type VerificationConfigFilter struct {
	TenantID          *int64             `json:"tenant_id"`
	ConfigMode        *entity.ConfigMode `json:"config_mode"`
	Purpose           *entity.Purpose    `json:"purpose"`
	TargetType        *entity.TargetType `json:"target_type"`
	TokenType         *entity.TokenType  `json:"token_type"`
	BusinessScene     string             `json:"business_scene"`
	JudgmentDimension string             `json:"judgment_dimension"`
	IsActive          *bool              `json:"is_active"`
	TemplateCode      string             `json:"template_code"`
	MinPriority       *int               `json:"min_priority"`
	MaxPriority       *int               `json:"max_priority"`
	VerificationLevel *int               `json:"verification_level"`
	Page              int                `json:"page"`
	Size              int                `json:"size"`
	OrderBy           string             `json:"order_by"`
	OrderDesc         bool               `json:"order_desc"`
}

// Validate 验证过滤器参数
func (f *VerificationConfigFilter) Validate() error {
	if f.Page <= 0 {
		f.Page = 1
	}
	if f.Size <= 0 || f.Size > 100 {
		f.Size = 20
	}
	if f.OrderBy == "" {
		f.OrderBy = "created_at"
		f.OrderDesc = true
	}
	return nil
}

// GetOffset 获取分页偏移量
func (f *VerificationConfigFilter) GetOffset() int {
	return (f.Page - 1) * f.Size
}

// GetLimit 获取分页限制
func (f *VerificationConfigFilter) GetLimit() int {
	return f.Size
}

// GetOrderClause 获取排序子句
func (f *VerificationConfigFilter) GetOrderClause() string {
	order := f.OrderBy
	if f.OrderDesc {
		order += " DESC"
	} else {
		order += " ASC"
	}
	return order
}

// ConfigValidationResult 配置验证结果
type ConfigValidationResult struct {
	Valid        bool                       `json:"valid"`
	Config       *entity.VerificationConfig `json:"config"`
	ErrorCode    string                     `json:"error_code"`
	ErrorMessage string                     `json:"error_message"`
}

// IsValid 检查验证结果是否有效
func (r *ConfigValidationResult) IsValid() bool {
	return r.Valid && r.Config != nil
}

// HasError 检查是否有错误
func (r *ConfigValidationResult) HasError() bool {
	return !r.Valid && r.ErrorCode != ""
}

// GetConfig 获取配置
func (r *ConfigValidationResult) GetConfig() *entity.VerificationConfig {
	return r.Config
}

// SetError 设置错误
func (r *ConfigValidationResult) SetError(code, message string) {
	r.Valid = false
	r.ErrorCode = code
	r.ErrorMessage = message
}

// SetValid 设置为有效
func (r *ConfigValidationResult) SetValid(config *entity.VerificationConfig) {
	r.Valid = true
	r.Config = config
	r.ErrorCode = ""
	r.ErrorMessage = ""
}

// ConfigStatistics 配置统计信息
type ConfigStatistics struct {
	TotalConfigs      int64                       `json:"total_configs"`
	ActiveConfigs     int64                       `json:"active_configs"`
	InactiveConfigs   int64                       `json:"inactive_configs"`
	StaticConfigs     int64                       `json:"static_configs"`
	DynamicConfigs    int64                       `json:"dynamic_configs"`
	ByPurpose         map[entity.Purpose]int64    `json:"by_purpose"`
	ByTargetType      map[entity.TargetType]int64 `json:"by_target_type"`
	ByTokenType       map[entity.TokenType]int64  `json:"by_token_type"`
	ByConfigMode      map[entity.ConfigMode]int64 `json:"by_config_mode"`
	ByTenant          map[int64]int64             `json:"by_tenant"`
	ByBusinessScene   map[string]int64            `json:"by_business_scene"`
	ByVerificationLvl map[int]int64               `json:"by_verification_level"`
}

// CalculateRates 计算比率
func (s *ConfigStatistics) CalculateRates() map[string]float64 {
	rates := make(map[string]float64)

	if s.TotalConfigs > 0 {
		rates["active_rate"] = float64(s.ActiveConfigs) / float64(s.TotalConfigs)
		rates["inactive_rate"] = float64(s.InactiveConfigs) / float64(s.TotalConfigs)
		rates["static_rate"] = float64(s.StaticConfigs) / float64(s.TotalConfigs)
		rates["dynamic_rate"] = float64(s.DynamicConfigs) / float64(s.TotalConfigs)
	}

	return rates
}

// ConfigBatchOperation 配置批量操作
type ConfigBatchOperation struct {
	IDs       []int64 `json:"ids"`
	Operation string  `json:"operation"` // "enable", "disable", "delete"
}

// Validate 验证批量操作参数
func (o *ConfigBatchOperation) Validate() error {
	if len(o.IDs) == 0 {
		return fmt.Errorf("ids cannot be empty")
	}

	if len(o.IDs) > 100 {
		return fmt.Errorf("too many ids (max 100)")
	}

	validOperations := map[string]bool{
		"enable":  true,
		"disable": true,
		"delete":  true,
	}

	if !validOperations[o.Operation] {
		return fmt.Errorf("invalid operation: %s", o.Operation)
	}

	return nil
}

// ConfigTemplate 配置模板
type ConfigTemplate struct {
	ConfigMode          entity.ConfigMode `json:"config_mode"`
	Purpose             *entity.Purpose   `json:"purpose"`
	BusinessScene       *string           `json:"business_scene"`
	JudgmentDimension   *string           `json:"judgment_dimension"`
	ConditionExpr       *string           `json:"condition_expr"`
	TargetType          entity.TargetType `json:"target_type"`
	TokenType           entity.TokenType  `json:"token_type"`
	TokenLength         int               `json:"token_length"`
	ExpireMinutes       int               `json:"expire_minutes"`
	MaxAttempts         int               `json:"max_attempts"`
	RateLimitPerMinute  int               `json:"rate_limit_per_minute"`
	RateLimitPerHour    int               `json:"rate_limit_per_hour"`
	RateLimitPerDay     int               `json:"rate_limit_per_day"`
	TemplateCode        string            `json:"template_code"`
	RequireVerification bool              `json:"require_verification"`
	VerificationLevel   int               `json:"verification_level"`
	Priority            int               `json:"priority"`
	Description         string            `json:"description"`
}

// ToConfig 转换为配置实体
func (t *ConfigTemplate) ToConfig(tenantID int64) *entity.VerificationConfig {
	var config *entity.VerificationConfig

	if t.ConfigMode == entity.ConfigModeStatic && t.Purpose != nil {
		config = entity.NewStaticConfig(tenantID, *t.Purpose, t.TargetType, t.TokenType, t.TemplateCode)
	} else if t.ConfigMode == entity.ConfigModeDynamic && t.BusinessScene != nil && t.JudgmentDimension != nil && t.ConditionExpr != nil {
		config = entity.NewDynamicConfig(tenantID, *t.BusinessScene, *t.JudgmentDimension, *t.ConditionExpr, t.TargetType, t.TokenType, t.TemplateCode)
	} else {
		// 默认创建静态配置（保持向后兼容）
		if t.Purpose != nil {
			config = entity.NewStaticConfig(tenantID, *t.Purpose, t.TargetType, t.TokenType, t.TemplateCode)
		} else {
			return nil
		}
	}

	// 设置其他字段
	config.TokenLength = t.TokenLength
	config.ExpireMinutes = t.ExpireMinutes
	config.MaxAttempts = t.MaxAttempts
	config.RateLimitPerMinute = t.RateLimitPerMinute
	config.RateLimitPerHour = t.RateLimitPerHour
	config.RateLimitPerDay = t.RateLimitPerDay
	config.RequireVerification = t.RequireVerification
	config.VerificationLevel = t.VerificationLevel
	config.Priority = t.Priority

	if t.Description != "" {
		config.UpdateDescription(t.Description)
	}

	return config
}

// GetDefaultTemplates 获取默认配置模板
func GetDefaultTemplates() []*ConfigTemplate {
	purpose1 := entity.PurposeRegistration
	purpose2 := entity.PurposePasswordReset
	purpose3 := entity.PurposeLoginVerify
	purpose4 := entity.PurposeMFAVerify

	return []*ConfigTemplate{
		// 注册激活 - 邮箱验证码
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose1, TargetType: entity.TargetTypeEmail, TokenType: entity.TokenTypeCode,
			TokenLength: 6, ExpireMinutes: 30, MaxAttempts: 5,
			RateLimitPerMinute: 1, RateLimitPerHour: 5, RateLimitPerDay: 20,
			TemplateCode: "email_activation_code", RequireVerification: true, VerificationLevel: 1, Priority: 0,
			Description: "邮箱注册激活验证码",
		},
		// 注册激活 - 手机验证码
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose1, TargetType: entity.TargetTypePhone, TokenType: entity.TokenTypeCode,
			TokenLength: 6, ExpireMinutes: 10, MaxAttempts: 3,
			RateLimitPerMinute: 1, RateLimitPerHour: 3, RateLimitPerDay: 10,
			TemplateCode: "sms_activation_code", RequireVerification: true, VerificationLevel: 1, Priority: 0,
			Description: "手机注册激活验证码",
		},
		// 密码重置 - 邮箱链接
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose2, TargetType: entity.TargetTypeEmail, TokenType: entity.TokenTypeLink,
			TokenLength: 32, ExpireMinutes: 60, MaxAttempts: 3,
			RateLimitPerMinute: 1, RateLimitPerHour: 3, RateLimitPerDay: 10,
			TemplateCode: "email_password_reset", RequireVerification: true, VerificationLevel: 2, Priority: 0,
			Description: "邮箱密码重置链接",
		},
		// 密码重置 - 手机验证码
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose2, TargetType: entity.TargetTypePhone, TokenType: entity.TokenTypeCode,
			TokenLength: 6, ExpireMinutes: 10, MaxAttempts: 3,
			RateLimitPerMinute: 1, RateLimitPerHour: 3, RateLimitPerDay: 10,
			TemplateCode: "sms_password_reset", RequireVerification: true, VerificationLevel: 2, Priority: 0,
			Description: "手机密码重置验证码",
		},
		// 登录验证 - 手机验证码
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose3, TargetType: entity.TargetTypePhone, TokenType: entity.TokenTypeCode,
			TokenLength: 6, ExpireMinutes: 5, MaxAttempts: 3,
			RateLimitPerMinute: 1, RateLimitPerHour: 10, RateLimitPerDay: 100,
			TemplateCode: "sms_login_verification", RequireVerification: true, VerificationLevel: 1, Priority: 0,
			Description: "手机登录验证码",
		},
		// MFA验证 - MFA验证码
		{
			ConfigMode: entity.ConfigModeStatic, Purpose: &purpose4, TargetType: entity.TargetTypeMFA, TokenType: entity.TokenTypeCode,
			TokenLength: 6, ExpireMinutes: 5, MaxAttempts: 3,
			RateLimitPerMinute: 1, RateLimitPerHour: 20, RateLimitPerDay: 200,
			TemplateCode: "mfa_verification", RequireVerification: true, VerificationLevel: 3, Priority: 0,
			Description: "MFA多因素认证验证码",
		},
	}
}
