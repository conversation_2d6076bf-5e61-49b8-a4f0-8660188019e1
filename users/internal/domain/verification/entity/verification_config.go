package entity

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/id"
)

// ConfigMode 配置模式
type ConfigMode string

const (
	ConfigModeStatic  ConfigMode = "static"  // 静态配置
	ConfigModeDynamic ConfigMode = "dynamic" // 动态策略
)

// VerificationConfig 统一验证配置实体
type VerificationConfig struct {
	ID       int64 `json:"id" gorm:"primaryKey"`
	TenantID int64 `json:"tenant_id" gorm:"not null;index"`

	// 配置模式
	ConfigMode ConfigMode `json:"config_mode" gorm:"type:enum('static','dynamic');not null;default:'static'"`

	// 静态配置标识
	Purpose    *Purpose   `json:"purpose" gorm:"index"`
	TargetType TargetType `json:"target_type" gorm:"not null"`

	// 动态策略标识
	BusinessScene     *string `json:"business_scene" gorm:"size:100;index"`
	JudgmentDimension *string `json:"judgment_dimension" gorm:"size:100"`
	ConditionExpr     *string `json:"condition_expr" gorm:"type:text"`

	// 验证配置
	TokenType     TokenType `json:"token_type" gorm:"not null"`
	TokenLength   int       `json:"token_length" gorm:"not null;default:6"`
	ExpireMinutes int       `json:"expire_minutes" gorm:"not null;default:30"`
	MaxAttempts   int       `json:"max_attempts" gorm:"not null;default:5"`

	// 频率限制
	RateLimitPerMinute int `json:"rate_limit_per_minute" gorm:"not null;default:3"`
	RateLimitPerHour   int `json:"rate_limit_per_hour" gorm:"not null;default:10"`
	RateLimitPerDay    int `json:"rate_limit_per_day" gorm:"not null;default:50"`

	// 模板配置
	TemplateCode string `json:"template_code" gorm:"not null;size:100"`

	// 策略控制
	RequireVerification bool `json:"require_verification" gorm:"not null;default:true"`
	VerificationLevel   int  `json:"verification_level" gorm:"not null;default:1"`
	Priority            int  `json:"priority" gorm:"not null;default:0"`

	// 状态控制
	IsActive    bool       `json:"is_active" gorm:"default:true;index"`
	Description *string    `json:"description" gorm:"type:text"`
	DeletedAt   *time.Time `json:"deleted_at" gorm:"index"`

	// 基础字段
	CreatedAt time.Time `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (VerificationConfig) TableName() string {
	return "verification_configs"
}

// NewStaticConfig 创建静态配置
func NewStaticConfig(tenantID int64, purpose Purpose, targetType TargetType, tokenType TokenType, templateCode string) *VerificationConfig {
	return &VerificationConfig{
		TenantID:            tenantID,
		ConfigMode:          ConfigModeStatic,
		Purpose:             &purpose,
		TargetType:          targetType,
		TokenType:           tokenType,
		TokenLength:         6,
		ExpireMinutes:       30,
		MaxAttempts:         5,
		RateLimitPerMinute:  3,
		RateLimitPerHour:    10,
		RateLimitPerDay:     50,
		TemplateCode:        templateCode,
		RequireVerification: true,
		VerificationLevel:   1,
		Priority:            0,
		IsActive:            true,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}
}

// NewDynamicConfig 创建动态策略
func NewDynamicConfig(tenantID int64, businessScene, judgmentDimension, conditionExpr string, targetType TargetType, tokenType TokenType, templateCode string) *VerificationConfig {
	return &VerificationConfig{
		TenantID:            tenantID,
		ConfigMode:          ConfigModeDynamic,
		BusinessScene:       &businessScene,
		JudgmentDimension:   &judgmentDimension,
		ConditionExpr:       &conditionExpr,
		TargetType:          targetType,
		TokenType:           tokenType,
		TokenLength:         6,
		ExpireMinutes:       30,
		MaxAttempts:         5,
		RateLimitPerMinute:  3,
		RateLimitPerHour:    10,
		RateLimitPerDay:     50,
		TemplateCode:        templateCode,
		RequireVerification: true,
		VerificationLevel:   1,
		Priority:            0,
		IsActive:            true,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}
}

// NewVerificationConfig 创建新的验证配置 (保持向后兼容)
func NewVerificationConfig(tenantID int64, purpose Purpose, targetType TargetType, tokenType TokenType, templateCode string) *VerificationConfig {
	return NewStaticConfig(tenantID, purpose, targetType, tokenType, templateCode)
}

// NewVerificationConfigWithContext 使用分布式ID创建静态配置
func NewStaticConfigWithContext(ctx context.Context, tenantID int64, purpose Purpose, targetType TargetType, tokenType TokenType, templateCode string) (*VerificationConfig, error) {
	config := NewStaticConfig(tenantID, purpose, targetType, tokenType, templateCode)
	generatedID := id.GenerateID()
	config.ID = generatedID
	return config, nil
}

// NewDynamicConfigWithContext 使用分布式ID创建动态配置
func NewDynamicConfigWithContext(ctx context.Context, tenantID int64, businessScene, judgmentDimension, conditionExpr string, targetType TargetType, tokenType TokenType, templateCode string) (*VerificationConfig, error) {
	config := NewDynamicConfig(tenantID, businessScene, judgmentDimension, conditionExpr, targetType, tokenType, templateCode)
	config.ID = id.GenerateID()
	return config, nil
}

// NewVerificationConfigWithContext 使用分布式ID创建验证配置 (保持向后兼容)
func NewVerificationConfigWithContext(ctx context.Context, tenantID int64, purpose Purpose, targetType TargetType, tokenType TokenType, templateCode string) (*VerificationConfig, error) {
	return NewStaticConfigWithContext(ctx, tenantID, purpose, targetType, tokenType, templateCode)
}

// IsEnabled 检查配置是否启用
func (vc *VerificationConfig) IsEnabled() bool {
	return vc.IsActive
}

// Enable 启用配置
func (vc *VerificationConfig) Enable() {
	vc.IsActive = true
	vc.UpdatedAt = time.Now()
}

// Disable 禁用配置
func (vc *VerificationConfig) Disable() {
	vc.IsActive = false
	vc.UpdatedAt = time.Now()
}

// UpdateTokenStrategy 更新令牌策略
func (vc *VerificationConfig) UpdateTokenStrategy(tokenType TokenType, tokenLength, expireMinutes, maxAttempts int) {
	vc.TokenType = tokenType
	vc.TokenLength = tokenLength
	vc.ExpireMinutes = expireMinutes
	vc.MaxAttempts = maxAttempts
	vc.UpdatedAt = time.Now()
}

// UpdateRateLimit 更新频率限制
func (vc *VerificationConfig) UpdateRateLimit(perMinute, perHour, perDay int) {
	vc.RateLimitPerMinute = perMinute
	vc.RateLimitPerHour = perHour
	vc.RateLimitPerDay = perDay
	vc.UpdatedAt = time.Now()
}

// UpdateTemplate 更新模板配置
func (vc *VerificationConfig) UpdateTemplate(templateCode string) {
	vc.TemplateCode = templateCode
	vc.UpdatedAt = time.Now()
}

// GetPurposeName 获取用途名称
func (vc *VerificationConfig) GetPurposeName() string {
	if vc.Purpose == nil {
		return "动态策略"
	}
	switch *vc.Purpose {
	case PurposeRegistration:
		return "注册激活"
	case PurposePasswordReset:
		return "密码重置"
	case PurposeEmailChange:
		return "邮箱变更"
	case PurposePhoneChange:
		return "手机变更"
	case PurposeLoginVerify:
		return "登录验证"
	case PurposeMFAVerify:
		return "MFA验证"
	default:
		return "未知"
	}
}

// GetTargetTypeName 获取目标类型名称
func (vc *VerificationConfig) GetTargetTypeName() string {
	switch vc.TargetType {
	case TargetTypeEmail:
		return "邮箱"
	case TargetTypePhone:
		return "手机号"
	case TargetTypeMFA:
		return "MFA"
	default:
		return "未知"
	}
}

// GetTokenTypeName 获取令牌类型名称
func (vc *VerificationConfig) GetTokenTypeName() string {
	switch vc.TokenType {
	case TokenTypeLink:
		return "链接"
	case TokenTypeCode:
		return "验证码"
	default:
		return "未知"
	}
}

// Validate 验证配置的有效性
func (vc *VerificationConfig) Validate() error {
	if vc.TenantID <= 0 {
		return fmt.Errorf("tenant_id must be positive")
	}

	// 验证配置模式
	if vc.ConfigMode != ConfigModeStatic && vc.ConfigMode != ConfigModeDynamic {
		return fmt.Errorf("invalid config_mode: %s", vc.ConfigMode)
	}

	// 静态配置验证
	if vc.ConfigMode == ConfigModeStatic {
		if vc.Purpose == nil {
			return fmt.Errorf("purpose is required for static config")
		}
		if *vc.Purpose < PurposeRegistration || *vc.Purpose > PurposeMFAVerify {
			return fmt.Errorf("invalid purpose: %d", *vc.Purpose)
		}
		// 静态配置不应该有动态字段
		if vc.BusinessScene != nil || vc.JudgmentDimension != nil || vc.ConditionExpr != nil {
			return fmt.Errorf("static config should not have dynamic fields")
		}
	}

	// 动态配置验证
	if vc.ConfigMode == ConfigModeDynamic {
		if vc.BusinessScene == nil || *vc.BusinessScene == "" {
			return fmt.Errorf("business_scene is required for dynamic config")
		}
		if vc.JudgmentDimension == nil || *vc.JudgmentDimension == "" {
			return fmt.Errorf("judgment_dimension is required for dynamic config")
		}
		if vc.ConditionExpr == nil || *vc.ConditionExpr == "" {
			return fmt.Errorf("condition_expr is required for dynamic config")
		}
		// 动态配置不应该有purpose字段
		if vc.Purpose != nil {
			return fmt.Errorf("dynamic config should not have purpose field")
		}
	}

	if vc.TargetType < TargetTypeEmail || vc.TargetType > TargetTypeMFA {
		return fmt.Errorf("invalid target_type: %d", vc.TargetType)
	}

	if vc.TokenType < TokenTypeLink || vc.TokenType > TokenTypeCode {
		return fmt.Errorf("invalid token_type: %d", vc.TokenType)
	}

	if vc.TokenLength <= 0 || vc.TokenLength > 32 {
		return fmt.Errorf("token_length must be between 1 and 32")
	}

	if vc.ExpireMinutes <= 0 || vc.ExpireMinutes > 1440 { // 最大24小时
		return fmt.Errorf("expire_minutes must be between 1 and 1440")
	}

	if vc.MaxAttempts <= 0 || vc.MaxAttempts > 10 {
		return fmt.Errorf("max_attempts must be between 1 and 10")
	}

	if vc.RateLimitPerMinute < 0 || vc.RateLimitPerMinute > 60 {
		return fmt.Errorf("rate_limit_per_minute must be between 0 and 60")
	}

	if vc.RateLimitPerHour < 0 || vc.RateLimitPerHour > 3600 {
		return fmt.Errorf("rate_limit_per_hour must be between 0 and 3600")
	}

	if vc.RateLimitPerDay < 0 || vc.RateLimitPerDay > 86400 {
		return fmt.Errorf("rate_limit_per_day must be between 0 and 86400")
	}

	if vc.TemplateCode == "" {
		return fmt.Errorf("template_code cannot be empty")
	}

	if vc.VerificationLevel < 1 || vc.VerificationLevel > 3 {
		return fmt.Errorf("verification_level must be between 1 and 3")
	}

	return nil
}

// Clone 克隆配置（用于创建租户特定配置）
func (vc *VerificationConfig) Clone(newTenantID int64) *VerificationConfig {
	cloned := &VerificationConfig{
		TenantID:            newTenantID,
		ConfigMode:          vc.ConfigMode,
		TargetType:          vc.TargetType,
		TokenType:           vc.TokenType,
		TokenLength:         vc.TokenLength,
		ExpireMinutes:       vc.ExpireMinutes,
		MaxAttempts:         vc.MaxAttempts,
		RateLimitPerMinute:  vc.RateLimitPerMinute,
		RateLimitPerHour:    vc.RateLimitPerHour,
		RateLimitPerDay:     vc.RateLimitPerDay,
		TemplateCode:        vc.TemplateCode,
		RequireVerification: vc.RequireVerification,
		VerificationLevel:   vc.VerificationLevel,
		Priority:            vc.Priority,
		IsActive:            vc.IsActive,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// 克隆指针字段
	if vc.Purpose != nil {
		purpose := *vc.Purpose
		cloned.Purpose = &purpose
	}
	if vc.BusinessScene != nil {
		scene := *vc.BusinessScene
		cloned.BusinessScene = &scene
	}
	if vc.JudgmentDimension != nil {
		dimension := *vc.JudgmentDimension
		cloned.JudgmentDimension = &dimension
	}
	if vc.ConditionExpr != nil {
		expr := *vc.ConditionExpr
		cloned.ConditionExpr = &expr
	}
	if vc.Description != nil {
		desc := *vc.Description
		cloned.Description = &desc
	}

	return cloned
}

// IsCompatibleWith 检查配置是否与指定参数兼容
func (vc *VerificationConfig) IsCompatibleWith(purpose Purpose, targetType TargetType) bool {
	if !vc.IsActive {
		return false
	}

	// 静态配置检查purpose匹配
	if vc.ConfigMode == ConfigModeStatic {
		return vc.Purpose != nil && *vc.Purpose == purpose && vc.TargetType == targetType
	}

	// 动态配置只检查目标类型
	return vc.TargetType == targetType
}

// IsStaticConfig 判断是否为静态配置
func (vc *VerificationConfig) IsStaticConfig() bool {
	return vc.ConfigMode == ConfigModeStatic
}

// IsDynamicConfig 判断是否为动态策略
func (vc *VerificationConfig) IsDynamicConfig() bool {
	return vc.ConfigMode == ConfigModeDynamic
}

// GetBusinessScene 获取业务场景
func (vc *VerificationConfig) GetBusinessScene() string {
	if vc.BusinessScene == nil {
		return ""
	}
	return *vc.BusinessScene
}

// GetJudgmentDimension 获取判定维度
func (vc *VerificationConfig) GetJudgmentDimension() string {
	if vc.JudgmentDimension == nil {
		return ""
	}
	return *vc.JudgmentDimension
}

// GetConditionExpr 获取条件表达式
func (vc *VerificationConfig) GetConditionExpr() string {
	if vc.ConditionExpr == nil {
		return ""
	}
	return *vc.ConditionExpr
}

// GetDescription 获取描述
func (vc *VerificationConfig) GetDescription() string {
	if vc.Description == nil {
		return ""
	}
	return *vc.Description
}

// UpdateDescription 更新描述
func (vc *VerificationConfig) UpdateDescription(description string) {
	vc.Description = &description
	vc.UpdatedAt = time.Now()
}

// UpdatePriority 更新优先级
func (vc *VerificationConfig) UpdatePriority(priority int) {
	vc.Priority = priority
	vc.UpdatedAt = time.Now()
}

// UpdateVerificationLevel 更新验证级别
func (vc *VerificationConfig) UpdateVerificationLevel(level int) {
	vc.VerificationLevel = level
	vc.UpdatedAt = time.Now()
}

// SoftDelete 软删除
func (vc *VerificationConfig) SoftDelete() {
	now := time.Now()
	vc.DeletedAt = &now
	vc.IsActive = false
	vc.UpdatedAt = now
}

// GetExpirationDuration 获取过期时间间隔
func (vc *VerificationConfig) GetExpirationDuration() time.Duration {
	return time.Duration(vc.ExpireMinutes) * time.Minute
}

// ShouldUseCode 是否应该使用验证码
func (vc *VerificationConfig) ShouldUseCode() bool {
	return vc.TokenType == TokenTypeCode
}

// ShouldUseLink 是否应该使用链接
func (vc *VerificationConfig) ShouldUseLink() bool {
	return vc.TokenType == TokenTypeLink
}
