package entity

import (
	"errors"
	"time"
)

// VerificationPolicy 验证策略实体
type VerificationPolicy struct {
	ID                 int64
	TenantID           int64
	Scene              string
	Dimension          string
	ConditionExpr      string
	NeedVerification   bool
	VerificationLevel  string
	TargetType         int
	TokenType          int
	TokenLength        int
	ExpireMinutes      int
	MaxAttempts        int
	RateLimitPerMinute int
	RateLimitPerHour   int
	RateLimitPerDay    int
	TemplateCode       string
	IsActive           bool
	Priority           int
	Description        string
	CreatedAt          time.Time
	UpdatedAt          time.Time
}

// TableName 指定表名
func (VerificationPolicy) TableName() string {
	return "verification_policies"
}

// 验证策略相关常量
const (
	// 业务场景
	SceneLogin    = "login"
	SceneRegister = "register"
	SceneReset    = "reset"
	SceneTransfer = "transfer"

	// 判定维度
	DimensionIP       = "ip"
	DimensionUser     = "user"
	DimensionDevice   = "device"
	DimensionLocation = "location"

	// 验证级别
	LevelLow    = "low"
	LevelMedium = "medium"
	LevelHigh   = "high"

	// 验证目标类型（策略用）
	PolicyTargetEmail = 1
	PolicyTargetPhone = 2
	PolicyTargetMFA   = 3

	// 验证码类型（策略用）
	PolicyTokenTypeLink   = 1
	PolicyTokenTypeDigits = 2
)

// Validate 验证策略实体的有效性
func (p *VerificationPolicy) Validate() error {
	if p.TenantID <= 0 {
		return errors.New("tenant_id must be positive")
	}

	if p.Scene == "" {
		return errors.New("scene cannot be empty")
	}

	if p.ConditionExpr == "" {
		return errors.New("condition_expr cannot be empty")
	}

	if p.TokenLength <= 0 {
		return errors.New("token_length must be positive")
	}

	if p.ExpireMinutes <= 0 {
		return errors.New("expire_minutes must be positive")
	}

	if p.MaxAttempts <= 0 {
		return errors.New("max_attempts must be positive")
	}

	return nil
}
