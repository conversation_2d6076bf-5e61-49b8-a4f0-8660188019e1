package entity

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math/big"
	"time"

	"platforms-pkg/id"
)

// TokenType 令牌类型
type TokenType uint8

const (
	TokenTypeLink   TokenType = 1 // 链接
	TokenTypeCode   TokenType = 2 // 验证码
	TokenTypeHybrid TokenType = 3 // 链接+验证码混合模式
)

// TargetType 目标类型
type TargetType uint8

const (
	TargetTypeEmail TargetType = 1 // 邮箱
	TargetTypePhone TargetType = 2 // 手机号
	TargetTypeMFA   TargetType = 3 // MFA
)

// Purpose 验证用途
type Purpose uint8

const (
	PurposeRegistration  Purpose = 1 // 注册激活
	PurposePasswordReset Purpose = 2 // 密码重置
	PurposeEmailChange   Purpose = 3 // 邮箱变更
	PurposePhoneChange   Purpose = 4 // 手机变更
	PurposeLoginVerify   Purpose = 5 // 登录验证
	PurposeMFAVerify     Purpose = 6 // MFA验证
)

// TokenStatus 令牌状态
type TokenStatus uint8

const (
	TokenStatusUnused  TokenStatus = 1 // 未使用
	TokenStatusUsed    TokenStatus = 2 // 已使用
	TokenStatusExpired TokenStatus = 3 // 已过期
	TokenStatusRevoked TokenStatus = 4 // 已撤销
)

// VerificationToken 验证令牌实体
type VerificationToken struct {
	ID       int64  `json:"id" gorm:"primaryKey"`
	TenantID int64  `json:"tenant_id" gorm:"not null;index"`
	UserID   *int64 `json:"user_id" gorm:"index"` // 可为空，如注册时

	// 验证信息
	Token      string     `json:"token" gorm:"uniqueIndex;not null;size:255"`
	TokenType  TokenType  `json:"token_type" gorm:"not null"`
	Target     string     `json:"target" gorm:"not null;size:255;index:idx_target"`
	TargetType TargetType `json:"target_type" gorm:"not null;index:idx_target"`

	// 业务信息
	Purpose      Purpose `json:"purpose" gorm:"not null;index"`
	TemplateCode string  `json:"template_code" gorm:"not null;size:100"`

	// 状态管理
	Status    TokenStatus `json:"status" gorm:"default:1;index"`
	ExpiresAt time.Time   `json:"expires_at" gorm:"not null;index"`
	UsedAt    *time.Time  `json:"used_at"`
	RevokedAt *time.Time  `json:"revoked_at"`

	// 安全信息
	IPAddress    string `json:"ip_address" gorm:"size:45"`
	UserAgent    string `json:"user_agent" gorm:"type:text"`
	AttemptCount int    `json:"attempt_count" gorm:"default:0"`
	MaxAttempts  int    `json:"max_attempts" gorm:"default:5"`

	// 基础字段
	CreatedAt time.Time `json:"created_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (VerificationToken) TableName() string {
	return "verification_tokens"
}

// NewVerificationToken 创建新的验证令牌
func NewVerificationToken(tenantID int64, userID *int64, target string, targetType TargetType, purpose Purpose, templateCode string) *VerificationToken {
	return &VerificationToken{
		TenantID:     tenantID,
		UserID:       userID,
		Target:       target,
		TargetType:   targetType,
		Purpose:      purpose,
		TemplateCode: templateCode,
		Status:       TokenStatusUnused,
		AttemptCount: 0,
		MaxAttempts:  5,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
}

// NewVerificationTokenWithContext 使用分布式ID创建验证令牌
func NewVerificationTokenWithContext(ctx context.Context, tenantID int64, userID *int64, target string, targetType TargetType, purpose Purpose, templateCode string) (*VerificationToken, error) {
	token := NewVerificationToken(tenantID, userID, target, targetType, purpose, templateCode)

	// 使用雪花算法生成分布式ID
	token.ID = id.GenerateID()

	return token, nil
}

// GenerateToken 生成令牌内容
func (vt *VerificationToken) GenerateToken(tokenType TokenType, length int) error {
	vt.TokenType = tokenType

	switch tokenType {
	case TokenTypeLink:
		// 生成32位随机字符串作为链接令牌
		token, err := generateRandomString(32)
		if err != nil {
			return fmt.Errorf("failed to generate link token: %w", err)
		}
		vt.Token = token
	case TokenTypeCode:
		// 生成指定长度的数字验证码
		code, err := generateRandomCode(length)
		if err != nil {
			return fmt.Errorf("failed to generate verification code: %w", err)
		}
		vt.Token = code
	case TokenTypeHybrid:
		// 混合模式：生成链接令牌，验证码通过算法从链接令牌中提取
		linkToken, err := generateRandomString(32)
		if err != nil {
			return fmt.Errorf("failed to generate link token for hybrid mode: %w", err)
		}

		// 使用链接令牌作为主令牌
		vt.Token = linkToken
	default:
		return fmt.Errorf("unsupported token type: %d", tokenType)
	}

	return nil
}

// SetExpiration 设置过期时间
func (vt *VerificationToken) SetExpiration(minutes int) {
	vt.ExpiresAt = time.Now().Add(time.Duration(minutes) * time.Minute)
}

// IsExpired 检查是否已过期
func (vt *VerificationToken) IsExpired() bool {
	return time.Now().After(vt.ExpiresAt)
}

// IsUsed 检查是否已使用
func (vt *VerificationToken) IsUsed() bool {
	return vt.Status == TokenStatusUsed
}

// IsRevoked 检查是否已撤销
func (vt *VerificationToken) IsRevoked() bool {
	return vt.Status == TokenStatusRevoked
}

// IsValid 检查令牌是否有效
func (vt *VerificationToken) IsValid() bool {
	return vt.Status == TokenStatusUnused && !vt.IsExpired()
}

// CanAttempt 检查是否可以尝试验证
func (vt *VerificationToken) CanAttempt() bool {
	return vt.IsValid() && vt.AttemptCount < vt.MaxAttempts
}

// IncrementAttempt 增加尝试次数
func (vt *VerificationToken) IncrementAttempt() {
	vt.AttemptCount++
	vt.UpdatedAt = time.Now()
}

// MarkAsUsed 标记为已使用
func (vt *VerificationToken) MarkAsUsed() {
	vt.Status = TokenStatusUsed
	now := time.Now()
	vt.UsedAt = &now
	vt.UpdatedAt = now
}

// MarkAsExpired 标记为已过期
func (vt *VerificationToken) MarkAsExpired() {
	vt.Status = TokenStatusExpired
	vt.UpdatedAt = time.Now()
}

// Revoke 撤销令牌
func (vt *VerificationToken) Revoke() {
	vt.Status = TokenStatusRevoked
	now := time.Now()
	vt.RevokedAt = &now
	vt.UpdatedAt = now
}

// GetStatusName 获取状态名称
func (vt *VerificationToken) GetStatusName() string {
	switch vt.Status {
	case TokenStatusUnused:
		return "未使用"
	case TokenStatusUsed:
		return "已使用"
	case TokenStatusExpired:
		return "已过期"
	case TokenStatusRevoked:
		return "已撤销"
	default:
		return "未知"
	}
}

// GetPurposeName 获取用途名称
func (vt *VerificationToken) GetPurposeName() string {
	switch vt.Purpose {
	case PurposeRegistration:
		return "注册激活"
	case PurposePasswordReset:
		return "密码重置"
	case PurposeEmailChange:
		return "邮箱变更"
	case PurposePhoneChange:
		return "手机变更"
	case PurposeLoginVerify:
		return "登录验证"
	case PurposeMFAVerify:
		return "MFA验证"
	default:
		return "未知"
	}
}

// GetTargetTypeName 获取目标类型名称
func (vt *VerificationToken) GetTargetTypeName() string {
	switch vt.TargetType {
	case TargetTypeEmail:
		return "邮箱"
	case TargetTypePhone:
		return "手机号"
	case TargetTypeMFA:
		return "MFA"
	default:
		return "未知"
	}
}

// GetTokenTypeName 获取令牌类型名称
func (vt *VerificationToken) GetTokenTypeName() string {
	switch vt.TokenType {
	case TokenTypeLink:
		return "链接"
	case TokenTypeCode:
		return "验证码"
	case TokenTypeHybrid:
		return "链接+验证码"
	default:
		return "未知"
	}
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateRandomCode 生成随机数字验证码
func generateRandomCode(length int) (string, error) {
	if length <= 0 {
		return "", fmt.Errorf("code length must be positive")
	}

	// 计算最大值
	max := big.NewInt(1)
	for i := 0; i < length; i++ {
		max.Mul(max, big.NewInt(10))
	}
	max.Sub(max, big.NewInt(1))

	// 生成随机数
	n, err := rand.Int(rand.Reader, max)
	if err != nil {
		return "", err
	}

	// 格式化为指定长度的字符串
	format := fmt.Sprintf("%%0%dd", length)
	return fmt.Sprintf(format, n), nil
}
