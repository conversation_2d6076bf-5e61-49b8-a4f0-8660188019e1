# Users模块自定义错误处理

## 概述

Users模块实现了统一的自定义错误处理系统，提供了结构化的错误码和错误消息，确保API响应的一致性和可维护性。

## 错误码范围

- **用户模块错误码范围**: 110000-119999
- **认证相关错误**: 110000-110099
- **注册相关错误**: 110100-110199
- **用户信息相关错误**: 110200-110299
- **权限相关错误**: 110300-110399
- **租户相关错误**: 110400-110499
- **第三方认证相关错误**: 110500-110599
- **文件上传相关错误**: 110600-110699
- **系统错误**: 110900-110999

## 核心组件

### 1. UserError 结构体

```go
type UserError struct {
    Code    int    `json:"code"`              // 错误码
    Message string `json:"message"`           // 错误消息
    Details string `json:"details,omitempty"` // 错误详情
}
```

### 2. 错误创建函数

```go
// 基础错误创建
NewUserError(code int, details ...string) *UserError

// 便捷错误创建函数
NewUserNotFoundError(userID interface{}) *UserError
NewInvalidCredentialsError(username string) *UserError
NewAccountLockedError(reason string) *UserError
NewAccountDisabledError(reason string) *UserError
NewPermissionDeniedError(operation, resource string) *UserError
NewTenantNotFoundError(tenantCode string) *UserError
// ... 更多便捷函数
```

## 使用示例

### 1. 在应用服务中使用

```go
package service

import (
    userErrors "platforms-user/internal/domain/errors"
)

func (s *UserApplicationService) GetUser(ctx context.Context, id int64) (*userEntity.User, error) {
    user, err := s.userRepo.FindByID(ctx, id)
    if err != nil {
        return nil, userErrors.NewDatabaseError("get user by ID", err.Error())
    }
    if user == nil {
        return nil, userErrors.NewUserNotFoundError(id)
    }
    return user, nil
}

func (s *AuthApplicationService) Login(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
    // 检查登录限制
    if err := s.checkLoginLimits(ctx, loginDTO.Username, ipAddress); err != nil {
        return nil, err
    }
    
    // 验证用户
    user, err := s.userRepo.FindByUsername(ctx, tenantID, loginDTO.Username)
    if err != nil {
        return nil, userErrors.NewDatabaseError("user lookup", err.Error())
    }
    if user == nil {
        return nil, userErrors.NewInvalidCredentialsError(loginDTO.Username)
    }
    
    // 检查用户状态
    if user.Status == userValueObject.UserStatusLocked {
        return nil, userErrors.NewAccountLockedError(user.LockReason)
    }
    
    // 验证密码
    if !user.Password.Verify(loginDTO.Password) {
        return nil, userErrors.NewInvalidCredentialsError(loginDTO.Username)
    }
    
    // ... 其他逻辑
}
```

### 2. 在HTTP处理器中使用

```go
package handlers

import (
    "github.com/gin-gonic/gin"
    userErrors "platforms-user/internal/domain/errors"
)

func (h *AuthHandler) Login(c *gin.Context) {
    result, err := h.authService.Login(c.Request.Context(), &req, ipAddress, userAgent)
    if err != nil {
        // 使用统一的错误处理函数
        HandleUserError(c, err)
        return
    }
    
    commonResponse.Success(c, result)
}

func (h *UserHandler) GetUser(c *gin.Context) {
    user, err := h.userService.GetUser(c.Request.Context(), id)
    if err != nil {
        // 使用统一的错误处理函数
        HandleUserError(c, err)
        return
    }
    
    commonResponse.Success(c, user)
}
```

### 3. 错误处理函数

```go
// HandleUserError 处理用户模块自定义错误
func HandleUserError(c *gin.Context, err error) {
    if userErr, ok := err.(*userErrors.UserError); ok {
        // 根据错误码返回相应的HTTP响应
        switch userErr.Code {
        case userErrors.CodeUserNotFound:
            commonResponse.NotFound(c, "用户")
        case userErrors.CodeInvalidCredentials:
            commonResponse.Error(c, commonResponse.CodeInvalidCredentials, userErr.Message)
        case userErrors.CodeAccountLocked:
            commonResponse.Error(c, commonResponse.CodeAccountLocked, userErr.Message)
        case userErrors.CodePermissionDenied:
            commonResponse.Error(c, commonResponse.CodePermissionDenied, userErr.Message)
        // ... 更多错误码处理
        default:
            commonResponse.Error(c, commonResponse.CodeBusinessLogicError, userErr.Message)
        }
    } else {
        // 非自定义错误，使用通用错误处理
        commonResponse.InternalError(c, err)
    }
}
```

## 错误码映射

### 认证相关错误
- `CodeUserNotFound` (110000): 用户不存在
- `CodeInvalidCredentials` (110007): 用户名或密码错误
- `CodeAccountLocked` (110013): 账户被锁定
- `CodeAccountDisabled` (110014): 账户被禁用
- `CodeLoginAttemptsExceeded` (110015): 登录尝试次数超限
- `CodeIPBlocked` (110016): IP被阻止
- `CodeMFARequired` (110029): 需要多因素认证
- `CodeCaptchaRequired` (110032): 需要验证码

### 注册相关错误
- `CodeUsernameExists` (110108): 用户名已存在
- `CodeEmailExists` (110109): 邮箱已存在
- `CodePhoneExists` (110110): 手机号已存在
- `CodeInvitationRequired` (110101): 需要邀请码
- `CodeInvitationInvalid` (110102): 邀请码无效

### 权限相关错误
- `CodePermissionDenied` (110300): 权限不足
- `CodeRoleNotFound` (110301): 角色不存在
- `CodeResourceNotFound` (110308): 资源不存在
- `CodeOperationNotAllowed` (110310): 操作不被允许

### 系统错误
- `CodeSystemError` (110900): 系统错误
- `CodeDatabaseError` (110901): 数据库错误
- `CodeThirdPartyError` (110907): 第三方服务错误
- `CodeServiceUnavailable` (110904): 服务不可用

## 最佳实践

### 1. 错误创建
- 优先使用便捷的错误创建函数
- 提供有意义的错误详情
- 避免暴露敏感信息

### 2. 错误处理
- 在应用服务层创建自定义错误
- 在HTTP处理器层使用统一的错误处理函数
- 记录详细的错误日志用于调试

### 3. 错误码管理
- 保持错误码的唯一性和一致性
- 按功能模块划分错误码范围
- 定期审查和更新错误码

### 4. 国际化支持
- 错误消息支持多语言
- 根据用户语言环境返回相应消息
- 保持错误码的稳定性

## 扩展指南

### 添加新的错误码

1. 在 `user_errors.go` 中添加错误码常量
2. 在 `errorMessages` 映射中添加错误消息
3. 创建便捷的错误创建函数
4. 在 `HandleUserError` 函数中添加处理逻辑

### 示例

```go
// 1. 添加错误码常量
const (
    CodeNewFeatureError = 110999 // 新功能错误
)

// 2. 添加错误消息
var errorMessages = map[int]string{
    CodeNewFeatureError: "新功能暂时不可用",
}

// 3. 创建便捷函数
func NewNewFeatureError(feature string) *UserError {
    return NewUserError(CodeNewFeatureError, fmt.Sprintf("feature: %s", feature))
}

// 4. 在HandleUserError中添加处理
case userErrors.CodeNewFeatureError:
    commonResponse.Error(c, commonResponse.CodeServiceUnavailable, userErr.Message)
```

## 总结

Users模块的自定义错误处理系统提供了：

1. **结构化错误**: 统一的错误结构和错误码
2. **类型安全**: 编译时错误检查
3. **易于维护**: 集中的错误定义和处理
4. **用户友好**: 清晰的错误消息和详情
5. **可扩展性**: 易于添加新的错误类型
6. **国际化支持**: 支持多语言错误消息

通过使用这个错误处理系统，可以确保API响应的一致性和可维护性，同时提供良好的用户体验。 