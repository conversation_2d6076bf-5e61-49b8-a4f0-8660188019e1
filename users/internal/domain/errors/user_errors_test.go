package errors

import (
	"testing"
)

func TestNewUserError(t *testing.T) {
	tests := []struct {
		name     string
		code     int
		details  []string
		expected *UserError
	}{
		{
			name:    "创建用户不存在错误",
			code:    CodeUserNotFound,
			details: []string{"user_id: 123"},
			expected: &UserError{
				Code:    CodeUserNotFound,
				Message: "用户不存在",
				Details: "user_id: 123",
			},
		},
		{
			name:    "创建凭证无效错误",
			code:    CodeInvalidCredentials,
			details: []string{"username: testuser"},
			expected: &UserError{
				Code:    CodeInvalidCredentials,
				Message: "用户名或密码错误",
				Details: "username: testuser",
			},
		},
		{
			name:    "创建账户锁定错误",
			code:    CodeAccountLocked,
			details: []string{"reason: too many failed attempts"},
			expected: &UserError{
				Code:    CodeAccountLocked,
				Message: "用户账户被锁定",
				Details: "reason: too many failed attempts",
			},
		},
		{
			name:    "无详情的错误",
			code:    CodeSystemError,
			details: []string{},
			expected: &UserError{
				Code:    CodeSystemError,
				Message: "系统错误",
				Details: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewUserError(tt.code, tt.details...)
			if result.Code != tt.expected.Code {
				t.Errorf("错误码不匹配，期望 %d，实际 %d", tt.expected.Code, result.Code)
			}
			if result.Message != tt.expected.Message {
				t.Errorf("错误消息不匹配，期望 %s，实际 %s", tt.expected.Message, result.Message)
			}
			if result.Details != tt.expected.Details {
				t.Errorf("错误详情不匹配，期望 %s，实际 %s", tt.expected.Details, result.Details)
			}
		})
	}
}

func TestUserError_Error(t *testing.T) {
	tests := []struct {
		name     string
		userErr  *UserError
		expected string
	}{
		{
			name: "有详情的错误",
			userErr: &UserError{
				Code:    CodeUserNotFound,
				Message: "用户不存在",
				Details: "user_id: 123",
			},
			expected: "[110000] 用户不存在: user_id: 123",
		},
		{
			name: "无详情的错误",
			userErr: &UserError{
				Code:    CodeSystemError,
				Message: "系统错误",
				Details: "",
			},
			expected: "[110900] 系统错误",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.userErr.Error()
			if result != tt.expected {
				t.Errorf("错误字符串不匹配，期望 %s，实际 %s", tt.expected, result)
			}
		})
	}
}

func TestUserError_GetCode(t *testing.T) {
	userErr := &UserError{
		Code:    CodeUserNotFound,
		Message: "用户不存在",
		Details: "user_id: 123",
	}

	expected := CodeUserNotFound
	result := userErr.GetCode()

	if result != expected {
		t.Errorf("获取错误码失败，期望 %d，实际 %d", expected, result)
	}
}

func TestUserError_GetMessage(t *testing.T) {
	userErr := &UserError{
		Code:    CodeInvalidCredentials,
		Message: "用户名或密码错误",
		Details: "username: testuser",
	}

	expected := "用户名或密码错误"
	result := userErr.GetMessage()

	if result != expected {
		t.Errorf("获取错误消息失败，期望 %s，实际 %s", expected, result)
	}
}

func TestUserError_GetDetails(t *testing.T) {
	userErr := &UserError{
		Code:    CodeAccountLocked,
		Message: "账户被锁定",
		Details: "reason: too many failed attempts",
	}

	expected := "reason: too many failed attempts"
	result := userErr.GetDetails()

	if result != expected {
		t.Errorf("获取错误详情失败，期望 %s，实际 %s", expected, result)
	}
}

func TestNewUserNotFoundError(t *testing.T) {
	tests := []struct {
		name     string
		userID   interface{}
		expected string
	}{
		{
			name:     "数字用户ID",
			userID:   123,
			expected: "user_id: 123",
		},
		{
			name:     "字符串用户ID",
			userID:   "user123",
			expected: "user_id: user123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewUserNotFoundError(tt.userID)
			if result.Code != CodeUserNotFound {
				t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeUserNotFound, result.Code)
			}
			if result.Details != tt.expected {
				t.Errorf("错误详情不匹配，期望 %s，实际 %s", tt.expected, result.Details)
			}
		})
	}
}

func TestNewInvalidCredentialsError(t *testing.T) {
	username := "testuser"
	result := NewInvalidCredentialsError(username)

	if result.Code != CodeInvalidCredentials {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeInvalidCredentials, result.Code)
	}
	if result.Details != "username: testuser" {
		t.Errorf("错误详情不匹配，期望 username: testuser，实际 %s", result.Details)
	}
}

func TestNewAccountLockedError(t *testing.T) {
	reason := "too many failed attempts"
	result := NewAccountLockedError(reason)

	if result.Code != CodeAccountLocked {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeAccountLocked, result.Code)
	}
	if result.Details != "reason: too many failed attempts" {
		t.Errorf("错误详情不匹配，期望 reason: too many failed attempts，实际 %s", result.Details)
	}
}

func TestNewAccountDisabledError(t *testing.T) {
	reason := "account suspended by admin"
	result := NewAccountDisabledError(reason)

	if result.Code != CodeAccountDisabled {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeAccountDisabled, result.Code)
	}
	if result.Details != "reason: account suspended by admin" {
		t.Errorf("错误详情不匹配，期望 reason: account suspended by admin，实际 %s", result.Details)
	}
}

func TestNewLoginAttemptsExceededError(t *testing.T) {
	attempts := 5
	maxAttempts := 3
	result := NewLoginAttemptsExceededError(attempts, maxAttempts)

	if result.Code != CodeLoginAttemptsExceeded {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeLoginAttemptsExceeded, result.Code)
	}
	if result.Details != "attempts: 5, max: 3" {
		t.Errorf("错误详情不匹配，期望 attempts: 5, max: 3，实际 %s", result.Details)
	}
}

func TestNewIPBlockedError(t *testing.T) {
	ip := "***********"
	reason := "too many failed attempts"
	result := NewIPBlockedError(ip, reason)

	if result.Code != CodeIPBlocked {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeIPBlocked, result.Code)
	}
	if result.Details != "ip: ***********, reason: too many failed attempts" {
		t.Errorf("错误详情不匹配，期望 ip: ***********, reason: too many failed attempts，实际 %s", result.Details)
	}
}

func TestNewPermissionDeniedError(t *testing.T) {
	operation := "delete"
	resource := "user"
	result := NewPermissionDeniedError(operation, resource)

	if result.Code != CodePermissionDenied {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodePermissionDenied, result.Code)
	}
	if result.Details != "operation: delete, resource: user" {
		t.Errorf("错误详情不匹配，期望 operation: delete, resource: user，实际 %s", result.Details)
	}
}

func TestNewTenantNotFoundError(t *testing.T) {
	tenantCode := "test-tenant"
	result := NewTenantNotFoundError(tenantCode)

	if result.Code != CodeTenantNotFound {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeTenantNotFound, result.Code)
	}
	if result.Details != "tenant_code: test-tenant" {
		t.Errorf("错误详情不匹配，期望 tenant_code: test-tenant，实际 %s", result.Details)
	}
}

func TestNewSystemError(t *testing.T) {
	operation := "database connection"
	reason := "connection timeout"
	result := NewSystemError(operation, reason)

	if result.Code != CodeSystemError {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeSystemError, result.Code)
	}
	if result.Details != "operation: database connection, reason: connection timeout" {
		t.Errorf("错误详情不匹配，期望 operation: database connection, reason: connection timeout，实际 %s", result.Details)
	}
}

func TestNewDatabaseError(t *testing.T) {
	operation := "user query"
	reason := "table not found"
	result := NewDatabaseError(operation, reason)

	if result.Code != CodeDatabaseError {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeDatabaseError, result.Code)
	}
	if result.Details != "operation: user query, reason: table not found" {
		t.Errorf("错误详情不匹配，期望 operation: user query, reason: table not found，实际 %s", result.Details)
	}
}

func TestNewThirdPartyError(t *testing.T) {
	service := "email service"
	reason := "service unavailable"
	result := NewThirdPartyError(service, reason)

	if result.Code != CodeThirdPartyError {
		t.Errorf("错误码不匹配，期望 %d，实际 %d", CodeThirdPartyError, result.Code)
	}
	if result.Details != "service: email service, reason: service unavailable" {
		t.Errorf("错误详情不匹配，期望 service: email service, reason: service unavailable，实际 %s", result.Details)
	}
}

func TestErrorMessages(t *testing.T) {
	// 测试一些关键错误消息是否存在
	expectedMessages := map[int]string{
		CodeUserNotFound:        "用户不存在",
		CodeInvalidCredentials:  "用户名或密码错误",
		CodeUserAccountLocked:   "用户账户被锁定",
		CodeUserAccountDisabled: "用户账户被禁用",
		CodePermissionDenied:    "权限不足",
		CodeSystemError:         "系统错误",
		CodeDatabaseError:       "数据库错误",
	}

	for code, expectedMessage := range expectedMessages {
		if message, exists := errorMessages[code]; !exists {
			t.Errorf("错误码 %d 的消息不存在", code)
		} else if message != expectedMessage {
			t.Errorf("错误码 %d 的消息不匹配，期望 %s，实际 %s", code, expectedMessage, message)
		}
	}
}
