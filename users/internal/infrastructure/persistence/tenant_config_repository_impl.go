package persistence

import (
	"context"
	"fmt"
	"platforms-pkg/common"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/user/dto"
	"platforms-user/internal/domain/user/entity"
	"platforms-user/internal/domain/user/repository"

	"gorm.io/gorm"
)

// TenantConfigRepositoryImpl 租户配置仓储实现
type TenantConfigRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewTenantConfigRepository 创建租户配置仓储实例
func NewTenantConfigRepository(db *gorm.DB, logger logiface.Logger) repository.TenantConfigRepository {
	return &TenantConfigRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建配置
func (r *TenantConfigRepositoryImpl) Create(ctx context.Context, config *entity.TenantConfig) error {
	if err := r.db.WithContext(ctx).Create(config).Error; err != nil {
		r.logger.Error(ctx, "Failed to create tenant config", logiface.Error(err), logiface.Any("config", config))
		return fmt.Errorf("创建租户配置失败: %w", err)
	}
	r.logger.Info(ctx, "Tenant config created successfully", logiface.Int64("tenant_id", config.TenantID), logiface.String("config_key", config.ConfigKey))
	return nil
}

// Update 更新配置
func (r *TenantConfigRepositoryImpl) Update(ctx context.Context, config *entity.TenantConfig) error {
	if err := r.db.WithContext(ctx).Save(config).Error; err != nil {
		r.logger.Error(ctx, "Failed to update tenant config", logiface.Error(err), logiface.Any("config", config))
		return fmt.Errorf("更新租户配置失败: %w", err)
	}
	r.logger.Info(ctx, "Tenant config updated successfully", logiface.Int64("tenant_id", config.TenantID), logiface.String("config_key", config.ConfigKey))
	return nil
}

// Delete 删除配置
func (r *TenantConfigRepositoryImpl) Delete(ctx context.Context, tenantID int64, configKey string) error {
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_key = ?", tenantID, configKey).Delete(&entity.TenantConfig{}).Error; err != nil {
		r.logger.Error(ctx, "Failed to delete tenant config", logiface.Error(err), logiface.Int64("tenant_id", tenantID), logiface.String("config_key", configKey))
		return fmt.Errorf("删除租户配置失败: %w", err)
	}
	r.logger.Info(ctx, "Tenant config deleted successfully", logiface.Int64("tenant_id", tenantID), logiface.String("config_key", configKey))
	return nil
}

// FindByKey 根据键查找配置
func (r *TenantConfigRepositoryImpl) FindByKey(ctx context.Context, tenantID int64, configKey string) (*entity.TenantConfig, error) {
	var config entity.TenantConfig
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_key = ?", tenantID, configKey).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find tenant config by key", logiface.Error(err), logiface.Int64("tenant_id", tenantID), logiface.String("config_key", configKey))
		return nil, fmt.Errorf("查找租户配置失败: %w", err)
	}
	return &config, nil
}

// FindByTenantID 根据租户ID查找所有配置
func (r *TenantConfigRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64) ([]*entity.TenantConfig, error) {
	var configs []*entity.TenantConfig
	err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Find(&configs).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find tenant configs by tenant ID", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("查找租户配置失败: %w", err)
	}
	return configs, nil
}

// FindSystemConfig 查找系统配置
func (r *TenantConfigRepositoryImpl) FindSystemConfig(ctx context.Context, configKey string) (*entity.TenantConfig, error) {
	var config entity.TenantConfig
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_key = ?", common.SystemTenantID, configKey).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find system config", logiface.Error(err), logiface.String("config_key", configKey))
		return nil, fmt.Errorf("查找系统配置失败: %w", err)
	}
	return &config, nil
}

// FindAllSystemConfigs 查找所有系统配置
func (r *TenantConfigRepositoryImpl) FindAllSystemConfigs(ctx context.Context) ([]*entity.TenantConfig, error) {
	var configs []*entity.TenantConfig
	err := r.db.WithContext(ctx).Where("tenant_id = ?", common.SystemTenantID).Find(&configs).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find all system configs", logiface.Error(err))
		return nil, fmt.Errorf("查找系统配置失败: %w", err)
	}
	return configs, nil
}

// UpsertConfig 插入或更新配置
func (r *TenantConfigRepositoryImpl) UpsertConfig(ctx context.Context, req *dto.UpsertConfigRequest) error {
	// 使用 ON DUPLICATE KEY UPDATE 语法
	sql := `INSERT INTO system_config (tenant_id, config_key, config_value, config_type, created_at, updated_at) 
			VALUES (?, ?, ?, ?, NOW(), NOW()) 
			ON DUPLICATE KEY UPDATE 
			config_value = VALUES(config_value), 
			config_type = VALUES(config_type), 
			updated_at = NOW()`

	if err := r.db.WithContext(ctx).Exec(sql, req.TenantID, req.ConfigKey, req.ConfigValue, req.ConfigType).Error; err != nil {
		r.logger.Error(ctx, "Failed to upsert tenant config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
		return fmt.Errorf("插入或更新租户配置失败: %w", err)
	}

	r.logger.Info(ctx, "Tenant config upserted successfully", logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
	return nil
}

// CopySystemConfigsToTenant 复制系统配置到租户
func (r *TenantConfigRepositoryImpl) CopySystemConfigsToTenant(ctx context.Context, tenantID int64) error {
	// 获取所有系统配置
	systemConfigs, err := r.FindAllSystemConfigs(ctx)
	if err != nil {
		return err
	}

	// 复制到租户
	for _, systemConfig := range systemConfigs {
		upsertReq := &dto.UpsertConfigRequest{
			TenantID:    tenantID,
			ConfigKey:   systemConfig.ConfigKey,
			ConfigValue: systemConfig.ConfigValue,
			ConfigType:  systemConfig.ConfigType,
		}
		if err := r.UpsertConfig(ctx, upsertReq); err != nil {
			return err
		}
	}

	r.logger.Info(ctx, "System configs copied to tenant successfully", logiface.Int64("tenant_id", tenantID), logiface.Int("config_count", len(systemConfigs)))
	return nil
}

// DeleteTenantConfigs 删除租户所有配置
func (r *TenantConfigRepositoryImpl) DeleteTenantConfigs(ctx context.Context, tenantID int64) error {
	if err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Delete(&entity.TenantConfig{}).Error; err != nil {
		r.logger.Error(ctx, "Failed to delete tenant configs", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return fmt.Errorf("删除租户配置失败: %w", err)
	}
	r.logger.Info(ctx, "Tenant configs deleted successfully", logiface.Int64("tenant_id", tenantID))
	return nil
}

// Exists 检查配置是否存在
func (r *TenantConfigRepositoryImpl) Exists(ctx context.Context, tenantID int64, configKey string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.TenantConfig{}).Where("tenant_id = ? AND config_key = ?", tenantID, configKey).Count(&count).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to check tenant config existence", logiface.Error(err), logiface.Int64("tenant_id", tenantID), logiface.String("config_key", configKey))
		return false, fmt.Errorf("检查租户配置存在性失败: %w", err)
	}
	return count > 0, nil
}

// GetEffectiveConfig 获取有效配置（租户配置优先，回退到系统配置）
func (r *TenantConfigRepositoryImpl) GetEffectiveConfig(ctx context.Context, tenantID int64, configKey string) (*entity.TenantConfig, error) {
	// 先查找租户配置
	tenantConfig, err := r.FindByKey(ctx, tenantID, configKey)
	if err != nil {
		return nil, err
	}

	// 如果租户配置存在，返回租户配置
	if tenantConfig != nil {
		return tenantConfig, nil
	}

	// 否则返回系统配置
	return r.FindSystemConfig(ctx, configKey)
}
