package persistence

import (
	"context"
	"fmt"

	"platforms-pkg/logiface"

	"gorm.io/gorm"

	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"
	"platforms-user/internal/infrastructure/persistence/model"
)

// VerificationPolicyRepositoryImpl 验证策略仓储实现
type VerificationPolicyRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewVerificationPolicyRepository 创建验证策略仓储实例
func NewVerificationPolicyRepository(db *gorm.DB, logger logiface.Logger) repository.VerificationPolicyRepository {
	return &VerificationPolicyRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建策略
func (r *VerificationPolicyRepositoryImpl) Create(ctx context.Context, policy *entity.VerificationPolicy) (*entity.VerificationPolicy, error) {
	policyModel := model.NewVerificationPolicyFromEntity(policy)

	if err := r.db.WithContext(ctx).Create(policyModel).Error; err != nil {
		r.logger.Error(ctx, "创建验证策略失败",
			logiface.Error(err),
			logiface.Int64("tenant_id", policy.TenantID),
			logiface.String("scene", policy.Scene),
			logiface.String("dimension", policy.Dimension))
		return nil, fmt.Errorf("创建验证策略失败: %w", err)
	}

	r.logger.Info(ctx, "验证策略创建成功",
		logiface.Int64("policy_id", policyModel.ID),
		logiface.Int64("tenant_id", policy.TenantID),
		logiface.String("scene", policy.Scene),
		logiface.String("dimension", policy.Dimension))

	return policyModel.ToEntity(), nil
}

// Update 更新策略
func (r *VerificationPolicyRepositoryImpl) Update(ctx context.Context, policy *entity.VerificationPolicy) error {
	policyModel := model.NewVerificationPolicyFromEntity(policy)

	result := r.db.WithContext(ctx).Save(policyModel)
	if result.Error != nil {
		r.logger.Error(ctx, "更新验证策略失败",
			logiface.Error(result.Error),
			logiface.Int64("policy_id", policy.ID))
		return fmt.Errorf("更新验证策略失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("验证策略不存在: %d", policy.ID)
	}

	r.logger.Info(ctx, "验证策略更新成功",
		logiface.Int64("policy_id", policy.ID))

	return nil
}

// Delete 删除策略
func (r *VerificationPolicyRepositoryImpl) Delete(ctx context.Context, id int64, tenantID int64) error {
	result := r.db.WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).Delete(&model.VerificationPolicy{})
	if result.Error != nil {
		r.logger.Error(ctx, "删除验证策略失败",
			logiface.Error(result.Error),
			logiface.Int64("policy_id", id),
			logiface.Int64("tenant_id", tenantID))
		return fmt.Errorf("删除验证策略失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("验证策略不存在: %d", id)
	}

	r.logger.Info(ctx, "验证策略删除成功",
		logiface.Int64("policy_id", id),
		logiface.Int64("tenant_id", tenantID))

	return nil
}

// GetByID 根据ID获取策略
func (r *VerificationPolicyRepositoryImpl) GetByID(ctx context.Context, id int64, tenantID int64) (*entity.VerificationPolicy, error) {
	var policyModel model.VerificationPolicy
	if err := r.db.WithContext(ctx).Where("id = ? AND tenant_id = ?", id, tenantID).First(&policyModel).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "根据ID查找验证策略失败",
			logiface.Error(err),
			logiface.Int64("policy_id", id),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("查找验证策略失败: %w", err)
	}

	return policyModel.ToEntity(), nil
}

// List 获取策略列表
func (r *VerificationPolicyRepositoryImpl) List(ctx context.Context, params *repository.ListPolicyParams) ([]*entity.VerificationPolicy, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.VerificationPolicy{}).Where("tenant_id = ?", params.TenantID)

	// 应用过滤条件
	if params.Scene != "" {
		query = query.Where("scene = ?", params.Scene)
	}
	if params.IsActive != nil {
		query = query.Where("is_active = ?", *params.IsActive)
	}
	if params.Keyword != "" {
		query = query.Where("description LIKE ? OR scene LIKE ?", "%"+params.Keyword+"%", "%"+params.Keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "获取策略总数失败",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, 0, fmt.Errorf("获取策略总数失败: %w", err)
	}

	// 获取分页数据
	var policyModels []*model.VerificationPolicy
	offset := (params.Page - 1) * params.PageSize
	if err := query.Order("priority DESC, created_at DESC").Offset(offset).Limit(params.PageSize).Find(&policyModels).Error; err != nil {
		r.logger.Error(ctx, "获取策略列表失败",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID),
			logiface.Int("page", params.Page),
			logiface.Int("page_size", params.PageSize))
		return nil, 0, fmt.Errorf("获取策略列表失败: %w", err)
	}

	// 转换为实体
	policies := make([]*entity.VerificationPolicy, len(policyModels))
	for i, policyModel := range policyModels {
		policies[i] = policyModel.ToEntity()
	}

	r.logger.Info(ctx, "策略列表获取成功",
		logiface.Int64("tenant_id", params.TenantID),
		logiface.Int("count", len(policies)),
		logiface.Int64("total", total))

	return policies, total, nil
}

// SetStatus 设置策略状态
func (r *VerificationPolicyRepositoryImpl) SetStatus(ctx context.Context, id int64, tenantID int64, enabled bool) error {
	result := r.db.WithContext(ctx).Model(&model.VerificationPolicy{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).Update("is_active", enabled)

	if result.Error != nil {
		r.logger.Error(ctx, "设置策略状态失败",
			logiface.Error(result.Error),
			logiface.Int64("policy_id", id),
			logiface.Int64("tenant_id", tenantID),
			logiface.Bool("enabled", enabled))
		return fmt.Errorf("设置策略状态失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("验证策略不存在: %d", id)
	}

	r.logger.Info(ctx, "策略状态设置成功",
		logiface.Int64("policy_id", id),
		logiface.Int64("tenant_id", tenantID),
		logiface.Bool("enabled", enabled))

	return nil
}

// GetActivePoliciesByScene 根据业务场景获取激活的策略
func (r *VerificationPolicyRepositoryImpl) GetActivePoliciesByScene(ctx context.Context, tenantID int64, businessScene string) ([]*entity.VerificationPolicy, error) {
	var policyModels []*model.VerificationPolicy
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND scene = ? AND is_active = ?", tenantID, businessScene, true).
		Order("priority DESC, created_at ASC").Find(&policyModels).Error; err != nil {
		r.logger.Error(ctx, "获取场景激活策略失败",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("scene", businessScene))
		return nil, fmt.Errorf("获取场景激活策略失败: %w", err)
	}

	// 转换为实体
	policies := make([]*entity.VerificationPolicy, len(policyModels))
	for i, policyModel := range policyModels {
		policies[i] = policyModel.ToEntity()
	}

	r.logger.Info(ctx, "场景激活策略获取成功",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("scene", businessScene),
		logiface.Int("count", len(policies)))

	return policies, nil
}

// ExistsConditionPolicy 检查条件策略是否存在
func (r *VerificationPolicyRepositoryImpl) ExistsConditionPolicy(ctx context.Context, params *repository.ExistsConditionPolicyParams) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.VerificationPolicy{}).
		Where("tenant_id = ? AND scene = ? AND dimension = ?", params.TenantID, params.BusinessScene, params.Dimension)

	// 如果有条件表达式，则检查相同的条件表达式
	if params.ConditionExpression != "" {
		query = query.Where("condition_expr = ?", params.ConditionExpression)
	} else {
		// 如果没有条件表达式，则检查是否存在空的条件表达式
		query = query.Where("(condition_expr = '' OR condition_expr IS NULL)")
	}

	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "检查条件策略是否存在失败",
			logiface.Any("error", err),
			logiface.Int64("tenant_id", params.TenantID),
			logiface.String("scene", params.BusinessScene),
			logiface.String("dimension", params.Dimension))
		return false, fmt.Errorf("检查条件策略失败: %w", err)
	}

	return count > 0, nil
}
