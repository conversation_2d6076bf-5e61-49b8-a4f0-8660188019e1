package persistence

import (
	"context"
	"fmt"
	"time"

	userErrors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/idgenerator/entity"
	"platforms-user/internal/domain/idgenerator/repository"

	"gorm.io/gorm"
)

// SequenceRepositoryImpl 序列仓储实现
type SequenceRepositoryImpl struct {
	db *gorm.DB
}

// NewSequenceRepositoryImpl 创建序列仓储实现
func NewSequenceRepositoryImpl(db *gorm.DB) repository.SequenceRepository {
	return &SequenceRepositoryImpl{
		db: db,
	}
}

// sequenceModel 序列数据模型
type sequenceModel struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement"`
	BusinessType  string    `gorm:"column:business_type;not null;size:50"`
	SequenceName  string    `gorm:"column:sequence_name;not null;size:100"`
	TenantID      int64     `gorm:"column:tenant_id;not null"`
	CurrentValue  int64     `gorm:"column:current_value;not null;default:0"`
	IncrementStep int       `gorm:"column:increment_step;not null;default:1"`
	CacheSize     int       `gorm:"column:cache_size;not null;default:1000"`
	MaxValue      int64     `gorm:"column:max_value;not null;default:0"`
	MinValue      int64     `gorm:"column:min_value;not null;default:1"`
	Threshold     int       `gorm:"column:threshold;not null;default:20"`
	IsActive      bool      `gorm:"column:is_active;not null;default:true"`
	Remarks       string    `gorm:"column:remarks;size:100"`
	CreatedAt     time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt     time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (sequenceModel) TableName() string {
	return "id_sequence"
}

// Create 创建序列
func (r *SequenceRepositoryImpl) Create(ctx context.Context, sequence *entity.Sequence) error {
	// 检查business_type和tenant_id组合是否已存在
	exists, err := r.ExistsByBusinessTypeAndTenant(ctx, sequence.BusinessType, sequence.TenantID)
	if err != nil {
		return err
	}
	if exists {
		return userErrors.NewParameterValidationFailedError("business_type", fmt.Sprintf("business_type: %s, tenant_id: %d", sequence.BusinessType, sequence.TenantID))
	}

	// 使用事务确保原子性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 再次检查是否存在（避免并发创建）
		var count int64
		if err := tx.Model(&sequenceModel{}).
			Where("business_type = ? AND sequence_name = ? AND tenant_id = ?", sequence.BusinessType, sequence.SequenceName, sequence.TenantID).
			Count(&count).Error; err != nil {
			return err
		}

		if count > 0 {
			// 已存在，不创建
			return userErrors.NewParameterValidationFailedError("business_type", fmt.Sprintf("business_type: %s, tenant_id: %d", sequence.BusinessType, sequence.TenantID))
		}

		model := &sequenceModel{
			BusinessType:  sequence.BusinessType,
			SequenceName:  sequence.SequenceName,
			TenantID:      sequence.TenantID,
			CurrentValue:  sequence.CurrentValue,
			IncrementStep: sequence.IncrementStep,
			CacheSize:     sequence.CacheSize,
			MaxValue:      sequence.MaxValue,
			MinValue:      sequence.MinValue,
			Threshold:     sequence.Threshold,
			IsActive:      sequence.IsActive,
			Remarks:       sequence.Remarks,
		}

		if err := tx.Create(model).Error; err != nil {
			return err
		}

		// 设置ID
		sequence.ID = model.ID
		sequence.CreatedAt = model.CreatedAt
		sequence.UpdatedAt = model.UpdatedAt

		// 为新序列创建初始ID段
		if err := r.createInitialSegments(ctx, tx, sequence); err != nil {
			return fmt.Errorf("failed to create initial segments: %w", err)
		}

		return nil
	})
}

// FindByBusinessAndName 根据业务类型和名称查找序列
func (r *SequenceRepositoryImpl) FindByBusinessAndName(ctx context.Context, businessType, sequenceName string, tenantID int64) (*entity.Sequence, error) {
	// 参数验证
	if businessType == "" {
		return nil, userErrors.NewParameterValidationFailedError("business_type", "业务类型不能为空")
	}
	if sequenceName == "" {
		return nil, userErrors.NewParameterValidationFailedError("sequence_name", "序列名称不能为空")
	}
	if tenantID < 0 {
		return nil, userErrors.NewParameterValidationFailedError("tenant_id", "租户ID不能为负数")
	}

	var model sequenceModel
	err := r.db.WithContext(ctx).
		Where("business_type = ? AND sequence_name = ? AND tenant_id = ? AND is_active = ?", businessType, sequenceName, tenantID, true).
		First(&model).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.ErrSequenceNotFound
		}
		return nil, err
	}

	return r.modelToEntity(&model), nil
}

// FindByID 根据ID查找序列
func (r *SequenceRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.Sequence, error) {
	// 参数验证
	if id <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("id", "序列ID必须大于0")
	}

	var model sequenceModel
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.ErrSequenceNotFound
		}
		return nil, err
	}

	return r.modelToEntity(&model), nil
}

// Update 更新序列
func (r *SequenceRepositoryImpl) Update(ctx context.Context, sequence *entity.Sequence) error {
	// 检查business_type和tenant_id组合是否与其他记录冲突
	var count int64
	err := r.db.WithContext(ctx).Model(&sequenceModel{}).
		Where("business_type = ? AND tenant_id = ? AND id != ?", sequence.BusinessType, sequence.TenantID, sequence.ID).
		Count(&count).Error

	if err != nil {
		return err
	}
	if count > 0 {
		return userErrors.NewParameterValidationFailedError("business_type", fmt.Sprintf("business_type: %s, tenant_id: %d", sequence.BusinessType, sequence.TenantID))
	}

	// 使用 Updates 方法，只更新指定字段，避免更新 created_at
	updates := map[string]interface{}{
		"business_type":  sequence.BusinessType,
		"sequence_name":  sequence.SequenceName,
		"tenant_id":      sequence.TenantID,
		"current_value":  sequence.CurrentValue,
		"increment_step": sequence.IncrementStep,
		"cache_size":     sequence.CacheSize,
		"max_value":      sequence.MaxValue,
		"min_value":      sequence.MinValue,
		"threshold":      sequence.Threshold,
		"is_active":      sequence.IsActive,
		"remarks":        sequence.Remarks,
		"updated_at":     time.Now(), // 手动设置 updated_at
	}

	return r.db.WithContext(ctx).
		Model(&sequenceModel{}).
		Where("id = ?", sequence.ID).
		Updates(updates).Error
}

// UpdateCurrentValue 更新当前值
func (r *SequenceRepositoryImpl) UpdateCurrentValue(ctx context.Context, sequenceID int64, currentValue int64) error {
	// 参数验证
	if sequenceID <= 0 {
		return userErrors.NewParameterValidationFailedError("sequence_id", "序列ID必须大于0")
	}
	if currentValue < 0 {
		return userErrors.NewParameterValidationFailedError("current_value", "当前值不能为负数")
	}

	return r.db.WithContext(ctx).
		Model(&sequenceModel{}).
		Where("id = ?", sequenceID).
		Update("current_value", currentValue).Error
}

// FindActiveSequences 查找活跃的序列
func (r *SequenceRepositoryImpl) FindActiveSequences(ctx context.Context) ([]*entity.Sequence, error) {
	var models []sequenceModel
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&models).Error
	if err != nil {
		return nil, err
	}

	sequences := make([]*entity.Sequence, len(models))
	for i, model := range models {
		sequences[i] = r.modelToEntity(&model)
	}

	return sequences, nil
}

// createInitialSegments 为新序列创建初始ID段
func (r *SequenceRepositoryImpl) createInitialSegments(ctx context.Context, tx *gorm.DB, sequence *entity.Sequence) error {
	// 创建3个初始段，确保有足够的可用段
	segments := []map[string]interface{}{
		{
			"sequence_id":  sequence.ID,
			"tenant_id":    sequence.TenantID,
			"start_value":  1,
			"end_value":    int64(sequence.IncrementStep),
			"segment_size": sequence.IncrementStep,
			"status":       "AVAILABLE",
			// 移除手动设置的时间字段，让数据库自动处理
		},
	}

	// 批量插入ID段
	if err := tx.Table("id_allocation").Create(segments).Error; err != nil {
		return err
	}

	// 更新序列的当前值
	sequence.CurrentValue = int64(sequence.IncrementStep * 3)
	if err := tx.Model(&sequenceModel{}).Where("id = ?", sequence.ID).Update("current_value", sequence.CurrentValue).Error; err != nil {
		return err
	}

	return nil
}

// ExistsByBusinessTypeAndTenant 检查business_type和tenant_id组合是否已存在
func (r *SequenceRepositoryImpl) ExistsByBusinessTypeAndTenant(ctx context.Context, businessType string, tenantID int64) (bool, error) {
	// 参数验证
	if businessType == "" {
		return false, userErrors.NewParameterValidationFailedError("business_type", "业务类型不能为空")
	}
	if tenantID < 0 {
		return false, userErrors.NewParameterValidationFailedError("tenant_id", "租户ID不能为负数")
	}

	var count int64
	err := r.db.WithContext(ctx).Model(&sequenceModel{}).
		Where("business_type = ? AND tenant_id = ?", businessType, tenantID).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// modelToEntity 模型转实体
func (r *SequenceRepositoryImpl) modelToEntity(model *sequenceModel) *entity.Sequence {
	return &entity.Sequence{
		ID:            model.ID,
		BusinessType:  model.BusinessType,
		SequenceName:  model.SequenceName,
		TenantID:      model.TenantID,
		CurrentValue:  model.CurrentValue,
		IncrementStep: model.IncrementStep,
		CacheSize:     model.CacheSize,
		MaxValue:      model.MaxValue,
		MinValue:      model.MinValue,
		Threshold:     model.Threshold,
		IsActive:      model.IsActive,
		Remarks:       model.Remarks,
		CreatedAt:     model.CreatedAt,
		UpdatedAt:     model.UpdatedAt,
	}
}
