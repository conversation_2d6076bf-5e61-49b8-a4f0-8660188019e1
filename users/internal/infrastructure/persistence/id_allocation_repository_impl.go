package persistence

import (
	"context"
	"database/sql"
	"time"

	"platforms-user/internal/domain/idgenerator/entity"
	"platforms-user/internal/domain/idgenerator/repository"

	"gorm.io/gorm"
)

// AllocationRepositoryImpl 分配仓储实现
type AllocationRepositoryImpl struct {
	db *gorm.DB
}

// NewAllocationRepositoryImpl 创建分配仓储实现
func NewAllocationRepositoryImpl(db *gorm.DB) repository.AllocationRepository {
	return &AllocationRepositoryImpl{
		db: db,
	}
}

// allocationModel 分配数据模型
type allocationModel struct {
	ID           int64        `gorm:"column:id;primaryKey;autoIncrement"`
	SequenceID   int64        `gorm:"column:sequence_id;not null"`
	TenantID     int64        `gorm:"column:tenant_id;not null"`
	BusinessType string       `gorm:"column:business_type;not null;size:50"`
	StartValue   int64        `gorm:"column:start_value;not null"`
	EndValue     int64        `gorm:"column:end_value;not null"`
	SegmentSize  int          `gorm:"column:segment_size;not null"`
	Status       string       `gorm:"column:status;not null;size:20;default:AVAILABLE"`
	AllocatedAt  sql.NullTime `gorm:"column:allocated_at"`
	CreatedAt    time.Time    `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt    time.Time    `gorm:"column:updated_at;autoUpdateTime"`
}

func (allocationModel) TableName() string {
	return "id_allocation"
}

// Create 创建分配记录
func (r *AllocationRepositoryImpl) Create(ctx context.Context, allocation *entity.Allocation) error {
	model := &allocationModel{
		SequenceID:   allocation.SequenceID,
		TenantID:     allocation.TenantID,
		BusinessType: allocation.BusinessType,
		StartValue:   allocation.StartValue,
		EndValue:     allocation.EndValue,
		SegmentSize:  allocation.SegmentSize,
		Status:       string(allocation.Status),
		AllocatedAt:  sql.NullTime{Valid: allocation.AllocatedAt != nil, Time: time.Time{}},
	}

	if allocation.AllocatedAt != nil {
		model.AllocatedAt.Time = *allocation.AllocatedAt
	}

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return err
	}

	allocation.ID = model.ID
	allocation.CreatedAt = model.CreatedAt
	allocation.UpdatedAt = model.UpdatedAt

	return nil
}

// BatchCreate 批量创建分配记录
func (r *AllocationRepositoryImpl) BatchCreate(ctx context.Context, allocations []*entity.Allocation) error {
	if len(allocations) == 0 {
		return nil
	}

	models := make([]*allocationModel, len(allocations))
	for i, allocation := range allocations {
		models[i] = &allocationModel{
			SequenceID:   allocation.SequenceID,
			TenantID:     allocation.TenantID,
			BusinessType: allocation.BusinessType,
			StartValue:   allocation.StartValue,
			EndValue:     allocation.EndValue,
			SegmentSize:  allocation.SegmentSize,
			Status:       string(allocation.Status),
			AllocatedAt:  sql.NullTime{Valid: allocation.AllocatedAt != nil, Time: time.Time{}},
			// 不手动设置 CreatedAt 和 UpdatedAt，让数据库自动处理
		}

		if allocation.AllocatedAt != nil {
			models[i].AllocatedAt.Time = *allocation.AllocatedAt
		}
	}

	return r.db.WithContext(ctx).CreateInBatches(models, 100).Error
}

// ClaimAvailableSegment 原子性抢占可用段
func (r *AllocationRepositoryImpl) ClaimAvailableSegment(ctx context.Context, sequenceID int64, tenantID int64) (*entity.Allocation, error) {
	var allocation *entity.Allocation

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找可用段
		var model allocationModel
		err := tx.Where("sequence_id = ? AND tenant_id = ? AND status = ?", sequenceID, tenantID, string(entity.AllocationStatusAvailable)).
			Order("id ASC").
			First(&model).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return entity.ErrNoAvailableSegments
			}
			return err
		}

		// 更新状态为已分配
		now := time.Now()
		err = tx.Model(&allocationModel{}).
			Where("id = ? AND status = ?", model.ID, string(entity.AllocationStatusAvailable)).
			Updates(map[string]interface{}{
				"status":       string(entity.AllocationStatusAllocated),
				"allocated_at": now,
				"updated_at":   now,
			}).Error

		if err != nil {
			return err
		}

		// 转换为实体
		allocation = &entity.Allocation{
			ID:           model.ID,
			SequenceID:   model.SequenceID,
			TenantID:     model.TenantID,
			BusinessType: model.BusinessType,
			StartValue:   model.StartValue,
			EndValue:     model.EndValue,
			SegmentSize:  model.SegmentSize,
			Status:       entity.AllocationStatusAllocated,
			AllocatedAt:  &now,
			CreatedAt:    model.CreatedAt,
			UpdatedAt:    now,
		}

		return nil
	})

	return allocation, err
}

// CountBySequenceAndStatus 统计指定序列和状态的段数量
func (r *AllocationRepositoryImpl) CountBySequenceAndStatus(ctx context.Context, sequenceID int64, status entity.AllocationStatus) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&allocationModel{}).
		Where("sequence_id = ? AND status = ?", sequenceID, string(status)).
		Count(&count).Error

	return int(count), err
}

// CleanupAllocated 清理已分配的历史记录
func (r *AllocationRepositoryImpl) CleanupAllocated(ctx context.Context, beforeTime int64) error {
	return r.db.WithContext(ctx).
		Where("status = ? AND allocated_at < ?", string(entity.AllocationStatusAllocated), time.Unix(beforeTime, 0)).
		Delete(&allocationModel{}).Error
}

// ClaimAvailableSegmentByBusinessType 原子性抢占可用段（基于business_type和tenant_id）
func (r *AllocationRepositoryImpl) ClaimAvailableSegmentByBusinessType(ctx context.Context, businessType string, tenantID int64) (*entity.Allocation, error) {
	var allocation *entity.Allocation

	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找可用段
		var model allocationModel
		err := tx.Where("business_type = ? AND tenant_id = ? AND status = ?", businessType, tenantID, string(entity.AllocationStatusAvailable)).
			Order("id ASC").
			First(&model).Error

		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return entity.ErrNoAvailableSegments
			}
			return err
		}

		// 更新状态为已分配
		now := time.Now()
		err = tx.Model(&allocationModel{}).
			Where("id = ? AND status = ?", model.ID, string(entity.AllocationStatusAvailable)).
			Updates(map[string]interface{}{
				"status":       string(entity.AllocationStatusAllocated),
				"allocated_at": now,
				"updated_at":   now,
			}).Error

		if err != nil {
			return err
		}

		// 转换为实体
		allocation = &entity.Allocation{
			ID:           model.ID,
			SequenceID:   model.SequenceID,
			TenantID:     model.TenantID,
			BusinessType: model.BusinessType,
			StartValue:   model.StartValue,
			EndValue:     model.EndValue,
			SegmentSize:  model.SegmentSize,
			Status:       entity.AllocationStatusAllocated,
			AllocatedAt:  &now,
			CreatedAt:    model.CreatedAt,
			UpdatedAt:    now,
		}

		return nil
	})

	return allocation, err
}

// CountByBusinessTypeAndStatus 统计指定业务类型和状态的段数量
func (r *AllocationRepositoryImpl) CountByBusinessTypeAndStatus(ctx context.Context, businessType string, tenantID int64, status entity.AllocationStatus) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&allocationModel{}).
		Where("business_type = ? AND tenant_id = ? AND status = ?", businessType, tenantID, string(status)).
		Count(&count).Error

	return int(count), err
}

// CreateSegments 批量创建可用段
func (r *AllocationRepositoryImpl) CreateSegments(ctx context.Context, sequenceID int64, tenantID int64, startValue int64, count int, incrementStep int) error {
	// 获取序列信息以获取business_type
	var sequenceModel struct {
		BusinessType string `gorm:"column:business_type"`
	}
	err := r.db.WithContext(ctx).Table("id_sequence").
		Select("business_type").
		Where("id = ?", sequenceID).
		First(&sequenceModel).Error
	if err != nil {
		return err
	}

	allocations := make([]*entity.Allocation, count)

	for i := 0; i < count; i++ {
		segmentStart := startValue + int64(i*incrementStep)
		segmentEnd := segmentStart + int64(incrementStep) - 1

		allocations[i] = entity.NewAllocation(sequenceID, tenantID, sequenceModel.BusinessType, segmentStart, segmentEnd, incrementStep)
	}

	return r.BatchCreate(ctx, allocations)
}
