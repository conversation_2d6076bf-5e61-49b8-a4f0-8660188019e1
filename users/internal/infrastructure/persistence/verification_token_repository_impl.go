package persistence

import (
	"context"
	"fmt"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"
	"time"

	"gorm.io/gorm"
	"platforms-pkg/logiface"
)

// VerificationTokenRepositoryImpl 验证令牌仓储实现
type VerificationTokenRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewVerificationTokenRepository 创建验证令牌仓储实例
func NewVerificationTokenRepository(db *gorm.DB, logger logiface.Logger) repository.VerificationTokenRepository {
	return &VerificationTokenRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建验证令牌
func (r *VerificationTokenRepositoryImpl) Create(ctx context.Context, token *entity.VerificationToken) error {
	if err := r.db.WithContext(ctx).Create(token).Error; err != nil {
		r.logger.Error(ctx, "Failed to create verification token",
			logiface.Error(err),
			logiface.String("target", token.Target),
			logiface.Int("purpose", int(token.Purpose)))
		return fmt.Errorf("failed to create verification token: %w", err)
	}

	r.logger.Info(ctx, "Verification token created successfully",
		logiface.Int64("token_id", token.ID),
		logiface.String("target", token.Target),
		logiface.Int("purpose", int(token.Purpose)))

	return nil
}

// Update 更新验证令牌
func (r *VerificationTokenRepositoryImpl) Update(ctx context.Context, token *entity.VerificationToken) error {
	result := r.db.WithContext(ctx).Save(token)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update verification token",
			logiface.Error(result.Error),
			logiface.Int64("token_id", token.ID))
		return fmt.Errorf("failed to update verification token: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", token.ID)
	}

	return nil
}

// Delete 删除验证令牌
func (r *VerificationTokenRepositoryImpl) Delete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Delete(&entity.VerificationToken{}, id)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete verification token",
			logiface.Error(result.Error),
			logiface.Int64("token_id", id))
		return fmt.Errorf("failed to delete verification token: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", id)
	}

	return nil
}

// FindByID 根据ID查找验证令牌
func (r *VerificationTokenRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.VerificationToken, error) {
	var token entity.VerificationToken
	if err := r.db.WithContext(ctx).First(&token, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find verification token by ID",
			logiface.Error(err),
			logiface.Int64("token_id", id))
		return nil, fmt.Errorf("failed to find verification token: %w", err)
	}

	return &token, nil
}

// FindByToken 根据令牌字符串查找验证令牌
func (r *VerificationTokenRepositoryImpl) FindByToken(ctx context.Context, token string) (*entity.VerificationToken, error) {
	var verificationToken entity.VerificationToken
	if err := r.db.WithContext(ctx).Where("token = ?", token).First(&verificationToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find verification token by token",
			logiface.Error(err),
			logiface.String("token", token))
		return nil, fmt.Errorf("failed to find verification token: %w", err)
	}

	return &verificationToken, nil
}

// FindByTarget 根据目标地址查找验证令牌
func (r *VerificationTokenRepositoryImpl) FindByTarget(ctx context.Context, tenantID int64, target string, targetType entity.TargetType, purpose entity.Purpose) ([]*entity.VerificationToken, error) {
	var tokens []*entity.VerificationToken
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND target = ? AND target_type = ? AND purpose = ?",
		tenantID, target, targetType, purpose).Order("created_at DESC")

	if err := query.Find(&tokens).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification tokens by target",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", target))
		return nil, fmt.Errorf("failed to find verification tokens: %w", err)
	}

	return tokens, nil
}

// FindActiveByTarget 查找目标地址的活跃验证令牌
func (r *VerificationTokenRepositoryImpl) FindActiveByTarget(ctx context.Context, tenantID int64, target string, targetType entity.TargetType, purpose entity.Purpose) (*entity.VerificationToken, error) {
	var token entity.VerificationToken
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND target = ? AND target_type = ? AND purpose = ? AND status = ? AND expires_at > ?",
		tenantID, target, targetType, purpose, entity.TokenStatusUnused, time.Now()).Order("created_at DESC")

	if err := query.First(&token).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find active verification token",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", target))
		return nil, fmt.Errorf("failed to find active verification token: %w", err)
	}

	return &token, nil
}

// FindByUserID 根据用户ID查找验证令牌
func (r *VerificationTokenRepositoryImpl) FindByUserID(ctx context.Context, tenantID int64, userID int64, purpose entity.Purpose) ([]*entity.VerificationToken, error) {
	var tokens []*entity.VerificationToken
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND user_id = ? AND purpose = ?",
		tenantID, userID, purpose).Order("created_at DESC")

	if err := query.Find(&tokens).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification tokens by user ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("user_id", userID))
		return nil, fmt.Errorf("failed to find verification tokens: %w", err)
	}

	return tokens, nil
}

// MarkAsUsed 标记令牌为已使用
func (r *VerificationTokenRepositoryImpl) MarkAsUsed(ctx context.Context, id int64) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     entity.TokenStatusUsed,
			"used_at":    now,
			"updated_at": now,
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to mark token as used",
			logiface.Error(result.Error),
			logiface.Int64("token_id", id))
		return fmt.Errorf("failed to mark token as used: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", id)
	}

	return nil
}

// MarkAsExpired 标记令牌为已过期
func (r *VerificationTokenRepositoryImpl) MarkAsExpired(ctx context.Context, id int64) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     entity.TokenStatusExpired,
			"updated_at": now,
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to mark token as expired",
			logiface.Error(result.Error),
			logiface.Int64("token_id", id))
		return fmt.Errorf("failed to mark token as expired: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", id)
	}

	return nil
}

// RevokeToken 撤销令牌
func (r *VerificationTokenRepositoryImpl) RevokeToken(ctx context.Context, id int64) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     entity.TokenStatusRevoked,
			"revoked_at": now,
			"updated_at": now,
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to revoke token",
			logiface.Error(result.Error),
			logiface.Int64("token_id", id))
		return fmt.Errorf("failed to revoke token: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", id)
	}

	return nil
}

// RevokeByTarget 撤销目标地址的所有活跃令牌
func (r *VerificationTokenRepositoryImpl) RevokeByTarget(ctx context.Context, tenantID int64, target string, targetType entity.TargetType, purpose entity.Purpose) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("tenant_id = ? AND target = ? AND target_type = ? AND purpose = ? AND status = ?",
			tenantID, target, targetType, purpose, entity.TokenStatusUnused).
		Updates(map[string]interface{}{
			"status":     entity.TokenStatusRevoked,
			"revoked_at": now,
			"updated_at": now,
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to revoke tokens by target",
			logiface.Error(result.Error),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", target))
		return fmt.Errorf("failed to revoke tokens: %w", result.Error)
	}

	r.logger.Info(ctx, "Tokens revoked by target",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("target", target),
		logiface.Int64("revoked_count", result.RowsAffected))

	return nil
}

// IncrementAttemptCount 增加尝试次数
func (r *VerificationTokenRepositoryImpl) IncrementAttemptCount(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"attempt_count": gorm.Expr("attempt_count + 1"),
			"updated_at":    time.Now(),
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to increment attempt count",
			logiface.Error(result.Error),
			logiface.Int64("token_id", id))
		return fmt.Errorf("failed to increment attempt count: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification token not found: %d", id)
	}

	return nil
}

// CleanupExpiredTokens 清理过期令牌
func (r *VerificationTokenRepositoryImpl) CleanupExpiredTokens(ctx context.Context, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("expires_at < ?", before).Delete(&entity.VerificationToken{})
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to cleanup expired tokens",
			logiface.Error(result.Error),
			logiface.Any("before", before))
		return 0, fmt.Errorf("failed to cleanup expired tokens: %w", result.Error)
	}

	r.logger.Info(ctx, "Expired tokens cleaned up",
		logiface.Int64("deleted_count", result.RowsAffected),
		logiface.Time("before", before))

	return result.RowsAffected, nil
}

// CleanupUsedTokens 清理已使用令牌
func (r *VerificationTokenRepositoryImpl) CleanupUsedTokens(ctx context.Context, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("status = ? AND used_at < ?", entity.TokenStatusUsed, before).Delete(&entity.VerificationToken{})
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to cleanup used tokens",
			logiface.Error(result.Error),
			logiface.Time("before", before))
		return 0, fmt.Errorf("failed to cleanup used tokens: %w", result.Error)
	}

	r.logger.Info(ctx, "Used tokens cleaned up",
		logiface.Int64("deleted_count", result.RowsAffected),
		logiface.Time("before", before))

	return result.RowsAffected, nil
}

// CountByTarget 统计目标地址的令牌数量
func (r *VerificationTokenRepositoryImpl) CountByTarget(ctx context.Context, tenantID int64, target string, targetType entity.TargetType, purpose entity.Purpose, since time.Time) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("tenant_id = ? AND target = ? AND target_type = ? AND purpose = ? AND created_at >= ?",
			tenantID, target, targetType, purpose, since)

	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tokens by target",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", target))
		return 0, fmt.Errorf("failed to count tokens: %w", err)
	}

	return count, nil
}

// CountByIP 统计IP地址的令牌数量
func (r *VerificationTokenRepositoryImpl) CountByIP(ctx context.Context, tenantID int64, ipAddress string, purpose entity.Purpose, since time.Time) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("tenant_id = ? AND ip_address = ? AND purpose = ? AND created_at >= ?",
			tenantID, ipAddress, purpose, since)

	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tokens by IP",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip_address", ipAddress))
		return 0, fmt.Errorf("failed to count tokens: %w", err)
	}

	return count, nil
}

// CountByTenant 统计租户的令牌数量
func (r *VerificationTokenRepositoryImpl) CountByTenant(ctx context.Context, tenantID int64, purpose entity.Purpose, since time.Time) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entity.VerificationToken{}).
		Where("tenant_id = ? AND purpose = ? AND created_at >= ?", tenantID, purpose, since)

	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tokens by tenant",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count tokens: %w", err)
	}

	return count, nil
}

// FindWithPagination 分页查询验证令牌
func (r *VerificationTokenRepositoryImpl) FindWithPagination(ctx context.Context, filter *repository.VerificationTokenFilter) ([]*entity.VerificationToken, int64, error) {
	if err := filter.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid filter: %w", err)
	}

	query := r.db.WithContext(ctx).Model(&entity.VerificationToken{})

	// 应用过滤条件
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.UserID != nil {
		query = query.Where("user_id = ?", *filter.UserID)
	}
	if filter.Target != "" {
		query = query.Where("target LIKE ?", "%"+filter.Target+"%")
	}
	if filter.TargetType != nil {
		query = query.Where("target_type = ?", *filter.TargetType)
	}
	if filter.Purpose != nil {
		query = query.Where("purpose = ?", *filter.Purpose)
	}
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}
	if filter.TokenType != nil {
		query = query.Where("token_type = ?", *filter.TokenType)
	}
	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}
	if filter.IPAddress != "" {
		query = query.Where("ip_address = ?", filter.IPAddress)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count verification tokens",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to count tokens: %w", err)
	}

	// 分页查询
	var tokens []*entity.VerificationToken
	query = query.Order(filter.GetOrderClause()).
		Offset(filter.GetOffset()).
		Limit(filter.GetLimit())

	if err := query.Find(&tokens).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification tokens with pagination",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to find tokens: %w", err)
	}

	return tokens, total, nil
}
