package persistence

import (
	"context"
	"fmt"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"

	"platforms-pkg/common"
	"platforms-pkg/logiface"

	"gorm.io/gorm"
)

// VerificationConfigRepositoryImpl 验证配置仓储实现
type VerificationConfigRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewVerificationConfigRepository 创建验证配置仓储实例
func NewVerificationConfigRepository(db *gorm.DB, logger logiface.Logger) repository.VerificationConfigRepository {
	return &VerificationConfigRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建验证配置
func (r *VerificationConfigRepositoryImpl) Create(ctx context.Context, config *entity.VerificationConfig) error {
	r.logger.Info(ctx, "Attempting to create verification config",
		logiface.Int64("tenant_id", config.TenantID),
		logiface.String("config_mode", string(config.ConfigMode)),
		logiface.Int("target_type", int(config.TargetType)),
		logiface.Int64("config_id", config.ID))

	if err := r.db.WithContext(ctx).Create(config).Error; err != nil {
		r.logger.Error(ctx, "Failed to create verification config",
			logiface.Error(err),
			logiface.Int64("tenant_id", config.TenantID),
			logiface.String("config_mode", string(config.ConfigMode)),
			logiface.Int("target_type", int(config.TargetType)),
			logiface.Int64("config_id", config.ID))
		return fmt.Errorf("failed to create verification config: %w", err)
	}

	r.logger.Info(ctx, "Verification config created successfully",
		logiface.Int64("config_id", config.ID),
		logiface.Int64("tenant_id", config.TenantID),
		logiface.String("config_mode", string(config.ConfigMode)),
		logiface.Int("target_type", int(config.TargetType)))

	return nil
}

// Update 更新验证配置
func (r *VerificationConfigRepositoryImpl) Update(ctx context.Context, config *entity.VerificationConfig) error {
	result := r.db.WithContext(ctx).Save(config)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update verification config",
			logiface.Error(result.Error),
			logiface.Int64("config_id", config.ID))
		return fmt.Errorf("failed to update verification config: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification config not found: %d", config.ID)
	}

	r.logger.Info(ctx, "Verification config updated successfully",
		logiface.Int64("config_id", config.ID))

	return nil
}

// Delete 删除验证配置
func (r *VerificationConfigRepositoryImpl) Delete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Delete(&entity.VerificationConfig{}, id)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete verification config",
			logiface.Error(result.Error),
			logiface.Int64("config_id", id))
		return fmt.Errorf("failed to delete verification config: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification config not found: %d", id)
	}

	r.logger.Info(ctx, "Verification config deleted successfully",
		logiface.Int64("config_id", id))

	return nil
}

// FindByID 根据ID查找验证配置
func (r *VerificationConfigRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.VerificationConfig, error) {
	var config entity.VerificationConfig
	if err := r.db.WithContext(ctx).First(&config, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find verification config by ID",
			logiface.Error(err),
			logiface.Int64("config_id", id))
		return nil, fmt.Errorf("failed to find verification config: %w", err)
	}

	return &config, nil
}

// FindByTenantID 根据租户ID查找验证配置
func (r *VerificationConfigRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("purpose, target_type").Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification configs by tenant ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to find verification configs: %w", err)
	}

	return configs, nil
}

// FindByPurposeAndTarget 根据用途和目标类型查找验证配置
func (r *VerificationConfigRepositoryImpl) FindByPurposeAndTarget(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error) {
	var config entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND purpose = ? AND target_type = ?",
		tenantID, purpose, targetType).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find verification config by purpose and target",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("purpose", int(purpose)),
			logiface.Int("target_type", int(targetType)))
		return nil, fmt.Errorf("failed to find verification config: %w", err)
	}

	return &config, nil
}

// FindActiveConfigs 查找活跃的验证配置
func (r *VerificationConfigRepositoryImpl) FindActiveConfigs(ctx context.Context, tenantID int64) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND is_active = ?", tenantID, true).
		Order("purpose, target_type").Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find active verification configs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to find active verification configs: %w", err)
	}

	return configs, nil
}

// FindByTemplateCode 根据模板代码查找验证配置
func (r *VerificationConfigRepositoryImpl) FindByTemplateCode(ctx context.Context, tenantID int64, templateCode string) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND template_code = ?", tenantID, templateCode).
		Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification configs by template code",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("template_code", templateCode))
		return nil, fmt.Errorf("failed to find verification configs: %w", err)
	}

	return configs, nil
}

// EnableConfig 启用配置
func (r *VerificationConfigRepositoryImpl) EnableConfig(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("id = ?", id).Update("is_active", true)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to enable verification config",
			logiface.Error(result.Error),
			logiface.Int64("config_id", id))
		return fmt.Errorf("failed to enable verification config: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification config not found: %d", id)
	}

	r.logger.Info(ctx, "Verification config enabled",
		logiface.Int64("config_id", id))

	return nil
}

// DisableConfig 禁用配置
func (r *VerificationConfigRepositoryImpl) DisableConfig(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("id = ?", id).Update("is_active", false)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to disable verification config",
			logiface.Error(result.Error),
			logiface.Int64("config_id", id))
		return fmt.Errorf("failed to disable verification config: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification config not found: %d", id)
	}

	r.logger.Info(ctx, "Verification config disabled",
		logiface.Int64("config_id", id))

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *VerificationConfigRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []int64, isActive bool) error {
	if len(ids) == 0 {
		return fmt.Errorf("ids cannot be empty")
	}

	result := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("id IN ?", ids).Update("is_active", isActive)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to batch update verification config status",
			logiface.Error(result.Error),
			logiface.Any("ids", ids),
			logiface.Bool("is_active", isActive))
		return fmt.Errorf("failed to batch update verification config status: %w", result.Error)
	}

	r.logger.Info(ctx, "Verification configs status updated",
		logiface.Int64("updated_count", result.RowsAffected),
		logiface.Bool("is_active", isActive))

	return nil
}

// ExistsByPurposeAndTarget 检查配置是否存在
func (r *VerificationConfigRepositoryImpl) ExistsByPurposeAndTarget(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("tenant_id = ? AND purpose = ? AND target_type = ?", tenantID, purpose, targetType).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check verification config existence",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("purpose", int(purpose)),
			logiface.Int("target_type", int(targetType)))
		return false, fmt.Errorf("failed to check verification config existence: %w", err)
	}

	return count > 0, nil
}

// IsConfigActive 检查配置是否活跃
func (r *VerificationConfigRepositoryImpl) IsConfigActive(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("tenant_id = ? AND purpose = ? AND target_type = ? AND is_active = ?",
			tenantID, purpose, targetType, true).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check verification config active status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("purpose", int(purpose)),
			logiface.Int("target_type", int(targetType)))
		return false, fmt.Errorf("failed to check verification config active status: %w", err)
	}

	return count > 0, nil
}

// CopySystemConfigsToTenant 复制系统配置到租户
func (r *VerificationConfigRepositoryImpl) CopySystemConfigsToTenant(ctx context.Context, tenantID int64) error {
	// 获取系统默认配置（使用系统租户ID）
	var systemConfigs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ?", common.SystemTenantID).Find(&systemConfigs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find system configs",
			logiface.Error(err))
		return fmt.Errorf("failed to find system configs: %w", err)
	}

	// 为租户创建配置副本
	var tenantConfigs []*entity.VerificationConfig
	for _, systemConfig := range systemConfigs {
		tenantConfig := systemConfig.Clone(tenantID)
		tenantConfigs = append(tenantConfigs, tenantConfig)
	}

	// 批量创建租户配置
	if len(tenantConfigs) > 0 {
		if err := r.db.WithContext(ctx).Create(&tenantConfigs).Error; err != nil {
			r.logger.Error(ctx, "Failed to copy system configs to tenant",
				logiface.Error(err),
				logiface.Int64("tenant_id", tenantID))
			return fmt.Errorf("failed to copy system configs to tenant: %w", err)
		}

		r.logger.Info(ctx, "System configs copied to tenant",
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("config_count", len(tenantConfigs)))
	}

	return nil
}

// FindEffectiveConfig 查找有效配置（新的统一方法）
func (r *VerificationConfigRepositoryImpl) FindEffectiveConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType, businessScene *string) (*entity.VerificationConfig, error) {
	// 1. 优先查找动态策略配置
	if businessScene != nil {
		dynamicConfig, err := r.FindDynamicConfig(ctx, tenantID, *businessScene, targetType)
		if err != nil {
			return nil, err
		}
		if dynamicConfig != nil && dynamicConfig.IsActive {
			return dynamicConfig, nil
		}
	}

	// 2. 回退到静态配置
	staticConfig, err := r.FindStaticConfig(ctx, tenantID, purpose, targetType)
	if err != nil {
		return nil, err
	}

	if staticConfig != nil && staticConfig.IsActive {
		return staticConfig, nil
	}

	// 3. 回退到系统默认配置
	return r.FindSystemDefaultConfig(ctx, purpose, targetType)
}

// FindStaticConfig 查找静态配置
func (r *VerificationConfigRepositoryImpl) FindStaticConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error) {
	var config entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_mode = ? AND purpose = ? AND target_type = ? AND deleted_at IS NULL",
		tenantID, entity.ConfigModeStatic, purpose, targetType).First(&config).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find static verification config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("purpose", int(purpose)),
			logiface.Int("target_type", int(targetType)))
		return nil, fmt.Errorf("failed to find static verification config: %w", err)
	}

	return &config, nil
}

// FindDynamicConfig 查找动态配置
func (r *VerificationConfigRepositoryImpl) FindDynamicConfig(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) (*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig

	// 查找匹配的动态配置，按优先级排序
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_mode = ? AND business_scene = ? AND target_type = ? AND is_active = ? AND deleted_at IS NULL",
		tenantID, entity.ConfigModeDynamic, businessScene, targetType, true).
		Order("priority DESC, created_at DESC").Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find dynamic verification config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("business_scene", businessScene),
			logiface.Int("target_type", int(targetType)))
		return nil, fmt.Errorf("failed to find dynamic verification config: %w", err)
	}

	if len(configs) == 0 {
		return nil, nil
	}

	// 这里可以添加条件表达式评估逻辑
	// 暂时返回优先级最高的配置
	return configs[0], nil
}

// FindSystemDefaultConfig 查找系统默认配置
func (r *VerificationConfigRepositoryImpl) FindSystemDefaultConfig(ctx context.Context, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error) {
	return r.FindStaticConfig(ctx, common.SystemTenantID, purpose, targetType)
}

// FindByBusinessScene 根据业务场景查找配置
func (r *VerificationConfigRepositoryImpl) FindByBusinessScene(ctx context.Context, tenantID int64, businessScene string) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND business_scene = ? AND deleted_at IS NULL", tenantID, businessScene).
		Order("priority DESC, created_at DESC").Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification configs by business scene",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("business_scene", businessScene))
		return nil, fmt.Errorf("failed to find verification configs: %w", err)
	}

	return configs, nil
}

// FindByJudgmentDimension 根据判定维度查找配置
func (r *VerificationConfigRepositoryImpl) FindByJudgmentDimension(ctx context.Context, tenantID int64, dimension string) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND judgment_dimension = ? AND deleted_at IS NULL", tenantID, dimension).
		Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification configs by judgment dimension",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("judgment_dimension", dimension))
		return nil, fmt.Errorf("failed to find verification configs: %w", err)
	}

	return configs, nil
}

// FindDynamicConfigsByPriority 根据优先级查找动态配置
func (r *VerificationConfigRepositoryImpl) FindDynamicConfigsByPriority(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) ([]*entity.VerificationConfig, error) {
	var configs []*entity.VerificationConfig
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND config_mode = ? AND business_scene = ? AND target_type = ? AND is_active = ? AND deleted_at IS NULL",
		tenantID, entity.ConfigModeDynamic, businessScene, targetType, true).
		Order("priority DESC, verification_level DESC, created_at DESC").Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find dynamic verification configs by priority",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("business_scene", businessScene),
			logiface.Int("target_type", int(targetType)))
		return nil, fmt.Errorf("failed to find dynamic verification configs: %w", err)
	}

	return configs, nil
}

// SoftDelete 软删除配置
func (r *VerificationConfigRepositoryImpl) SoftDelete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("id = ?", id).Update("deleted_at", gorm.Expr("CURRENT_TIMESTAMP"))

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to soft delete verification config",
			logiface.Error(result.Error),
			logiface.Int64("config_id", id))
		return fmt.Errorf("failed to soft delete verification config: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification config not found: %d", id)
	}

	r.logger.Info(ctx, "Verification config soft deleted successfully",
		logiface.Int64("config_id", id))

	return nil
}

// ExistsByBusinessScene 检查动态配置是否存在
func (r *VerificationConfigRepositoryImpl) ExistsByBusinessScene(ctx context.Context, tenantID int64, businessScene string, targetType entity.TargetType) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).
		Where("tenant_id = ? AND business_scene = ? AND target_type = ? AND deleted_at IS NULL", tenantID, businessScene, targetType).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check verification config existence by business scene",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("business_scene", businessScene),
			logiface.Int("target_type", int(targetType)))
		return false, fmt.Errorf("failed to check verification config existence: %w", err)
	}

	return count > 0, nil
}

// FindWithPagination 分页查询验证配置
func (r *VerificationConfigRepositoryImpl) FindWithPagination(ctx context.Context, filter *repository.VerificationConfigFilter) ([]*entity.VerificationConfig, int64, error) {
	if err := filter.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid filter: %w", err)
	}

	query := r.db.WithContext(ctx).Model(&entity.VerificationConfig{}).Where("deleted_at IS NULL")

	// 应用过滤条件
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.ConfigMode != nil {
		query = query.Where("config_mode = ?", *filter.ConfigMode)
	}
	if filter.Purpose != nil {
		query = query.Where("purpose = ?", *filter.Purpose)
	}
	if filter.TargetType != nil {
		query = query.Where("target_type = ?", *filter.TargetType)
	}
	if filter.TokenType != nil {
		query = query.Where("token_type = ?", *filter.TokenType)
	}
	if filter.BusinessScene != "" {
		query = query.Where("business_scene = ?", filter.BusinessScene)
	}
	if filter.JudgmentDimension != "" {
		query = query.Where("judgment_dimension = ?", filter.JudgmentDimension)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.TemplateCode != "" {
		query = query.Where("template_code LIKE ?", "%"+filter.TemplateCode+"%")
	}
	if filter.MinPriority != nil {
		query = query.Where("priority >= ?", *filter.MinPriority)
	}
	if filter.MaxPriority != nil {
		query = query.Where("priority <= ?", *filter.MaxPriority)
	}
	if filter.VerificationLevel != nil {
		query = query.Where("verification_level = ?", *filter.VerificationLevel)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count verification configs",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to count configs: %w", err)
	}

	// 分页查询
	var configs []*entity.VerificationConfig
	query = query.Order(filter.GetOrderClause()).
		Offset(filter.GetOffset()).
		Limit(filter.GetLimit())

	if err := query.Find(&configs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification configs with pagination",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to find configs: %w", err)
	}

	return configs, total, nil
}

// GetEffectiveConfig 获取有效配置（保持向后兼容）
func (r *VerificationConfigRepositoryImpl) GetEffectiveConfig(ctx context.Context, tenantID int64, purpose entity.Purpose, targetType entity.TargetType) (*entity.VerificationConfig, error) {
	return r.FindEffectiveConfig(ctx, tenantID, purpose, targetType, nil)
}
