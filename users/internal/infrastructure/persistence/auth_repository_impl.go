package persistence

import (
	"context"
	"platforms-user/internal/domain/auth/entity"
	"platforms-user/internal/domain/auth/repository"
	"time"

	"gorm.io/gorm"
)

// AuthRepositoryImpl 认证仓储实现
type AuthRepositoryImpl struct {
	db *gorm.DB
}

// NewAuthRepository 创建认证仓储
func NewAuthRepository(db *gorm.DB) repository.AuthRepository {
	return &AuthRepositoryImpl{db: db}
}

// CreateSession 创建会话
func (r *AuthRepositoryImpl) CreateSession(ctx context.Context, session *entity.AuthSession) error {
	return r.db.WithContext(ctx).Create(session).Error
}

// GetSession 获取会话（向后兼容）
func (r *AuthRepositoryImpl) GetSession(ctx context.Context, sessionID string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("id = ? AND status = 'active' AND expires_at > ?", sessionID, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// UpdateSession 更新会话
func (r *AuthRepositoryImpl) UpdateSession(ctx context.Context, session *entity.AuthSession) error {
	return r.db.WithContext(ctx).Save(session).Error
}

// RevokeSession 撤销会话（向后兼容）
func (r *AuthRepositoryImpl) RevokeSession(ctx context.Context, sessionID string) error {
	return r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("id = ?", sessionID).
		Update("status", "revoked").Error
}

// RevokeUserSessions 撤销用户所有会话
func (r *AuthRepositoryImpl) RevokeUserSessions(ctx context.Context, userID int64) error {
	return r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("user_id = ? AND status = 'active'", userID).
		Update("status", "revoked").Error
}

// GetUserSessions 获取用户活跃会话
func (r *AuthRepositoryImpl) GetUserSessions(ctx context.Context, userID int64) ([]entity.AuthSession, error) {
	var sessions []entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = 'active' AND expires_at > ?", userID, time.Now()).
		Order("created_at DESC").
		Find(&sessions).Error
	return sessions, err
}

// CreateLoginAttempt 创建登录尝试记录
func (r *AuthRepositoryImpl) CreateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error {
	return r.db.WithContext(ctx).Create(attempt).Error
}

// GetLoginAttempts 获取登录尝试记录
func (r *AuthRepositoryImpl) GetLoginAttempts(ctx context.Context, identifier string, since time.Time) ([]entity.LoginAttempt, error) {
	var attempts []entity.LoginAttempt
	err := r.db.WithContext(ctx).
		Where("identifier = ? AND created_at >= ?", identifier, since).
		Order("created_at DESC").
		Find(&attempts).Error
	return attempts, err
}

// GetFailedLoginAttempts 获取失败的登录尝试次数
func (r *AuthRepositoryImpl) GetFailedLoginAttempts(ctx context.Context, identifier string, since time.Time) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("identifier = ? AND status = 'failed' AND created_at >= ?", identifier, since).
		Count(&count).Error
	return count, err
}

// UpdateLoginAttempt 更新登录尝试状态
func (r *AuthRepositoryImpl) UpdateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error {
	return r.db.WithContext(ctx).Save(attempt).Error
}

// DeleteSession 删除会话
func (r *AuthRepositoryImpl) DeleteSession(ctx context.Context, sessionID string) error {
	return r.db.WithContext(ctx).Where("id = ?", sessionID).Delete(&entity.AuthSession{}).Error
}

// FindSessionByID 根据会话ID查找会话
func (r *AuthRepositoryImpl) FindSessionByID(ctx context.Context, sessionID string) (*entity.AuthSession, error) {
	return r.GetSession(ctx, sessionID)
}

// FindSessionByJTI 根据JTI查找会话
func (r *AuthRepositoryImpl) FindSessionByJTI(ctx context.Context, jti string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("jti = ? AND status = 'active' AND expires_at > ?", jti, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionByAccessTokenHash 根据访问令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByAccessTokenHash(ctx context.Context, accessTokenHash string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("access_token_hash = ? AND status = 'active' AND expires_at > ?", accessTokenHash, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionByRefreshTokenHash 根据刷新令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByRefreshTokenHash(ctx context.Context, refreshTokenHash string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("refresh_token_hash = ? AND status = 'active' AND expires_at > ?", refreshTokenHash, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionsByUserID 根据用户ID查找会话列表
func (r *AuthRepositoryImpl) FindSessionsByUserID(ctx context.Context, userID int64, offset, limit int) ([]*entity.AuthSession, int64, error) {
	var sessions []entity.AuthSession
	var total int64

	// 获取总数
	err := r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("user_id = ?", userID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取会话列表
	err = r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&sessions).Error

	sessionPtrs := make([]*entity.AuthSession, len(sessions))
	for i := range sessions {
		sessionPtrs[i] = &sessions[i]
	}

	return sessionPtrs, total, err
}

// FindActiveSessionsByUserID 根据用户ID查找活跃会话列表
func (r *AuthRepositoryImpl) FindActiveSessionsByUserID(ctx context.Context, userID int64) ([]*entity.AuthSession, error) {
	sessions, err := r.GetUserSessions(ctx, userID)
	if err != nil {
		return nil, err
	}

	sessionPtrs := make([]*entity.AuthSession, len(sessions))
	for i := range sessions {
		sessionPtrs[i] = &sessions[i]
	}

	return sessionPtrs, nil
}

// RevokeAllSessionsByUserID 撤销用户所有会话
func (r *AuthRepositoryImpl) RevokeAllSessionsByUserID(ctx context.Context, userID int64) error {
	return r.RevokeUserSessions(ctx, userID)
}

// RevokeSessionsByUserIDExcept 撤销用户除指定会话外的所有会话
func (r *AuthRepositoryImpl) RevokeSessionsByUserIDExcept(ctx context.Context, userID int64, exceptSessionID string) error {
	return r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("user_id = ? AND id != ? AND status = 'active'", userID, exceptSessionID).
		Update("status", "revoked").Error
}

// RevokeSessionByJTI 根据JTI撤销会话
func (r *AuthRepositoryImpl) RevokeSessionByJTI(ctx context.Context, jti string) error {
	return r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("jti = ?", jti).
		Update("status", "revoked").Error
}

// CleanExpiredSessions 清理过期会话
func (r *AuthRepositoryImpl) CleanExpiredSessions(ctx context.Context, before time.Time) error {
	return r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("expires_at <= ? AND status = 'active'", before).
		Update("status", "expired").Error
}

// FindLoginAttemptsByUsername 根据用户名查找登录尝试
func (r *AuthRepositoryImpl) FindLoginAttemptsByUsername(ctx context.Context, tenantID int64, username string, since time.Time) ([]*entity.LoginAttempt, error) {
	var attempts []entity.LoginAttempt
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND username = ? AND created_at >= ?", tenantID, username, since).
		Order("created_at DESC").
		Find(&attempts).Error

	attemptPtrs := make([]*entity.LoginAttempt, len(attempts))
	for i := range attempts {
		attemptPtrs[i] = &attempts[i]
	}

	return attemptPtrs, err
}

// FindLoginAttemptsByIP 根据IP地址查找登录尝试
func (r *AuthRepositoryImpl) FindLoginAttemptsByIP(ctx context.Context, tenantID int64, ipAddress string, since time.Time) ([]*entity.LoginAttempt, error) {
	var attempts []entity.LoginAttempt
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND ip_address = ? AND created_at >= ?", tenantID, ipAddress, since).
		Order("created_at DESC").
		Find(&attempts).Error

	attemptPtrs := make([]*entity.LoginAttempt, len(attempts))
	for i := range attempts {
		attemptPtrs[i] = &attempts[i]
	}

	return attemptPtrs, err
}

// CountFailedAttemptsByUsername 统计用户名失败次数
func (r *AuthRepositoryImpl) CountFailedAttemptsByUsername(ctx context.Context, tenantID int64, username string, since time.Time) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("tenant_id = ? AND username = ? AND status = 'failed' AND created_at >= ?", tenantID, username, since).
		Count(&count).Error
	return count, err
}

// CountFailedAttemptsByIP 统计IP地址失败次数
func (r *AuthRepositoryImpl) CountFailedAttemptsByIP(ctx context.Context, tenantID int64, ipAddress string, since time.Time) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("tenant_id = ? AND ip_address = ? AND status = 'failed' AND created_at >= ?", tenantID, ipAddress, since).
		Count(&count).Error
	return count, err
}

// CleanOldLoginAttempts 清理旧的登录尝试记录
func (r *AuthRepositoryImpl) CleanOldLoginAttempts(ctx context.Context, before time.Time) error {
	return r.db.WithContext(ctx).
		Where("created_at < ?", before).
		Delete(&entity.LoginAttempt{}).Error
}

// BlockIP 封禁IP地址
func (r *AuthRepositoryImpl) BlockIP(ctx context.Context, tenantID int64, ipAddress string, reason string, duration time.Duration) error {
	blockedUntil := time.Now().Add(duration)
	return r.db.WithContext(ctx).
		Table("blocked_ips").
		Create(map[string]interface{}{
			"tenant_id":     tenantID,
			"ip_address":    ipAddress,
			"reason":        reason,
			"blocked_until": blockedUntil,
			"created_at":    time.Now(),
		}).Error
}

// IsIPBlocked 检查IP是否被封禁
func (r *AuthRepositoryImpl) IsIPBlocked(ctx context.Context, tenantID int64, ipAddress string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("blocked_ips").
		Where("tenant_id = ? AND ip_address = ? AND blocked_until > ?", tenantID, ipAddress, time.Now()).
		Count(&count).Error
	return count > 0, err
}

// UnblockIP 解封IP地址
func (r *AuthRepositoryImpl) UnblockIP(ctx context.Context, tenantID int64, ipAddress string) error {
	return r.db.WithContext(ctx).
		Table("blocked_ips").
		Where("tenant_id = ? AND ip_address = ?", tenantID, ipAddress).
		Delete(nil).Error
}

// GetSessionStats 获取会话统计
func (r *AuthRepositoryImpl) GetSessionStats(ctx context.Context, tenantID int64) (*repository.SessionStats, error) {
	stats := &repository.SessionStats{}

	// 总会话数
	err := r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.TotalSessions).Error
	if err != nil {
		return nil, err
	}

	// 活跃会话数
	err = r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("tenant_id = ? AND status = 'active' AND expires_at > ?", tenantID, time.Now()).
		Count(&stats.ActiveSessions).Error
	if err != nil {
		return nil, err
	}

	// 过期会话数
	err = r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("tenant_id = ? AND status = 'expired'", tenantID).
		Count(&stats.ExpiredSessions).Error
	if err != nil {
		return nil, err
	}

	// 撤销会话数
	err = r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("tenant_id = ? AND status = 'revoked'", tenantID).
		Count(&stats.RevokedSessions).Error
	if err != nil {
		return nil, err
	}

	// 登出会话数
	err = r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("tenant_id = ? AND status = 'logged_out'", tenantID).
		Count(&stats.LoggedOutSessions).Error

	return stats, err
}

// GetLoginAttemptStats 获取登录尝试统计
func (r *AuthRepositoryImpl) GetLoginAttemptStats(ctx context.Context, tenantID int64) (*repository.LoginAttemptStats, error) {
	stats := &repository.LoginAttemptStats{}

	// 总尝试次数
	err := r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("tenant_id = ?", tenantID).
		Count(&stats.TotalAttempts).Error
	if err != nil {
		return nil, err
	}

	// 成功尝试次数
	err = r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("tenant_id = ? AND status = 'success'", tenantID).
		Count(&stats.SuccessfulAttempts).Error
	if err != nil {
		return nil, err
	}

	// 失败尝试次数
	err = r.db.WithContext(ctx).
		Model(&entity.LoginAttempt{}).
		Where("tenant_id = ? AND status = 'failed'", tenantID).
		Count(&stats.FailedAttempts).Error
	if err != nil {
		return nil, err
	}

	// 被封禁的IP数量
	err = r.db.WithContext(ctx).
		Table("blocked_ips").
		Where("tenant_id = ? AND blocked_until > ?", tenantID, time.Now()).
		Count(&stats.BlockedIPs).Error

	return stats, err
}
