package model

import (
	"time"

	"platforms-user/internal/domain/verification/entity"
)

// VerificationPolicy 验证策略数据库模型
type VerificationPolicy struct {
	ID                   int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID             int64      `gorm:"not null;index:idx_tenant_id" json:"tenant_id"`
	Scene                string     `gorm:"not null;size:50;index:idx_scene;column:scene" json:"scene"`
	Dimension            string     `gorm:"not null;size:50;index:idx_dimension" json:"dimension"`
	ConditionExpr        string     `gorm:"type:text;column:condition_expr" json:"condition_expr"`
	NeedVerification     bool       `gorm:"type:tinyint(1);default:1;column:need_verification" json:"need_verification"`
	VerificationLevel    string     `gorm:"type:varchar(20);not null;default:'medium';column:verification_level" json:"verification_level"`
	TargetType           int        `gorm:"not null;default:1;column:target_type" json:"target_type"`
	TokenType            int        `gorm:"not null;default:2;column:token_type" json:"token_type"`
	TokenLength          int        `gorm:"not null;default:6;column:token_length" json:"token_length"`
	ExpireMinutes        int        `gorm:"not null;default:10;column:expire_minutes" json:"expire_minutes"`
	MaxAttempts          int        `gorm:"not null;default:3;column:max_attempts" json:"max_attempts"`
	RateLimitPerMinute   int        `gorm:"not null;default:1;column:rate_limit_per_minute" json:"rate_limit_per_minute"`
	RateLimitPerHour     int        `gorm:"not null;default:5;column:rate_limit_per_hour" json:"rate_limit_per_hour"`
	RateLimitPerDay      int        `gorm:"not null;default:20;column:rate_limit_per_day" json:"rate_limit_per_day"`
	TemplateCode         string     `gorm:"type:varchar(100);column:template_code" json:"template_code"`
	IsActive             bool       `gorm:"not null;default:true;index:idx_is_active;column:is_active" json:"is_active"`
	Priority             int        `gorm:"not null;default:100" json:"priority"`
	Description          string     `gorm:"type:varchar(500)" json:"description"`
	CreatedAt            time.Time  `gorm:"autoCreateTime;column:created_at" json:"created_at"`
	UpdatedAt            time.Time  `gorm:"autoUpdateTime;column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (VerificationPolicy) TableName() string {
	return "verification_policies"
}

// ToEntity 转换为领域实体
func (m *VerificationPolicy) ToEntity() *entity.VerificationPolicy {
	return &entity.VerificationPolicy{
		ID:                m.ID,
		TenantID:          m.TenantID,
		Scene:             m.Scene,
		Dimension:         m.Dimension,
		ConditionExpr:     m.ConditionExpr,
		NeedVerification:  m.NeedVerification,
		VerificationLevel: m.VerificationLevel,
		TargetType:        m.TargetType,
		TokenType:         m.TokenType,
		TokenLength:       m.TokenLength,
		ExpireMinutes:     m.ExpireMinutes,
		MaxAttempts:       m.MaxAttempts,
		RateLimitPerMinute: m.RateLimitPerMinute,
		RateLimitPerHour:  m.RateLimitPerHour,
		RateLimitPerDay:   m.RateLimitPerDay,
		TemplateCode:      m.TemplateCode,
		IsActive:          m.IsActive,
		Priority:          m.Priority,
		Description:       m.Description,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// FromEntity 从领域实体转换
func (m *VerificationPolicy) FromEntity(policy *entity.VerificationPolicy) {
	m.ID = policy.ID
	m.TenantID = policy.TenantID
	m.Scene = policy.Scene
	m.Dimension = policy.Dimension
	m.ConditionExpr = policy.ConditionExpr
	m.NeedVerification = policy.NeedVerification
	m.VerificationLevel = policy.VerificationLevel
	m.TargetType = policy.TargetType
	m.TokenType = policy.TokenType
	m.TokenLength = policy.TokenLength
	m.ExpireMinutes = policy.ExpireMinutes
	m.MaxAttempts = policy.MaxAttempts
	m.RateLimitPerMinute = policy.RateLimitPerMinute
	m.RateLimitPerHour = policy.RateLimitPerHour
	m.RateLimitPerDay = policy.RateLimitPerDay
	m.TemplateCode = policy.TemplateCode
	m.IsActive = policy.IsActive
	m.Priority = policy.Priority
	m.Description = policy.Description
	m.CreatedAt = policy.CreatedAt
	m.UpdatedAt = policy.UpdatedAt
}

// NewVerificationPolicy 创建新的数据库模型
func NewVerificationPolicy() *VerificationPolicy {
	return &VerificationPolicy{}
}

// NewVerificationPolicyFromEntity 从实体创建数据库模型
func NewVerificationPolicyFromEntity(policy *entity.VerificationPolicy) *VerificationPolicy {
	model := NewVerificationPolicy()
	model.FromEntity(policy)
	return model
}