package persistence

import (
	"context"
	"fmt"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"
	"time"

	"gorm.io/gorm"
	"platforms-pkg/logiface"
)

// VerificationLogRepositoryImpl 验证日志仓储实现
type VerificationLogRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewVerificationLogRepository 创建验证日志仓储实例
func NewVerificationLogRepository(db *gorm.DB, logger logiface.Logger) repository.VerificationLogRepository {
	return &VerificationLogRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建验证日志
func (r *VerificationLogRepositoryImpl) Create(ctx context.Context, log *entity.VerificationLog) error {
	if err := r.db.WithContext(ctx).Create(log).Error; err != nil {
		r.logger.Error(ctx, "Failed to create verification log",
			logiface.Error(err),
			logiface.Int64("tenant_id", log.TenantID),
			logiface.Int64("token_id", log.TokenID),
			logiface.Int("action", int(log.Action)))
		return fmt.Errorf("failed to create verification log: %w", err)
	}

	return nil
}

// BatchCreate 批量创建验证日志
func (r *VerificationLogRepositoryImpl) BatchCreate(ctx context.Context, logs []*entity.VerificationLog) error {
	if len(logs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).Create(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to batch create verification logs",
			logiface.Error(err),
			logiface.Int("log_count", len(logs)))
		return fmt.Errorf("failed to batch create verification logs: %w", err)
	}

	r.logger.Info(ctx, "Verification logs batch created successfully",
		logiface.Int("log_count", len(logs)))

	return nil
}

// FindByID 根据ID查找验证日志
func (r *VerificationLogRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.VerificationLog, error) {
	var log entity.VerificationLog
	if err := r.db.WithContext(ctx).First(&log, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find verification log by ID",
			logiface.Error(err),
			logiface.Int64("log_id", id))
		return nil, fmt.Errorf("failed to find verification log: %w", err)
	}

	return &log, nil
}

// Delete 删除验证日志
func (r *VerificationLogRepositoryImpl) Delete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Delete(&entity.VerificationLog{}, id)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete verification log",
			logiface.Error(result.Error),
			logiface.Int64("log_id", id))
		return fmt.Errorf("failed to delete verification log: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("verification log not found: %d", id)
	}

	return nil
}

// FindByTokenID 根据令牌ID查找验证日志
func (r *VerificationLogRepositoryImpl) FindByTokenID(ctx context.Context, tokenID int64) ([]*entity.VerificationLog, error) {
	var logs []*entity.VerificationLog
	if err := r.db.WithContext(ctx).Where("token_id = ?", tokenID).Order("created_at DESC").Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs by token ID",
			logiface.Error(err),
			logiface.Int64("token_id", tokenID))
		return nil, fmt.Errorf("failed to find verification logs: %w", err)
	}

	return logs, nil
}

// FindByTenantID 根据租户ID查找验证日志
func (r *VerificationLogRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64, limit int) ([]*entity.VerificationLog, error) {
	var logs []*entity.VerificationLog
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs by tenant ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to find verification logs: %w", err)
	}

	return logs, nil
}

// FindByAction 根据操作类型查找验证日志
func (r *VerificationLogRepositoryImpl) FindByAction(ctx context.Context, tenantID int64, action entity.LogAction, limit int) ([]*entity.VerificationLog, error) {
	var logs []*entity.VerificationLog
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND action = ?", tenantID, action).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs by action",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("action", int(action)))
		return nil, fmt.Errorf("failed to find verification logs: %w", err)
	}

	return logs, nil
}

// FindByTimeRange 根据时间范围查找验证日志
func (r *VerificationLogRepositoryImpl) FindByTimeRange(ctx context.Context, tenantID int64, startTime, endTime time.Time) ([]*entity.VerificationLog, error) {
	var logs []*entity.VerificationLog
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND created_at BETWEEN ? AND ?",
		tenantID, startTime, endTime).Order("created_at DESC").Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs by time range",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Time("start_time", startTime),
			logiface.Time("end_time", endTime))
		return nil, fmt.Errorf("failed to find verification logs: %w", err)
	}

	return logs, nil
}

// FindByIPAddress 根据IP地址查找验证日志
func (r *VerificationLogRepositoryImpl) FindByIPAddress(ctx context.Context, tenantID int64, ipAddress string, limit int) ([]*entity.VerificationLog, error) {
	var logs []*entity.VerificationLog
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND ip_address = ?", tenantID, ipAddress).Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs by IP address",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip_address", ipAddress))
		return nil, fmt.Errorf("failed to find verification logs: %w", err)
	}

	return logs, nil
}

// CountByAction 统计操作类型的日志数量
func (r *VerificationLogRepositoryImpl) CountByAction(ctx context.Context, tenantID int64, action entity.LogAction, since time.Time) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Where("tenant_id = ? AND action = ? AND created_at >= ?", tenantID, action, since)

	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count logs by action",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("action", int(action)))
		return 0, fmt.Errorf("failed to count logs: %w", err)
	}

	return count, nil
}

// CountByTokenID 统计令牌的日志数量
func (r *VerificationLogRepositoryImpl) CountByTokenID(ctx context.Context, tokenID int64) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Where("token_id = ?", tokenID).Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count logs by token ID",
			logiface.Error(err),
			logiface.Int64("token_id", tokenID))
		return 0, fmt.Errorf("failed to count logs: %w", err)
	}

	return count, nil
}

// CountByTimeRange 统计时间范围内的日志数量
func (r *VerificationLogRepositoryImpl) CountByTimeRange(ctx context.Context, tenantID int64, startTime, endTime time.Time) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ?", tenantID, startTime, endTime).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count logs by time range",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count logs: %w", err)
	}

	return count, nil
}

// CountSuccessfulOperations 统计成功操作数量
func (r *VerificationLogRepositoryImpl) CountSuccessfulOperations(ctx context.Context, tenantID int64, since time.Time) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Where("tenant_id = ? AND success = ? AND created_at >= ?", tenantID, true, since).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count successful operations",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count successful operations: %w", err)
	}

	return count, nil
}

// CountFailedOperations 统计失败操作数量
func (r *VerificationLogRepositoryImpl) CountFailedOperations(ctx context.Context, tenantID int64, since time.Time) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Where("tenant_id = ? AND success = ? AND created_at >= ?", tenantID, false, since).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count failed operations",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count failed operations: %w", err)
	}

	return count, nil
}

// CleanupOldLogs 清理旧日志
func (r *VerificationLogRepositoryImpl) CleanupOldLogs(ctx context.Context, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("created_at < ?", before).Delete(&entity.VerificationLog{})
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to cleanup old logs",
			logiface.Error(result.Error),
			logiface.Time("before", before))
		return 0, fmt.Errorf("failed to cleanup old logs: %w", result.Error)
	}

	r.logger.Info(ctx, "Old logs cleaned up",
		logiface.Int64("deleted_count", result.RowsAffected),
		logiface.Time("before", before))

	return result.RowsAffected, nil
}

// CleanupByTenant 清理租户的旧日志
func (r *VerificationLogRepositoryImpl) CleanupByTenant(ctx context.Context, tenantID int64, before time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("tenant_id = ? AND created_at < ?", tenantID, before).Delete(&entity.VerificationLog{})
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to cleanup tenant logs",
			logiface.Error(result.Error),
			logiface.Int64("tenant_id", tenantID),
			logiface.Time("before", before))
		return 0, fmt.Errorf("failed to cleanup tenant logs: %w", result.Error)
	}

	r.logger.Info(ctx, "Tenant logs cleaned up",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("deleted_count", result.RowsAffected),
		logiface.Time("before", before))

	return result.RowsAffected, nil
}

// FindWithPagination 分页查询验证日志
func (r *VerificationLogRepositoryImpl) FindWithPagination(ctx context.Context, filter *entity.VerificationLogFilter) ([]*entity.VerificationLog, int64, error) {
	if err := filter.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid filter: %w", err)
	}

	query := r.db.WithContext(ctx).Model(&entity.VerificationLog{})

	// 应用过滤条件
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.TokenID != nil {
		query = query.Where("token_id = ?", *filter.TokenID)
	}
	if filter.Action != nil {
		query = query.Where("action = ?", *filter.Action)
	}
	if filter.Success != nil {
		query = query.Where("success = ?", *filter.Success)
	}
	if filter.StartTime != nil {
		query = query.Where("created_at >= ?", *filter.StartTime)
	}
	if filter.EndTime != nil {
		query = query.Where("created_at <= ?", *filter.EndTime)
	}
	if filter.IPAddress != "" {
		query = query.Where("ip_address = ?", filter.IPAddress)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count verification logs",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to count logs: %w", err)
	}

	// 分页查询
	var logs []*entity.VerificationLog
	query = query.Order("created_at DESC").
		Offset(filter.GetOffset()).
		Limit(filter.GetLimit())

	if err := query.Find(&logs).Error; err != nil {
		r.logger.Error(ctx, "Failed to find verification logs with pagination",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to find logs: %w", err)
	}

	return logs, total, nil
}

// FindSuspiciousActivities 查找可疑活动
func (r *VerificationLogRepositoryImpl) FindSuspiciousActivities(ctx context.Context, tenantID int64, since time.Time) ([]*repository.SuspiciousActivity, error) {
	// 简单实现，查找失败次数较多的IP
	var results []struct {
		IPAddress    string
		FailureCount int64
	}

	err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Select("ip_address, COUNT(*) as failure_count").
		Where("tenant_id = ? AND success = ? AND created_at >= ?", tenantID, false, since).
		Group("ip_address").
		Having("COUNT(*) > ?", 10). // 失败超过10次认为可疑
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find suspicious activities: %w", err)
	}

	activities := make([]*repository.SuspiciousActivity, len(results))
	for i, result := range results {
		activities[i] = &repository.SuspiciousActivity{
			IPAddress:       result.IPAddress,
			ActivityType:    "verification_failure",
			Count:           result.FailureCount,
			FirstOccurrence: since,
			LastOccurrence:  time.Now(),
			Description:     fmt.Sprintf("IP %s has %d verification failures", result.IPAddress, result.FailureCount),
			RiskLevel:       "medium",
		}
	}

	return activities, nil
}

// FindHighFrequencyIPs 查找高频IP
func (r *VerificationLogRepositoryImpl) FindHighFrequencyIPs(ctx context.Context, tenantID int64, since time.Time, threshold int) ([]*repository.IPActivity, error) {
	var results []struct {
		IPAddress string
		Count     int64
	}

	err := r.db.WithContext(ctx).Model(&entity.VerificationLog{}).
		Select("ip_address, COUNT(*) as count").
		Where("tenant_id = ? AND created_at >= ?", tenantID, since).
		Group("ip_address").
		Having("COUNT(*) > ?", threshold).
		Order("count DESC").
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find high frequency IPs: %w", err)
	}

	activities := make([]*repository.IPActivity, len(results))
	for i, result := range results {
		activities[i] = &repository.IPActivity{
			IPAddress:   result.IPAddress,
			TotalLogs:   result.Count,
			SuccessLogs: 0, // 简化实现
			FailedLogs:  result.Count,
			FirstSeen:   since,
			LastSeen:    time.Now(),
			UserAgents:  []string{},
			Actions:     make(map[entity.LogAction]int64),
		}
	}

	return activities, nil
}

// GetStatistics 获取统计信息
func (r *VerificationLogRepositoryImpl) GetStatistics(ctx context.Context, tenantID int64, startTime, endTime time.Time) (*repository.LogStatistics, error) {
	// 简单实现
	total, _ := r.CountByTimeRange(ctx, tenantID, startTime, endTime)
	successful, _ := r.CountSuccessfulOperations(ctx, tenantID, startTime)
	failed, _ := r.CountFailedOperations(ctx, tenantID, startTime)

	stats := &repository.LogStatistics{
		TotalLogs:      total,
		SuccessfulLogs: successful,
		FailedLogs:     failed,
		ByAction:       make(map[entity.LogAction]int64),
		ByHour:         make(map[int]int64),
		TopIPs:         []*repository.IPActivity{},
		ErrorMessages:  make(map[string]int64),
	}
	stats.CalculateSuccessRate()

	return stats, nil
}

// GetHourlyStatistics 获取小时统计
func (r *VerificationLogRepositoryImpl) GetHourlyStatistics(ctx context.Context, tenantID int64, date time.Time) (map[int]*repository.HourlyLogStats, error) {
	// 简单实现，返回空map
	return make(map[int]*repository.HourlyLogStats), nil
}

// GetDailyStatistics 获取日统计
func (r *VerificationLogRepositoryImpl) GetDailyStatistics(ctx context.Context, tenantID int64, startDate, endDate time.Time) (map[string]*repository.DailyLogStats, error) {
	// 简单实现，返回空map
	return make(map[string]*repository.DailyLogStats), nil
}
