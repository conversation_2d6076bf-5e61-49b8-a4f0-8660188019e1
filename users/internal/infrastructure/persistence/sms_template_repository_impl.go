package persistence

import (
	"context"
	"fmt"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"

	"gorm.io/gorm"
	"platforms-pkg/logiface"
)

// SMSTemplateRepositoryImpl 短信模板仓储实现
type SMSTemplateRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSMSTemplateRepository 创建短信模板仓储实例
func NewSMSTemplateRepository(db *gorm.DB, logger logiface.Logger) repository.SMSTemplateRepository {
	return &SMSTemplateRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建短信模板
func (r *SMSTemplateRepositoryImpl) Create(ctx context.Context, template *entity.SMSTemplate) error {
	if err := r.db.WithContext(ctx).Create(template).Error; err != nil {
		r.logger.Error(ctx, "Failed to create SMS template",
			logiface.Error(err),
			logiface.Int64("tenant_id", template.TenantID),
			logiface.String("code", template.Code),
			logiface.String("name", template.Name))
		return fmt.Errorf("failed to create SMS template: %w", err)
	}

	r.logger.Info(ctx, "SMS template created successfully",
		logiface.Int64("template_id", template.ID),
		logiface.Int64("tenant_id", template.TenantID),
		logiface.String("code", template.Code),
		logiface.String("name", template.Name))

	return nil
}

// Update 更新短信模板
func (r *SMSTemplateRepositoryImpl) Update(ctx context.Context, template *entity.SMSTemplate) error {
	result := r.db.WithContext(ctx).Save(template)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update SMS template",
			logiface.Error(result.Error),
			logiface.Int64("template_id", template.ID))
		return fmt.Errorf("failed to update SMS template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("SMS template not found: %d", template.ID)
	}

	r.logger.Info(ctx, "SMS template updated successfully",
		logiface.Int64("template_id", template.ID))

	return nil
}

// Delete 删除短信模板
func (r *SMSTemplateRepositoryImpl) Delete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Delete(&entity.SMSTemplate{}, id)
	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete SMS template",
			logiface.Error(result.Error),
			logiface.Int64("template_id", id))
		return fmt.Errorf("failed to delete SMS template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("SMS template not found: %d", id)
	}

	r.logger.Info(ctx, "SMS template deleted successfully",
		logiface.Int64("template_id", id))

	return nil
}

// FindByID 根据ID查找短信模板
func (r *SMSTemplateRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.SMSTemplate, error) {
	var template entity.SMSTemplate
	if err := r.db.WithContext(ctx).First(&template, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find SMS template by ID",
			logiface.Error(err),
			logiface.Int64("template_id", id))
		return nil, fmt.Errorf("failed to find SMS template: %w", err)
	}

	return &template, nil
}

// FindByCode 根据代码查找短信模板
func (r *SMSTemplateRepositoryImpl) FindByCode(ctx context.Context, tenantID int64, code string) (*entity.SMSTemplate, error) {
	var template entity.SMSTemplate
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND code = ?", tenantID, code).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find SMS template by code",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("code", code))
		return nil, fmt.Errorf("failed to find SMS template: %w", err)
	}

	return &template, nil
}

// FindByTenantID 根据租户ID查找短信模板
func (r *SMSTemplateRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64) ([]*entity.SMSTemplate, error) {
	var templates []*entity.SMSTemplate
	if err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("name").Find(&templates).Error; err != nil {
		r.logger.Error(ctx, "Failed to find SMS templates by tenant ID",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to find SMS templates: %w", err)
	}

	return templates, nil
}

// FindActiveTemplates 查找活跃的短信模板
func (r *SMSTemplateRepositoryImpl) FindActiveTemplates(ctx context.Context, tenantID int64) ([]*entity.SMSTemplate, error) {
	var templates []*entity.SMSTemplate
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND is_active = ?", tenantID, true).
		Order("name").Find(&templates).Error; err != nil {
		r.logger.Error(ctx, "Failed to find active SMS templates",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to find active SMS templates: %w", err)
	}

	return templates, nil
}

// FindSystemTemplates 查找系统模板
func (r *SMSTemplateRepositoryImpl) FindSystemTemplates(ctx context.Context) ([]*entity.SMSTemplate, error) {
	var templates []*entity.SMSTemplate
	if err := r.db.WithContext(ctx).Where("is_system = ?", true).Order("name").Find(&templates).Error; err != nil {
		r.logger.Error(ctx, "Failed to find system SMS templates",
			logiface.Error(err))
		return nil, fmt.Errorf("failed to find system SMS templates: %w", err)
	}

	return templates, nil
}

// FindByKeyword 根据关键词查找短信模板
func (r *SMSTemplateRepositoryImpl) FindByKeyword(ctx context.Context, tenantID int64, keyword string) ([]*entity.SMSTemplate, error) {
	var templates []*entity.SMSTemplate
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	if keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	if err := query.Order("name").Find(&templates).Error; err != nil {
		r.logger.Error(ctx, "Failed to find SMS templates by keyword",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("keyword", keyword))
		return nil, fmt.Errorf("failed to find SMS templates: %w", err)
	}

	return templates, nil
}

// EnableTemplate 启用模板
func (r *SMSTemplateRepositoryImpl) EnableTemplate(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("id = ?", id).Update("is_active", true)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to enable SMS template",
			logiface.Error(result.Error),
			logiface.Int64("template_id", id))
		return fmt.Errorf("failed to enable SMS template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("SMS template not found: %d", id)
	}

	r.logger.Info(ctx, "SMS template enabled",
		logiface.Int64("template_id", id))

	return nil
}

// DisableTemplate 禁用模板
func (r *SMSTemplateRepositoryImpl) DisableTemplate(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("id = ?", id).Update("is_active", false)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to disable SMS template",
			logiface.Error(result.Error),
			logiface.Int64("template_id", id))
		return fmt.Errorf("failed to disable SMS template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("SMS template not found: %d", id)
	}

	r.logger.Info(ctx, "SMS template disabled",
		logiface.Int64("template_id", id))

	return nil
}

// BatchUpdateStatus 批量更新状态
func (r *SMSTemplateRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []int64, isActive bool) error {
	if len(ids) == 0 {
		return fmt.Errorf("ids cannot be empty")
	}

	result := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("id IN ?", ids).Update("is_active", isActive)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to batch update SMS template status",
			logiface.Error(result.Error),
			logiface.Any("ids", ids),
			logiface.Bool("is_active", isActive))
		return fmt.Errorf("failed to batch update SMS template status: %w", result.Error)
	}

	r.logger.Info(ctx, "SMS templates status updated",
		logiface.Int64("updated_count", result.RowsAffected),
		logiface.Bool("is_active", isActive))

	return nil
}

// ExistsByCode 检查模板代码是否存在
func (r *SMSTemplateRepositoryImpl) ExistsByCode(ctx context.Context, tenantID int64, code string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ? AND code = ?", tenantID, code).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check SMS template existence",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("code", code))
		return false, fmt.Errorf("failed to check SMS template existence: %w", err)
	}

	return count > 0, nil
}

// IsTemplateActive 检查模板是否活跃
func (r *SMSTemplateRepositoryImpl) IsTemplateActive(ctx context.Context, tenantID int64, code string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ? AND code = ? AND is_active = ?", tenantID, code, true).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check SMS template active status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("code", code))
		return false, fmt.Errorf("failed to check SMS template active status: %w", err)
	}

	return count > 0, nil
}

// IsSystemTemplate 检查是否是系统模板
func (r *SMSTemplateRepositoryImpl) IsSystemTemplate(ctx context.Context, id int64) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("id = ? AND is_system = ?", id, true).
		Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to check if SMS template is system template",
			logiface.Error(err),
			logiface.Int64("template_id", id))
		return false, fmt.Errorf("failed to check system template status: %w", err)
	}

	return count > 0, nil
}

// GetEffectiveTemplate 获取有效模板（租户模板优先，回退到系统模板）
func (r *SMSTemplateRepositoryImpl) GetEffectiveTemplate(ctx context.Context, tenantID int64, code string) (*entity.SMSTemplate, error) {
	// 首先尝试获取租户特定模板
	tenantTemplate, err := r.FindByCode(ctx, tenantID, code)
	if err != nil {
		return nil, err
	}

	if tenantTemplate != nil && tenantTemplate.IsActive {
		return tenantTemplate, nil
	}

	// 如果租户模板不存在或未激活，回退到系统模板
	systemTemplate, err := r.FindByCode(ctx, 0, code)
	if err != nil {
		return nil, err
	}

	if systemTemplate != nil && systemTemplate.IsActive {
		return systemTemplate, nil
	}

	return nil, nil
}

// FindWithPagination 分页查询短信模板
func (r *SMSTemplateRepositoryImpl) FindWithPagination(ctx context.Context, filter *entity.SMSTemplateFilter) ([]*entity.SMSTemplate, int64, error) {
	if err := filter.Validate(); err != nil {
		return nil, 0, fmt.Errorf("invalid filter: %w", err)
	}

	query := r.db.WithContext(ctx).Model(&entity.SMSTemplate{})

	// 应用过滤条件
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}
	if filter.Keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR description LIKE ?",
			"%"+filter.Keyword+"%", "%"+filter.Keyword+"%", "%"+filter.Keyword+"%")
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.IsSystem != nil {
		query = query.Where("is_system = ?", *filter.IsSystem)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count SMS templates",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// 分页查询
	var templates []*entity.SMSTemplate
	query = query.Order("name").
		Offset(filter.GetOffset()).
		Limit(filter.GetLimit())

	if err := query.Find(&templates).Error; err != nil {
		r.logger.Error(ctx, "Failed to find SMS templates with pagination",
			logiface.Error(err))
		return nil, 0, fmt.Errorf("failed to find templates: %w", err)
	}

	return templates, total, nil
}

// CountByTenant 统计租户的模板数量
func (r *SMSTemplateRepositoryImpl) CountByTenant(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ?", tenantID).Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count SMS templates by tenant",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count templates: %w", err)
	}

	return count, nil
}

// CountActiveTemplates 统计活跃模板数量
func (r *SMSTemplateRepositoryImpl) CountActiveTemplates(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ? AND is_active = ?", tenantID, true).Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count active SMS templates",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, fmt.Errorf("failed to count active templates: %w", err)
	}

	return count, nil
}

// GetTemplateStatistics 获取模板统计信息
func (r *SMSTemplateRepositoryImpl) GetTemplateStatistics(ctx context.Context, tenantID int64) (*repository.TemplateStatistics, error) {
	stats := &repository.TemplateStatistics{
		ByTenant: make(map[int64]int64),
	}

	// 统计总数
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ?", tenantID).Count(&stats.TotalTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count total templates: %w", err)
	}

	// 统计活跃和非活跃数量
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ? AND is_active = ?", tenantID, true).
		Count(&stats.ActiveTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count active templates: %w", err)
	}

	stats.InactiveTemplates = stats.TotalTemplates - stats.ActiveTemplates

	// 统计系统和自定义模板数量
	if err := r.db.WithContext(ctx).Model(&entity.SMSTemplate{}).
		Where("tenant_id = ? AND is_system = ?", tenantID, true).
		Count(&stats.SystemTemplates).Error; err != nil {
		return nil, fmt.Errorf("failed to count system templates: %w", err)
	}

	stats.CustomTemplates = stats.TotalTemplates - stats.SystemTemplates

	return stats, nil
}

// CloneTemplate 克隆模板
func (r *SMSTemplateRepositoryImpl) CloneTemplate(ctx context.Context, sourceID int64, newTenantID int64, newCode string) (*entity.SMSTemplate, error) {
	// 获取源模板
	var sourceTemplate entity.SMSTemplate
	if err := r.db.WithContext(ctx).First(&sourceTemplate, sourceID).Error; err != nil {
		return nil, fmt.Errorf("failed to find source template: %w", err)
	}

	// 创建新模板
	newTemplate := &entity.SMSTemplate{
		TenantID:    newTenantID,
		Name:        sourceTemplate.Name + " (Copy)",
		Code:        newCode,
		Description: sourceTemplate.Description,
		Content:     sourceTemplate.Content,
		Variables:   sourceTemplate.Variables,
		IsActive:    false, // 克隆的模板默认不激活
		IsSystem:    false, // 克隆的模板不是系统模板
	}

	if err := r.db.WithContext(ctx).Create(newTemplate).Error; err != nil {
		return nil, fmt.Errorf("failed to create cloned template: %w", err)
	}

	return newTemplate, nil
}

// CopySystemTemplatesToTenant 复制系统模板到租户
func (r *SMSTemplateRepositoryImpl) CopySystemTemplatesToTenant(ctx context.Context, tenantID int64) error {
	// 简单实现，实际项目中可以根据需要完善
	return nil
}

// ValidateTemplate 验证模板
func (r *SMSTemplateRepositoryImpl) ValidateTemplate(ctx context.Context, template *entity.SMSTemplate) (*repository.TemplateValidationResult, error) {
	// 简单实现，返回验证通过
	return &repository.TemplateValidationResult{
		Valid:          true,
		ErrorMessages:  []string{},
		Warnings:       []string{},
		SyntaxValid:    true,
		VariablesValid: true,
		LengthValid:    true,
		ContentPreview: template.Content,
	}, nil
}

// TestTemplate 测试模板
func (r *SMSTemplateRepositoryImpl) TestTemplate(ctx context.Context, templateID int64, testData map[string]interface{}) (*repository.TemplateTestResult, error) {
	// 简单实现
	return &repository.TemplateTestResult{
		Success:         true,
		RenderedContent: "Test rendered content",
		ContentLength:   20,
		ErrorMessage:    "",
		TestData:        testData,
		RenderTime:      10,
	}, nil
}
