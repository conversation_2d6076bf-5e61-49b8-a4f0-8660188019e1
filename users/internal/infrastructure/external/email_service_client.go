package external

import (
	"context"
	"fmt"
	"time"

	"platforms-email/api/emailpb"
	"platforms-pkg/grpcregistry"
	"platforms-pkg/logiface"
	"platforms-pkg/usercontext"
	"platforms-user/internal/domain/errors"

	"google.golang.org/grpc"
)

// EmailServiceClient 邮件服务客户端包装器
type EmailServiceClient struct {
	serviceName string
	logger      logiface.Logger
}

// NewEmailServiceClient 创建邮件服务客户端
func NewEmailServiceClient(logger logiface.Logger) *EmailServiceClient {
	return &EmailServiceClient{
		serviceName: "platforms-email",
		logger:      logger,
	}
}

// GetEmailClient 获取邮件服务gRPC客户端
func (c *EmailServiceClient) GetEmailClient(ctx context.Context) (emailpb.EmailServiceClient, error) {
	// 使用全局管理器获取客户端
	client, err := grpcregistry.GetClientGlobal(ctx, c.serviceName, func(conn *grpc.ClientConn) interface{} {
		return emailpb.NewEmailServiceClient(conn)
	})
	if err != nil {
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("email service client acquisition failed: %v", err))
	}

	emailClient, ok := client.(emailpb.EmailServiceClient)
	if !ok {
		return nil, errors.NewIntegrationFailedError("failed to cast to EmailServiceClient")
	}

	return emailClient, nil
}

// SendTemplateEmail 基于模板发送邮件
func (c *EmailServiceClient) SendTemplateEmail(ctx context.Context, req *emailpb.SendTemplateEmailRequest) (*emailpb.SendTemplateEmailResponse, error) {
	client, err := c.GetEmailClient(ctx)
	if err != nil {
		return nil, err
	}

	c.logger.Info(ctx, "Sending template email via gRPC",
		logiface.String("service", c.serviceName),
		logiface.String("template_code", req.TemplateCode),
		logiface.Any("to", req.To))

	// 调用 gRPC 服务
	resp, err := client.SendTemplateEmail(ctx, req)
	if err != nil {
		c.logger.Error(ctx, "Failed to send template email via gRPC",
			logiface.Error(err),
			logiface.String("service", c.serviceName))
		return nil, errors.NewIntegrationFailedError(fmt.Sprintf("gRPC call failed: %v", err))
	}

	c.logger.Debug(ctx, "Got template email response via gRPC",
		logiface.String("service", c.serviceName),
		logiface.Int("code", int(resp.Code)))

	c.logger.Info(ctx, "Template email sent successfully via gRPC",
		logiface.String("service", c.serviceName),
		logiface.String("template_code", req.TemplateCode))

	return resp, nil
}

// SendVerificationEmail 发送验证邮件的便捷方法
func (c *EmailServiceClient) SendVerificationEmail(ctx context.Context, templateCode string, to string, variables map[string]string) error {
	tenantID, _ := usercontext.GetTenantID(ctx)

	req := &emailpb.SendTemplateEmailRequest{
		TenantId:     tenantID,
		TemplateCode: templateCode,
		To:           []string{to},
		Variables:    variables,
		RequestId:    fmt.Sprintf("verify-%d-%s", time.Now().Unix(), templateCode),
	}

	resp, err := c.SendTemplateEmail(ctx, req)
	if err != nil {
		return err
	}

	if resp.Code != 200 && resp.Code != 0 {
		return errors.NewBusinessError(errors.CodeEmailSendFailed, fmt.Sprintf("email service error: %s", resp.Message))
	}

	return nil
}

// SendBulkTemplateEmail 批量发送模板邮件
func (c *EmailServiceClient) SendBulkTemplateEmail(ctx context.Context, templateCode string, recipients []string, variables map[string]string) error {
	tenantID, _ := usercontext.GetTenantID(ctx)

	req := &emailpb.SendTemplateEmailRequest{
		TenantId:     tenantID,
		TemplateCode: templateCode,
		To:           recipients,
		Variables:    variables,
		RequestId:    fmt.Sprintf("bulk-%d-%s", time.Now().Unix(), templateCode),
	}

	resp, err := c.SendTemplateEmail(ctx, req)
	if err != nil {
		return err
	}

	if resp.Code != 200 && resp.Code != 0 {
		return errors.NewBusinessError(errors.CodeEmailSendFailed, fmt.Sprintf("email service error: %s", resp.Message))
	}

	return nil
}

// SendPasswordResetEmail 发送密码重置邮件的便捷方法
func (c *EmailServiceClient) SendPasswordResetEmail(ctx context.Context, to string, resetToken string, resetURL string) error {
	variables := map[string]string{}

	return c.SendVerificationEmail(ctx, "password_reset", to, variables)
}

// SendAccountVerificationEmail 发送账户验证邮件的便捷方法
func (c *EmailServiceClient) SendAccountVerificationEmail(ctx context.Context, to string, verificationToken string, verificationURL string) error {
	variables := map[string]string{
		"verification_token": verificationToken,
		"verification_url":   verificationURL,
		"expire_time":        "24小时",
	}

	return c.SendVerificationEmail(ctx, "account_verification", to, variables)
}

// Health 检查邮件服务健康状态
func (c *EmailServiceClient) Health(ctx context.Context) error {
	manager, err := grpcregistry.GetGlobalManager().GetServiceManager(c.serviceName)
	if err != nil {
		return errors.NewIntegrationFailedError(fmt.Sprintf("service not subscribed: %v", err))
	}

	return manager.Health(ctx)
}

// GetConnectionStats 获取连接统计信息
func (c *EmailServiceClient) GetConnectionStats() map[string]interface{} {
	manager, err := grpcregistry.GetGlobalManager().GetServiceManager(c.serviceName)
	if err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	return manager.GetConnectionStats()
}

// Close 关闭客户端连接
func (c *EmailServiceClient) Close() error {
	manager, err := grpcregistry.GetGlobalManager().GetServiceManager(c.serviceName)
	if err != nil {
		return errors.NewIntegrationFailedError(fmt.Sprintf("service not subscribed: %v", err))
	}

	return manager.Close()
}

// CheckTemplateExists 检查邮件模板是否存在
func (c *EmailServiceClient) CheckTemplateExists(ctx context.Context, tenantID int64, templateCode string) (bool, error) {
	client, err := c.GetEmailClient(ctx)
	if err != nil {
		return false, err
	}

	req := &emailpb.CheckTemplateExistsRequest{
		TenantId:     tenantID,
		TemplateCode: templateCode,
	}

	c.logger.Info(ctx, "Checking email template existence via gRPC",
		logiface.String("service", c.serviceName),
		logiface.String("template_code", templateCode),
		logiface.Int64("tenant_id", tenantID))

	// 调用 gRPC 服务
	resp, err := client.CheckTemplateExists(ctx, req)
	if err != nil {
		c.logger.Error(ctx, "Failed to check email template existence via gRPC",
			logiface.Error(err),
			logiface.String("service", c.serviceName),
			logiface.String("template_code", templateCode),
			logiface.Int64("tenant_id", tenantID))
		return false, err
	}

	// 检查响应状态
	if resp.Code != 200 && resp.Code != 0 {
		return false, errors.NewBusinessError(200100, fmt.Sprintf("email service error: %s", resp.Message))
	}

	// 返回模板是否存在
	return resp.Data.Exists, nil
}
