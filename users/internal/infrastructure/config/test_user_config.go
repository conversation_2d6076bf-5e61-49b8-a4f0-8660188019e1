package config

import (
	"context"
	"platforms-pkg/usercontext"
	"strconv"
)

// TestUserConfig 测试用户配置
type TestUserConfig struct {
	Enabled bool `json:"enabled" toml:"enabled"`
}

// TestUserService 测试用户服务
type TestUserService struct {
	config *TestUserConfig
}

// NewTestUserService 创建测试用户服务
func NewTestUserService(config *TestUserConfig) *TestUserService {
	return &TestUserService{
		config: config,
	}
}

// GetTestUserInfo 获取测试用户信息
// 仅当 userID 为 1 且在开发环境时返回固定的测试用户信息
func (s *TestUserService) GetTestUserInfo(ctx context.Context, userID int64) *usercontext.UserInfo {
	// 只有当用户ID为1时才启用测试功能
	if userID != 1 {
		return nil
	}

	// 检查是否启用测试用户功能
	if !s.config.Enabled {
		return nil
	}

	// 返回固定的测试用户信息
	return &usercontext.UserInfo{
		UserID:     1,
		Username:   "test_user",
		RealName:   "测试用户",
		Email:      "<EMAIL>",
		Phone:      "13800138000",
		TenantID:   1,
		TenantCode: "test_tenant",
		TenantName: "测试租户",
	}
}

// GetTestTenantInfo 获取测试租户信息
func (s *TestUserService) GetTestTenantInfo(ctx context.Context, tenantID int64) *usercontext.TenantInfo {
	// 只有当租户ID为1时才启用测试功能
	if tenantID != 1 {
		return nil
	}

	// 检查是否启用测试用户功能
	if !s.config.Enabled {
		return nil
	}

	// 返回固定的测试租户信息
	return &usercontext.TenantInfo{
		TenantID:   1,
		TenantCode: "test_tenant",
		TenantName: "测试租户",
	}
}

// IsTestUser 检查是否为测试用户
func (s *TestUserService) IsTestUser(userID int64) bool {
	return s.config.Enabled && userID == 1
}

// IsTestTenant 检查是否为测试租户
func (s *TestUserService) IsTestTenant(tenantID int64) bool {
	return s.config.Enabled && tenantID == 1
}

// GetTestUserByHeader 通过请求头获取测试用户信息
func (s *TestUserService) GetTestUserByHeader(userIDHeader string) *usercontext.UserInfo {
	if !s.config.Enabled {
		return nil
	}

	userID, err := strconv.ParseInt(userIDHeader, 10, 64)
	if err != nil || userID != 1 {
		return nil
	}

	return s.GetTestUserInfo(context.Background(), userID)
}

// GetDefaultTestUserInfo 获取默认测试用户信息（用于开发环境快速测试）
func GetDefaultTestUserInfo() *usercontext.UserInfo {
	return &usercontext.UserInfo{
		UserID:     1,
		Username:   "test_user",
		RealName:   "测试用户",
		Email:      "<EMAIL>",
		Phone:      "13800138000",
		TenantID:   1,
		TenantCode: "test_tenant",
		TenantName: "测试租户",
	}
}

// GetDefaultTestTenantInfo 获取默认测试租户信息
func GetDefaultTestTenantInfo() *usercontext.TenantInfo {
	return &usercontext.TenantInfo{
		TenantID:   1,
		TenantCode: "test_tenant",
		TenantName: "测试租户",
	}
}
