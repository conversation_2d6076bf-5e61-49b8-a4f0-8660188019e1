package config

import (
	"context"
	"platforms-pkg/usercontext"
	"platforms-user/pkg/config"

	"github.com/gin-gonic/gin"
)

// TestUserIntegration 测试用户集成服务
type TestUserIntegration struct {
	testUserService *TestUserService
	appConfig       *config.AppConfig
}

// NewTestUserIntegration 创建测试用户集成服务
func NewTestUserIntegration(appConfig *config.AppConfig) *TestUserIntegration {
	testUserService := NewTestUserService(&TestUserConfig{
		Enabled: appConfig.TestUser.Enabled,
	})

	return &TestUserIntegration{
		testUserService: testUserService,
		appConfig:       appConfig,
	}
}

// GetTestUserService 获取测试用户服务
func (t *TestUserIntegration) GetTestUserService() *TestUserService {
	return t.testUserService
}

// IsEnabled 检查测试用户功能是否启用
func (t *TestUserIntegration) IsEnabled() bool {
	return t.appConfig.TestUser.Enabled
}

// CreateTestUserMiddleware 创建测试用户中间件
func (t *TestUserIntegration) CreateTestUserMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只在开发环境且功能启用时处理
		if !t.IsEnabled() {
			c.Next()
			return
		}

		// 检查是否有测试用户ID头部
		userIDHeader := c.GetHeader("X-User-ID")
		if userIDHeader != "1" {
			c.Next()
			return
		}

		// 获取测试用户信息
		testUserInfo := GetDefaultTestUserInfo()
		testTenantInfo := GetDefaultTestTenantInfo()

		// 注入用户和租户信息到上下文
		usercontext.InjectUserAndTenant(c, testUserInfo, testTenantInfo)

		// 设置测试标识
		c.Set("is_test_user", true)
		c.Set("test_user_id", int64(1))

		// 在响应头中添加测试标识（便于调试）
		c.Header("X-Test-User", "true")
		c.Header("X-Test-User-ID", "1")

		c.Next()
	}
}

// CreateTestUserAuthMiddleware 创建测试用户认证中间件
func (t *TestUserIntegration) CreateTestUserAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为测试用户
		if isTestUser, exists := c.Get("is_test_user"); exists && isTestUser.(bool) {
			// 测试用户自动通过认证
			c.Next()
			return
		}

		// 非测试用户，执行正常认证逻辑
		userInfo, exists := usercontext.GetUserInfo(c.Request.Context())
		if !exists || userInfo == nil {
			c.JSON(403, gin.H{
				"success": false,
				"code":    "UNAUTHORIZED",
				"message": "用户未认证",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetUserInfoWithTestSupport 获取用户信息（支持测试用户）
func (t *TestUserIntegration) GetUserInfoWithTestSupport(ctx context.Context, token string, userIDHeader string) *usercontext.UserInfo {
	// 首先检查是否为测试用户
	if t.IsEnabled() && userIDHeader == "1" {
		return GetDefaultTestUserInfo()
	}

	// 这里应该调用实际的用户信息获取逻辑
	// 由于这是一个示例，返回nil让调用方处理
	return nil
}

// GetTenantInfoWithTestSupport 获取租户信息（支持测试租户）
func (t *TestUserIntegration) GetTenantInfoWithTestSupport(ctx context.Context, tenantCode string) *usercontext.TenantInfo {
	// 首先检查是否为测试租户
	if t.IsEnabled() && tenantCode == "test_tenant" {
		return GetDefaultTestTenantInfo()
	}

	// 这里应该调用实际的租户信息获取逻辑
	// 由于这是一个示例，返回nil让调用方处理
	return nil
}
