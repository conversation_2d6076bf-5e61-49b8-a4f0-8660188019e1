package sms

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/logiface"
)

// SMSProvider 短信服务提供商接口
type SMSProvider interface {
	// SendSMS 发送短信
	SendSMS(ctx context.Context, request *SMSRequest) (*SMSResponse, error)
	// GetProviderName 获取提供商名称
	GetProviderName() string
	// IsAvailable 检查服务是否可用
	IsAvailable(ctx context.Context) bool
}

// SMSRequest 短信发送请求
type SMSRequest struct {
	Phone       string            `json:"phone"`        // 手机号
	Content     string            `json:"content"`      // 短信内容
	TemplateID  string            `json:"template_id"`  // 模板ID（可选）
	Variables   map[string]string `json:"variables"`    // 模板变量（可选）
	SignName    string            `json:"sign_name"`    // 签名名称
	Priority    int               `json:"priority"`     // 优先级（1-10）
	RequestID   string            `json:"request_id"`   // 请求ID
	TenantID    int64             `json:"tenant_id"`    // 租户ID
}

// Validate 验证请求参数
func (r *SMSRequest) Validate() error {
	if r.Phone == "" {
		return fmt.Errorf("phone number is required")
	}
	
	if r.Content == "" && r.TemplateID == "" {
		return fmt.Errorf("either content or template_id is required")
	}
	
	if len(r.Content) > 500 {
		return fmt.Errorf("content too long (max 500 characters)")
	}
	
	if r.TenantID <= 0 {
		return fmt.Errorf("tenant_id must be positive")
	}
	
	return nil
}

// SMSResponse 短信发送响应
type SMSResponse struct {
	Success     bool              `json:"success"`      // 是否成功
	MessageID   string            `json:"message_id"`   // 消息ID
	RequestID   string            `json:"request_id"`   // 请求ID
	Provider    string            `json:"provider"`     // 提供商名称
	Cost        float64           `json:"cost"`         // 费用
	ErrorCode   string            `json:"error_code"`   // 错误码
	ErrorMsg    string            `json:"error_msg"`    // 错误信息
	SentAt      time.Time         `json:"sent_at"`      // 发送时间
	Extra       map[string]string `json:"extra"`        // 额外信息
}

// IsSuccessful 检查是否发送成功
func (r *SMSResponse) IsSuccessful() bool {
	return r.Success && r.ErrorCode == ""
}

// HasError 检查是否有错误
func (r *SMSResponse) HasError() bool {
	return !r.Success || r.ErrorCode != ""
}

// SMSService 短信服务
type SMSService struct {
	providers []SMSProvider
	logger    logiface.Logger
	config    *SMSConfig
}

// SMSConfig 短信服务配置
type SMSConfig struct {
	DefaultProvider   string        `json:"default_provider"`   // 默认提供商
	RetryCount        int           `json:"retry_count"`        // 重试次数
	RetryInterval     time.Duration `json:"retry_interval"`     // 重试间隔
	Timeout           time.Duration `json:"timeout"`            // 超时时间
	EnableFailover    bool          `json:"enable_failover"`    // 启用故障转移
	RateLimitPerMin   int           `json:"rate_limit_per_min"` // 每分钟限制
	MaxConcurrent     int           `json:"max_concurrent"`     // 最大并发数
}

// DefaultSMSConfig 默认配置
func DefaultSMSConfig() *SMSConfig {
	return &SMSConfig{
		DefaultProvider:   "aliyun",
		RetryCount:        3,
		RetryInterval:     time.Second * 2,
		Timeout:           time.Second * 30,
		EnableFailover:    true,
		RateLimitPerMin:   100,
		MaxConcurrent:     10,
	}
}

// NewSMSService 创建短信服务
func NewSMSService(providers []SMSProvider, logger logiface.Logger, config *SMSConfig) *SMSService {
	if config == nil {
		config = DefaultSMSConfig()
	}
	
	return &SMSService{
		providers: providers,
		logger:    logger,
		config:    config,
	}
}

// SendSMS 发送短信
func (s *SMSService) SendSMS(ctx context.Context, request *SMSRequest) (*SMSResponse, error) {
	// 验证请求参数
	if err := request.Validate(); err != nil {
		s.logger.Error(ctx, "Invalid SMS request",
			logiface.Error(err),
			logiface.String("phone", request.Phone))
		return nil, fmt.Errorf("invalid SMS request: %w", err)
	}
	
	// 设置超时
	ctx, cancel := context.WithTimeout(ctx, s.config.Timeout)
	defer cancel()
	
	// 尝试发送短信
	var lastErr error
	for attempt := 0; attempt <= s.config.RetryCount; attempt++ {
		if attempt > 0 {
			s.logger.Info(ctx, "Retrying SMS send",
				logiface.Int("attempt", attempt),
				logiface.String("phone", request.Phone))
			
			// 等待重试间隔
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(s.config.RetryInterval):
			}
		}
		
		// 选择提供商并发送
		response, err := s.sendWithProvider(ctx, request)
		if err == nil && response.IsSuccessful() {
			s.logger.Info(ctx, "SMS sent successfully",
				logiface.String("phone", request.Phone),
				logiface.String("provider", response.Provider),
				logiface.String("message_id", response.MessageID))
			return response, nil
		}
		
		lastErr = err
		if response != nil {
			s.logger.Warn(ctx, "SMS send failed",
				logiface.String("phone", request.Phone),
				logiface.String("provider", response.Provider),
				logiface.String("error_code", response.ErrorCode),
				logiface.String("error_msg", response.ErrorMsg))
		}
	}
	
	s.logger.Error(ctx, "SMS send failed after all retries",
		logiface.Error(lastErr),
		logiface.String("phone", request.Phone),
		logiface.Int("retry_count", s.config.RetryCount))
	
	return nil, fmt.Errorf("SMS send failed after %d retries: %w", s.config.RetryCount, lastErr)
}

// sendWithProvider 使用指定提供商发送短信
func (s *SMSService) sendWithProvider(ctx context.Context, request *SMSRequest) (*SMSResponse, error) {
	// 选择可用的提供商
	provider := s.selectProvider(ctx)
	if provider == nil {
		return nil, fmt.Errorf("no available SMS provider")
	}
	
	// 发送短信
	response, err := provider.SendSMS(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("provider %s send failed: %w", provider.GetProviderName(), err)
	}
	
	// 设置提供商信息
	response.Provider = provider.GetProviderName()
	response.SentAt = time.Now()
	
	return response, nil
}

// selectProvider 选择可用的提供商
func (s *SMSService) selectProvider(ctx context.Context) SMSProvider {
	// 首先尝试默认提供商
	for _, provider := range s.providers {
		if provider.GetProviderName() == s.config.DefaultProvider && provider.IsAvailable(ctx) {
			return provider
		}
	}
	
	// 如果启用故障转移，尝试其他可用提供商
	if s.config.EnableFailover {
		for _, provider := range s.providers {
			if provider.IsAvailable(ctx) {
				return provider
			}
		}
	}
	
	return nil
}

// GetAvailableProviders 获取可用的提供商列表
func (s *SMSService) GetAvailableProviders(ctx context.Context) []string {
	var available []string
	for _, provider := range s.providers {
		if provider.IsAvailable(ctx) {
			available = append(available, provider.GetProviderName())
		}
	}
	return available
}

// GetProviderStatus 获取提供商状态
func (s *SMSService) GetProviderStatus(ctx context.Context) map[string]bool {
	status := make(map[string]bool)
	for _, provider := range s.providers {
		status[provider.GetProviderName()] = provider.IsAvailable(ctx)
	}
	return status
}

// BatchSendSMS 批量发送短信
func (s *SMSService) BatchSendSMS(ctx context.Context, requests []*SMSRequest) ([]*SMSResponse, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("requests cannot be empty")
	}
	
	if len(requests) > 100 {
		return nil, fmt.Errorf("too many requests (max 100)")
	}
	
	responses := make([]*SMSResponse, len(requests))
	
	// 使用goroutine并发发送，但限制并发数
	semaphore := make(chan struct{}, s.config.MaxConcurrent)
	errChan := make(chan error, len(requests))
	
	for i, request := range requests {
		go func(index int, req *SMSRequest) {
			semaphore <- struct{}{} // 获取信号量
			defer func() { <-semaphore }() // 释放信号量
			
			response, err := s.SendSMS(ctx, req)
			if err != nil {
				errChan <- fmt.Errorf("request %d failed: %w", index, err)
				return
			}
			
			responses[index] = response
			errChan <- nil
		}(i, request)
	}
	
	// 等待所有请求完成
	var errors []error
	for i := 0; i < len(requests); i++ {
		if err := <-errChan; err != nil {
			errors = append(errors, err)
		}
	}
	
	if len(errors) > 0 {
		s.logger.Error(ctx, "Batch SMS send completed with errors",
			logiface.Int("total_requests", len(requests)),
			logiface.Int("error_count", len(errors)))
		
		// 返回第一个错误作为代表
		return responses, errors[0]
	}
	
	s.logger.Info(ctx, "Batch SMS send completed successfully",
		logiface.Int("total_requests", len(requests)))
	
	return responses, nil
}
