package sms

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/logiface"
)

// MockSMSProvider 模拟短信提供商（用于开发和测试）
type MockSMSProvider struct {
	name       string
	shouldFail bool
	delay      time.Duration
	logger     logiface.Logger
}

// NewMockSMSProvider 创建模拟短信提供商
func NewMockSMSProvider(name string, shouldFail bool, delay time.Duration, logger logiface.Logger) *MockSMSProvider {
	return &MockSMSProvider{
		name:       name,
		shouldFail: shouldFail,
		delay:      delay,
		logger:     logger,
	}
}

// GetProviderName 获取提供商名称
func (p *MockSMSProvider) GetProviderName() string {
	return p.name
}

// IsAvailable 检查服务是否可用
func (p *MockSMSProvider) IsAvailable(ctx context.Context) bool {
	return true
}

// SendSMS 发送短信（模拟）
func (p *MockSMSProvider) SendSMS(ctx context.Context, request *SMSRequest) (*SMSResponse, error) {
	p.logger.Info(ctx, "Sending SMS via mock provider",
		logiface.String("provider", p.name),
		logiface.String("phone", request.Phone),
		logiface.String("content", request.Content),
		logiface.Int64("tenant_id", request.TenantID))

	// 模拟延迟
	if p.delay > 0 {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(p.delay):
		}
	}

	// 模拟失败
	if p.shouldFail {
		return &SMSResponse{
			Success:   false,
			ErrorCode: "MOCK_ERROR",
			ErrorMsg:  "Mock SMS provider configured to fail",
			Provider:  p.name,
			SentAt:    time.Now(),
		}, nil
	}

	// 模拟成功
	return &SMSResponse{
		Success:   true,
		MessageID: fmt.Sprintf("mock_%d", time.Now().UnixNano()),
		RequestID: request.RequestID,
		Provider:  p.name,
		Cost:      0.0,
		SentAt:    time.Now(),
		Extra: map[string]string{
			"mock_provider": "true",
			"simulated":     "true",
		},
	}, nil
}
