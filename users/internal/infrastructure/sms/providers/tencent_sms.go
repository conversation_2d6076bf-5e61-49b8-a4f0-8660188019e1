package providers

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/logiface"

	"platforms-user/internal/infrastructure/sms"
)

// TencentSMSProvider 腾讯云短信提供商
type TencentSMSProvider struct {
	secretID  string
	secretKey string
	region    string
	appID     string
	signName  string
	logger    logiface.Logger
	client    TencentSMSClient
}

// TencentSMSConfig 腾讯云短信配置
type TencentSMSConfig struct {
	SecretID  string `json:"secret_id"`
	SecretKey string `json:"secret_key"`
	Region    string `json:"region"`
	AppID     string `json:"app_id"`
	SignName  string `json:"sign_name"`
}

// TencentSMSClient 腾讯云短信客户端接口
type TencentSMSClient interface {
	SendSMS(ctx context.Context, request *TencentSMSRequest) (*TencentSMSResult, error)
}

// TencentSMSRequest 腾讯云短信请求
type TencentSMSRequest struct {
	PhoneNumberSet   []string `json:"PhoneNumberSet"`
	SmsSdkAppID      string   `json:"SmsSdkAppId"`
	SignName         string   `json:"SignName"`
	TemplateID       string   `json:"TemplateId"`
	TemplateParamSet []string `json:"TemplateParamSet,omitempty"`
	SessionContext   string   `json:"SessionContext,omitempty"`
	ExtendCode       string   `json:"ExtendCode,omitempty"`
	SenderID         string   `json:"SenderId,omitempty"`
}

// TencentSMSResult 腾讯云短信响应
type TencentSMSResult struct {
	Response TencentSMSResponse `json:"Response"`
}

// TencentSMSResponse 腾讯云短信响应详情
type TencentSMSResponse struct {
	SendStatusSet []TencentSendStatus `json:"SendStatusSet"`
	RequestID     string              `json:"RequestId"`
	Error         *TencentError       `json:"Error,omitempty"`
}

// TencentSendStatus 腾讯云发送状态
type TencentSendStatus struct {
	SerialNo       string `json:"SerialNo"`
	PhoneNumber    string `json:"PhoneNumber"`
	Fee            int    `json:"Fee"`
	SessionContext string `json:"SessionContext"`
	Code           string `json:"Code"`
	Message        string `json:"Message"`
	ISOCode        string `json:"IsoCode"`
}

// TencentError 腾讯云错误信息
type TencentError struct {
	Code    string `json:"Code"`
	Message string `json:"Message"`
}

// IsSuccess 检查是否成功
func (r *TencentSMSResult) IsSuccess() bool {
	if r.Response.Error != nil {
		return false
	}

	for _, status := range r.Response.SendStatusSet {
		if status.Code != "Ok" {
			return false
		}
	}

	return len(r.Response.SendStatusSet) > 0
}

// GetFirstError 获取第一个错误
func (r *TencentSMSResult) GetFirstError() (string, string) {
	if r.Response.Error != nil {
		return r.Response.Error.Code, r.Response.Error.Message
	}

	for _, status := range r.Response.SendStatusSet {
		if status.Code != "Ok" {
			return status.Code, status.Message
		}
	}

	return "", ""
}

// NewTencentSMSProvider 创建腾讯云短信提供商
func NewTencentSMSProvider(config *TencentSMSConfig, logger logiface.Logger) *TencentSMSProvider {
	if config.Region == "" {
		config.Region = "ap-beijing"
	}

	provider := &TencentSMSProvider{
		secretID:  config.SecretID,
		secretKey: config.SecretKey,
		region:    config.Region,
		appID:     config.AppID,
		signName:  config.SignName,
		logger:    logger,
	}

	// 创建真实的客户端（这里简化处理，实际应该使用腾讯云SDK）
	provider.client = &DefaultTencentSMSClient{
		secretID:  config.SecretID,
		secretKey: config.SecretKey,
		region:    config.Region,
		logger:    logger,
	}

	return provider
}

// GetProviderName 获取提供商名称
func (p *TencentSMSProvider) GetProviderName() string {
	return "tencent"
}

// IsAvailable 检查服务是否可用
func (p *TencentSMSProvider) IsAvailable(ctx context.Context) bool {
	if p.secretID == "" || p.secretKey == "" || p.appID == "" {
		return false
	}

	// 可以添加健康检查逻辑
	return true
}

// SendSMS 发送短信
func (p *TencentSMSProvider) SendSMS(ctx context.Context, request *sms.SMSRequest) (*sms.SMSResponse, error) {
	// 构建腾讯云请求
	tencentRequest := &TencentSMSRequest{
		PhoneNumberSet: []string{request.Phone},
		SmsSdkAppID:    p.appID,
		SignName:       p.getSignName(request),
		SessionContext: request.RequestID,
	}

	// 处理模板或直接内容
	if request.TemplateID != "" {
		tencentRequest.TemplateID = request.TemplateID
		if len(request.Variables) > 0 {
			var templateParams []string
			for _, value := range request.Variables {
				templateParams = append(templateParams, value)
			}
			tencentRequest.TemplateParamSet = templateParams
		}
	} else {
		// 腾讯云需要模板，这里需要预先配置一个通用模板
		tencentRequest.TemplateID = "GENERAL_TEMPLATE_ID"
		tencentRequest.TemplateParamSet = []string{request.Content}
	}

	// 发送请求
	result, err := p.client.SendSMS(ctx, tencentRequest)
	if err != nil {
		p.logger.Error(ctx, "Tencent SMS send failed",
			logiface.Error(err),
			logiface.String("phone", request.Phone))
		return &sms.SMSResponse{
			Success:   false,
			RequestID: request.RequestID,
			Provider:  p.GetProviderName(),
			ErrorMsg:  err.Error(),
			SentAt:    time.Now(),
		}, err
	}

	// 构建响应
	response := &sms.SMSResponse{
		Success:   result.IsSuccess(),
		RequestID: result.Response.RequestID,
		Provider:  p.GetProviderName(),
		SentAt:    time.Now(),
		Extra: map[string]string{
			"tencent_request_id": result.Response.RequestID,
		},
	}

	if len(result.Response.SendStatusSet) > 0 {
		status := result.Response.SendStatusSet[0]
		response.MessageID = status.SerialNo
		response.Cost = float64(status.Fee) / 100 // 腾讯云费用单位是分

		if status.Code != "Ok" {
			response.ErrorCode = status.Code
			response.ErrorMsg = status.Message
		}
	}

	if !result.IsSuccess() {
		errorCode, errorMsg := result.GetFirstError()
		if response.ErrorCode == "" {
			response.ErrorCode = errorCode
			response.ErrorMsg = errorMsg
		}
	}

	return response, nil
}

// getSignName 获取签名名称
func (p *TencentSMSProvider) getSignName(request *sms.SMSRequest) string {
	if request.SignName != "" {
		return request.SignName
	}
	return p.signName
}

// DefaultTencentSMSClient 默认腾讯云短信客户端实现
type DefaultTencentSMSClient struct {
	secretID  string
	secretKey string
	region    string
	logger    logiface.Logger
}

// SendSMS 发送短信（简化实现，实际应该使用腾讯云SDK）
func (c *DefaultTencentSMSClient) SendSMS(ctx context.Context, request *TencentSMSRequest) (*TencentSMSResult, error) {
	// 这里应该实现真实的腾讯云API调用
	// 为了演示，这里返回模拟结果

	c.logger.Info(ctx, "Sending SMS via Tencent",
		logiface.Any("phones", request.PhoneNumberSet),
		logiface.String("template_id", request.TemplateID),
		logiface.String("sign_name", request.SignName))

	// 模拟API调用延迟
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(150 * time.Millisecond):
	}

	// 模拟成功响应
	var sendStatusSet []TencentSendStatus
	for _, phone := range request.PhoneNumberSet {
		sendStatusSet = append(sendStatusSet, TencentSendStatus{
			SerialNo:       fmt.Sprintf("tencent-serial-%d", time.Now().UnixNano()),
			PhoneNumber:    phone,
			Fee:            10, // 0.1元，单位分
			SessionContext: request.SessionContext,
			Code:           "Ok",
			Message:        "send success",
			ISOCode:        "CN",
		})
	}

	return &TencentSMSResult{
		Response: TencentSMSResponse{
			SendStatusSet: sendStatusSet,
			RequestID:     fmt.Sprintf("tencent-req-%d", time.Now().UnixNano()),
		},
	}, nil
}

// MockTencentSMSClient 模拟腾讯云短信客户端（用于测试）
type MockTencentSMSClient struct {
	shouldFail bool
	delay      time.Duration
}

// NewMockTencentSMSClient 创建模拟客户端
func NewMockTencentSMSClient(shouldFail bool, delay time.Duration) *MockTencentSMSClient {
	return &MockTencentSMSClient{
		shouldFail: shouldFail,
		delay:      delay,
	}
}

// SendSMS 模拟发送短信
func (c *MockTencentSMSClient) SendSMS(ctx context.Context, request *TencentSMSRequest) (*TencentSMSResult, error) {
	// 模拟延迟
	if c.delay > 0 {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(c.delay):
		}
	}

	// 模拟失败
	if c.shouldFail {
		return &TencentSMSResult{
			Response: TencentSMSResponse{
				RequestID: fmt.Sprintf("mock-req-%d", time.Now().UnixNano()),
				Error: &TencentError{
					Code:    "FailedOperation.ContainSensitiveWord",
					Message: "短信内容包含敏感词",
				},
			},
		}, nil
	}

	// 模拟成功
	var sendStatusSet []TencentSendStatus
	for _, phone := range request.PhoneNumberSet {
		sendStatusSet = append(sendStatusSet, TencentSendStatus{
			SerialNo:       fmt.Sprintf("mock-serial-%d", time.Now().UnixNano()),
			PhoneNumber:    phone,
			Fee:            10,
			SessionContext: request.SessionContext,
			Code:           "Ok",
			Message:        "send success",
			ISOCode:        "CN",
		})
	}

	return &TencentSMSResult{
		Response: TencentSMSResponse{
			SendStatusSet: sendStatusSet,
			RequestID:     fmt.Sprintf("mock-req-%d", time.Now().UnixNano()),
		},
	}, nil
}

// SetMockClient 设置模拟客户端（用于测试）
func (p *TencentSMSProvider) SetMockClient(client TencentSMSClient) {
	p.client = client
}

// TencentErrorCodes 腾讯云错误码映射
var TencentErrorCodes = map[string]string{
	"Ok":                                   "发送成功",
	"FailedOperation.ContainSensitiveWord": "短信内容包含敏感词",
	"FailedOperation.ForbidAddMarketingTemplateFreqLimit":             "添加营销类模板频率限制",
	"FailedOperation.JsonParseFail":                                   "JSON解析失败",
	"FailedOperation.MarketingSendTimeConstraint":                     "营销短信发送时间限制",
	"FailedOperation.MissingTemplateToModify":                         "缺少要修改的模板",
	"FailedOperation.NotEnterpriseCertification":                      "非企业认证用户",
	"FailedOperation.PhoneNumberInBlacklist":                          "手机号在黑名单中",
	"FailedOperation.TemplateIncorrectOrUnapproved":                   "模板未审核或审核未通过",
	"InvalidParameterValue.IncorrectPhoneNumber":                      "手机号格式错误",
	"InvalidParameterValue.InvalidTemplateID":                         "模板ID无效",
	"InvalidParameterValue.TemplateParameterFormatError":              "模板参数格式错误",
	"InvalidParameterValue.TemplateParameterLengthLimit":              "模板参数长度限制",
	"LimitExceeded.AppCountLimit":                                     "应用数量超过限制",
	"LimitExceeded.DailyLimit":                                        "日发送量超过限制",
	"LimitExceeded.DeliveryFrequencyLimit":                            "下发频率限制",
	"LimitExceeded.PhoneNumberCountLimit":                             "手机号数量超过限制",
	"LimitExceeded.PhoneNumberDailyLimit":                             "手机号日发送量超过限制",
	"LimitExceeded.PhoneNumberSameContentDailyLimit":                  "手机号相同内容日发送量超过限制",
	"MissingParameter.EmptyPhoneNumberSet":                            "手机号集合为空",
	"UnauthorizedOperation.RequestPermissionDeny":                     "请求没有权限",
	"UnauthorizedOperation.SdkAppidIsDisabled":                        "SDK AppID已禁用",
	"UnsupportedOperation.ContainDomesticAndInternationalPhoneNumber": "包含国内和国际手机号",
}

// GetTencentErrorMessage 获取错误信息
func GetTencentErrorMessage(code string) string {
	if msg, exists := TencentErrorCodes[code]; exists {
		return msg
	}
	return fmt.Sprintf("未知错误码: %s", code)
}
