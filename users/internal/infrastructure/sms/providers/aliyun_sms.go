package providers

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"platforms-pkg/logiface"

	"platforms-user/internal/infrastructure/sms"
)

// AliyunSMSProvider 阿里云短信提供商
type AliyunSMSProvider struct {
	accessKeyID     string
	accessKeySecret string
	endpoint        string
	signName        string
	logger          logiface.Logger
	client          AliyunSMSClient
}

// AliyunSMSConfig 阿里云短信配置
type AliyunSMSConfig struct {
	AccessKeyID     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
	Endpoint        string `json:"endpoint"`
	SignName        string `json:"sign_name"`
}

// AliyunSMSClient 阿里云短信客户端接口（用于测试）
type AliyunSMSClient interface {
	SendSMS(ctx context.Context, request *AliyunSMSRequest) (*AliyunSMSResult, error)
}

// AliyunSMSRequest 阿里云短信请求
type AliyunSMSRequest struct {
	PhoneNumbers  string `json:"PhoneNumbers"`
	SignName      string `json:"SignName"`
	TemplateCode  string `json:"TemplateCode"`
	TemplateParam string `json:"TemplateParam,omitempty"`
	OutID         string `json:"OutId,omitempty"`
}

// AliyunSMSResult 阿里云短信响应
type AliyunSMSResult struct {
	RequestID string `json:"RequestId"`
	BizID     string `json:"BizId"`
	Code      string `json:"Code"`
	Message   string `json:"Message"`
}

// IsSuccess 检查是否成功
func (r *AliyunSMSResult) IsSuccess() bool {
	return r.Code == "OK"
}

// NewAliyunSMSProvider 创建阿里云短信提供商
func NewAliyunSMSProvider(config *AliyunSMSConfig, logger logiface.Logger) *AliyunSMSProvider {
	if config.Endpoint == "" {
		config.Endpoint = "https://dysmsapi.aliyuncs.com"
	}

	provider := &AliyunSMSProvider{
		accessKeyID:     config.AccessKeyID,
		accessKeySecret: config.AccessKeySecret,
		endpoint:        config.Endpoint,
		signName:        config.SignName,
		logger:          logger,
	}

	// 创建真实的客户端（这里简化处理，实际应该使用阿里云SDK）
	provider.client = &DefaultAliyunSMSClient{
		accessKeyID:     config.AccessKeyID,
		accessKeySecret: config.AccessKeySecret,
		endpoint:        config.Endpoint,
		logger:          logger,
	}

	return provider
}

// GetProviderName 获取提供商名称
func (p *AliyunSMSProvider) GetProviderName() string {
	return "aliyun"
}

// IsAvailable 检查服务是否可用
func (p *AliyunSMSProvider) IsAvailable(ctx context.Context) bool {
	if p.accessKeyID == "" || p.accessKeySecret == "" {
		return false
	}

	// 可以添加健康检查逻辑
	return true
}

// SendSMS 发送短信
func (p *AliyunSMSProvider) SendSMS(ctx context.Context, request *sms.SMSRequest) (*sms.SMSResponse, error) {
	// 构建阿里云请求
	aliyunRequest := &AliyunSMSRequest{
		PhoneNumbers: request.Phone,
		SignName:     p.getSignName(request),
		OutID:        request.RequestID,
	}

	// 处理模板或直接内容
	if request.TemplateID != "" {
		aliyunRequest.TemplateCode = request.TemplateID
		if len(request.Variables) > 0 {
			templateParam, err := json.Marshal(request.Variables)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal template variables: %w", err)
			}
			aliyunRequest.TemplateParam = string(templateParam)
		}
	} else {
		// 阿里云需要模板，这里需要预先配置一个通用模板
		aliyunRequest.TemplateCode = "SMS_GENERAL_TEMPLATE"
		templateParam, _ := json.Marshal(map[string]string{
			"content": request.Content,
		})
		aliyunRequest.TemplateParam = string(templateParam)
	}

	// 发送请求
	result, err := p.client.SendSMS(ctx, aliyunRequest)
	if err != nil {
		p.logger.Error(ctx, "Aliyun SMS send failed",
			logiface.Error(err),
			logiface.String("phone", request.Phone))
		return &sms.SMSResponse{
			Success:   false,
			RequestID: request.RequestID,
			Provider:  p.GetProviderName(),
			ErrorMsg:  err.Error(),
			SentAt:    time.Now(),
		}, err
	}

	// 构建响应
	response := &sms.SMSResponse{
		Success:   result.IsSuccess(),
		MessageID: result.BizID,
		RequestID: result.RequestID,
		Provider:  p.GetProviderName(),
		SentAt:    time.Now(),
		Extra: map[string]string{
			"aliyun_request_id": result.RequestID,
			"aliyun_biz_id":     result.BizID,
		},
	}

	if !result.IsSuccess() {
		response.ErrorCode = result.Code
		response.ErrorMsg = result.Message
	}

	return response, nil
}

// getSignName 获取签名名称
func (p *AliyunSMSProvider) getSignName(request *sms.SMSRequest) string {
	if request.SignName != "" {
		return request.SignName
	}
	return p.signName
}

// DefaultAliyunSMSClient 默认阿里云短信客户端实现
type DefaultAliyunSMSClient struct {
	accessKeyID     string
	accessKeySecret string
	endpoint        string
	logger          logiface.Logger
}

// SendSMS 发送短信（简化实现，实际应该使用阿里云SDK）
func (c *DefaultAliyunSMSClient) SendSMS(ctx context.Context, request *AliyunSMSRequest) (*AliyunSMSResult, error) {
	// 这里应该实现真实的阿里云API调用
	// 为了演示，这里返回模拟结果

	c.logger.Info(ctx, "Sending SMS via Aliyun",
		logiface.String("phone", request.PhoneNumbers),
		logiface.String("template_code", request.TemplateCode),
		logiface.String("sign_name", request.SignName))

	// 模拟API调用延迟
	select {
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(100 * time.Millisecond):
	}

	// 模拟成功响应
	return &AliyunSMSResult{
		RequestID: fmt.Sprintf("aliyun-req-%d", time.Now().UnixNano()),
		BizID:     fmt.Sprintf("aliyun-biz-%d", time.Now().UnixNano()),
		Code:      "OK",
		Message:   "OK",
	}, nil
}

// MockAliyunSMSClient 模拟阿里云短信客户端（用于测试）
type MockAliyunSMSClient struct {
	shouldFail bool
	delay      time.Duration
}

// NewMockAliyunSMSClient 创建模拟客户端
func NewMockAliyunSMSClient(shouldFail bool, delay time.Duration) *MockAliyunSMSClient {
	return &MockAliyunSMSClient{
		shouldFail: shouldFail,
		delay:      delay,
	}
}

// SendSMS 模拟发送短信
func (c *MockAliyunSMSClient) SendSMS(ctx context.Context, request *AliyunSMSRequest) (*AliyunSMSResult, error) {
	// 模拟延迟
	if c.delay > 0 {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(c.delay):
		}
	}

	// 模拟失败
	if c.shouldFail {
		return &AliyunSMSResult{
			RequestID: fmt.Sprintf("mock-req-%d", time.Now().UnixNano()),
			Code:      "isv.BUSINESS_LIMIT_CONTROL",
			Message:   "业务限流",
		}, nil
	}

	// 模拟成功
	return &AliyunSMSResult{
		RequestID: fmt.Sprintf("mock-req-%d", time.Now().UnixNano()),
		BizID:     fmt.Sprintf("mock-biz-%d", time.Now().UnixNano()),
		Code:      "OK",
		Message:   "OK",
	}, nil
}

// SetMockClient 设置模拟客户端（用于测试）
func (p *AliyunSMSProvider) SetMockClient(client AliyunSMSClient) {
	p.client = client
}

// AliyunErrorCodes 阿里云错误码映射
var AliyunErrorCodes = map[string]string{
	"OK":                              "发送成功",
	"isp.RAM_PERMISSION_DENY":         "RAM权限DENY",
	"isv.OUT_OF_SERVICE":              "业务停机",
	"isv.PRODUCT_UN_SUBSCRIPT":        "未开通云通信产品的阿里云客户",
	"isv.PRODUCT_UNSUBSCRIBE":         "产品未开通",
	"isv.ACCOUNT_NOT_EXISTS":          "账户不存在",
	"isv.ACCOUNT_ABNORMAL":            "账户异常",
	"isv.SMS_TEMPLATE_ILLEGAL":        "短信模板不合法",
	"isv.SMS_SIGNATURE_ILLEGAL":       "短信签名不合法",
	"isv.INVALID_PARAMETERS":          "参数异常",
	"isv.MOBILE_NUMBER_ILLEGAL":       "非法手机号",
	"isv.MOBILE_COUNT_OVER_LIMIT":     "手机号码数量超过限制",
	"isv.TEMPLATE_MISSING_PARAMETERS": "模板缺少变量",
	"isv.BUSINESS_LIMIT_CONTROL":      "业务限流",
	"isv.INVALID_JSON_PARAM":          "JSON参数不合法",
	"isv.BLACK_KEY_CONTROL_LIMIT":     "黑名单管控",
	"isv.PARAM_LENGTH_LIMIT":          "参数超出长度限制",
	"isv.PARAM_NOT_SUPPORT_URL":       "不支持URL",
	"isv.AMOUNT_NOT_ENOUGH":           "账户余额不足",
}

// GetErrorMessage 获取错误信息
func GetAliyunErrorMessage(code string) string {
	if msg, exists := AliyunErrorCodes[code]; exists {
		return msg
	}
	return fmt.Sprintf("未知错误码: %s", code)
}
