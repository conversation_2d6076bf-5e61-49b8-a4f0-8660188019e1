package dto

// RegisterDTO 用户注册DTO
type RegisterDTO struct {
	TenantID   int64  `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	Username   string `json:"username" binding:"required,min=3,max=50"`
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required,min=8,max=128"`
	RealName   string `json:"real_name" binding:"omitempty,max=100"`
	Phone      string `json:"phone" binding:"omitempty,max=20"`
	InviteCode string `json:"invite_code" binding:"omitempty"`
}

// RegisterResponseDTO 注册响应DTO
type RegisterResponseDTO struct {
	User    UserInfoDTO `json:"user"`
	Message string      `json:"message,omitempty"` // 可选的消息，如邮箱验证提示
}

// CaptchaGenerateDTO 验证码生成DTO
type CaptchaGenerateDTO struct {
	Type   string `json:"type" binding:"required"`
	Width  int    `json:"width" binding:"omitempty,min=100,max=400"`
	Height int    `json:"height" binding:"omitempty,min=50,max=200"`
}

// CaptchaResponseDTO 验证码响应DTO
type CaptchaResponseDTO struct {
	CaptchaID    string `json:"captcha_id"`
	CaptchaImage string `json:"captcha_image"`
	ExpiresIn    int    `json:"expires_in"`
}

// ActivateAccountResponseDTO 账户激活响应DTO
type ActivateAccountResponseDTO struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	UserID  int64  `json:"user_id"`
}
