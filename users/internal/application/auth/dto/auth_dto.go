package dto

// LoginDTO 登录DTO
type LoginDTO struct {
	Username    string `json:"username" binding:"required"`
	Password    string `json:"password" binding:"required"`
	CaptchaID   string `json:"captcha_id"`   // ✅ 修改：添加必填验证
	CaptchaCode string `json:"captcha_code"` // ✅ 修改：添加必填验证
}

// MFALoginDTO MFA登录DTO
type MFALoginDTO struct {
	SessionID string `json:"session_id" binding:"required"`
	Code      string `json:"code" binding:"required"`
}

// RefreshTokenDTO 刷新令牌DTO
type RefreshTokenDTO struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// LogoutDTO 登出DTO
type LogoutDTO struct {
	SessionID string `json:"session_id"`
	All       bool   `json:"all"`
}

// RevokeSessionDTO 撤销会话DTO
type RevokeSessionDTO struct {
	SessionID string `json:"session_id" binding:"required"`
}

// ForgotPasswordDTO 忘记密码DTO
type ForgotPasswordDTO struct {
	Email string `json:"email" binding:"required,email"`
}

// ResetPasswordDTO 重置密码DTO
type ResetPasswordDTO struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// SendMFACodeDTO 发送MFA验证码DTO
type SendMFACodeDTO struct {
	UserID int64 `json:"user_id" binding:"required"`
}

// VerifyMFACodeDTO 验证MFA验证码DTO
type VerifyMFACodeDTO struct {
	UserID int64  `json:"user_id" binding:"required"`
	Code   string `json:"code" binding:"required"`
}

// LoginResponseDTO 登录响应DTO
type LoginResponseDTO struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	TokenType    string      `json:"token_type"`
	ExpiresIn    int         `json:"expires_in"`
	User         UserInfoDTO `json:"user"`
	RequiresMFA  bool        `json:"requires_mfa"`
	MFAType      string      `json:"mfa_type,omitempty"`
}

// UserInfoDTO 用户信息DTO
type UserInfoDTO struct {
	ID       int64     `json:"id"`
	Username string    `json:"username"`
	Email    string    `json:"email"`
	RealName string    `json:"real_name"`
	Status   string    `json:"status"`
	Roles    []RoleDTO `json:"roles"`
}

// RoleDTO 角色DTO
type RoleDTO struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// SessionListResponseDTO 会话列表响应DTO
type SessionListResponseDTO struct {
	Sessions   []SessionInfoDTO `json:"sessions"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	Total      int64            `json:"total"`
	TotalPages int              `json:"total_pages"`
}

// SessionInfoDTO 会话信息DTO
type SessionInfoDTO struct {
	SessionID    string        `json:"session_id"`
	DeviceInfo   DeviceInfoDTO `json:"device_info"`
	Status       string        `json:"status"`
	LoginAt      string        `json:"login_at"`
	LastActiveAt string        `json:"last_active_at"`
	ExpiresAt    string        `json:"expires_at"`
}

// DeviceInfoDTO 设备信息DTO
type DeviceInfoDTO struct {
	DeviceType  string `json:"device_type"`
	OS          string `json:"os"`
	OSVersion   string `json:"os_version"`
	Browser     string `json:"browser"`
	DeviceModel string `json:"device_model"`
	DisplayName string `json:"display_name"`
}

// SessionStatsDTO 会话统计DTO
type SessionStatsDTO struct {
	TotalSessions     int64 `json:"total_sessions"`
	ActiveSessions    int64 `json:"active_sessions"`
	ExpiredSessions   int64 `json:"expired_sessions"`
	RevokedSessions   int64 `json:"revoked_sessions"`
	LoggedOutSessions int64 `json:"logged_out_sessions"`
}
