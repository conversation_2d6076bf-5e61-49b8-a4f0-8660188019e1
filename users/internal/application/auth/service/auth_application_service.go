package service

import (
	"context"
	"errors"
	"fmt"
	"platforms-pkg/logiface"
	"platforms-pkg/usercontext"
	"platforms-user/internal/application/auth/dto"
	userDTO "platforms-user/internal/application/user/dto"
	userAppService "platforms-user/internal/application/user/service"
	verificationDTO "platforms-user/internal/application/verification/dto"
	verificationAppService "platforms-user/internal/application/verification/service"
	authEntity "platforms-user/internal/domain/auth/entity"
	authRepo "platforms-user/internal/domain/auth/repository"
	authValueObject "platforms-user/internal/domain/auth/value_object"
	userErrors "platforms-user/internal/domain/errors"
	userEntity "platforms-user/internal/domain/user/entity"
	userRepo "platforms-user/internal/domain/user/repository"
	userDomainService "platforms-user/internal/domain/user/service"
	userValueObject "platforms-user/internal/domain/user/value_object"
	verificationEntity "platforms-user/internal/domain/verification/entity"
	"platforms-user/pkg/jwt"
	"time"
)

var ErrInvalidCredentials = errors.New("invalid credentials")

// LoginLimitConfig 登录限流配置
type LoginLimitConfig struct {
	MaxFailedAttemptsPerUser int           `json:"max_failed_attempts_per_user"` // 用户最大失败次数
	MaxFailedAttemptsPerIP   int           `json:"max_failed_attempts_per_ip"`   // IP最大失败次数
	LockDuration             time.Duration `json:"lock_duration"`                // 锁定时长
	ResetDuration            time.Duration `json:"reset_duration"`               // 重置时长
}

// DefaultLoginLimitConfig 默认登录限流配置
var DefaultLoginLimitConfig = LoginLimitConfig{
	MaxFailedAttemptsPerUser: 5,                // 用户5次失败后锁定
	MaxFailedAttemptsPerIP:   10,               // IP10次失败后锁定
	LockDuration:             30 * time.Minute, // 锁定30分钟
	ResetDuration:            24 * time.Hour,   // 24小时后重置
}

// AuthApplicationService 认证应用服务
type AuthApplicationService struct {
	logger              logiface.Logger
	authRepo            authRepo.AuthRepository
	userRepo            userRepo.UserRepository
	jwtService          *jwt.JWTService
	tenantLookupService *TenantLookupService
	captchaService      CaptchaService
	verificationService *verificationAppService.VerificationApplicationService
	tenantConfigService *userAppService.TenantConfigService
	userService         *userAppService.UserApplicationService
}

// NewAuthApplicationService 创建认证应用服务
func NewAuthApplicationService(
	logger logiface.Logger,
	authRepo authRepo.AuthRepository,
	userRepo userRepo.UserRepository,
	jwtService *jwt.JWTService,
	tenantLookupService *TenantLookupService,
	captchaService CaptchaService,
	verificationService *verificationAppService.VerificationApplicationService,
	tenantConfigService *userAppService.TenantConfigService,
	userService *userAppService.UserApplicationService,
) *AuthApplicationService {
	return &AuthApplicationService{
		logger:              logger,
		authRepo:            authRepo,
		userRepo:            userRepo,
		jwtService:          jwtService,
		tenantLookupService: tenantLookupService,
		captchaService:      captchaService,
		verificationService: verificationService,
		tenantConfigService: tenantConfigService,
		userService:         userService,
	}
}

// LoginHandler 登录处理函数类型，返回userID便于记录
// 返回: 登录响应, userID(成功时), error
type LoginHandler func(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, int64, error)

// WithLoginAttemptRecord 登录尝试记录装饰器
func WithLoginAttemptRecord(
	next LoginHandler,
	repo authRepo.AuthRepository,
) LoginHandler {
	return func(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, int64, error) {
		resp, userID, err := next(ctx, loginDTO, ipAddress, userAgent)
		tenantID, _ := usercontext.GetTenantID(ctx)
		attempt := authEntity.NewLoginAttempt(tenantID, loginDTO.Username, ipAddress, userAgent)
		if userID > 0 {
			attempt.SetUserID(userID)
		}
		if err == nil {
			attempt.MarkSuccess()
		} else {
			attempt.MarkFailed()
		}
		_ = repo.CreateLoginAttempt(ctx, attempt) // 记录失败不影响主流程
		return resp, userID, err
	}
}

// loginCoreHandler 主登录业务逻辑（无login_attempts记录）
func (s *AuthApplicationService) loginCoreHandler(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, int64, error) {
	// 从usercontext获取租户信息
	tenantInfo, _ := usercontext.GetTenantInfo(ctx)
	tenantCode := ""
	if tenantInfo != nil {
		tenantCode = tenantInfo.TenantCode
	}

	s.logger.Debug(ctx, "auth service login called",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.String("ip", ipAddress),
		logiface.String("user_agent", userAgent),
	)

	// ✅ 新增：1. 验证验证码
	//if err := s.captchaService.ValidateCaptcha(ctx, loginDTO.CaptchaID, loginDTO.CaptchaCode); err != nil {
	//	s.logger.Warn(ctx, "captcha validation failed",
	//		logiface.String("username", loginDTO.Username),
	//		logiface.String("captcha_id", loginDTO.CaptchaID),
	//		logiface.String("ip", ipAddress),
	//	)
	//	s.recordLoginAttempt(ctx, 0, loginDTO.Username, ipAddress, userAgent, "Captcha validation failed")
	//	return nil, fmt.Errorf("验证码错误")
	//}

	// ✅ 新增：2. 检查登录限制
	if err := s.checkLoginLimits(ctx, loginDTO.Username, ipAddress); err != nil {
		return nil, 0, err
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	if tenantID == 0 {
		// 租户信息不可用，无法继续登录
		s.logger.Error(ctx, "tenant information not available, cannot proceed with login",
			logiface.String("tenant_code", tenantCode),
		)
		return nil, 0, userErrors.NewTenantNotFoundError(tenantCode)
	}
	s.logger.Info(ctx, "login attempt",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", ipAddress),
	)

	user, err := s.userRepo.FindByUsername(ctx, tenantID, loginDTO.Username)
	if err != nil {
		s.logger.Error(ctx, "database error during user lookup",
			logiface.Error(err),
			logiface.String("error_type", fmt.Sprintf("%T", err)),
			logiface.String("error_message", err.Error()),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", ipAddress),
		)
		// 记录登录失败
		return nil, 0, userErrors.NewDatabaseError("user lookup", err.Error())
	}

	if user == nil {
		s.logger.Warn(ctx, "user not found during login",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", ipAddress),
		)
		// 记录登录失败
		return nil, 0, userErrors.NewInvalidCredentialsError(loginDTO.Username)
	}

	if user.Status == userValueObject.UserStatusLocked {
		s.logger.Warn(ctx, "locked user login attempt",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
			logiface.String("user_status", string(user.Status)),
		)
		return nil, 0, userErrors.NewAccountLockedError(user.LockReason)
	}

	if user.Status == userValueObject.UserStatusDisabled {
		s.logger.Warn(ctx, "disabled user login attempt",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
			logiface.String("user_status", string(user.Status)),
		)
		return nil, 0, userErrors.NewUserError(userErrors.CodeUserAccountDisabled, fmt.Sprintf("reason: %s", user.LockReason))
	}

	s.logger.Debug(ctx, "user status check passed",
		logiface.String("username", loginDTO.Username),
		logiface.String("user_status", string(user.Status)),
	)

	// 验证密码
	s.logger.Debug(ctx, "verifying password",
		logiface.String("username", loginDTO.Username),
		logiface.Bool("has_password", loginDTO.Password != ""),
		logiface.Bool("has_stored_password", user.Password.Hash() != ""),
		logiface.Int("password_length", len(loginDTO.Password)),
		logiface.Int("stored_hash_length", len(user.Password.Hash())),
	)

	if !user.Password.Verify(loginDTO.Password) {
		s.logger.Warn(ctx, "invalid password during login",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
			logiface.Int64("user_id", user.ID),
		)
		// 记录登录失败
		return nil, 0, userErrors.NewInvalidCredentialsError(loginDTO.Username)
	}

	s.logger.Debug(ctx, "password verification successful",
		logiface.String("username", loginDTO.Username),
		logiface.Int64("user_id", user.ID),
	)

	// 检查是否需要MFA
	if user.IsMFAEnabled() {
		s.logger.Info(ctx, "mfa required for login",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		// 创建临时会话用于MFA验证
		deviceInfo := authValueObject.NewDeviceInfo(userAgent)
		session := authEntity.NewAuthSession(user.ID, tenantID, "", "", deviceInfo, time.Now().Add(30*time.Minute))
		if err := s.authRepo.CreateSession(ctx, session); err != nil {
			s.logger.Error(ctx, "failed to create mfa session",
				logiface.Error(err),
				logiface.String("username", loginDTO.Username),
				logiface.String("tenant_code", tenantCode),
				logiface.String("ip", ipAddress),
			)
			return nil, 0, userErrors.NewSystemError("create MFA session", err.Error())
		}

		return &dto.LoginResponseDTO{
			RequiresMFA: true,
			MFAType:     "totp",
			User:        s.toUserInfoDTO(user),
		}, user.ID, nil
	}

	// 生成JWT令牌
	accessToken, err := s.jwtService.GenerateAccessToken(user.ID, tenantID, user.Username)
	if err != nil {
		s.logger.Error(ctx, "failed to generate access token",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("generate access token", err.Error())
	}

	refreshToken, err := s.jwtService.GenerateRefreshToken(user.ID, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to generate refresh token",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("generate refresh token", err.Error())
	}

	// 创建会话
	deviceInfo := authValueObject.NewDeviceInfo(userAgent)
	session := authEntity.NewAuthSession(user.ID, tenantID, accessToken, refreshToken, deviceInfo, time.Now().Add(24*time.Hour))
	if err := s.authRepo.CreateSession(ctx, session); err != nil {
		s.logger.Error(ctx, "failed to create auth session",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("create auth session", err.Error())
	}

	// 记录登录成功
	return &dto.LoginResponseDTO{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 60 * 60, // 24小时
		User:         s.toUserInfoDTO(user),
		RequiresMFA:  false,
	}, user.ID, nil
}

// MFALogin MFA登录
func (s *AuthApplicationService) MFALogin(ctx context.Context, mfaDTO *dto.MFALoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
	// 验证MFA代码
	// TODO: 实现MFA验证逻辑

	// 生成JWT令牌
	// TODO: 从临时会话中获取用户信息

	// 创建正式会话

	return nil, userErrors.NewSystemError("mfa_login", "MFA login not implemented")
}

// RefreshToken 刷新令牌
func (s *AuthApplicationService) RefreshToken(ctx context.Context, refreshDTO *dto.RefreshTokenDTO) (*dto.LoginResponseDTO, error) {
	// 验证刷新令牌
	claims, err := s.jwtService.ParseToken(refreshDTO.RefreshToken)
	if err != nil {
		return nil, userErrors.NewBusinessError(userErrors.CodeVerificationTokenInvalid, fmt.Sprintf("invalid refresh token: %v", err))
	}

	// 检查令牌类型
	if claims.Type != "refresh" {
		return nil, userErrors.NewBusinessError(userErrors.CodeTokenInvalid, "invalid token type for refresh")
	}

	// 根据JTI查找会话
	session, err := s.authRepo.FindSessionByJTI(ctx, claims.ID)
	if err != nil {
		return nil, userErrors.NewBusinessError(userErrors.CodeSessionNotFound, fmt.Sprintf("session not found: %v", err))
	}

	if session == nil {
		return nil, userErrors.NewBusinessError(userErrors.CodeSessionNotFound, "session not found")
	}

	// 检查会话状态
	if !session.IsActive() {
		return nil, userErrors.NewBusinessError(userErrors.CodeUserOperationNotAllowed, "session is not active")
	}

	// 查找用户
	user, err := s.userRepo.FindByID(ctx, session.UserID)
	if err != nil {
		return nil, userErrors.NewBusinessError(userErrors.CodeUserNotFound, fmt.Sprintf("user not found: %v", err))
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, userErrors.NewBusinessError(userErrors.CodeUserAccountDisabled, "user account is not active")
	}

	// 生成新的令牌（使用JTI方案）
	accessTokenInfo, err := s.jwtService.GenerateAccessTokenWithInfo(user.ID, user.TenantID, user.Username)
	if err != nil {
		return nil, userErrors.NewSystemError("generate_access_token", fmt.Sprintf("failed to generate access token: %v", err))
	}

	refreshTokenInfo, err := s.jwtService.GenerateRefreshTokenWithInfo(user.ID, user.TenantID)
	if err != nil {
		return nil, userErrors.NewSystemError("generate_refresh_token", fmt.Sprintf("failed to generate refresh token: %v", err))
	}

	// 更新会话（使用JTI和令牌哈希）
	session.RefreshWithJTI(accessTokenInfo.JTI, accessTokenInfo.TokenHash, refreshTokenInfo.TokenHash, time.Now().Add(24*time.Hour))
	if err := s.authRepo.UpdateSession(ctx, session); err != nil {
		return nil, userErrors.NewSystemError("update_session", fmt.Sprintf("failed to update session: %v", err))
	}

	// 返回新的令牌
	return &dto.LoginResponseDTO{
		AccessToken:  accessTokenInfo.Token,
		RefreshToken: refreshTokenInfo.Token,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 60 * 60, // 24小时
		User:         s.toUserInfoDTO(user),
		RequiresMFA:  false,
	}, nil
}

// Logout 用户登出
func (s *AuthApplicationService) Logout(ctx context.Context, logoutDTO *dto.LogoutDTO, userID int64) error {
	// 如果要求撤销所有会话
	if logoutDTO.All {
		if err := s.authRepo.RevokeAllSessionsByUserID(ctx, userID); err != nil {
			return userErrors.NewSystemError("revoke_sessions", fmt.Sprintf("failed to revoke all sessions: %v", err))
		}
		return nil
	}

	// 撤销指定会话
	if logoutDTO.SessionID != "" {
		session, err := s.authRepo.FindSessionByID(ctx, logoutDTO.SessionID)
		if err != nil {
			return userErrors.NewBusinessError(userErrors.CodeSessionNotFound, fmt.Sprintf("session_id: %s", logoutDTO.SessionID))
		}

		// 检查会话是否属于当前用户
		if session.UserID != userID {
			return userErrors.NewBusinessError(userErrors.CodeUserOperationNotAllowed, "session does not belong to user")
		}

		// 撤销会话
		session.Revoke()
		if err := s.authRepo.UpdateSession(ctx, session); err != nil {
			return userErrors.NewSystemError("revoke_session", fmt.Sprintf("failed to revoke session: %v", err))
		}
	}

	return nil
}

// GetSessions 获取用户会话列表
func (s *AuthApplicationService) GetSessions(ctx context.Context, userID int64, page, pageSize int) (*dto.SessionListResponseDTO, error) {
	// 计算偏移量
	offset := (page - 1) * pageSize

	// 获取会话列表
	sessions, total, err := s.authRepo.FindSessionsByUserID(ctx, userID, offset, pageSize)
	if err != nil {
		return nil, userErrors.NewSystemError("get_sessions", fmt.Sprintf("failed to get sessions: %v", err))
	}

	// 转换为DTO - 确保即使没有数据也返回空数组而不是null
	sessionDTOs := make([]dto.SessionInfoDTO, 0, len(sessions))
	for _, session := range sessions {
		sessionDTOs = append(sessionDTOs, s.toSessionInfoDTO(session))
	}

	// 计算总页数
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	return &dto.SessionListResponseDTO{
		Sessions:   sessionDTOs,
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}, nil
}

// RevokeSession 撤销会话
func (s *AuthApplicationService) RevokeSession(ctx context.Context, sessionID string, userID int64) error {
	// 查找会话
	session, err := s.authRepo.FindSessionByID(ctx, sessionID)
	if err != nil {
		return userErrors.NewBusinessError(userErrors.CodeSessionNotFound, fmt.Sprintf("session_id: %s", sessionID))
	}

	// 检查会话是否属于当前用户
	if session.UserID != userID {
		return userErrors.NewBusinessError(userErrors.CodeSessionInvalid, "会话不属于当前用户")
	}

	// 撤销会话
	session.Revoke()
	if err := s.authRepo.UpdateSession(ctx, session); err != nil {
		return userErrors.NewSystemError("撤销会话", fmt.Sprintf("session revoke failed: %v", err))
	}

	return nil
}

// ForgotPassword 忘记密码
func (s *AuthApplicationService) ForgotPassword(ctx context.Context, forgotDTO *dto.ForgotPasswordDTO) error {
	return s.ForgotPasswordWithContext(ctx, forgotDTO, "", "")
}

// ForgotPasswordWithContext 忘记密码 (带上下文信息)
func (s *AuthApplicationService) ForgotPasswordWithContext(ctx context.Context, forgotDTO *dto.ForgotPasswordDTO, ipAddress, userAgent string) error {
	// 从usercontext获取租户信息
	tenantInfo, _ := usercontext.GetTenantInfo(ctx)
	tenantCode := ""
	tenantID := int64(0)

	if tenantInfo != nil {
		tenantCode = tenantInfo.TenantCode
		tenantID = tenantInfo.TenantID
	}

	s.logger.Info(ctx, "password reset request",
		logiface.String("email", forgotDTO.Email),
		logiface.String("tenant_code", tenantCode),
		logiface.String("ip_address", ipAddress),
		logiface.String("user_agent", userAgent))

	// 检查租户信息是否可用
	if tenantID == 0 {
		s.logger.Warn(ctx, "tenant information not available for password reset",
			logiface.String("tenant_code", tenantCode),
		)
		// 不返回错误，避免泄露租户信息
		return nil
	}

	// 查找用户
	user, err := s.userRepo.FindByEmail(ctx, tenantID, forgotDTO.Email)
	if err != nil {
		s.logger.Info(ctx, "user not found for password reset",
			logiface.String("email", forgotDTO.Email),
			logiface.Int64("tenant_id", tenantID))
		// 不返回错误，避免泄露用户信息
		return nil
	}

	// 检查用户状态
	if user.Status != userValueObject.UserStatusActive {
		s.logger.Info(ctx, "inactive user attempted password reset",
			logiface.String("email", forgotDTO.Email),
			logiface.Int64("user_id", user.ID),
			logiface.String("status", user.Status.String()))
		// 不返回错误，避免泄露用户状态信息
		return nil
	}

	// 发送密码重置邮件
	if err := s.sendPasswordResetEmail(ctx, user); err != nil {
		s.logger.Error(ctx, "failed to send password reset email",
			logiface.Error(err),
			logiface.Int64("user_id", user.ID),
			logiface.String("email", user.Email))
		return userErrors.NewSystemError("send password reset email", err.Error())
	}

	s.logger.Info(ctx, "password reset email sent successfully",
		logiface.Int64("user_id", user.ID),
		logiface.String("email", user.Email))

	return nil
}

// ResetPassword 重置密码
func (s *AuthApplicationService) ResetPassword(ctx context.Context, resetDTO *dto.ResetPasswordDTO) error {
	s.logger.Info(ctx, "password reset attempt",
		logiface.String("token", resetDTO.Token))

	if s.verificationService == nil {
		s.logger.Error(ctx, "verification service not configured")
		return userErrors.NewSystemError("password reset", "verification service not available")
	}

	// 验证重置令牌
	verifyResp, err := s.verifyPasswordResetToken(ctx, resetDTO.Token)
	if err != nil {
		s.logger.Warn(ctx, "password reset token verification failed",
			logiface.String("token", resetDTO.Token),
			logiface.Error(err))
		return err
	}

	// 检查是否关联了用户ID
	if verifyResp.UserID == nil {
		s.logger.Error(ctx, "no user associated with password reset token",
			logiface.String("token", resetDTO.Token))
		return userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌未关联用户")
	}

	// 验证新密码
	if err := s.validateNewPassword(ctx, *verifyResp.UserID, resetDTO.NewPassword); err != nil {
		s.logger.Warn(ctx, "new password validation failed",
			logiface.Int64("user_id", *verifyResp.UserID),
			logiface.Error(err))
		return err
	}

	// 更新密码
	if err := s.updateUserPassword(ctx, *verifyResp.UserID, resetDTO.NewPassword); err != nil {
		s.logger.Error(ctx, "failed to update user password",
			logiface.Error(err),
			logiface.Int64("user_id", *verifyResp.UserID))
		return err
	}

	// 撤销用户的所有活跃会话（强制重新登录）
	if err := s.revokeAllUserSessions(ctx, *verifyResp.UserID); err != nil {
		s.logger.Warn(ctx, "failed to revoke user sessions after password reset",
			logiface.Error(err),
			logiface.Int64("user_id", *verifyResp.UserID))
		// 不阻断流程，密码重置已成功
	}

	s.logger.Info(ctx, "password reset successful",
		logiface.Int64("user_id", *verifyResp.UserID))

	return nil
}

// recordLoginAttempt 记录登录尝试
func (s *AuthApplicationService) recordLoginAttempt(ctx context.Context, tenantID int64, username, ipAddress, userAgent, reason string) {
	attempt := authEntity.NewLoginAttempt(tenantID, username, ipAddress, userAgent)

	if reason == "Login successful" {
		attempt.MarkSuccess()
	} else {
		attempt.MarkFailed()
	}

	// ✅ 保存登录尝试记录
	if err := s.authRepo.CreateLoginAttempt(ctx, attempt); err != nil {
		s.logger.Error(ctx, "failed to create login attempt",
			logiface.Error(err),
			logiface.String("username", username),
			logiface.String("ip", ipAddress),
		)
	}
}

// toUserInfoDTO 转换为用户信息DTO
func (s *AuthApplicationService) toUserInfoDTO(user *userEntity.User) dto.UserInfoDTO {
	roles := make([]dto.RoleDTO, 0, len(user.Roles))
	for _, role := range user.Roles {
		roles = append(roles, dto.RoleDTO{
			ID:   role.ID,
			Name: role.Name,
		})
	}

	return dto.UserInfoDTO{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		RealName: user.RealName,
		Status:   string(user.Status),
		Roles:    roles,
	}
}

// toSessionInfoDTO 转换为会话信息DTO
func (s *AuthApplicationService) toSessionInfoDTO(session *authEntity.AuthSession) dto.SessionInfoDTO {
	deviceInfo := dto.DeviceInfoDTO{
		DeviceType:  session.DeviceType,
		OS:          session.OS,
		OSVersion:   session.OSVersion,
		Browser:     session.Browser,
		DeviceModel: session.DeviceModel,
		DisplayName: getDeviceDisplayName(session.DeviceType, session.OS, session.Browser),
	}

	return dto.SessionInfoDTO{
		SessionID:    session.ID,
		DeviceInfo:   deviceInfo,
		Status:       string(session.Status),
		LoginAt:      session.CreatedAt.Format("2006-01-02T15:04:05Z"),
		LastActiveAt: session.LastUsedAt.Format("2006-01-02T15:04:05Z"),
		ExpiresAt:    session.ExpiresAt.Format("2006-01-02T15:04:05Z"),
	}
}

// getDeviceDisplayName 获取设备显示名称
func getDeviceDisplayName(deviceType, os, browser string) string {
	if deviceType == "mobile" {
		return "移动设备"
	} else if deviceType == "tablet" {
		return "平板设备"
	} else {
		if os != "" && browser != "" {
			return browser + " on " + os
		} else if os != "" {
			return os
		} else if browser != "" {
			return browser
		}
		return "桌面设备"
	}
}

// SendMFACode 发送MFA验证码
func (s *AuthApplicationService) SendMFACode(ctx context.Context, mfaDTO *dto.SendMFACodeDTO) error {
	// TODO: 实现MFA验证码发送逻辑
	// 1. 查找用户
	// 2. 检查用户是否启用了MFA
	// 3. 生成验证码
	// 4. 发送验证码（短信、邮件等）
	return userErrors.NewSystemError("mfa_code_sending", "MFA code sending not implemented")
}

// VerifyMFACode 验证MFA验证码
func (s *AuthApplicationService) VerifyMFACode(ctx context.Context, mfaDTO *dto.VerifyMFACodeDTO) (*dto.LoginResponseDTO, error) {
	// TODO: 实现MFA验证码验证逻辑
	// 1. 查找用户
	// 2. 验证验证码
	// 3. 生成JWT令牌
	// 4. 创建会话
	return nil, userErrors.NewSystemError("mfa_code_verification", "MFA code verification not implemented")
}

// sendPasswordResetEmail 发送密码重置邮件
func (s *AuthApplicationService) sendPasswordResetEmail(ctx context.Context, user *userEntity.User) error {
	if s.verificationService == nil {
		return userErrors.NewSystemError("verification_service", "verification service not configured")
	}

	// 构建发送验证请求
	verificationReq := &verificationDTO.SendVerificationRequest{
		Target:     user.Email,
		TargetType: verificationEntity.TargetTypeEmail,
		Purpose:    verificationEntity.PurposePasswordReset,
		UserID:     &user.ID,
		Variables: map[string]string{
			"username": user.Username,
		},
	}

	// 发送密码重置邮件
	_, err := s.verificationService.SendVerification(ctx, user.TenantID, verificationReq)
	if err != nil {
		return userErrors.NewSystemError("send_password_reset_email", fmt.Sprintf("failed to send password reset email: %v", err))
	}

	return nil
}

// verifyPasswordResetToken 验证密码重置令牌
func (s *AuthApplicationService) verifyPasswordResetToken(ctx context.Context, token string) (*verificationDTO.VerifyTokenResponse, error) {
	// 获取租户ID，优先从上下文获取，否则通过令牌查找
	tenantID, _ := usercontext.GetTenantID(ctx)
	if tenantID == 0 {
		// 如果上下文中没有租户ID，需要通过token从数据库获取
		// 这是密码重置场景的特殊情况，用户可能还未登录
		s.logger.Debug(ctx, "no tenant ID in context for password reset, will validate token across all tenants")
		tenantID = 0 // 使用0表示跨租户查询
	}

	// 首先检查token状态 - 如果tenantID为0，验证服务应该支持跨租户查询
	checkReq := &verificationDTO.CheckTokenStatusRequest{
		Token: token,
	}

	// 对于密码重置场景，我们需要特殊处理租户验证
	// 因为用户可能通过邮件链接访问，此时上下文中没有租户信息
	var statusResp *verificationDTO.CheckTokenStatusResponse
	var err error

	if tenantID > 0 {
		statusResp, err = s.verificationService.CheckTokenStatus(ctx, tenantID, checkReq)
	} else {
		// 跨租户验证 - 这里需要遍历可能的租户或使用特殊的验证方法
		// 临时实现：尝试常用的租户ID
		for _, tid := range []int64{1, 2} { // 可以从配置获取活跃租户列表
			statusResp, err = s.verificationService.CheckTokenStatus(ctx, tid, checkReq)
			if err == nil {
				tenantID = tid
				s.logger.Info(ctx, "found token in tenant", logiface.Int64("tenant_id", tenantID))
				break
			}
		}
	}

	if err != nil {
		s.logger.Warn(ctx, "password reset token not found or invalid",
			logiface.String("token", token),
			logiface.Error(err))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌无效或已过期")
	}

	// 检查token状态
	if statusResp.Status != verificationEntity.TokenStatusUnused {
		s.logger.Warn(ctx, "password reset token already used or expired",
			logiface.String("token", token),
			logiface.String("status", statusResp.StatusName))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌已被使用或已过期")
	}

	// 为了获取完整的token信息（包括user_id），我们需要通过verification service
	// 但由于当前verification service的VerifyToken需要target参数，我们需要先获取token详情
	// 这是一个架构限制，在生产环境中应该扩展verification service支持token-only验证

	// 临时解决方案：直接查询token repository获取完整信息
	// 注意：这违反了DDD的分层原则，但是为了完成功能必要的妥协
	tokenEntity, err := s.getTokenDirectly(ctx, token)
	if err != nil {
		s.logger.Warn(ctx, "failed to get token details directly, using fallback verification",
			logiface.String("token", token),
			logiface.Error(err))

		// fallback: 创建临时的验证响应，但缺少user_id
		// 这种情况下我们无法获取真实的user_id，但可以尝试继续验证流程
		// 在生产环境中，应该扩展verification service或添加proper repository访问
		return nil, userErrors.NewSystemError("token verification", "无法获取完整令牌信息，请联系系统管理员")
	}

	if tokenEntity == nil {
		s.logger.Error(ctx, "token entity is null",
			logiface.String("token", token))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌未关联用户")
	}

	// 如果token没有关联user_id，这是一个数据问题
	if tokenEntity.UserID == nil {
		s.logger.Error(ctx, "token has no associated user_id",
			logiface.String("token", token))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌未关联用户")
	}

	// 验证token用途
	if tokenEntity.Purpose != verificationEntity.PurposePasswordReset {
		s.logger.Warn(ctx, "token purpose mismatch for password reset",
			logiface.String("token", token),
			logiface.Int("expected_purpose", int(verificationEntity.PurposePasswordReset)),
			logiface.Int("actual_purpose", int(tokenEntity.Purpose)))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "令牌用途不匹配")
	}

	// 现在我们有了完整的信息，可以进行真正的验证
	verifyReq := &verificationDTO.VerifyTokenRequest{
		Token:     token,
		Target:    tokenEntity.Target,
		IPAddress: "", // 可以从上下文获取
		UserAgent: "", // 可以从上下文获取
	}

	// 调用真正的验证方法来消费token
	verifyResp, err := s.verificationService.VerifyToken(ctx, tokenEntity.TenantID, verifyReq)
	if err != nil {
		s.logger.Error(ctx, "token verification failed",
			logiface.String("token", token),
			logiface.Error(err))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "令牌验证失败")
	}

	s.logger.Info(ctx, "password reset token verified successfully",
		logiface.Int64("user_id", *tokenEntity.UserID),
		logiface.Int64("tenant_id", tokenEntity.TenantID))

	return verifyResp, nil
}

// validateNewPassword 验证新密码
func (s *AuthApplicationService) validateNewPassword(ctx context.Context, userID int64, newPassword string) error {
	// 获取用户信息
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return userErrors.NewUserNotFoundError(fmt.Sprintf("%d", userID))
	}

	// 获取租户密码策略
	if s.tenantConfigService != nil {
		policyResp, err := s.tenantConfigService.GetPasswordPolicy(ctx, &userDTO.GetPasswordPolicyRequest{
			TenantID: user.TenantID,
		})
		if err == nil && policyResp != nil {
			// 使用密码策略验证器验证新密码
			validator := userDomainService.NewPasswordPolicyValidator()
			if err := validator.ValidatePassword(newPassword, policyResp.Policy); err != nil {
				return userErrors.NewUserError(userErrors.CodePasswordPolicyViolated, err.Error())
			}
		}
	}

	return nil
}

// updateUserPassword 更新用户密码
func (s *AuthApplicationService) updateUserPassword(ctx context.Context, userID int64, newPassword string) error {
	if s.userService == nil {
		return userErrors.NewSystemError("用户服务配置", "用户服务未配置")
	}

	// 创建密码对象
	password, err := userValueObject.NewPassword(newPassword)
	if err != nil {
		return userErrors.NewUserError(userErrors.CodePasswordTooWeak, err.Error())
	}

	// 更新用户密码
	updateReq := &userDTO.UpdateUserRequest{
		Password: password.Hash(),
	}

	_, err = s.userService.UpdateUser(ctx, userID, updateReq)
	if err != nil {
		return userErrors.NewSystemError("update user password", err.Error())
	}

	return nil
}

// revokeAllUserSessions 撤销用户的所有活跃会话
func (s *AuthApplicationService) revokeAllUserSessions(ctx context.Context, userID int64) error {
	// 获取用户的所有活跃会话
	sessions, err := s.authRepo.FindActiveSessionsByUserID(ctx, userID)
	if err != nil {
		return userErrors.NewDatabaseError("查询用户会话", fmt.Sprintf("database query failed: %v", err))
	}

	// 撤销每个会话
	for _, session := range sessions {
		session.Revoke()
		if err := s.authRepo.UpdateSession(ctx, session); err != nil {
			s.logger.Warn(ctx, "failed to revoke session",
				logiface.Error(err),
				logiface.String("session_id", session.ID),
				logiface.Int64("user_id", userID))
			// 继续撤销其他会话
		}
	}

	s.logger.Info(ctx, "revoked user sessions after password reset",
		logiface.Int64("user_id", userID),
		logiface.Int("sessions_count", len(sessions)))

	return nil
}

// getTokenDirectly 直接获取验证令牌详情
// 注意：这违反了DDD的分层原则，但是为了获取完整token信息的必要妥协
// 在生产环境中，应该扩展verification service提供token详情查询接口
func (s *AuthApplicationService) getTokenDirectly(ctx context.Context, token string) (*verificationEntity.VerificationToken, error) {
	// 这里需要直接访问verification token repository
	// 由于当前架构限制，我们需要通过依赖注入获取repository
	// 或者通过verification service提供新的方法来获取token详情

	// 临时解决方案：返回nil并在调用处处理
	// 在实际生产环境中，需要：
	// 1. 在constructor中注入verification token repository
	// 2. 或者扩展verification service提供GetTokenDetails方法

	s.logger.Warn(ctx, "getTokenDirectly not fully implemented - using fallback",
		logiface.String("token", token))

	// 返回nil表示无法直接获取token详情
	// 调用方应该有fallback处理逻辑
	return nil, userErrors.NewSystemError("令牌访问", "直接令牌访问未实现")
}

// ValidateResetToken 验证重置令牌有效性（不消费令牌）
func (s *AuthApplicationService) ValidateResetToken(ctx context.Context, token string) error {
	if s.verificationService == nil {
		s.logger.Error(ctx, "verification service not configured")
		return userErrors.NewSystemError("validate reset token", "verification service not available")
	}

	// 获取租户ID，优先从上下文获取
	tenantID, _ := usercontext.GetTenantID(ctx)
	if tenantID == 0 {
		s.logger.Debug(ctx, "no tenant ID in context for token validation, will search across tenants")
	}

	checkReq := &verificationDTO.CheckTokenStatusRequest{
		Token: token,
	}

	// 处理租户验证
	var statusResp *verificationDTO.CheckTokenStatusResponse
	var err error

	if tenantID > 0 {
		statusResp, err = s.verificationService.CheckTokenStatus(ctx, tenantID, checkReq)
	} else {
		// 跨租户搜索
		for _, tid := range []int64{1, 2} { // 可以从配置获取活跃租户列表
			statusResp, err = s.verificationService.CheckTokenStatus(ctx, tid, checkReq)
			if err == nil {
				tenantID = tid
				s.logger.Debug(ctx, "found token in tenant for validation",
					logiface.Int64("tenant_id", tenantID))
				break
			}
		}
	}

	if err != nil {
		s.logger.Debug(ctx, "token validation failed",
			logiface.String("token", token),
			logiface.Error(err))
		return userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌无效或已过期")
	}

	// 检查token状态
	if statusResp.Status != verificationEntity.TokenStatusUnused {
		s.logger.Debug(ctx, "token not in valid state",
			logiface.String("token", token),
			logiface.String("status", statusResp.StatusName))
		return userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "重置令牌已被使用或已过期")
	}

	// 额外验证：确认这是密码重置令牌
	tokenEntity, err := s.getTokenDirectly(ctx, token)
	if err != nil {
		s.logger.Debug(ctx, "failed to get token details for validation, using basic validation",
			logiface.String("token", token),
			logiface.Error(err))
		// 如果无法获取完整token信息，但基础验证已通过，则认为token有效
		// 在生产环境中应该有更严格的验证
		return nil
	}

	if tokenEntity != nil && tokenEntity.Purpose != verificationEntity.PurposePasswordReset {
		s.logger.Warn(ctx, "token purpose validation failed",
			logiface.String("token", token),
			logiface.Int("expected_purpose", int(verificationEntity.PurposePasswordReset)),
			logiface.Int("actual_purpose", int(tokenEntity.Purpose)))
		return userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "令牌用途不正确")
	}

	return nil
}

// checkLoginLimits 检查登录限制
func (s *AuthApplicationService) checkLoginLimits(ctx context.Context, username, ipAddress string) error {
	config := DefaultLoginLimitConfig
	since := time.Now().Add(-config.ResetDuration)

	// 检查用户失败次数
	userFailedCount, err := s.authRepo.GetFailedLoginAttempts(ctx, username, since)
	if err != nil {
		s.logger.Error(ctx, "failed to count user failed attempts", logiface.Error(err))
		return userErrors.NewSystemError("count user failed attempts", err.Error())
	}

	if userFailedCount >= int64(config.MaxFailedAttemptsPerUser) {
		s.logger.Warn(ctx, "user login blocked due to too many failed attempts",
			logiface.String("username", username),
			logiface.Int64("failed_count", userFailedCount),
		)
		return userErrors.NewUserError(userErrors.CodeLoginAttemptsExceeded, fmt.Sprintf("attempts: %d, max: %d", int(userFailedCount), config.MaxFailedAttemptsPerUser))
	}

	// 检查IP失败次数
	ipFailedCount, err := s.authRepo.GetFailedLoginAttempts(ctx, ipAddress, since)
	if err != nil {
		s.logger.Error(ctx, "failed to count IP failed attempts", logiface.Error(err))
		return userErrors.NewSystemError("count IP failed attempts", err.Error())
	}

	if ipFailedCount >= int64(config.MaxFailedAttemptsPerIP) {
		s.logger.Warn(ctx, "IP login blocked due to too many failed attempts",
			logiface.String("ip", ipAddress),
			logiface.Int64("failed_count", ipFailedCount),
		)
		return userErrors.NewUserError(userErrors.CodeIPBlocked, fmt.Sprintf("ip: %s, reason: %s", ipAddress, fmt.Sprintf("too many failed attempts: %d", ipFailedCount)))
	}

	return nil
}

// LoginRiskLevel 登录风险等级
// 0: 无风险 1: 需要图片验证码 2: 需要邮箱验证 3: 禁用账户
// 可根据需要扩展
// LoginRiskLevel 定义
// RiskNone: 无风险
// RiskCaptcha: 需要图片验证码
// RiskEmailVerification: 需要邮箱验证
// RiskAccountDisabled: 禁用账户
//
//go:generate stringer -type=LoginRiskLevel
type LoginRiskLevel int

const (
	RiskNone LoginRiskLevel = iota
	RiskCaptcha
	RiskEmailVerification
	RiskAccountDisabled
)

// AnalyzeLoginRisk 分析当前登录风险等级，判断是否需要验证码/邮箱验证/禁用账户
// 失败3次及以上给图片验证码，超过5次需要邮箱验证，10次及以上禁用账户
func (s *AuthApplicationService) AnalyzeLoginRisk(ctx context.Context, userID string, failedAttempts int) (LoginRiskLevel, error) {
	s.logger.Info(ctx, "analyze login risk", logiface.String("user_id", userID), logiface.Int("failed_attempts", failedAttempts))
	switch {
	case failedAttempts < 3:
		return RiskNone, nil
	case failedAttempts < 6:
		return RiskCaptcha, nil
	case failedAttempts < 10:
		return RiskEmailVerification, nil
	default:
		return RiskAccountDisabled, nil
	}
}

// Login 用户登录（对外暴露，自动记录login_attempts）
func (s *AuthApplicationService) Login(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, error) {
	// 装饰器嵌入流程，最佳实践：每次调用都自动记录login_attempts
	loginHandler := WithLoginAttemptRecord(
		s.loginCoreHandlerWithJTI,
		s.authRepo,
	)
	resp, _, err := loginHandler(ctx, loginDTO, ipAddress, userAgent)
	return resp, err
}

// loginCoreHandlerWithJTI 使用JTI方案的登录核心处理
func (s *AuthApplicationService) loginCoreHandlerWithJTI(ctx context.Context, loginDTO *dto.LoginDTO, ipAddress, userAgent string) (*dto.LoginResponseDTO, int64, error) {
	// 从usercontext获取租户信息
	tenantInfo, _ := usercontext.GetTenantInfo(ctx)
	tenantCode := ""
	if tenantID, _ := usercontext.GetTenantID(ctx); tenantID > 0 {
		if tenantInfo != nil {
			tenantCode = tenantInfo.TenantCode
		}
	}

	s.logger.Debug(ctx, "auth service login with JTI called",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.String("ip", ipAddress),
		logiface.String("user_agent", userAgent),
	)

	// 检查登录限制
	if err := s.checkLoginLimits(ctx, loginDTO.Username, ipAddress); err != nil {
		return nil, 0, err
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	if tenantID == 0 {
		// 租户信息不可用，无法继续登录
		s.logger.Error(ctx, "tenant information not available, cannot proceed with login",
			logiface.String("tenant_code", tenantCode),
		)
		return nil, 0, userErrors.NewTenantNotFoundError(tenantCode)
	}

	s.logger.Info(ctx, "login attempt with JTI",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", ipAddress),
	)

	// 查找用户
	user, err := s.userRepo.FindByUsername(ctx, tenantID, loginDTO.Username)
	if err != nil {
		s.logger.Error(ctx, "database error during user lookup",
			logiface.Error(err),
			logiface.String("error_type", fmt.Sprintf("%T", err)),
			logiface.String("error_message", err.Error()),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewDatabaseError("user lookup", err.Error())
	}

	if user == nil {
		s.logger.Warn(ctx, "user not found during login",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewInvalidCredentialsError(loginDTO.Username)
	}

	// 检查用户状态
	if user.Status == userValueObject.UserStatusLocked {
		s.logger.Warn(ctx, "locked user login attempt",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
			logiface.String("user_status", string(user.Status)),
		)
		return nil, 0, userErrors.NewAccountLockedError(user.LockReason)
	}

	if user.Status == userValueObject.UserStatusDisabled {
		s.logger.Warn(ctx, "disabled user login attempt",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
			logiface.String("user_status", string(user.Status)),
		)
		return nil, 0, userErrors.NewUserError(userErrors.CodeUserAccountDisabled, fmt.Sprintf("reason: %s", user.LockReason))
	}

	s.logger.Debug(ctx, "user status check passed",
		logiface.String("username", loginDTO.Username),
		logiface.String("user_status", string(user.Status)),
	)

	// 验证密码
	s.logger.Debug(ctx, "verifying password",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", ipAddress),
	)

	// 验证密码
	if !user.Password.Verify(loginDTO.Password) {
		s.logger.Warn(ctx, "password validation failed",
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewInvalidCredentialsError(loginDTO.Username)
	}

	s.logger.Info(ctx, "password validation successful",
		logiface.String("username", loginDTO.Username),
		logiface.String("tenant_code", tenantCode),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", ipAddress),
	)

	// 生成JWT令牌（使用新的JTI方案）
	accessTokenInfo, err := s.jwtService.GenerateAccessTokenWithInfo(user.ID, tenantID, user.Username)
	if err != nil {
		s.logger.Error(ctx, "failed to generate access token with info",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("generate access token", err.Error())
	}

	refreshTokenInfo, err := s.jwtService.GenerateRefreshTokenWithInfo(user.ID, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to generate refresh token with info",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("generate refresh token", err.Error())
	}

	// 创建会话（使用JTI和令牌哈希）
	deviceInfo := authValueObject.NewDeviceInfo(userAgent)
	session := authEntity.NewAuthSessionWithJTI(
		user.ID,
		tenantID,
		accessTokenInfo.JTI, // 使用访问令牌的JTI作为会话JTI
		accessTokenInfo.TokenHash,
		refreshTokenInfo.TokenHash,
		deviceInfo,
		time.Now().Add(24*time.Hour),
	)

	if err := s.authRepo.CreateSession(ctx, session); err != nil {
		s.logger.Error(ctx, "failed to create auth session",
			logiface.Error(err),
			logiface.String("username", loginDTO.Username),
			logiface.String("tenant_code", tenantCode),
			logiface.String("ip", ipAddress),
		)
		return nil, 0, userErrors.NewSystemError("create auth session", err.Error())
	}

	// 记录登录成功
	return &dto.LoginResponseDTO{
		AccessToken:  accessTokenInfo.Token,
		RefreshToken: refreshTokenInfo.Token,
		TokenType:    "Bearer",
		ExpiresIn:    24 * 60 * 60, // 24小时
		User:         s.toUserInfoDTO(user),
		RequiresMFA:  false,
	}, user.ID, nil
}
