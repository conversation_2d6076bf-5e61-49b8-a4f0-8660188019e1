package service

import (
	"context"
	"errors"
	"fmt"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/auth/dto"
	userDTO "platforms-user/internal/application/user/dto"
	userAppService "platforms-user/internal/application/user/service"
	verificationDTO "platforms-user/internal/application/verification/dto"
	userErrors "platforms-user/internal/domain/errors"
	userEntity "platforms-user/internal/domain/user/entity"
	userDomainService "platforms-user/internal/domain/user/service"
	userValueObject "platforms-user/internal/domain/user/value_object"
	verificationEntity "platforms-user/internal/domain/verification/entity"
	"platforms-user/pkg/config"
)

// RegisterApplicationService 注册应用服务
type RegisterApplicationService struct {
	logger                  logiface.Logger
	userService             *userAppService.UserApplicationService
	tenantService           *userAppService.TenantApplicationService // 添加租户服务依赖
	tenantConfigService     *userAppService.TenantConfigService
	verificationService     VerificationService // 使用接口而不是具体类型
	captchaService          CaptchaService
	passwordPolicyValidator *userDomainService.PasswordPolicyValidator
	appConfig               *config.AppConfig // 添加应用配置引用
}

// NewRegisterApplicationService 创建注册应用服务
func NewRegisterApplicationService(
	logger logiface.Logger,
	userService *userAppService.UserApplicationService,
	tenantService *userAppService.TenantApplicationService, // 添加租户服务参数
	tenantConfigService *userAppService.TenantConfigService,
	verificationService VerificationService, // 使用接口
	captchaService CaptchaService,
	appConfig *config.AppConfig, // 添加appConfig参数
) *RegisterApplicationService {
	return &RegisterApplicationService{
		logger:                  logger,
		userService:             userService,
		tenantService:           tenantService, // 初始化租户服务
		tenantConfigService:     tenantConfigService,
		verificationService:     verificationService,
		captchaService:          captchaService,
		passwordPolicyValidator: userDomainService.NewPasswordPolicyValidator(),
		appConfig:               appConfig, // 初始化appConfig
	}
}

// RegisterUser 用户注册
func (s *RegisterApplicationService) RegisterUser(ctx context.Context, registerDTO *dto.RegisterDTO) (*dto.RegisterResponseDTO, error) {
	s.logger.Info(ctx, "user registration attempt",
		logiface.String("username", registerDTO.Username),
		logiface.String("email", registerDTO.Email),
		logiface.Int64("tenant_id", registerDTO.TenantID),
	)

	// 2. 验证邀请码（如果提供）
	if registerDTO.InviteCode != "" {
		if err := s.validateInviteCode(ctx, registerDTO.InviteCode, registerDTO.TenantID); err != nil {
			s.logger.Warn(ctx, "invite code validation failed",
				logiface.String("username", registerDTO.Username),
				logiface.String("invite_code", registerDTO.InviteCode),
				logiface.Error(err),
			)
			return nil, userErrors.NewUserError(userErrors.CodeInvitationInvalid, fmt.Sprintf("invitation_code: %s", registerDTO.InviteCode))
		}
	}

	// 3. 验证租户是否存在且允许注册
	if err := s.validateTenant(ctx, registerDTO.TenantID); err != nil {
		s.logger.Warn(ctx, "tenant validation failed",
			logiface.String("username", registerDTO.Username),
			logiface.Int64("tenant_id", registerDTO.TenantID),
			logiface.Error(err),
		)
		return nil, userErrors.NewUserError(userErrors.CodeTenantNotFound, fmt.Sprintf("tenant_id: %d", registerDTO.TenantID))
	}

	// 4. 获取租户密码策略并验证密码
	if err := s.validatePasswordWithPolicy(ctx, registerDTO.Password, registerDTO.TenantID); err != nil {
		s.logger.Warn(ctx, "password validation failed",
			logiface.String("username", registerDTO.Username),
			logiface.Int64("tenant_id", registerDTO.TenantID),
			logiface.Error(err),
		)
		return nil, userErrors.NewUserError(userErrors.CodePasswordPolicyViolated, err.Error())
	}

	// 5. 检查是否需要邮箱验证
	registrationMethods, err := s.getRegistrationMethods(ctx, registerDTO.TenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to get registration methods",
			logiface.Error(err),
			logiface.Int64("tenant_id", registerDTO.TenantID),
		)
		return nil, userErrors.NewSystemError("get_registration_methods", err.Error())
	}

	// 判断用户状态和是否需要发送验证邮件
	// 使用枚举代替字符串字面量，提高类型安全性
	userStatus := userValueObject.UserStatusActive // 默认激活状态
	needEmailVerification := false

	// 根据注册配置决定用户初始状态
	// 如果需要邮箱验证且启用手动激活，则用户状态设为待审核
	if registerDTO.Email != "" && registrationMethods.Email.Enabled && registrationMethods.Email.RequireVerification {
		needEmailVerification = true
		if registrationMethods.Email.ManualActivation {
			userStatus = userValueObject.UserStatusPending // 等待管理员手动激活
		}
	}

	// 6. 创建用户请求
	createUserReq := &userDTO.CreateUserRequest{
		TenantID: registerDTO.TenantID,
		Username: registerDTO.Username,
		Email:    registerDTO.Email,
		Password: registerDTO.Password,
		RealName: registerDTO.RealName,
		Phone:    registerDTO.Phone,
		Status:   userStatus.String(), // 转换为字符串
	}

	// 7. 创建用户
	user, err := s.userService.CreateUser(ctx, createUserReq)
	if err != nil {
		// 根据错误类型返回相应的用户错误
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			return nil, userErr
		}
		s.logger.Error(ctx, "user creation failed",
			logiface.Error(err),
			logiface.String("username", registerDTO.Username),
			logiface.String("email", registerDTO.Email),
			logiface.Int64("tenant_id", registerDTO.TenantID),
		)
		// 其他错误返回系统错误
		return nil, userErrors.NewSystemError("user_registration", err.Error())
	}

	// 8. 发送验证邮件（如果需要）
	if needEmailVerification {
		if err := s.sendEmailVerification(ctx, user.ID, registerDTO.TenantID, registerDTO.Email, registerDTO); err != nil {
			s.logger.Error(ctx, "failed to send verification email",
				logiface.Error(err),
				logiface.Int64("user_id", user.ID),
				logiface.String("email", registerDTO.Email),
			)
			// 用户已创建，但邮件发送失败，记录错误但不阻断注册流程
			// 可以后续提供重新发送验证邮件的功能
		} else {
			s.logger.Info(ctx, "verification email sent successfully",
				logiface.Int64("user_id", user.ID),
				logiface.String("email", registerDTO.Email),
			)
		}
	}

	s.logger.Info(ctx, "user registration successful",
		logiface.String("username", registerDTO.Username),
		logiface.String("email", registerDTO.Email),
		logiface.Int64("user_id", user.ID),
		logiface.Int64("tenant_id", registerDTO.TenantID),
		logiface.String("status", userStatus.String()),
		logiface.Bool("email_verification_sent", needEmailVerification),
	)

	// 9. 返回注册结果
	response := &dto.RegisterResponseDTO{
		User: s.toUserInfoDTO(user),
	}

	// 如果发送了验证邮件，在响应中添加提示信息
	if needEmailVerification {
		response.Message = "注册成功，验证邮件已发送到您的邮箱，请点击邮件中的链接激活账户"
	}

	return response, nil
}

// GenerateCaptcha 生成验证码
func (s *RegisterApplicationService) GenerateCaptcha(ctx context.Context, captchaDTO *dto.CaptchaGenerateDTO) (*dto.CaptchaResponseDTO, error) {
	s.logger.Info(ctx, "generating captcha",
		logiface.String("type", captchaDTO.Type),
		logiface.Int("width", captchaDTO.Width),
		logiface.Int("height", captchaDTO.Height),
	)

	// 设置默认尺寸
	width := captchaDTO.Width
	if width == 0 {
		width = 200
	}
	height := captchaDTO.Height
	if height == 0 {
		height = 80
	}

	captcha, err := s.captchaService.GenerateCaptcha(ctx, captchaDTO.Type, width, height)
	if err != nil {
		s.logger.Error(ctx, "captcha generation failed",
			logiface.Error(err),
			logiface.String("type", captchaDTO.Type),
		)
		return nil, userErrors.NewSystemError("captcha_generation", err.Error())
	}

	s.logger.Info(ctx, "captcha generated successfully",
		logiface.String("captcha_id", captcha.CaptchaID),
		logiface.String("type", captchaDTO.Type),
	)

	return captcha, nil
}

// validateTenant 验证租户
func (s *RegisterApplicationService) validateTenant(ctx context.Context, tenantID int64) error {
	// TODO: 实现租户验证逻辑
	// 1. 检查租户是否存在
	// 2. 检查租户状态是否为active
	// 3. 检查租户是否允许注册
	// 4. 检查租户用户数量限制

	// 暂时返回nil，表示验证通过
	// 在实际实现中，应该根据验证结果返回相应的错误
	// 例如：
	// if tenant == nil {
	//     return userErrors.NewTenantNotFoundError(fmt.Sprintf("%d", tenantID))
	// }
	// if tenant.Status != "active" {
	//     return userErrors.NewTenantDisabledError(fmt.Sprintf("%d", tenantID))
	// }
	// if !tenant.AllowRegistration {
	//     return userErrors.NewUserError(userErrors.CodeRegistrationDisabled)
	// }

	return nil
}

// validateInviteCode 验证邀请码
func (s *RegisterApplicationService) validateInviteCode(ctx context.Context, inviteCode string, tenantID int64) error {
	// TODO: 实现邀请码验证逻辑
	// 1. 检查邀请码是否存在
	// 2. 检查邀请码是否过期
	// 3. 检查邀请码使用次数
	// 4. 检查邀请码是否属于指定租户

	// 暂时返回nil，表示验证通过
	// 在实际实现中，应该根据验证结果返回相应的错误
	// 例如：
	// if invitation == nil {
	//     return userErrors.NewInvitationInvalidError(inviteCode)
	// }
	// if invitation.ExpiredAt.Before(time.Now()) {
	//     return userErrors.NewUserError(userErrors.CodeInvitationExpired, fmt.Sprintf("invitation_code: %s", inviteCode))
	// }
	// if invitation.UsedCount >= invitation.MaxUses {
	//     return userErrors.NewUserError(userErrors.CodeInvitationLimitReached, fmt.Sprintf("invitation_code: %s", inviteCode))
	// }

	return nil
}

// validatePasswordWithPolicy 根据租户密码策略验证密码
func (s *RegisterApplicationService) validatePasswordWithPolicy(ctx context.Context, password string, tenantID int64) error {
	// 获取租户密码策略
	policyReq := &userDTO.GetPasswordPolicyRequest{
		TenantID: tenantID,
	}

	policyResp, err := s.tenantConfigService.GetPasswordPolicy(ctx, policyReq)
	if err != nil {
		s.logger.Error(ctx, "failed to get password policy",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
		)
		// 如果获取策略失败，使用默认策略
		policy := userEntity.NewDefaultPasswordPolicy()
		return s.passwordPolicyValidator.ValidatePassword(password, policy)
	}

	// 使用租户密码策略验证密码
	return s.passwordPolicyValidator.ValidatePassword(password, policyResp.Policy)
}

// toUserInfoDTO 转换为用户信息DTO
func (s *RegisterApplicationService) toUserInfoDTO(user *userEntity.User) dto.UserInfoDTO {
	return dto.UserInfoDTO{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		RealName: user.RealName,
		Status:   string(user.Status),
		Roles:    []dto.RoleDTO{}, // 新注册用户默认无角色
	}
}

// getRegistrationMethods 获取租户注册方式配置
func (s *RegisterApplicationService) getRegistrationMethods(ctx context.Context, tenantID int64) (*userEntity.RegistrationMethods, error) {
	req := &userDTO.GetRegistrationMethodsRequest{
		TenantID: tenantID,
	}

	resp, err := s.tenantConfigService.GetRegistrationMethods(ctx, req)
	if err != nil {
		return nil, userErrors.NewSystemError("get_registration_methods", fmt.Sprintf("failed to get registration methods: %v", err))
	}

	return resp.Methods, nil
}

// sendEmailVerification 发送邮箱验证（优化版本）
// 使用EmailVerificationRequest对象传递参数，从verification_configs获取失效时间配置
func (s *RegisterApplicationService) sendEmailVerification(ctx context.Context, userID, tenantID int64, email string, registerDTO *dto.RegisterDTO) error {
	// 获取注册方式配置
	registrationMethods, err := s.getRegistrationMethods(ctx, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to get registration methods",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return userErrors.NewSystemError("get_registration_methods", fmt.Sprintf("failed to get registration methods: %v", err))
	}

	// 获取邮箱验证模板代码
	templateCode := registrationMethods.Email.VerificationTemplateCode
	if templateCode == "" {
		s.logger.Error(ctx, "email verification template code not configured",
			logiface.Int64("tenant_id", tenantID),
			logiface.String("email", email))
		return userErrors.NewBusinessError(userErrors.CodeEmailVerificationRequired,
			fmt.Sprintf("email verification template code not configured for tenant %d", tenantID))
	}

	// 构建邮件验证请求对象（优化参数传递）
	emailVerificationReq := &verificationDTO.EmailVerificationRequest{
		Target:     email,
		TargetType: verificationEntity.TargetTypeEmail,
		Purpose:    verificationEntity.PurposeRegistration,
		UserID:     &userID,
		URL:        s.appConfig.BaseURL + "/api/user/activate",
		Variables:  map[string]string{},
	}

	// 使用新的优化方法发送邮件验证
	// 失效时间将从verification_configs配置中自动获取
	_, err = s.verificationService.SendEmailVerification(ctx, tenantID, emailVerificationReq)
	if err != nil {
		s.logger.Error(ctx, "failed to send verification email",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("email", email))
		return userErrors.NewSystemError("email_verification", err.Error())
	}

	return nil
}

// ActivateAccount 激活账户
func (s *RegisterApplicationService) ActivateAccount(ctx context.Context, activationToken string) (*dto.ActivateAccountResponseDTO, error) {
	s.logger.Info(ctx, "account activation attempt",
		logiface.String("token", activationToken))

	// 获取令牌信息并激活账户
	tokenInfo, err := s.getTokenInfoAndActivate(ctx, activationToken)
	if err != nil {
		s.logger.Error(ctx, "account activation failed",
			logiface.String("token", activationToken),
			logiface.Error(err))
		return nil, err
	}

	// 激活用户账户
	if err := s.activateUserAccount(ctx, tokenInfo.UserID); err != nil {
		s.logger.Error(ctx, "user account activation failed",
			logiface.Int64("user_id", tokenInfo.UserID),
			logiface.Error(err))
		return nil, userErrors.NewSystemError("activate_user_account", err.Error())
	}

	s.logger.Info(ctx, "account activation successful",
		logiface.Int64("user_id", tokenInfo.UserID),
		logiface.String("target", tokenInfo.Target))

	return &dto.ActivateAccountResponseDTO{
		Message: "账户激活成功",
		UserID:  tokenInfo.UserID,
	}, nil
}

// getTokenInfoAndActivate 获取令牌信息并激活账户
func (s *RegisterApplicationService) getTokenInfoAndActivate(ctx context.Context, token string) (*tokenActivationInfo, error) {
	// 通过token字符串获取完整令牌信息
	verificationToken, err := s.verificationService.GetTokenByTokenString(ctx, token)
	if err != nil {
		s.logger.Warn(ctx, "token verification failed",
			logiface.String("token", token),
			logiface.Error(err))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "激活令牌无效或已过期")
	}

	// 检查令牌是否有效
	if !verificationToken.IsValid() {
		s.logger.Warn(ctx, "token is not in valid status",
			logiface.String("token", token),
			logiface.String("status", verificationToken.GetStatusName()))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "激活令牌无效或已过期")
	}

	// 检查用途是否为注册激活
	if verificationToken.Purpose != verificationEntity.PurposeRegistration {
		s.logger.Warn(ctx, "token purpose is not registration",
			logiface.String("token", token),
			logiface.Int("purpose", int(verificationToken.Purpose)))
		return nil, userErrors.NewUserError(userErrors.CodeVerificationTokenInvalid, "激活令牌无效或已过期")
	}

	// 标记令牌为已使用
	verificationToken.MarkAsUsed()
	if err := s.verificationService.SaveToken(ctx, verificationToken); err != nil {
		s.logger.Warn(ctx, "failed to update token as used",
			logiface.String("token", token),
			logiface.Error(err))
		// 不阻断激活流程，仅记录日志
	}

	// 返回令牌信息
	return &tokenActivationInfo{
		UserID:  *verificationToken.UserID, // 从token关联的user_id获取
		Target:  verificationToken.Target,  // 从token的target字段获取
		Purpose: int(verificationToken.Purpose),
	}, nil
}

type tokenActivationInfo struct {
	UserID  int64
	Target  string
	Purpose int
}

// activateUserAccount 激活用户账户
func (s *RegisterApplicationService) activateUserAccount(ctx context.Context, userID int64) error {
	// 获取用户信息
	user, err := s.userService.GetUser(ctx, userID)
	if err != nil {
		return userErrors.NewUserNotFoundError(fmt.Sprintf("%d", userID))
	}

	// 检查用户状态
	if user.Status == "active" {
		// 用户已经是激活状态，不需要再次激活
		s.logger.Info(ctx, "user already active", logiface.Int64("user_id", userID))
		return nil
	}

	if user.Status != "pending" {
		// 用户状态不是pending，不能激活
		return userErrors.NewUserError(userErrors.CodeUserStatusInvalid,
			fmt.Sprintf("无法激活状态为 %s 的用户", user.Status))
	}

	// 更新用户状态为active
	updateReq := &userDTO.UpdateUserRequest{
		Status: "active",
	}

	_, err = s.userService.UpdateUser(ctx, userID, updateReq)
	if err != nil {
		return userErrors.NewSystemError("activate_user_account", err.Error())
	}

	s.logger.Info(ctx, "user account activated successfully",
		logiface.Int64("user_id", userID))

	return nil
}

// getSupportEmail 获取租户的客服邮箱
func (s *RegisterApplicationService) getSupportEmail(ctx context.Context, tenantID int64) string {
	// 从租户配置中获取客服邮箱，如果没有配置则使用默认值
	if s.tenantConfigService != nil {
		config, err := s.tenantConfigService.GetConfig(ctx, tenantID, "support_email")
		if err == nil && config != nil && config.ConfigValue != "" {
			return config.ConfigValue
		}
	}

	// 返回系统默认客服邮箱（应该从系统配置获取）
	return s.getSystemConfig("default_support_email", "<EMAIL>")
}

// getCompanyName 获取租户的公司名称
func (s *RegisterApplicationService) getCompanyName(ctx context.Context, tenantID int64) string {
	// 从租户配置中获取公司名称，如果没有配置则使用租户名称
	if s.tenantConfigService != nil {
		config, err := s.tenantConfigService.GetConfig(ctx, tenantID, "company_name")
		if err == nil && config != nil && config.ConfigValue != "" {
			return config.ConfigValue
		}
	}

	// 如果没有配置公司名称，则使用租户名称
	if s.tenantService != nil {
		tenant, err := s.tenantService.GetTenant(ctx, tenantID)
		if err == nil && tenant != nil {
			return tenant.TenantName
		}
	}

	// 最终回退到系统默认名称
	return s.getSystemConfig("default_company_name", "Platform System")
}

// getSystemConfig 获取系统配置值，如果没有则返回默认值
func (s *RegisterApplicationService) getSystemConfig(key, defaultValue string) string {
	// 这里应该从系统配置服务获取，暂时返回默认值
	// TODO: 实现系统配置服务集成
	return defaultValue
}
