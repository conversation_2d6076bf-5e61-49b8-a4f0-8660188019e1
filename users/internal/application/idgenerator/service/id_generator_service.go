package service

import (
	"context"
	"fmt"
	"platforms-pkg/common"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"platforms-pkg/logiface"
	userErrors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/idgenerator/entity"
	"platforms-user/internal/domain/idgenerator/repository"
	"platforms-user/pkg/config"
)

// IDGeneratorService ID生成器服务
type IDGeneratorService struct {
	sequenceRepo         repository.SequenceRepository
	allocationRepo       repository.AllocationRepository
	preAllocator         *PreAllocator
	cache                map[string]*entity.IDSegment
	preloadCache         map[string]*entity.IDSegment
	mutex                sync.RWMutex
	maxRetries           int
	retryInterval        time.Duration
	preloadThreshold     float64
	defaultIncrementStep int
	logger               logiface.Logger
}

// NewIDGeneratorService 创建ID生成器服务
func NewIDGeneratorService(
	sequenceRepo repository.SequenceRepository,
	allocationRepo repository.AllocationRepository,
	preAllocator *PreAllocator,
	logger logiface.Logger,
) *IDGeneratorService {
	service := &IDGeneratorService{
		sequenceRepo:         sequenceRepo,
		allocationRepo:       allocationRepo,
		preAllocator:         preAllocator,
		cache:                make(map[string]*entity.IDSegment),
		preloadCache:         make(map[string]*entity.IDSegment),
		maxRetries:           config.MaxRetries,
		retryInterval:        config.RetryInterval,
		preloadThreshold:     config.PreloadThreshold,
		defaultIncrementStep: config.DefaultIncrementStep,
		logger:               logger,
	}

	// 启动缓存清理
	service.startCacheCleanup()

	return service
}

// GenerateID 生成单个ID
func (s *IDGeneratorService) GenerateID(ctx context.Context, businessType string, tenantID int64) (int64, error) {
	// 参数验证
	if businessType == "" {
		return 0, userErrors.NewParameterValidationFailedError("business_type", "业务类型不能为空")
	}
	if tenantID <= 0 {
		return 0, userErrors.NewParameterValidationFailedError("tenant_id", "租户ID必须大于0")
	}

	// 验证业务类型格式
	if !s.isValidBusinessType(businessType) {
		return 0, userErrors.NewParameterValidationFailedError("business_type", "业务类型格式无效")
	}

	key := s.makeKey(businessType, tenantID)

	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 获取可用段
	segment, err := s.getAvailableSegment(ctx, key)
	if err != nil {
		s.logger.Error(ctx, "failed to get available segment for ID generation",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("cache_key", key))
		return 0, err
	}

	// 生成ID
	id, err := segment.NextID()
	if err != nil {
		s.logger.Error(ctx, "failed to generate next ID from segment",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("cache_key", key),
			logiface.Int64("segment_start", segment.StartValue),
			logiface.Int64("segment_end", segment.EndValue),
			logiface.Int64("segment_current", segment.CurrentValue),
			logiface.Int("segment_size", segment.Size))
		return 0, err
	}

	// 检查预加载
	s.checkPreload(ctx, key, segment)

	return id, nil
}

// GenerateBatchIDs 批量生成ID
func (s *IDGeneratorService) GenerateBatchIDs(ctx context.Context, businessType string, tenantID int64, count int) ([]int64, error) {
	// 参数验证
	if businessType == "" {
		return nil, userErrors.NewParameterValidationFailedError("business_type", "业务类型不能为空")
	}
	if tenantID <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("tenant_id", "租户ID必须大于0")
	}
	if count <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("count", "生成数量必须大于0")
	}
	if count > 10000 {
		return nil, userErrors.NewParameterValidationFailedError("count", "生成数量不能超过10000")
	}

	// 验证业务类型格式
	if !s.isValidBusinessType(businessType) {
		return nil, userErrors.NewParameterValidationFailedError("business_type", "业务类型格式无效")
	}

	key := s.makeKey(businessType, tenantID)
	ids := make([]int64, 0, count)

	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := 0; i < count; i++ {
		// 获取可用段
		segment, err := s.getAvailableSegment(ctx, key)
		if err != nil {
			s.logger.Error(ctx, "failed to get available segment for batch ID generation",
				logiface.Error(err),
				logiface.String("business_type", businessType),
				logiface.Int64("tenant_id", tenantID),
				logiface.String("cache_key", key),
				logiface.Int("current_index", i),
				logiface.Int("total_count", count))
			return nil, err
		}

		// 生成ID
		id, err := segment.NextID()
		if err != nil {
			s.logger.Error(ctx, "failed to generate next ID from segment for batch generation",
				logiface.Error(err),
				logiface.String("business_type", businessType),
				logiface.Int64("tenant_id", tenantID),
				logiface.String("cache_key", key),
				logiface.Int("current_index", i),
				logiface.Int("total_count", count),
				logiface.Int64("segment_start", segment.StartValue),
				logiface.Int64("segment_end", segment.EndValue),
				logiface.Int64("segment_current", segment.CurrentValue),
				logiface.Int("segment_size", segment.Size))
			return nil, err
		}

		ids = append(ids, id)

		// 检查预加载
		s.checkPreload(ctx, key, segment)
	}

	return ids, nil
}

// getAvailableSegment 获取可用段
func (s *IDGeneratorService) getAvailableSegment(ctx context.Context, key string) (*entity.IDSegment, error) {
	// 检查预加载缓存
	if preloadSegment, exists := s.preloadCache[key]; exists {
		delete(s.preloadCache, key)
		s.cache[key] = preloadSegment
		return preloadSegment, nil
	}

	// 检查当前缓存
	segment, exists := s.cache[key]
	if exists && !segment.IsExhausted() {
		return segment, nil
	}

	// 分配新段
	businessType, tenantID := s.parseKey(key)
	newSegment, err := s.allocateSegmentWithRetry(ctx, businessType, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to allocate new segment with retry",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("cache_key", key),
			logiface.Bool("cache_exists", exists))
		return nil, err
	}

	s.cache[key] = newSegment
	return newSegment, nil
}

// allocateSegmentWithRetry 重试分配段
func (s *IDGeneratorService) allocateSegmentWithRetry(ctx context.Context, businessType string, tenantID int64) (*entity.IDSegment, error) {
	var segment *entity.IDSegment
	var err error

	for i := 0; i < s.maxRetries; i++ {
		segment, err = s.allocateSegment(ctx, businessType, tenantID)
		if err == nil {
			return segment, nil
		}

		if err == entity.ErrNoAvailableSegments {
			s.logger.Warn(ctx, "no available segments, triggering pre-allocation and retrying",
				logiface.Error(err),
				logiface.String("business_type", businessType),
				logiface.Int64("tenant_id", tenantID),
				logiface.Int("retry_attempt", i+1),
				logiface.Int("max_retries", s.maxRetries))
			// 触发预分配
			s.ensureSegmentsAvailable(ctx, businessType, tenantID)
			time.Sleep(s.retryInterval)
			continue
		}

		s.logger.Error(ctx, "failed to allocate segment, not retrying",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("retry_attempt", i+1),
			logiface.Int("max_retries", s.maxRetries))
		return nil, err
	}

	s.logger.Error(ctx, "failed to allocate segment after all retries",
		logiface.Error(err),
		logiface.String("business_type", businessType),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("max_retries", s.maxRetries))
	return nil, fmt.Errorf("failed to allocate segment after %d retries: %w", s.maxRetries, err)
}

// allocateSegment 分配段
func (s *IDGeneratorService) allocateSegment(ctx context.Context, businessType string, tenantID int64) (*entity.IDSegment, error) {
	// 直接基于business_type查询allocation表
	allocation, err := s.allocationRepo.ClaimAvailableSegmentByBusinessType(ctx, businessType, tenantID)
	if err != nil {
		if err == entity.ErrNoAvailableSegments {
			// 如果没有可用段，则通过序列创建新段
			return s.allocateSegmentViaSequence(ctx, businessType, tenantID)
		}
		s.logger.Error(ctx, "failed to claim available segment by business type",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	// 创建ID段
	segment := entity.NewIDSegment(allocation.StartValue, allocation.EndValue, allocation.SegmentSize)

	s.logger.Info(ctx, "successfully allocated new ID segment from allocation table",
		logiface.String("business_type", businessType),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("allocation_id", allocation.ID),
		logiface.Int64("segment_start", allocation.StartValue),
		logiface.Int64("segment_end", allocation.EndValue),
		logiface.Int("segment_size", allocation.SegmentSize))

	return segment, nil
}

// allocateSegmentViaSequence 通过序列分配段（备用方案）
func (s *IDGeneratorService) allocateSegmentViaSequence(ctx context.Context, businessType string, tenantID int64) (*entity.IDSegment, error) {
	// 查找序列，不存在则报错
	sequence, err := s.sequenceRepo.FindByBusinessAndName(ctx, businessType, businessType, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to find sequence",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("sequence not found for business_type: %s, tenant_id: %d", businessType, tenantID)
	}

	if sequence == nil {
		s.logger.Warn(ctx, "sequence not found",
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("sequence not found for business_type: %s, tenant_id: %d", businessType, tenantID)
	}

	// 抢占可用段
	allocation, err := s.allocationRepo.ClaimAvailableSegment(ctx, sequence.ID, tenantID)
	if err != nil {
		s.logger.Error(ctx, "failed to claim available segment",
			logiface.Error(err),
			logiface.String("business_type", businessType),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("sequence_id", sequence.ID),
			logiface.Int64("sequence_current_value", sequence.CurrentValue),
			logiface.Int("sequence_increment_step", sequence.IncrementStep))
		return nil, err
	}
	// 创建ID段
	segment := entity.NewIDSegment(allocation.StartValue, allocation.EndValue, allocation.SegmentSize)
	s.logger.Info(ctx, "successfully allocated new ID segment via sequence",
		logiface.String("business_type", businessType),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("sequence_id", sequence.ID),
		logiface.Int64("allocation_id", allocation.ID),
		logiface.Int64("segment_start", allocation.StartValue),
		logiface.Int64("segment_end", allocation.EndValue),
		logiface.Int("segment_size", allocation.SegmentSize),
		logiface.Int("sequence_increment_step", sequence.IncrementStep))
	return segment, nil
}

// ==================== 业务类型特定的ID生成方法 ====================

// GenerateUserID 生成用户ID
func (s *IDGeneratorService) GenerateUserID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "user", common.SystemTenantID)
}

// GenerateTenantID 生成租户ID
func (s *IDGeneratorService) GenerateTenantID(ctx context.Context) (int64, error) {
	// 租户ID生成不需要依赖特定租户，使用系统级序列
	return s.GenerateID(ctx, "tenant", common.SystemTenantID)
}

// GenerateRoleID 生成角色ID
func (s *IDGeneratorService) GenerateRoleID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "role", common.SystemTenantID)
}

// GeneratePermissionID 生成权限ID
func (s *IDGeneratorService) GeneratePermissionID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "permission", common.SystemTenantID)
}

// GenerateDepartmentID 生成部门ID
func (s *IDGeneratorService) GenerateDepartmentID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "department", common.SystemTenantID)
}

// GeneratePositionID 生成职位ID
func (s *IDGeneratorService) GeneratePositionID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "position", common.SystemTenantID)
}

// GenerateResourceID 生成资源ID
func (s *IDGeneratorService) GenerateResourceID(ctx context.Context) (int64, error) {
	return s.GenerateID(ctx, "resource", common.SystemTenantID)
}

// ensureSegmentsAvailable 确保有可用段
func (s *IDGeneratorService) ensureSegmentsAvailable(ctx context.Context, businessType string, tenantID int64) {
	go func() {
		sequence, err := s.sequenceRepo.FindByBusinessAndName(ctx, businessType, businessType, tenantID)
		if err != nil {
			s.logger.Error(ctx, "failed to find sequence for pre-allocation",
				logiface.Error(err),
				logiface.String("business_type", businessType),
				logiface.Int64("tenant_id", tenantID))
			return
		}

		if s.preAllocator != nil {
			s.preAllocator.EnsureAvailableSegments(ctx, sequence.ID, tenantID)
		}
	}()
}

// checkPreload 检查预加载
func (s *IDGeneratorService) checkPreload(ctx context.Context, key string, segment *entity.IDSegment) {
	if segment.UsageRate() > s.preloadThreshold {
		// 异步预加载
		go func() {
			businessType, tenantID := s.parseKey(key)
			newSegment, err := s.allocateSegmentWithRetry(ctx, businessType, tenantID)
			if err != nil {
				s.logger.Error(ctx, "failed to preload segment",
					logiface.Error(err),
					logiface.String("business_type", businessType),
					logiface.Int64("tenant_id", tenantID),
					logiface.String("cache_key", key))
				return
			}

			s.mutex.Lock()
			s.preloadCache[key] = newSegment
			s.mutex.Unlock()
		}()
	}
}

// startCacheCleanup 启动缓存清理
func (s *IDGeneratorService) startCacheCleanup() {
	ticker := time.NewTicker(10 * time.Minute)
	go func() {
		for range ticker.C {
			s.cleanupCache()
		}
	}()
}

// cleanupCache 清理缓存
func (s *IDGeneratorService) cleanupCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for key, segment := range s.cache {
		if segment.IsExhausted() {
			delete(s.cache, key)
		}
	}
}

// makeKey 只用businessType和tenantID
func (s *IDGeneratorService) makeKey(businessType string, tenantID int64) string {
	return businessType + ":" + strconv.FormatInt(tenantID, 10)
}

// parseKey 解析缓存键
func (s *IDGeneratorService) parseKey(key string) (businessType string, tenantID int64) {
	parts := strings.Split(key, ":")
	if len(parts) == 2 {
		tenantID, _ = strconv.ParseInt(parts[1], 10, 64)
		return parts[0], tenantID
	}
	return "", 0
}

// UpdateSequenceRequest 更新序列请求
type UpdateSequenceRequest struct {
	SequenceName  *string `json:"sequenceName"`
	IncrementStep *int    `json:"incrementStep"`
	CacheSize     *int    `json:"cacheSize"`
	MaxValue      *int64  `json:"maxValue"`
	Threshold     *int    `json:"threshold"`
	IsActive      *bool   `json:"isActive"`
	Remarks       *string `json:"remarks"`
}

// UpdateSequence 更新序列配置
func (s *IDGeneratorService) UpdateSequence(ctx context.Context, sequenceID int64, req UpdateSequenceRequest) (*entity.Sequence, error) {
	// 参数验证
	if sequenceID <= 0 {
		return nil, userErrors.NewParameterValidationFailedError("sequence_id", "序列ID必须大于0")
	}

	// 获取当前序列信息
	sequence, err := s.sequenceRepo.FindByID(ctx, sequenceID)
	if err != nil {
		return nil, err
	}

	// 记录更新前的状态
	s.logger.Info(ctx, "UpdateSequence called",
		logiface.Int64("sequenceID", sequenceID),
		logiface.String("oldSequenceName", sequence.SequenceName),
		logiface.Int("oldIncrementStep", sequence.IncrementStep),
		logiface.Int("oldCacheSize", sequence.CacheSize),
		logiface.Int64("oldMaxValue", sequence.MaxValue),
		logiface.Int("oldThreshold", sequence.Threshold),
		logiface.Bool("oldIsActive", sequence.IsActive),
	)

	// 只更新提供的字段
	if req.SequenceName != nil {
		if *req.SequenceName == "" {
			return nil, userErrors.NewParameterValidationFailedError("sequence_name", "序列名称不能为空")
		}
		sequence.SequenceName = *req.SequenceName
		s.logger.Info(ctx, "Updating sequence name",
			logiface.String("from", sequence.SequenceName),
			logiface.String("to", *req.SequenceName),
		)
	}

	if req.IncrementStep != nil {
		if *req.IncrementStep <= 0 {
			return nil, userErrors.NewParameterValidationFailedError("increment_step", "步长必须大于0")
		}
		if *req.IncrementStep > 1000000 {
			return nil, userErrors.NewParameterValidationFailedError("increment_step", "步长不能超过1000000")
		}
		sequence.IncrementStep = *req.IncrementStep
		s.logger.Info(ctx, "Updating increment step",
			logiface.Int("from", sequence.IncrementStep),
			logiface.Int("to", *req.IncrementStep),
		)
	}

	if req.CacheSize != nil {
		if *req.CacheSize < 0 {
			return nil, userErrors.NewParameterValidationFailedError("cache_size", "缓存大小不能为负数")
		}
		if *req.CacheSize > 100000 {
			return nil, userErrors.NewParameterValidationFailedError("cache_size", "缓存大小不能超过100000")
		}
		sequence.CacheSize = *req.CacheSize
		s.logger.Info(ctx, "Updating cache size",
			logiface.Int("from", sequence.CacheSize),
			logiface.Int("to", *req.CacheSize),
		)
	}

	if req.MaxValue != nil {
		sequence.MaxValue = *req.MaxValue
		s.logger.Info(ctx, "Updating max value",
			logiface.Int64("from", sequence.MaxValue),
			logiface.Int64("to", *req.MaxValue),
		)
	}

	if req.Threshold != nil {
		if *req.Threshold <= 0 {
			return nil, userErrors.NewParameterValidationFailedError("threshold", "阈值必须大于0")
		}
		if *req.Threshold > 100 {
			return nil, userErrors.NewParameterValidationFailedError("threshold", "阈值不能超过100")
		}
		sequence.Threshold = *req.Threshold
		s.logger.Info(ctx, "Updating threshold",
			logiface.Int("from", sequence.Threshold),
			logiface.Int("to", *req.Threshold),
		)
	}

	if req.IsActive != nil {
		sequence.IsActive = *req.IsActive
		s.logger.Info(ctx, "Updating is active",
			logiface.Bool("from", sequence.IsActive),
			logiface.Bool("to", *req.IsActive),
		)
	}

	if req.Remarks != nil {
		sequence.Remarks = *req.Remarks
		s.logger.Info(ctx, "Updating remarks",
			logiface.String("from", sequence.Remarks),
			logiface.String("to", *req.Remarks),
		)
	}

	sequence.UpdatedAt = time.Now()

	// 记录更新后的状态
	s.logger.Info(ctx, "Sequence updated",
		logiface.Int64("sequenceID", sequenceID),
		logiface.String("sequenceName", sequence.SequenceName),
		logiface.Int("incrementStep", sequence.IncrementStep),
		logiface.Int("cacheSize", sequence.CacheSize),
		logiface.Int64("maxValue", sequence.MaxValue),
		logiface.Int("threshold", sequence.Threshold),
		logiface.Bool("isActive", sequence.IsActive),
		logiface.String("remarks", sequence.Remarks),
	)

	// 保存更新
	if err := s.sequenceRepo.Update(ctx, sequence); err != nil {
		s.logger.Error(ctx, "Failed to update sequence",
			logiface.Error(err),
			logiface.Int64("sequenceID", sequenceID))
		return nil, err
	}

	return sequence, nil
}

// isValidBusinessType 验证业务类型格式
func (s *IDGeneratorService) isValidBusinessType(businessType string) bool {
	if businessType == "" {
		return false
	}

	// 支持 system:user 等格式的正则表达式
	// 必须以字母开头，只能包含字母、数字和下划线，支持冒号分隔但不能在开头或结尾
	pattern := `^[a-zA-Z][a-zA-Z0-9_]*(:[a-zA-Z][a-zA-Z0-9_]*)*$`
	matched, err := regexp.MatchString(pattern, businessType)
	if err != nil {
		return false
	}
	return matched
}
