package dto

import (
	"encoding/json"
	"fmt"
	user_errors "platforms-user/internal/domain/errors"
	"time"

	"platforms-user/internal/domain/verification/entity"
)

// CreateTemplateRequest 创建短信模板请求
type CreateTemplateRequest struct {
	Name        string            `json:"name" binding:"required,max=100"`    // 模板名称
	Code        string            `json:"code" binding:"required,max=100"`    // 模板代码
	Description string            `json:"description,omitempty"`              // 模板描述
	Content     string            `json:"content" binding:"required,max=500"` // 模板内容
	Variables   map[string]string `json:"variables,omitempty"`                // 变量说明
}

// Validate 验证请求参数
func (r *CreateTemplateRequest) Validate() error {
	if r.Name == "" {
		return user_errors.NewParameterValidationFailedError("name", "模板名称不能为空")
	}

	if r.Code == "" {
		return user_errors.NewParameterValidationFailedError("code", "模板代码不能为空")
	}

	if r.Content == "" {
		return user_errors.NewParameterValidationFailedError("content", "模板内容不能为空")
	}

	if len(r.Content) > 500 {
		return user_errors.NewParameterValidationFailedError("content", fmt.Sprintf("模板内容长度不能超过500个字符，当前长度: %d", len(r.Content)))
	}

	if len(r.Name) > 100 {
		return user_errors.NewParameterValidationFailedError("name", fmt.Sprintf("模板名称长度不能超过100个字符，当前长度: %d", len(r.Name)))
	}

	if len(r.Code) > 100 {
		return user_errors.NewParameterValidationFailedError("code", fmt.Sprintf("模板代码长度不能超过100个字符，当前长度: %d", len(r.Code)))
	}

	return nil
}

// UpdateTemplateRequest 更新短信模板请求
type UpdateTemplateRequest struct {
	ID          int64             `json:"id" binding:"required"`              // 模板ID
	Name        string            `json:"name" binding:"required,max=100"`    // 模板名称
	Description string            `json:"description,omitempty"`              // 模板描述
	Content     string            `json:"content" binding:"required,max=500"` // 模板内容
	Variables   map[string]string `json:"variables,omitempty"`                // 变量说明
	IsActive    bool              `json:"is_active"`                          // 是否激活
}

// Validate 验证请求参数
func (r *UpdateTemplateRequest) Validate() error {
	if r.ID <= 0 {
		return user_errors.NewParameterValidationFailedError("id", fmt.Sprintf("模板ID必须大于0，当前值: %d", r.ID))
	}

	if r.Name == "" {
		return user_errors.NewParameterValidationFailedError("name", "模板名称不能为空")
	}

	if r.Content == "" {
		return user_errors.NewParameterValidationFailedError("content", "模板内容不能为空")
	}

	if len(r.Content) > 500 {
		return user_errors.NewParameterValidationFailedError("content", fmt.Sprintf("模板内容长度不能超过500个字符，当前长度: %d", len(r.Content)))
	}

	if len(r.Name) > 100 {
		return user_errors.NewParameterValidationFailedError("name", fmt.Sprintf("模板名称长度不能超过100个字符，当前长度: %d", len(r.Name)))
	}

	return nil
}

// TemplateResponse 短信模板响应
type TemplateResponse struct {
	ID          int64             `json:"id"`          // 模板ID
	TenantID    int64             `json:"tenant_id"`   // 租户ID
	Name        string            `json:"name"`        // 模板名称
	Code        string            `json:"code"`        // 模板代码
	Description string            `json:"description"` // 模板描述
	Content     string            `json:"content"`     // 模板内容
	Variables   map[string]string `json:"variables"`   // 变量说明
	IsActive    bool              `json:"is_active"`   // 是否激活
	IsSystem    bool              `json:"is_system"`   // 是否系统模板
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`  // 更新时间
}

// NewTemplateResponse 创建模板响应
func NewTemplateResponse(template *entity.SMSTemplate) *TemplateResponse {
	// 转换Variables从json.RawMessage到map[string]string
	var variables map[string]string
	if template.Variables != nil {
		json.Unmarshal(template.Variables, &variables)
	}
	if variables == nil {
		variables = make(map[string]string)
	}

	return &TemplateResponse{
		ID:          template.ID,
		TenantID:    template.TenantID,
		Name:        template.Name,
		Code:        template.Code,
		Description: template.Description,
		Content:     template.Content,
		Variables:   variables,
		IsActive:    template.IsActive,
		IsSystem:    template.IsSystem,
		CreatedAt:   template.CreatedAt,
		UpdatedAt:   template.UpdatedAt,
	}
}

// TemplateListRequest 模板列表请求
type TemplateListRequest struct {
	Keyword   string `json:"keyword,omitempty"`    // 关键词搜索
	IsActive  *bool  `json:"is_active,omitempty"`  // 激活状态过滤
	IsSystem  *bool  `json:"is_system,omitempty"`  // 系统模板过滤
	Page      int    `json:"page"`                 // 页码
	Size      int    `json:"size"`                 // 页大小
	OrderBy   string `json:"order_by,omitempty"`   // 排序字段
	OrderDesc bool   `json:"order_desc,omitempty"` // 是否降序
}

// Validate 验证请求参数
func (r *TemplateListRequest) Validate() error {
	if r.Page <= 0 {
		r.Page = 1
	}
	if r.Size <= 0 || r.Size > 100 {
		r.Size = 20
	}
	if r.OrderBy == "" {
		r.OrderBy = "created_at"
		r.OrderDesc = true
	}
	return nil
}

// TemplateListResponse 模板列表响应
type TemplateListResponse struct {
	Items      []*TemplateResponse `json:"items"`       // 模板列表
	Total      int64               `json:"total"`       // 总数
	Page       int                 `json:"page"`        // 当前页
	Size       int                 `json:"size"`        // 页大小
	TotalPages int                 `json:"total_pages"` // 总页数
}

// NewTemplateListResponse 创建模板列表响应
func NewTemplateListResponse(templates []*entity.SMSTemplate, total int64, page, size int) *TemplateListResponse {
	items := make([]*TemplateResponse, len(templates))
	for i, template := range templates {
		items[i] = NewTemplateResponse(template)
	}

	totalPages := int((total + int64(size) - 1) / int64(size))

	return &TemplateListResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}
}

// BatchTemplateRequest 批量模板操作请求
type BatchTemplateRequest struct {
	IDs       []int64 `json:"ids" binding:"required"`       // 模板ID列表
	Operation string  `json:"operation" binding:"required"` // 操作类型：enable, disable, delete
}

// Validate 验证请求参数
func (r *BatchTemplateRequest) Validate() error {
	if len(r.IDs) == 0 {
		return user_errors.NewParameterValidationFailedError("ids", "模板ID列表不能为空")
	}

	if len(r.IDs) > 50 {
		return user_errors.NewParameterValidationFailedError("ids", fmt.Sprintf("模板ID数量不能超过50个，当前数量: %d", len(r.IDs)))
	}

	validOperations := map[string]bool{
		"enable":  true,
		"disable": true,
		"delete":  true,
	}

	if !validOperations[r.Operation] {
		return user_errors.NewParameterValidationFailedError("operation", fmt.Sprintf("无效的操作类型: %s", r.Operation))
	}

	return nil
}

// BatchTemplateResponse 批量模板操作响应
type BatchTemplateResponse struct {
	SuccessCount int      `json:"success_count"` // 成功数量
	FailureCount int      `json:"failure_count"` // 失败数量
	Errors       []string `json:"errors"`        // 错误信息列表
}

// CloneTemplateRequest 克隆模板请求
type CloneTemplateRequest struct {
	SourceID       int64  `json:"source_id" binding:"required"`        // 源模板ID
	TargetTenantID int64  `json:"target_tenant_id" binding:"required"` // 目标租户ID
	NewCode        string `json:"new_code" binding:"required,max=100"` // 新模板代码
	NewName        string `json:"new_name,omitempty"`                  // 新模板名称
	NewDescription string `json:"new_description,omitempty"`           // 新模板描述
}

// Validate 验证请求参数
func (r *CloneTemplateRequest) Validate() error {
	if r.SourceID <= 0 {
		return user_errors.NewParameterValidationFailedError("source_id", fmt.Sprintf("源模板ID必须大于0，当前值: %d", r.SourceID))
	}

	if r.TargetTenantID <= 0 {
		return user_errors.NewParameterValidationFailedError("target_tenant_id", fmt.Sprintf("目标租户ID必须大于0，当前值: %d", r.TargetTenantID))
	}

	if r.NewCode == "" {
		return user_errors.NewParameterValidationFailedError("new_code", "新模板代码不能为空")
	}

	if len(r.NewCode) > 100 {
		return user_errors.NewParameterValidationFailedError("new_code", fmt.Sprintf("新模板代码长度不能超过100个字符，当前长度: %d", len(r.NewCode)))
	}

	return nil
}

// ValidateTemplateRequest 验证模板请求
type ValidateTemplateRequest struct {
	Content   string            `json:"content" binding:"required"` // 模板内容
	Variables map[string]string `json:"variables,omitempty"`        // 变量说明
}

// Validate 验证请求参数
func (r *ValidateTemplateRequest) Validate() error {
	if r.Content == "" {
		return user_errors.NewParameterValidationFailedError("content", "模板内容不能为空")
	}

	if len(r.Content) > 500 {
		return user_errors.NewParameterValidationFailedError("content", fmt.Sprintf("模板内容长度不能超过500个字符，当前长度: %d", len(r.Content)))
	}

	return nil
}

// ValidateTemplateResponse 验证模板响应
type ValidateTemplateResponse struct {
	Valid          bool     `json:"valid"`           // 是否有效
	ErrorMessages  []string `json:"error_messages"`  // 错误信息
	Warnings       []string `json:"warnings"`        // 警告信息
	SyntaxValid    bool     `json:"syntax_valid"`    // 语法是否有效
	VariablesValid bool     `json:"variables_valid"` // 变量是否有效
	LengthValid    bool     `json:"length_valid"`    // 长度是否有效
	ContentPreview string   `json:"content_preview"` // 内容预览
	DetectedVars   []string `json:"detected_vars"`   // 检测到的变量
}

// TestTemplateRequest 测试模板请求
type TestTemplateRequest struct {
	TemplateID int64                  `json:"template_id" binding:"required"` // 模板ID
	TestData   map[string]interface{} `json:"test_data"`                      // 测试数据
}

// Validate 验证请求参数
func (r *TestTemplateRequest) Validate() error {
	if r.TemplateID <= 0 {
		return user_errors.NewParameterValidationFailedError("template_id", fmt.Sprintf("模板ID必须大于0，当前值: %d", r.TemplateID))
	}

	return nil
}

// TestTemplateResponse 测试模板响应
type TestTemplateResponse struct {
	Success         bool                   `json:"success"`          // 是否成功
	RenderedContent string                 `json:"rendered_content"` // 渲染后内容
	ContentLength   int                    `json:"content_length"`   // 内容长度
	ErrorMessage    string                 `json:"error_message"`    // 错误信息
	TestData        map[string]interface{} `json:"test_data"`        // 测试数据
	RenderTime      int64                  `json:"render_time_ms"`   // 渲染时间（毫秒）
}

// TemplateStatisticsResponse 模板统计响应
type TemplateStatisticsResponse struct {
	TotalTemplates    int64            `json:"total_templates"`    // 总模板数
	ActiveTemplates   int64            `json:"active_templates"`   // 激活模板数
	InactiveTemplates int64            `json:"inactive_templates"` // 未激活模板数
	SystemTemplates   int64            `json:"system_templates"`   // 系统模板数
	CustomTemplates   int64            `json:"custom_templates"`   // 自定义模板数
	UsageCount        map[string]int64 `json:"usage_count"`        // 使用次数统计
	AverageLength     float64          `json:"average_length"`     // 平均长度
	LongestTemplate   int              `json:"longest_template"`   // 最长模板长度
	ShortestTemplate  int              `json:"shortest_template"`  // 最短模板长度
}

// TemplateUsageStatsResponse 模板使用统计响应
type TemplateUsageStatsResponse struct {
	TemplateID   int64      `json:"template_id"`   // 模板ID
	TemplateCode string     `json:"template_code"` // 模板代码
	TemplateName string     `json:"template_name"` // 模板名称
	UsageCount   int64      `json:"usage_count"`   // 使用次数
	LastUsed     *time.Time `json:"last_used"`     // 最后使用时间
}

// TemplateContentAnalysisResponse 模板内容分析响应
type TemplateContentAnalysisResponse struct {
	TemplateID      int64    `json:"template_id"`      // 模板ID
	ContentLength   int      `json:"content_length"`   // 内容长度
	VariableCount   int      `json:"variable_count"`   // 变量数量
	Variables       []string `json:"variables"`        // 变量列表
	HasPlaceholders bool     `json:"has_placeholders"` // 是否有占位符
	ComplexityScore int      `json:"complexity_score"` // 复杂度评分（1-10）
	Recommendations []string `json:"recommendations"`  // 优化建议
}

// GetComplexityLevel 获取复杂度等级
func (r *TemplateContentAnalysisResponse) GetComplexityLevel() string {
	switch {
	case r.ComplexityScore <= 3:
		return "simple"
	case r.ComplexityScore <= 6:
		return "medium"
	default:
		return "complex"
	}
}
