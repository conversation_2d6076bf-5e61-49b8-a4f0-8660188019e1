package dto

import (
	"fmt"
	user_errors "platforms-user/internal/domain/errors"
	"regexp"
	"time"

	"platforms-user/internal/domain/verification/entity"
)

// SendVerificationRequest 发送验证请求
type SendVerificationRequest struct {
	Target        string            `json:"target" binding:"required"`      // 目标地址（邮箱/手机号）
	TargetType    entity.TargetType `json:"target_type" binding:"required"` // 目标类型
	Purpose       entity.Purpose    `json:"purpose" binding:"required"`     // 验证用途
	TokenType     *entity.TokenType `json:"token_type,omitempty"`           // 令牌类型（可选，使用配置默认值）
	UserID        *int64            `json:"user_id,omitempty"`              // 用户ID（可选）
	BusinessScene *string           `json:"business_scene,omitempty"`       // 业务场景（用于动态策略）
	IPAddress     string            `json:"ip_address,omitempty"`           // IP地址
	UserAgent     string            `json:"user_agent,omitempty"`           // 用户代理
	Variables     map[string]string `json:"variables,omitempty"`            // 模板变量
	URL           string            `json:"url,omitempty"`                  // 链接类型token的URL前缀
}

// Validate 验证请求参数
func (r *SendVerificationRequest) Validate() error {
	if r.Target == "" {
		return user_errors.NewParameterValidationFailedError("target", "目标地址不能为空")
	}

	if r.TargetType < entity.TargetTypeEmail || r.TargetType > entity.TargetTypeMFA {
		return user_errors.NewParameterValidationFailedError("target_type", fmt.Sprintf("无效的目标类型值: %d", r.TargetType))
	}

	if r.Purpose < entity.PurposeRegistration || r.Purpose > entity.PurposeMFAVerify {
		return user_errors.NewParameterValidationFailedError("purpose", fmt.Sprintf("无效的用途值: %d", r.Purpose))
	}

	// 验证目标地址格式
	switch r.TargetType {
	case entity.TargetTypeEmail:
		if !isValidEmail(r.Target) {
			return user_errors.NewParameterValidationFailedError("target", fmt.Sprintf("邮箱格式不正确: %s", r.Target))
		}
	case entity.TargetTypePhone:
		if !isValidPhone(r.Target) {
			return user_errors.NewParameterValidationFailedError("target", fmt.Sprintf("手机号格式不正确: %s", r.Target))
		}
	}

	return nil
}

// SendVerificationResponse 发送验证响应
type SendVerificationResponse struct {
	Success bool `json:"success"` // 是否发送成功
}

// VerifyTokenRequest 验证令牌请求
type VerifyTokenRequest struct {
	Token     string            `json:"token" binding:"required"`  // 验证令牌/验证码
	Target    string            `json:"target" binding:"required"` // 目标地址
	Variables map[string]string `json:"variables,omitempty"`       // 额外验证参数（如混合模式的验证码）
	IPAddress string            `json:"ip_address,omitempty"`      // IP地址
	UserAgent string            `json:"user_agent,omitempty"`      // 用户代理
}

// Validate 验证请求参数
func (r *VerifyTokenRequest) Validate() error {
	if r.Token == "" {
		return user_errors.NewParameterValidationFailedError("token", "验证令牌不能为空")
	}

	if r.Target == "" {
		return user_errors.NewParameterValidationFailedError("target", "目标地址不能为空")
	}

	if len(r.Token) < 4 || len(r.Token) > 64 {
		return user_errors.NewParameterValidationFailedError("token", "验证令牌长度必须在4-64位之间")
	}

	return nil
}

// VerifyTokenResponse 验证令牌响应
type VerifyTokenResponse struct {
	Verified   bool              `json:"verified"`          // 是否验证成功
	Purpose    entity.Purpose    `json:"purpose"`           // 验证用途
	Target     string            `json:"target"`            // 目标地址（脱敏）
	TargetType entity.TargetType `json:"target_type"`       // 目标类型
	VerifiedAt time.Time         `json:"verified_at"`       // 验证时间
	UserID     *int64            `json:"user_id,omitempty"` // 用户ID
}

// ResendVerificationRequest 重新发送验证请求
type ResendVerificationRequest struct {
	Target     string            `json:"target" binding:"required"`      // 目标地址
	TargetType entity.TargetType `json:"target_type" binding:"required"` // 目标类型
	Purpose    entity.Purpose    `json:"purpose" binding:"required"`     // 验证用途
	IPAddress  string            `json:"ip_address,omitempty"`           // IP地址
	UserAgent  string            `json:"user_agent,omitempty"`           // 用户代理
}

// Validate 验证请求参数
func (r *ResendVerificationRequest) Validate() error {
	if r.Target == "" {
		return user_errors.NewParameterValidationFailedError("target", "目标地址不能为空")
	}

	if r.TargetType < entity.TargetTypeEmail || r.TargetType > entity.TargetTypeMFA {
		return user_errors.NewParameterValidationFailedError("target_type", fmt.Sprintf("无效的目标类型值: %d", r.TargetType))
	}

	if r.Purpose < entity.PurposeRegistration || r.Purpose > entity.PurposeMFAVerify {
		return user_errors.NewParameterValidationFailedError("purpose", fmt.Sprintf("无效的用途值: %d", r.Purpose))
	}

	return nil
}

// CheckTokenStatusRequest 检查令牌状态请求
type CheckTokenStatusRequest struct {
	Token string `json:"token" binding:"required"` // 验证令牌
}

// Validate 验证请求参数
func (r *CheckTokenStatusRequest) Validate() error {
	if r.Token == "" {
		return user_errors.NewParameterValidationFailedError("token", "验证令牌不能为空")
	}

	return nil
}

// CheckTokenStatusResponse 检查令牌状态响应
type CheckTokenStatusResponse struct {
	Status            entity.TokenStatus `json:"status"`             // 令牌状态
	StatusName        string             `json:"status_name"`        // 状态名称
	ExpiresAt         time.Time          `json:"expires_at"`         // 过期时间
	RemainingAttempts int                `json:"remaining_attempts"` // 剩余尝试次数
	CreatedAt         time.Time          `json:"created_at"`         // 创建时间
}

// VerificationStatisticsRequest 验证统计请求
type VerificationStatisticsRequest struct {
	StartTime  *time.Time         `json:"start_time,omitempty"`  // 开始时间
	EndTime    *time.Time         `json:"end_time,omitempty"`    // 结束时间
	Purpose    *entity.Purpose    `json:"purpose,omitempty"`     // 验证用途
	TargetType *entity.TargetType `json:"target_type,omitempty"` // 目标类型
}

// Validate 验证请求参数
func (r *VerificationStatisticsRequest) Validate() error {
	if r.StartTime != nil && r.EndTime != nil {
		if r.StartTime.After(*r.EndTime) {
			return user_errors.NewParameterValidationFailedError("start_time", "开始时间不能晚于结束时间")
		}
	}

	// 限制查询时间范围不超过90天
	if r.StartTime != nil && r.EndTime != nil {
		if r.EndTime.Sub(*r.StartTime) > 90*24*time.Hour {
			return user_errors.NewParameterValidationFailedError("time_range", "查询时间范围不能超过90天")
		}
	}

	return nil
}

// VerificationStatisticsResponse 验证统计响应
type VerificationStatisticsResponse struct {
	TotalSent     int64                                  `json:"total_sent"`     // 总发送数
	TotalVerified int64                                  `json:"total_verified"` // 总验证数
	SuccessRate   float64                                `json:"success_rate"`   // 成功率
	FailedCount   int64                                  `json:"failed_count"`   // 失败数量
	ExpiredCount  int64                                  `json:"expired_count"`  // 过期数量
	ByPurpose     map[entity.Purpose]*PurposeStats       `json:"by_purpose"`     // 按用途统计
	ByTargetType  map[entity.TargetType]*TargetTypeStats `json:"by_target_type"` // 按目标类型统计
}

// PurposeStats 用途统计
type PurposeStats struct {
	Purpose       entity.Purpose `json:"purpose"`        // 用途
	PurposeName   string         `json:"purpose_name"`   // 用途名称
	SentCount     int64          `json:"sent_count"`     // 发送数量
	VerifiedCount int64          `json:"verified_count"` // 验证数量
	SuccessRate   float64        `json:"success_rate"`   // 成功率
}

// TargetTypeStats 目标类型统计
type TargetTypeStats struct {
	TargetType     entity.TargetType `json:"target_type"`      // 目标类型
	TargetTypeName string            `json:"target_type_name"` // 目标类型名称
	SentCount      int64             `json:"sent_count"`       // 发送数量
	VerifiedCount  int64             `json:"verified_count"`   // 验证数量
	SuccessRate    float64           `json:"success_rate"`     // 成功率
}

// EmailVerificationRequest 邮件验证请求参数对象
// 当参数超过3个时使用对象传递，提高代码可读性和维护性
type EmailVerificationRequest struct {
	// 基础验证信息
	Target     string            `json:"target" binding:"required"`      // 目标邮箱地址
	TargetType entity.TargetType `json:"target_type" binding:"required"` // 目标类型
	Purpose    entity.Purpose    `json:"purpose" binding:"required"`     // 验证用途

	// 可选参数
	UserID        *int64            `json:"user_id,omitempty"`        // 用户ID
	TokenType     *entity.TokenType `json:"token_type,omitempty"`     // 令牌类型
	BusinessScene *string           `json:"business_scene,omitempty"` // 业务场景
	URL           string            `json:"url,omitempty"`            // 验证链接URL前缀

	// 上下文信息
	IPAddress string `json:"ip_address,omitempty"` // IP地址
	UserAgent string `json:"user_agent,omitempty"` // 用户代理

	// 模板变量
	Variables map[string]string `json:"variables,omitempty"` // 自定义模板变量
}

// Validate 验证邮件验证请求参数
func (r *EmailVerificationRequest) Validate() error {
	if r.Target == "" {
		return user_errors.NewParameterValidationFailedError("target", "目标邮箱地址不能为空")
	}

	if r.TargetType != entity.TargetTypeEmail {
		return user_errors.NewParameterValidationFailedError("target_type", "邮件验证的目标类型必须是邮箱")
	}

	if r.Purpose < entity.PurposeRegistration || r.Purpose > entity.PurposeMFAVerify {
		return user_errors.NewParameterValidationFailedError("purpose", fmt.Sprintf("无效的用途值: %d", r.Purpose))
	}

	// 验证邮箱格式
	if !isValidEmail(r.Target) {
		return user_errors.NewParameterValidationFailedError("target", fmt.Sprintf("邮箱格式不正确: %s", r.Target))
	}

	return nil
}

// ToSendVerificationRequest 转换为通用的发送验证请求
func (r *EmailVerificationRequest) ToSendVerificationRequest() *SendVerificationRequest {
	return &SendVerificationRequest{
		Target:        r.Target,
		TargetType:    r.TargetType,
		Purpose:       r.Purpose,
		UserID:        r.UserID,
		TokenType:     r.TokenType,
		BusinessScene: r.BusinessScene,
		IPAddress:     r.IPAddress,
		UserAgent:     r.UserAgent,
		Variables:     r.Variables,
		URL:           r.URL,
	}
}

// 工具函数

// isValidEmail 验证邮箱格式
func isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// isValidPhone 验证手机号格式
func isValidPhone(phone string) bool {
	// 支持中国大陆手机号格式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}

// MaskTarget 脱敏目标地址
func MaskTarget(target string, targetType entity.TargetType) string {
	switch targetType {
	case entity.TargetTypeEmail:
		return maskEmail(target)
	case entity.TargetTypePhone:
		return maskPhone(target)
	default:
		return target
	}
}

// maskEmail 脱敏邮箱
func maskEmail(email string) string {
	if len(email) == 0 {
		return email
	}

	atIndex := -1
	for i, char := range email {
		if char == '@' {
			atIndex = i
			break
		}
	}

	if atIndex == -1 {
		return email
	}

	localPart := email[:atIndex]
	domainPart := email[atIndex:]

	if len(localPart) <= 2 {
		return email
	}

	maskedLocal := localPart[:1] + "***" + localPart[len(localPart)-1:]
	return maskedLocal + domainPart
}

// maskPhone 脱敏手机号
func maskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}

	return phone[:3] + "****" + phone[7:]
}

// MaskToken 脱敏令牌
func MaskToken(token string) string {
	if len(token) == 0 {
		return token
	}

	if len(token) <= 4 {
		return "****"
	}

	if len(token) <= 8 {
		return token[:2] + "****"
	}

	return token[:3] + "****" + token[len(token)-3:]
}

// GetPurposeName 获取用途名称
func GetPurposeName(purpose entity.Purpose) string {
	switch purpose {
	case entity.PurposeRegistration:
		return "注册激活"
	case entity.PurposePasswordReset:
		return "密码重置"
	case entity.PurposeEmailChange:
		return "邮箱变更"
	case entity.PurposePhoneChange:
		return "手机变更"
	case entity.PurposeLoginVerify:
		return "登录验证"
	case entity.PurposeMFAVerify:
		return "MFA验证"
	default:
		return "未知"
	}
}

// GetTargetTypeName 获取目标类型名称
func GetTargetTypeName(targetType entity.TargetType) string {
	switch targetType {
	case entity.TargetTypeEmail:
		return "邮箱"
	case entity.TargetTypePhone:
		return "手机号"
	case entity.TargetTypeMFA:
		return "MFA"
	default:
		return "未知"
	}
}

// GetTokenTypeName 获取令牌类型名称
func GetTokenTypeName(tokenType entity.TokenType) string {
	switch tokenType {
	case entity.TokenTypeLink:
		return "链接"
	case entity.TokenTypeCode:
		return "验证码"
	default:
		return "未知"
	}
}
