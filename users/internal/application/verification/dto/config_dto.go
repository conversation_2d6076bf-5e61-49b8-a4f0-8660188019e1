package dto

import (
	"fmt"
	user_errors "platforms-user/internal/domain/errors"
	"time"

	"platforms-user/internal/domain/verification/entity"
)

// CreateConfigRequest 创建验证配置请求
type CreateConfigRequest struct {
	Purpose            entity.Purpose    `json:"purpose" binding:"required"`                   // 验证用途
	TargetType         entity.TargetType `json:"target_type" binding:"required"`               // 目标类型
	TokenType          entity.TokenType  `json:"token_type" binding:"required"`                // 令牌类型
	TokenLength        int               `json:"token_length" binding:"min=4,max=32"`          // 令牌长度
	ExpireMinutes      int               `json:"expire_minutes" binding:"min=1,max=1440"`      // 过期分钟数
	MaxAttempts        int               `json:"max_attempts" binding:"min=1,max=10"`          // 最大尝试次数
	RateLimitPerMinute int               `json:"rate_limit_per_minute" binding:"min=0,max=60"` // 每分钟限制
	RateLimitPerHour   int               `json:"rate_limit_per_hour" binding:"min=0,max=3600"` // 每小时限制
	RateLimitPerDay    int               `json:"rate_limit_per_day" binding:"min=0,max=86400"` // 每日限制
	TemplateCode       string            `json:"template_code" binding:"required,max=100"`     // 模板代码
}

// Validate 验证请求参数
func (r *CreateConfigRequest) Validate() error {
	if r.Purpose < entity.PurposeRegistration || r.Purpose > entity.PurposeMFAVerify {
		return user_errors.NewParameterValidationFailedError("purpose", fmt.Sprintf("无效的用途值: %d", r.Purpose))
	}

	if r.TargetType < entity.TargetTypeEmail || r.TargetType > entity.TargetTypeMFA {
		return user_errors.NewParameterValidationFailedError("target_type", fmt.Sprintf("无效的目标类型值: %d", r.TargetType))
	}

	if r.TokenType < entity.TokenTypeLink || r.TokenType > entity.TokenTypeCode {
		return user_errors.NewParameterValidationFailedError("token_type", fmt.Sprintf("无效的令牌类型值: %d", r.TokenType))
	}

	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}

	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}

	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}

	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}

	return nil
}

// UpdateConfigRequest 更新验证配置请求
type UpdateConfigRequest struct {
	ID                 int64            `json:"id" binding:"required"`                        // 配置ID
	TokenType          entity.TokenType `json:"token_type" binding:"required"`                // 令牌类型
	TokenLength        int              `json:"token_length" binding:"min=4,max=32"`          // 令牌长度
	ExpireMinutes      int              `json:"expire_minutes" binding:"min=1,max=1440"`      // 过期分钟数
	MaxAttempts        int              `json:"max_attempts" binding:"min=1,max=10"`          // 最大尝试次数
	RateLimitPerMinute int              `json:"rate_limit_per_minute" binding:"min=0,max=60"` // 每分钟限制
	RateLimitPerHour   int              `json:"rate_limit_per_hour" binding:"min=0,max=3600"` // 每小时限制
	RateLimitPerDay    int              `json:"rate_limit_per_day" binding:"min=0,max=86400"` // 每日限制
	TemplateCode       string           `json:"template_code" binding:"required,max=100"`     // 模板代码
	IsActive           bool             `json:"is_active"`                                    // 是否激活
}

// Validate 验证请求参数
func (r *UpdateConfigRequest) Validate() error {
	if r.ID <= 0 {
		return user_errors.NewParameterValidationFailedError("id", fmt.Sprintf("配置ID必须大于0，当前值: %d", r.ID))
	}

	if r.TokenType < entity.TokenTypeLink || r.TokenType > entity.TokenTypeCode {
		return user_errors.NewParameterValidationFailedError("token_type", fmt.Sprintf("无效的令牌类型值: %d", r.TokenType))
	}

	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}

	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}

	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}

	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}

	return nil
}

// ConfigResponse 验证配置响应
type ConfigResponse struct {
	ID         int64             `json:"id"`          // 配置ID
	TenantID   int64             `json:"tenant_id"`   // 租户ID
	ConfigMode entity.ConfigMode `json:"config_mode"` // 配置模式

	// 静态配置字段
	Purpose     *entity.Purpose `json:"purpose,omitempty"`      // 验证用途
	PurposeName string          `json:"purpose_name,omitempty"` // 用途名称

	// 动态配置字段
	BusinessScene       *string `json:"business_scene,omitempty"`       // 业务场景
	JudgmentDimension   *string `json:"judgment_dimension,omitempty"`   // 判定维度
	ConditionExpr       *string `json:"condition_expr,omitempty"`       // 条件表达式
	VerificationLevel   int     `json:"verification_level,omitempty"`   // 验证级别
	RequireVerification bool    `json:"require_verification,omitempty"` // 是否需要验证

	// 通用配置字段
	TargetType         entity.TargetType `json:"target_type"`           // 目标类型
	TargetTypeName     string            `json:"target_type_name"`      // 目标类型名称
	TokenType          entity.TokenType  `json:"token_type"`            // 令牌类型
	TokenTypeName      string            `json:"token_type_name"`       // 令牌类型名称
	TokenLength        int               `json:"token_length"`          // 令牌长度
	ExpireMinutes      int               `json:"expire_minutes"`        // 过期分钟数
	MaxAttempts        int               `json:"max_attempts"`          // 最大尝试次数
	RateLimitPerMinute int               `json:"rate_limit_per_minute"` // 每分钟限制
	RateLimitPerHour   int               `json:"rate_limit_per_hour"`   // 每小时限制
	RateLimitPerDay    int               `json:"rate_limit_per_day"`    // 每日限制
	TemplateCode       string            `json:"template_code"`         // 模板代码
	Priority           int               `json:"priority"`              // 优先级
	Description        string            `json:"description,omitempty"` // 描述
	IsActive           bool              `json:"is_active"`             // 是否激活
	CreatedAt          time.Time         `json:"created_at"`            // 创建时间
	UpdatedAt          time.Time         `json:"updated_at"`            // 更新时间
}

// NewConfigResponse 创建配置响应
func NewConfigResponse(config *entity.VerificationConfig) *ConfigResponse {
	resp := &ConfigResponse{
		ID:                 config.ID,
		TenantID:           config.TenantID,
		ConfigMode:         config.ConfigMode,
		TargetType:         config.TargetType,
		TargetTypeName:     config.GetTargetTypeName(),
		TokenType:          config.TokenType,
		TokenTypeName:      config.GetTokenTypeName(),
		TokenLength:        config.TokenLength,
		ExpireMinutes:      config.ExpireMinutes,
		MaxAttempts:        config.MaxAttempts,
		RateLimitPerMinute: config.RateLimitPerMinute,
		RateLimitPerHour:   config.RateLimitPerHour,
		RateLimitPerDay:    config.RateLimitPerDay,
		TemplateCode:       config.TemplateCode,
		Priority:           config.Priority,
		IsActive:           config.IsActive,
		CreatedAt:          config.CreatedAt,
		UpdatedAt:          config.UpdatedAt,
	}

	// 处理Description字段（指针类型）
	if config.Description != nil {
		resp.Description = *config.Description
	}

	// 处理静态配置字段（指针类型）
	if config.Purpose != nil {
		resp.Purpose = config.Purpose
		resp.PurposeName = config.GetPurposeName()
	}

	// 处理动态配置字段（指针类型）
	if config.BusinessScene != nil {
		resp.BusinessScene = config.BusinessScene
	}
	if config.JudgmentDimension != nil {
		resp.JudgmentDimension = config.JudgmentDimension
	}
	if config.ConditionExpr != nil {
		resp.ConditionExpr = config.ConditionExpr
	}
	resp.VerificationLevel = config.VerificationLevel
	resp.RequireVerification = config.RequireVerification

	return resp
}

// ConfigListRequestBody 配置列表请求体（用于前端POST请求）
type ConfigListRequestBody struct {
	ConfigMode *string            `json:"config_mode,omitempty"` // 配置模式过滤 static/dynamic
	Purpose    *entity.Purpose    `json:"purpose,omitempty"`     // 验证用途过滤
	TargetType *entity.TargetType `json:"target_type,omitempty"` // 目标类型过滤
	TokenType  *entity.TokenType  `json:"token_type,omitempty"`  // 令牌类型过滤
	IsActive   *bool              `json:"is_active,omitempty"`   // 激活状态过滤
	Keyword    string             `json:"keyword,omitempty"`     // 搜索关键词
	Page       int                `json:"page"`                  // 页码，从1开始
	Size       int                `json:"size"`                  // 每页大小
	OrderBy    string             `json:"order_by,omitempty"`    // 排序字段
	OrderDesc  bool               `json:"order_desc,omitempty"`  // 是否降序
}

// NewConfigListRequestBody 创建新的配置列表请求体，设置默认值
func NewConfigListRequestBody() *ConfigListRequestBody {
	return &ConfigListRequestBody{
		Page:      1,
		Size:      20,
		OrderBy:   "created_at",
		OrderDesc: true,
	}
}

// ToConfigListRequest 转换为内部使用的请求格式
func (body *ConfigListRequestBody) ToConfigListRequest(tenantID int64) *ConfigListRequest {
	// 使用默认值填充零值字段
	if body.Page <= 0 {
		body.Page = 1
	}
	if body.Size <= 0 {
		body.Size = 20
	}
	if body.OrderBy == "" {
		body.OrderBy = "created_at"
		body.OrderDesc = true
	}

	req := &ConfigListRequest{
		TenantID:   tenantID,
		ConfigMode: body.ConfigMode,
		Purpose:    body.Purpose,
		TargetType: body.TargetType,
		TokenType:  body.TokenType,
		IsActive:   body.IsActive,
		Keyword:    body.Keyword,
		Page:       body.Page,
		Size:       body.Size,
		OrderBy:    body.OrderBy,
		OrderDesc:  body.OrderDesc,
	}

	return req
}

// ConfigListRequest 配置列表请求（内部使用）
type ConfigListRequest struct {
	TenantID   int64              `json:"tenant_id"`             // 租户ID
	ConfigMode *string            `json:"config_mode,omitempty"` // 配置模式过滤
	Purpose    *entity.Purpose    `json:"purpose,omitempty"`     // 验证用途过滤
	TargetType *entity.TargetType `json:"target_type,omitempty"` // 目标类型过滤
	TokenType  *entity.TokenType  `json:"token_type,omitempty"`  // 令牌类型过滤
	IsActive   *bool              `json:"is_active,omitempty"`   // 激活状态过滤
	Keyword    string             `json:"keyword,omitempty"`     // 搜索关键词
	Page       int                `json:"page"`                  // 页码
	Size       int                `json:"size"`                  // 页大小
	OrderBy    string             `json:"order_by,omitempty"`    // 排序字段
	OrderDesc  bool               `json:"order_desc,omitempty"`  // 是否降序
}

// ConfigListResponse 配置列表响应
type ConfigListResponse struct {
	Configs    []*ConfigResponse `json:"configs"`     // 配置列表
	Page       int               `json:"page"`        // 当前页
	PageSize   int               `json:"page_size"`   // 页大小
	Total      int64             `json:"total"`       // 总数
	TotalPages int               `json:"total_pages"` // 总页数
}

// BatchConfigRequest 批量配置操作请求
type BatchConfigRequest struct {
	IDs       []int64 `json:"ids" binding:"required"`       // 配置ID列表
	Operation string  `json:"operation" binding:"required"` // 操作类型：enable, disable, delete
}

// Validate 验证请求参数
func (r *BatchConfigRequest) Validate() error {
	if len(r.IDs) == 0 {
		return user_errors.NewParameterValidationFailedError("ids", "配置ID列表不能为空")
	}

	if len(r.IDs) > 100 {
		return user_errors.NewParameterValidationFailedError("ids", fmt.Sprintf("配置ID数量不能超过100个，当前数量: %d", len(r.IDs)))
	}

	validOperations := map[string]bool{
		"enable":  true,
		"disable": true,
		"delete":  true,
	}

	if !validOperations[r.Operation] {
		return user_errors.NewParameterValidationFailedError("operation", fmt.Sprintf("无效的操作类型: %s", r.Operation))
	}

	return nil
}

// BatchConfigResponse 批量配置操作响应
type BatchConfigResponse struct {
	SuccessCount int      `json:"success_count"` // 成功数量
	FailureCount int      `json:"failure_count"` // 失败数量
	Errors       []string `json:"errors"`        // 错误信息列表
}

// CopyConfigRequest 复制配置请求
type CopyConfigRequest struct {
	SourceTenantID int64 `json:"source_tenant_id" binding:"required"` // 源租户ID
	TargetTenantID int64 `json:"target_tenant_id" binding:"required"` // 目标租户ID
	Overwrite      bool  `json:"overwrite"`                           // 是否覆盖已存在的配置
}

// Validate 验证请求参数
func (r *CopyConfigRequest) Validate() error {
	if r.SourceTenantID <= 0 {
		return user_errors.NewParameterValidationFailedError("source_tenant_id", fmt.Sprintf("源租户ID必须大于0，当前值: %d", r.SourceTenantID))
	}

	if r.TargetTenantID <= 0 {
		return user_errors.NewParameterValidationFailedError("target_tenant_id", fmt.Sprintf("目标租户ID必须大于0，当前值: %d", r.TargetTenantID))
	}

	if r.SourceTenantID == r.TargetTenantID {
		return user_errors.NewParameterValidationFailedError("source_tenant_id", fmt.Sprintf("源租户ID不能与目标租户ID相同: %d", r.SourceTenantID))
	}

	return nil
}

// ConfigStatisticsResponse 配置统计响应
type ConfigStatisticsResponse struct {
	TotalConfigs    int64                                        `json:"total_configs"`    // 总配置数
	ActiveConfigs   int64                                        `json:"active_configs"`   // 激活配置数
	InactiveConfigs int64                                        `json:"inactive_configs"` // 未激活配置数
	ByPurpose       map[entity.Purpose]*PurposeConfigStats       `json:"by_purpose"`       // 按用途统计
	ByTargetType    map[entity.TargetType]*TargetTypeConfigStats `json:"by_target_type"`   // 按目标类型统计
	ByTokenType     map[entity.TokenType]*TokenTypeConfigStats   `json:"by_token_type"`    // 按令牌类型统计
}

// PurposeConfigStats 用途配置统计
type PurposeConfigStats struct {
	Purpose     entity.Purpose `json:"purpose"`      // 用途
	PurposeName string         `json:"purpose_name"` // 用途名称
	Count       int64          `json:"count"`        // 配置数量
	ActiveCount int64          `json:"active_count"` // 激活配置数量
}

// TargetTypeConfigStats 目标类型配置统计
type TargetTypeConfigStats struct {
	TargetType     entity.TargetType `json:"target_type"`      // 目标类型
	TargetTypeName string            `json:"target_type_name"` // 目标类型名称
	Count          int64             `json:"count"`            // 配置数量
	ActiveCount    int64             `json:"active_count"`     // 激活配置数量
}

// TokenTypeConfigStats 令牌类型配置统计
type TokenTypeConfigStats struct {
	TokenType     entity.TokenType `json:"token_type"`      // 令牌类型
	TokenTypeName string           `json:"token_type_name"` // 令牌类型名称
	Count         int64            `json:"count"`           // 配置数量
	ActiveCount   int64            `json:"active_count"`    // 激活配置数量
}

// GetEffectiveConfigRequest 获取有效配置请求
type GetEffectiveConfigRequest struct {
	Purpose       entity.Purpose    `json:"purpose" binding:"required"`     // 验证用途
	TargetType    entity.TargetType `json:"target_type" binding:"required"` // 目标类型
	BusinessScene *string           `json:"business_scene,omitempty"`       // 业务场景（可选）
}

// DeleteConfigRequest 删除配置请求
type DeleteConfigRequest struct {
	ID int64 `json:"id" binding:"required"` // 配置ID
}

// Validate 验证删除配置请求参数
func (r *DeleteConfigRequest) Validate() error {
	if r.ID <= 0 {
		return user_errors.NewParameterValidationFailedError("id", fmt.Sprintf("配置ID必须大于0，当前值: %d", r.ID))
	}
	return nil
}

// CreateStaticConfigRequest 创建静态配置请求
type CreateStaticConfigRequest struct {
	Purpose            entity.Purpose    `json:"purpose" binding:"required"`                       // 验证用途
	TargetType         entity.TargetType `json:"target_type" binding:"required"`                   // 目标类型
	TokenType          entity.TokenType  `json:"token_type" binding:"required"`                    // 令牌类型
	TokenLength        int               `json:"token_length" binding:"required,min=4,max=32"`     // 令牌长度
	ExpireMinutes      int               `json:"expire_minutes" binding:"required,min=1,max=1440"` // 过期分钟数
	MaxAttempts        int               `json:"max_attempts" binding:"required,min=1,max=10"`     // 最大尝试次数
	RateLimitPerMinute int               `json:"rate_limit_per_minute" binding:"required,min=0"`   // 每分钟限制
	RateLimitPerHour   int               `json:"rate_limit_per_hour" binding:"required,min=0"`     // 每小时限制
	RateLimitPerDay    int               `json:"rate_limit_per_day" binding:"required,min=0"`      // 每日限制
	TemplateCode       string            `json:"template_code" binding:"required"`                 // 模板代码
	Priority           int               `json:"priority"`                                         // 优先级
	Description        *string           `json:"description,omitempty"`                            // 描述
}

// Validate 验证创建静态配置请求参数
func (r *CreateStaticConfigRequest) Validate() error {
	// 验证必填字段
	if r.Purpose == 0 {
		return user_errors.NewParameterValidationFailedError("purpose", "用途不能为零")
	}
	if r.TargetType == 0 {
		return user_errors.NewParameterValidationFailedError("target_type", "目标类型不能为零")
	}
	if r.TokenType == 0 {
		return user_errors.NewParameterValidationFailedError("token_type", "令牌类型不能为零")
	}
	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}
	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}
	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}
	if r.RateLimitPerMinute < 0 {
		return user_errors.NewParameterValidationFailedError("rate_limit_per_minute", fmt.Sprintf("每分钟限制不能为负数，当前值: %d", r.RateLimitPerMinute))
	}
	if r.RateLimitPerHour < 0 {
		return user_errors.NewParameterValidationFailedError("rate_limit_per_hour", fmt.Sprintf("每小时限制不能为负数，当前值: %d", r.RateLimitPerHour))
	}
	if r.RateLimitPerDay < 0 {
		return user_errors.NewParameterValidationFailedError("rate_limit_per_day", fmt.Sprintf("每日限制不能为负数，当前值: %d", r.RateLimitPerDay))
	}
	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}
	return nil
}

// CreateDynamicConfigRequest 创建动态配置请求
type CreateDynamicConfigRequest struct {
	BusinessScene       string            `json:"business_scene" binding:"required"`                // 业务场景
	TargetType          entity.TargetType `json:"target_type" binding:"required"`                   // 目标类型
	JudgmentDimension   string            `json:"judgment_dimension" binding:"required"`            // 判定维度
	ConditionExpr       string            `json:"condition_expr" binding:"required"`                // 条件表达式
	TokenType           entity.TokenType  `json:"token_type" binding:"required"`                    // 令牌类型
	TokenLength         int               `json:"token_length" binding:"required,min=4,max=32"`     // 令牌长度
	ExpireMinutes       int               `json:"expire_minutes" binding:"required,min=1,max=1440"` // 过期分钟数
	MaxAttempts         int               `json:"max_attempts" binding:"required,min=1,max=10"`     // 最大尝试次数
	RateLimitPerMinute  int               `json:"rate_limit_per_minute" binding:"required,min=0"`   // 每分钟限制
	RateLimitPerHour    int               `json:"rate_limit_per_hour" binding:"required,min=0"`     // 每小时限制
	RateLimitPerDay     int               `json:"rate_limit_per_day" binding:"required,min=0"`      // 每日限制
	TemplateCode        string            `json:"template_code" binding:"required"`                 // 模板代码
	Priority            int               `json:"priority"`                                         // 优先级
	VerificationLevel   int               `json:"verification_level" binding:"min=1,max=5"`         // 验证级别
	RequireVerification bool              `json:"require_verification"`                             // 是否需要验证
	Description         *string           `json:"description,omitempty"`                            // 描述
}

// Validate 验证创建动态配置请求参数
func (r *CreateDynamicConfigRequest) Validate() error {
	if r.BusinessScene == "" {
		return user_errors.NewParameterValidationFailedError("business_scene", "业务场景不能为空")
	}
	if r.JudgmentDimension == "" {
		return user_errors.NewParameterValidationFailedError("judgment_dimension", "判定维度不能为空")
	}
	if r.ConditionExpr == "" {
		return user_errors.NewParameterValidationFailedError("condition_expr", "条件表达式不能为空")
	}
	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}
	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}
	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}
	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}
	if r.VerificationLevel < 1 || r.VerificationLevel > 5 {
		return user_errors.NewParameterValidationFailedError("verification_level", fmt.Sprintf("验证级别必须在1-5之间，当前值: %d", r.VerificationLevel))
	}
	return nil
}

// UpdateStaticConfigRequest 更新静态配置请求
type UpdateStaticConfigRequest struct {
	ID                 int64            `json:"id" binding:"required"`                            // 配置ID
	TokenType          entity.TokenType `json:"token_type" binding:"required"`                    // 令牌类型
	TokenLength        int              `json:"token_length" binding:"required,min=4,max=32"`     // 令牌长度
	ExpireMinutes      int              `json:"expire_minutes" binding:"required,min=1,max=1440"` // 过期分钟数
	MaxAttempts        int              `json:"max_attempts" binding:"required,min=1,max=10"`     // 最大尝试次数
	RateLimitPerMinute int              `json:"rate_limit_per_minute" binding:"required,min=0"`   // 每分钟限制
	RateLimitPerHour   int              `json:"rate_limit_per_hour" binding:"required,min=0"`     // 每小时限制
	RateLimitPerDay    int              `json:"rate_limit_per_day" binding:"required,min=0"`      // 每日限制
	TemplateCode       string           `json:"template_code" binding:"required"`                 // 模板代码
	Priority           int              `json:"priority"`                                         // 优先级
	Description        *string          `json:"description,omitempty"`                            // 描述
	IsActive           bool             `json:"is_active"`                                        // 是否激活
}

// Validate 验证更新静态配置请求参数
func (r *UpdateStaticConfigRequest) Validate() error {
	if r.ID <= 0 {
		return user_errors.NewParameterValidationFailedError("id", fmt.Sprintf("配置ID必须大于0，当前值: %d", r.ID))
	}
	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}
	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}
	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}
	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}
	return nil
}

// UpdateDynamicConfigRequest 更新动态配置请求
type UpdateDynamicConfigRequest struct {
	ID                  int64            `json:"id" binding:"required"`                            // 配置ID
	JudgmentDimension   string           `json:"judgment_dimension" binding:"required"`            // 判定维度
	ConditionExpr       string           `json:"condition_expr" binding:"required"`                // 条件表达式
	TokenType           entity.TokenType `json:"token_type" binding:"required"`                    // 令牌类型
	TokenLength         int              `json:"token_length" binding:"required,min=4,max=32"`     // 令牌长度
	ExpireMinutes       int              `json:"expire_minutes" binding:"required,min=1,max=1440"` // 过期分钟数
	MaxAttempts         int              `json:"max_attempts" binding:"required,min=1,max=10"`     // 最大尝试次数
	RateLimitPerMinute  int              `json:"rate_limit_per_minute" binding:"required,min=0"`   // 每分钟限制
	RateLimitPerHour    int              `json:"rate_limit_per_hour" binding:"required,min=0"`     // 每小时限制
	RateLimitPerDay     int              `json:"rate_limit_per_day" binding:"required,min=0"`      // 每日限制
	TemplateCode        string           `json:"template_code" binding:"required"`                 // 模板代码
	Priority            int              `json:"priority"`                                         // 优先级
	VerificationLevel   int              `json:"verification_level" binding:"min=1,max=5"`         // 验证级别
	RequireVerification bool             `json:"require_verification"`                             // 是否需要验证
	Description         *string          `json:"description,omitempty"`                            // 描述
	IsActive            bool             `json:"is_active"`                                        // 是否激活
}

// Validate 验证更新动态配置请求参数
func (r *UpdateDynamicConfigRequest) Validate() error {
	if r.ID <= 0 {
		return user_errors.NewParameterValidationFailedError("id", fmt.Sprintf("配置ID必须大于0，当前值: %d", r.ID))
	}
	if r.JudgmentDimension == "" {
		return user_errors.NewParameterValidationFailedError("judgment_dimension", "判定维度不能为空")
	}
	if r.ConditionExpr == "" {
		return user_errors.NewParameterValidationFailedError("condition_expr", "条件表达式不能为空")
	}
	if r.TokenLength < 4 || r.TokenLength > 32 {
		return user_errors.NewParameterValidationFailedError("token_length", fmt.Sprintf("令牌长度必须在4-32位之间，当前值: %d", r.TokenLength))
	}
	if r.ExpireMinutes < 1 || r.ExpireMinutes > 1440 {
		return user_errors.NewParameterValidationFailedError("expire_minutes", fmt.Sprintf("过期时间必须在1-1440分钟之间，当前值: %d", r.ExpireMinutes))
	}
	if r.MaxAttempts < 1 || r.MaxAttempts > 10 {
		return user_errors.NewParameterValidationFailedError("max_attempts", fmt.Sprintf("最大尝试次数必须在1-10次之间，当前值: %d", r.MaxAttempts))
	}
	if r.TemplateCode == "" {
		return user_errors.NewParameterValidationFailedError("template_code", "模板代码不能为空")
	}
	if r.VerificationLevel < 1 || r.VerificationLevel > 5 {
		return user_errors.NewParameterValidationFailedError("verification_level", fmt.Sprintf("验证级别必须在1-5之间，当前值: %d", r.VerificationLevel))
	}
	return nil
}
