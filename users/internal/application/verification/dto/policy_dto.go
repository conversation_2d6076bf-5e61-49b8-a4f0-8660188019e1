package dto

import (
	"fmt"
	"platforms-user/internal/domain/verification/entity"
	"time"
)

// CreatePolicyRequest 创建策略请求
type CreatePolicyRequest struct {
	TenantID           int64  `json:"-"`
	Scene              string `json:"scene" validate:"required,max=50" example:"login"`
	Dimension          string `json:"dimension" validate:"required,max=50" example:"IP"`
	ConditionExpr      string `json:"condition_expr" validate:"required" example:"fail_count > 5"`
	NeedVerification   bool   `json:"need_verification" example:"true"`
	VerificationLevel  string `json:"verification_level" validate:"required,oneof=none low medium high" example:"medium"`
	TargetType         int    `json:"target_type" validate:"required,oneof=1 2 3" example:"1"`
	TokenType          int    `json:"token_type" validate:"required,oneof=1 2" example:"2"`
	TokenLength        int    `json:"token_length" validate:"required,min=4,max=10" example:"6"`
	ExpireMinutes      int    `json:"expire_minutes" validate:"required,min=1,max=1440" example:"10"`
	MaxAttempts        int    `json:"max_attempts" validate:"required,min=1,max=10" example:"3"`
	RateLimitPerMinute int    `json:"rate_limit_per_minute" validate:"required,min=1,max=60" example:"1"`
	RateLimitPerHour   int    `json:"rate_limit_per_hour" validate:"required,min=1,max=3600" example:"5"`
	RateLimitPerDay    int    `json:"rate_limit_per_day" validate:"required,min=1,max=86400" example:"20"`
	TemplateCode       string `json:"template_code" validate:"max=100" example:"login_verification"`
	Priority           int    `json:"priority" validate:"required,min=1,max=1000" example:"100"`
	Description        string `json:"description" validate:"max=500" example:"登录失败次数超过5次时触发验证"`
}

// UpdatePolicyRequest 更新策略请求
type UpdatePolicyRequest struct {
	PolicyID           int64  `json:"policy_id" validate:"required" example:"1"`
	TenantID           int64  `json:"-"`
	Scene              string `json:"scene" validate:"required,max=50" example:"login"`
	Dimension          string `json:"dimension" validate:"required,max=50" example:"IP"`
	ConditionExpr      string `json:"condition_expr" validate:"required" example:"fail_count > 5"`
	NeedVerification   bool   `json:"need_verification" example:"true"`
	VerificationLevel  string `json:"verification_level" validate:"required,oneof=none low medium high" example:"medium"`
	TargetType         int    `json:"target_type" validate:"required,oneof=1 2 3" example:"1"`
	TokenType          int    `json:"token_type" validate:"required,oneof=1 2" example:"2"`
	TokenLength        int    `json:"token_length" validate:"required,min=4,max=10" example:"6"`
	ExpireMinutes      int    `json:"expire_minutes" validate:"required,min=1,max=1440" example:"10"`
	MaxAttempts        int    `json:"max_attempts" validate:"required,min=1,max=10" example:"3"`
	RateLimitPerMinute int    `json:"rate_limit_per_minute" validate:"required,min=1,max=60" example:"1"`
	RateLimitPerHour   int    `json:"rate_limit_per_hour" validate:"required,min=1,max=3600" example:"5"`
	RateLimitPerDay    int    `json:"rate_limit_per_day" validate:"required,min=1,max=86400" example:"20"`
	TemplateCode       string `json:"template_code" validate:"max=100" example:"login_verification"`
	Priority           int    `json:"priority" validate:"required,min=1,max=1000" example:"100"`
	Description        string `json:"description" validate:"max=500" example:"登录失败次数超过5次时触发验证"`
}

// DeletePolicyRequest 删除策略请求
type DeletePolicyRequest struct {
	PolicyID int64 `json:"policy_id" validate:"required" example:"1"`
	TenantID int64 `json:"-"`
}

// SetPolicyStatusRequest 设置策略状态请求
type SetPolicyStatusRequest struct {
	PolicyID int64 `json:"policy_id" validate:"required" example:"1"`
	TenantID int64 `json:"-"`
	Enabled  bool  `json:"enabled" example:"true"`
}

// PolicyListRequest 策略列表请求
type PolicyListRequest struct {
	Page     int    `form:"page" validate:"min=1" example:"1"`
	PageSize int    `form:"pageSize" validate:"min=1,max=100" example:"20"`
	TenantID int64  `form:"tenant_id" example:"1"`
	Scene    string `form:"scene" example:"login"`
	IsActive *bool  `form:"is_active" example:"true"`
	Keyword  string `form:"keyword" example:"登录"`
}

// PolicyDetailRequest 策略详情请求
type PolicyDetailRequest struct {
	PolicyID int64 `form:"policy_id" validate:"required" example:"1"`
	TenantID int64 `form:"-"`
}

// ExprValidateRequest 表达式校验请求
type ExprValidateRequest struct {
	ConditionExpr string `json:"condition_expr" validate:"required" example:"fail_count > 5 AND hour >= 22"`
}

// ExprTestRequest 表达式测试请求
type ExprTestRequest struct {
	ConditionExpr string                 `json:"condition_expr" validate:"required" example:"fail_count > 5"`
	Sample        map[string]interface{} `json:"sample" validate:"required" example:"{\"fail_count\": 6}"`
}

// PolicyResponse 策略响应
type PolicyResponse struct {
	ID                 int64     `json:"id"`
	TenantID           int64     `json:"tenant_id"`
	Scene              string    `json:"scene"`
	Dimension          string    `json:"dimension"`
	ConditionExpr      string    `json:"condition_expr"`
	NeedVerification   bool      `json:"need_verification"`
	VerificationLevel  string    `json:"verification_level"`
	TargetType         int       `json:"target_type"`
	TokenType          int       `json:"token_type"`
	TokenLength        int       `json:"token_length"`
	ExpireMinutes      int       `json:"expire_minutes"`
	MaxAttempts        int       `json:"max_attempts"`
	RateLimitPerMinute int       `json:"rate_limit_per_minute"`
	RateLimitPerHour   int       `json:"rate_limit_per_hour"`
	RateLimitPerDay    int       `json:"rate_limit_per_day"`
	TemplateCode       string    `json:"template_code"`
	IsActive           bool      `json:"is_active"`
	Priority           int       `json:"priority"`
	Description        string    `json:"description"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// PolicyListResponse 策略列表响应
type PolicyListResponse struct {
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	PageSize int               `json:"page_size"`
	List     []*PolicyResponse `json:"list"`
}

// CreatePolicyResponse 创建策略响应
type CreatePolicyResponse struct {
	ID int64 `json:"id"`
}

// ExprValidateResponse 表达式校验响应
type ExprValidateResponse struct {
	Valid bool   `json:"valid"`
	Error string `json:"error,omitempty"`
}

// ExprTestResponse 表达式测试响应
type ExprTestResponse struct {
	Result bool `json:"result"`
}

// ToEntity 将创建请求转换为实体
func (req *CreatePolicyRequest) ToEntity() *entity.VerificationPolicy {
	return &entity.VerificationPolicy{
		TenantID:           req.TenantID,
		Scene:              req.Scene,
		Dimension:          req.Dimension,
		ConditionExpr:      req.ConditionExpr,
		NeedVerification:   req.NeedVerification,
		VerificationLevel:  req.VerificationLevel,
		TargetType:         req.TargetType,
		TokenType:          req.TokenType,
		TokenLength:        req.TokenLength,
		ExpireMinutes:      req.ExpireMinutes,
		MaxAttempts:        req.MaxAttempts,
		RateLimitPerMinute: req.RateLimitPerMinute,
		RateLimitPerHour:   req.RateLimitPerHour,
		RateLimitPerDay:    req.RateLimitPerDay,
		TemplateCode:       req.TemplateCode,
		IsActive:           true, // 默认启用
		Priority:           req.Priority,
		Description:        req.Description,
	}
}

// UpdateEntity 将更新请求应用到实体
func (req *UpdatePolicyRequest) UpdateEntity(policy *entity.VerificationPolicy) {
	policy.ID = req.PolicyID
	policy.TenantID = req.TenantID
	policy.Scene = req.Scene
	policy.Dimension = req.Dimension
	policy.ConditionExpr = req.ConditionExpr
	policy.NeedVerification = req.NeedVerification
	policy.VerificationLevel = req.VerificationLevel
	policy.TargetType = req.TargetType
	policy.TokenType = req.TokenType
	policy.TokenLength = req.TokenLength
	policy.ExpireMinutes = req.ExpireMinutes
	policy.MaxAttempts = req.MaxAttempts
	policy.RateLimitPerMinute = req.RateLimitPerMinute
	policy.RateLimitPerHour = req.RateLimitPerHour
	policy.RateLimitPerDay = req.RateLimitPerDay
	policy.TemplateCode = req.TemplateCode
	policy.Priority = req.Priority
	policy.Description = req.Description
}

// FromEntity 从实体转换为响应
func FromEntity(policy *entity.VerificationPolicy) *PolicyResponse {
	return &PolicyResponse{
		ID:                 policy.ID,
		TenantID:           policy.TenantID,
		Scene:              policy.Scene,
		Dimension:          policy.Dimension,
		ConditionExpr:      policy.ConditionExpr,
		NeedVerification:   policy.NeedVerification,
		VerificationLevel:  policy.VerificationLevel,
		TargetType:         policy.TargetType,
		TokenType:          policy.TokenType,
		TokenLength:        policy.TokenLength,
		ExpireMinutes:      policy.ExpireMinutes,
		MaxAttempts:        policy.MaxAttempts,
		RateLimitPerMinute: policy.RateLimitPerMinute,
		RateLimitPerHour:   policy.RateLimitPerHour,
		RateLimitPerDay:    policy.RateLimitPerDay,
		TemplateCode:       policy.TemplateCode,
		IsActive:           policy.IsActive,
		Priority:           policy.Priority,
		Description:        policy.Description,
		CreatedAt:          policy.CreatedAt,
		UpdatedAt:          policy.UpdatedAt,
	}
}

// FromEntities 从实体列表转换为响应列表
func FromEntities(policies []*entity.VerificationPolicy) []*PolicyResponse {
	result := make([]*PolicyResponse, len(policies))
	for i, policy := range policies {
		result[i] = FromEntity(policy)
	}
	return result
}

// Validate 验证创建策略请求
func (req *CreatePolicyRequest) Validate() error {
	if req.Scene == "" {
		return fmt.Errorf("场景不能为空")
	}
	if req.Dimension == "" {
		return fmt.Errorf("维度不能为空")
	}
	if req.ConditionExpr == "" {
		return fmt.Errorf("条件表达式不能为空")
	}
	return nil
}

// Validate 验证更新策略请求
func (req *UpdatePolicyRequest) Validate() error {
	if req.PolicyID <= 0 {
		return fmt.Errorf("策略ID必须大于0")
	}
	if req.Scene == "" {
		return fmt.Errorf("场景不能为空")
	}
	if req.Dimension == "" {
		return fmt.Errorf("维度不能为空")
	}
	if req.ConditionExpr == "" {
		return fmt.Errorf("条件表达式不能为空")
	}
	return nil
}

// Validate 验证删除策略请求
func (req *DeletePolicyRequest) Validate() error {
	if req.PolicyID <= 0 {
		return fmt.Errorf("策略ID必须大于0")
	}
	return nil
}

// Validate 验证设置策略状态请求
func (req *SetPolicyStatusRequest) Validate() error {
	if req.PolicyID <= 0 {
		return fmt.Errorf("策略ID必须大于0")
	}
	return nil
}

// Validate 验证策略列表请求
func (req *PolicyListRequest) Validate() error {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	return nil
}

// Validate 验证策略详情请求
func (req *PolicyDetailRequest) Validate() error {
	if req.PolicyID <= 0 {
		return fmt.Errorf("策略ID必须大于0")
	}
	return nil
}

// Validate 验证表达式校验请求
func (req *ExprValidateRequest) Validate() error {
	if req.ConditionExpr == "" {
		return fmt.Errorf("条件表达式不能为空")
	}
	return nil
}

// Validate 验证表达式测试请求
func (req *ExprTestRequest) Validate() error {
	if req.ConditionExpr == "" {
		return fmt.Errorf("条件表达式不能为空")
	}
	if req.Sample == nil {
		return fmt.Errorf("测试样本不能为空")
	}
	return nil
}
