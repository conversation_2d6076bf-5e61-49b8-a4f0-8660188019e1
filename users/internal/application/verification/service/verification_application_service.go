package service

import (
	"context"
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"platforms-pkg/logiface"
	"platforms-pkg/usercontext"

	"platforms-user/internal/application/verification/dto"
	user_errors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/user/repository"
	"platforms-user/internal/domain/verification/entity"
	verificationRepo "platforms-user/internal/domain/verification/repository"
	"platforms-user/internal/infrastructure/external"
	"platforms-user/internal/infrastructure/mfa"
	"platforms-user/internal/infrastructure/sms"
	"platforms-user/pkg/config"
)

// VerificationApplicationService 验证应用服务
type VerificationApplicationService struct {
	// 仓储
	tokenRepo    verificationRepo.VerificationTokenRepository
	configRepo   verificationRepo.VerificationConfigRepository
	logRepo      verificationRepo.VerificationLogRepository
	templateRepo verificationRepo.SMSTemplateRepository
	userRepo     repository.UserRepository
	tenantRepo   repository.TenantRepository

	// 基础设施服务
	smsService         *sms.SMSService
	emailServiceClient *external.EmailServiceClient
	mfaService         *mfa.MFAService

	// 配置和日志
	logger    logiface.Logger
	appConfig *config.AppConfig
}

// NewVerificationApplicationService 创建验证应用服务
func NewVerificationApplicationService(
	tokenRepo verificationRepo.VerificationTokenRepository,
	configRepo verificationRepo.VerificationConfigRepository,
	logRepo verificationRepo.VerificationLogRepository,
	templateRepo verificationRepo.SMSTemplateRepository,
	userRepo repository.UserRepository,
	tenantRepo repository.TenantRepository,
	smsService *sms.SMSService,
	emailServiceClient *external.EmailServiceClient,
	mfaService *mfa.MFAService,
	logger logiface.Logger,
	appConfig *config.AppConfig,
) *VerificationApplicationService {
	return &VerificationApplicationService{
		tokenRepo:          tokenRepo,
		configRepo:         configRepo,
		logRepo:            logRepo,
		templateRepo:       templateRepo,
		userRepo:           userRepo,
		tenantRepo:         tenantRepo,
		smsService:         smsService,
		emailServiceClient: emailServiceClient,
		mfaService:         mfaService,
		logger:             logger,
		appConfig:          appConfig,
	}
}

// SendVerification 发送验证
func (s *VerificationApplicationService) SendVerification(ctx context.Context, tenantID int64, req *dto.SendVerificationRequest) (*dto.SendVerificationResponse, error) {

	// 获取验证配置 - 使用新的统一配置查找方法
	config, err := s.configRepo.FindEffectiveConfig(ctx, tenantID, req.Purpose, req.TargetType, req.BusinessScene)
	if err != nil {
		s.logger.Error(ctx, "Failed to get verification config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to get verification config: %w", err)
	}

	if config == nil {
		return nil, user_errors.NewUserError(user_errors.CodeVerificationTokenInvalid, fmt.Sprintf("config not found for purpose %d and target type %d", req.Purpose, req.TargetType))
	}

	if !config.IsEnabled() {
		return nil, user_errors.NewUserError(user_errors.CodeUserAccountDisabled, fmt.Sprintf("config disabled for purpose %d and target type %d", req.Purpose, req.TargetType))
	}

	// 基于verification_configs进行频控检查
	if err := s.checkRateLimit(ctx, tenantID, req, config); err != nil {
		s.logger.Warn(ctx, "Rate limit exceeded",
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int64("tenant_id", tenantID),
			logiface.Error(err))
		return nil, err
	}

	// 检查是否需要验证（动态配置可能不需要验证）
	if !config.RequireVerification {
		s.logger.Info(ctx, "Verification not required by configuration",
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int("purpose", int(req.Purpose)))

		// 返回无需验证的响应
		return &dto.SendVerificationResponse{
			Success: true,
		}, nil
	}

	// 验证模板是否存在
	if err := s.validateTemplate(ctx, tenantID, config.TemplateCode, req.TargetType); err != nil {
		s.logger.Error(ctx, "Template validation failed",
			logiface.Error(err),
			logiface.String("template_code", config.TemplateCode),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("template validation failed: %w", err)
	}

	// 创建验证令牌
	token, err := s.createVerificationToken(ctx, tenantID, req, config)
	if err != nil {
		s.logger.Error(ctx, "Failed to create verification token",
			logiface.Error(err),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to create verification token: %w", err)
	}

	// 发送验证令牌
	if err := s.sendVerificationToken(ctx, token, config, req); err != nil {
		s.logger.Error(ctx, "Failed to send verification token",
			logiface.Error(err),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to send verification token: %w", err)
	}

	// 记录发送日志
	s.logVerificationAction(ctx, token.ID, entity.LogActionSend, true, req.IPAddress, req.UserAgent, "")

	s.logger.Info(ctx, "Verification sent successfully",
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("token_type", token.GetTokenTypeName()),
		logiface.String("purpose", token.GetPurposeName()))

	return &dto.SendVerificationResponse{
		Success: true,
	}, nil
}

// VerifyToken 验证令牌
func (s *VerificationApplicationService) VerifyToken(ctx context.Context, tenantID int64, req *dto.VerifyTokenRequest) (*dto.VerifyTokenResponse, error) {

	// 检查依赖是否已初始化
	if s.tokenRepo == nil {
		s.logger.Error(ctx, "Verification token repository not initialized")
		return nil, fmt.Errorf("verification service not properly initialized")
	}

	// 查找验证令牌
	token, err := s.tokenRepo.FindByToken(ctx, req.Token)
	if err != nil {
		s.logger.Error(ctx, "Failed to find verification token",
			logiface.Error(err),
			logiface.String("token", req.Token))
		return nil, fmt.Errorf("failed to find verification token: %w", err)
	}

	if token == nil {
		s.logVerificationAction(ctx, 0, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "令牌不存在")
		return nil, user_errors.NewUserError(user_errors.CodeTokenNotFound, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查租户权限
	if token.TenantID != tenantID {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "租户权限不匹配")
		return nil, user_errors.NewUserError(user_errors.CodeTokenNotFound, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查目标地址匹配
	if token.Target != req.Target {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "目标地址不匹配")
		return nil, user_errors.NewUserError(user_errors.CodeTokenInvalid, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查令牌状态
	if token.IsUsed() {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "令牌已使用")
		return nil, user_errors.NewUserError(user_errors.CodeTokenInvalid, fmt.Sprintf("token: %s", req.Token))
	}

	if token.IsExpired() {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "令牌已过期")
		return nil, user_errors.NewUserError(user_errors.CodeTokenExpired, fmt.Sprintf("token: %s", req.Token))
	}

	if token.IsRevoked() {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "令牌已撤销")
		return nil, user_errors.NewUserError(user_errors.CodeTokenInvalid, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查尝试次数限制
	if token.AttemptCount >= token.MaxAttempts {
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "尝试次数超限")
		return nil, user_errors.NewUserError(user_errors.CodeAttemptLimitExceeded, fmt.Sprintf("token: %s", req.Token))
	}

	// 根据令牌类型进行验证
	var isValid bool
	switch token.TokenType {
	case entity.TokenTypeLink:
		// 链接类型：直接验证令牌
		isValid = token.Token == req.Token
	case entity.TokenTypeCode:
		// 验证码类型：直接验证令牌
		isValid = token.Token == req.Token
	case entity.TokenTypeHybrid:
		// 混合模式：需要验证链接令牌和验证码
		// 验证链接令牌
		isValid = token.Token == req.Token
		// 如果请求中包含验证码，验证从令牌中提取的验证码
		if codeToken, exists := req.Variables["code_token"]; exists {
			// 使用默认的验证码长度6位
			extractedCode := s.extractCodeFromToken(token.Token, 6)
			isValid = isValid && extractedCode == codeToken
		}
	default:
		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "不支持的令牌类型")
		return nil, user_errors.NewUserError(user_errors.CodeTokenInvalid, fmt.Sprintf("unsupported token type: %d", token.TokenType))
	}

	if !isValid {
		// 增加尝试次数
		token.IncrementAttempt()
		if err := s.tokenRepo.Update(ctx, token); err != nil {
			s.logger.Error(ctx, "Failed to update attempt count",
				logiface.Error(err),
				logiface.Int64("token_id", token.ID))
		}

		s.logVerificationAction(ctx, token.ID, entity.LogActionVerifyFailed, false, req.IPAddress, req.UserAgent, "令牌验证失败")
		return nil, user_errors.NewUserError(user_errors.CodeTokenInvalid, fmt.Sprintf("token: %s", req.Token))
	}

	// 标记令牌为已使用
	token.MarkAsUsed()
	if err := s.tokenRepo.Update(ctx, token); err != nil {
		s.logger.Error(ctx, "Failed to mark token as used",
			logiface.Error(err),
			logiface.Int64("token_id", token.ID))
		return nil, user_errors.NewSystemError("mark_token_used", err.Error())
	}

	// 记录成功日志
	s.logVerificationAction(ctx, token.ID, entity.LogActionVerifySuccess, true, req.IPAddress, req.UserAgent, "")

	s.logger.Info(ctx, "Token verification successful",
		logiface.String("token", dto.MaskToken(req.Token)),
		logiface.Int64("token_id", token.ID),
		logiface.String("target", dto.MaskTarget(token.Target, token.TargetType)),
		logiface.String("purpose", token.GetPurposeName()))

	return &dto.VerifyTokenResponse{
		Verified:   true,
		Purpose:    token.Purpose,
		Target:     dto.MaskTarget(token.Target, token.TargetType),
		TargetType: token.TargetType,
		VerifiedAt: time.Now(),
		UserID:     token.UserID,
	}, nil
}

// ResendVerification 重新发送验证
func (s *VerificationApplicationService) ResendVerification(ctx context.Context, tenantID int64, req *dto.ResendVerificationRequest) (*dto.SendVerificationResponse, error) {

	// 转换为发送请求
	sendReq := &dto.SendVerificationRequest{
		Target:     req.Target,
		TargetType: req.TargetType,
		Purpose:    req.Purpose,
		IPAddress:  req.IPAddress,
		UserAgent:  req.UserAgent,
	}

	return s.SendVerification(ctx, tenantID, sendReq)
}

// CheckTokenStatus 检查令牌状态
func (s *VerificationApplicationService) CheckTokenStatus(ctx context.Context, tenantID int64, req *dto.CheckTokenStatusRequest) (*dto.CheckTokenStatusResponse, error) {

	// 检查依赖是否已初始化
	if s.tokenRepo == nil {
		s.logger.Error(ctx, "Verification token repository not initialized")
		return nil, fmt.Errorf("verification service not properly initialized")
	}

	// 查找验证令牌
	token, err := s.tokenRepo.FindByToken(ctx, req.Token)
	if err != nil {
		s.logger.Error(ctx, "Failed to find verification token",
			logiface.Error(err),
			logiface.String("token", req.Token))
		return nil, fmt.Errorf("failed to find verification token: %w", err)
	}

	if token == nil {
		return nil, user_errors.NewUserError(user_errors.CodeTokenNotFound, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查租户权限
	if token.TenantID != tenantID {
		return nil, user_errors.NewUserError(user_errors.CodeTokenNotFound, fmt.Sprintf("token: %s", req.Token))
	}

	// 检查是否过期并更新状态
	if token.IsExpired() && token.Status == entity.TokenStatusUnused {
		s.tokenRepo.MarkAsExpired(ctx, token.ID)
		token.Status = entity.TokenStatusExpired
	}

	// 构建响应
	response := &dto.CheckTokenStatusResponse{
		Status:            token.Status,
		StatusName:        token.GetStatusName(),
		ExpiresAt:         token.ExpiresAt,
		RemainingAttempts: token.MaxAttempts - token.AttemptCount,
		CreatedAt:         token.CreatedAt,
	}

	return response, nil
}

// createVerificationToken 创建验证令牌
func (s *VerificationApplicationService) createVerificationToken(ctx context.Context, tenantID int64, req *dto.SendVerificationRequest, config *entity.VerificationConfig) (*entity.VerificationToken, error) {
	// 创建验证令牌实体
	token, err := entity.NewVerificationTokenWithContext(ctx, tenantID, req.UserID, req.Target, req.TargetType, req.Purpose, config.TemplateCode)
	if err != nil {
		return nil, user_errors.NewSystemError("create_verification_token",
			fmt.Sprintf("failed to create verification token entity: %s", err.Error()))
	}

	// 根据token_type生成不同类型的token
	tokenType := config.TokenType
	if req.TokenType != nil {
		tokenType = *req.TokenType
	}

	// 根据token_type生成token
	switch tokenType {
	case entity.TokenTypeLink:
		// 链接类型：生成安全的对外token
		if err := s.generateSecureLinkToken(token, config.TokenLength); err != nil {
			return nil, user_errors.NewSystemError("generate_secure_link_token",
				fmt.Sprintf("failed to generate secure link token: %s", err.Error()))
		}
	case entity.TokenTypeCode:
		// 验证码类型：生成数字验证码
		if err := token.GenerateToken(entity.TokenTypeCode, config.TokenLength); err != nil {
			return nil, user_errors.NewSystemError("generate_token",
				fmt.Sprintf("failed to generate token: %s", err.Error()))
		}
	case entity.TokenTypeHybrid:
		// 混合模式：同时生成链接令牌和验证码
		if err := token.GenerateToken(entity.TokenTypeHybrid, config.TokenLength); err != nil {
			return nil, user_errors.NewSystemError("generate_hybrid_token",
				fmt.Sprintf("failed to generate hybrid token: %s", err.Error()))
		}
	default:
		return nil, user_errors.NewSystemError("invalid_token_type",
			fmt.Sprintf("unsupported token type: %d", tokenType))
	}

	// 设置过期时间
	token.SetExpiration(config.ExpireMinutes)

	// 设置最大尝试次数
	token.MaxAttempts = config.MaxAttempts

	// 设置IP地址和用户代理
	token.IPAddress = req.IPAddress
	token.UserAgent = req.UserAgent

	// 保存到数据库
	if err := s.tokenRepo.Create(ctx, token); err != nil {
		s.logger.Error(ctx, "Failed to save verification token",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))
		return nil, user_errors.NewSystemError("save_verification_token",
			fmt.Sprintf("failed to save verification token: %s", err.Error()))
	}

	s.logger.Info(ctx, "Verification token created and saved",
		logiface.String("token", dto.MaskToken(token.Token)),
		logiface.Int64("tenant_id", tenantID),
		logiface.String("token_type", token.GetTokenTypeName()),
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))

	return token, nil
}

// generateSecureLinkToken 生成安全的链接token
func (s *VerificationApplicationService) generateSecureLinkToken(token *entity.VerificationToken, length int) error {
	// 生成基础随机字符串
	baseToken, err := generateSecureRandomString(length)
	if err != nil {
		return fmt.Errorf("failed to generate base token: %w", err)
	}

	// 添加时间戳和租户信息以增加安全性
	timestamp := time.Now().Unix()
	tenantInfo := fmt.Sprintf("%d_%d", token.TenantID, timestamp)

	// 使用HMAC或其他算法生成安全的token
	secureToken := s.generateHMACToken(baseToken, tenantInfo)

	// 限制长度并确保唯一性
	if len(secureToken) > length {
		secureToken = secureToken[:length]
	}

	token.Token = secureToken
	token.TokenType = entity.TokenTypeLink

	return nil
}

// generateHMACToken 使用HMAC算法生成安全token
func (s *VerificationApplicationService) generateHMACToken(baseToken, tenantInfo string) string {
	// 使用应用配置的密钥或默认密钥
	secret := s.appConfig.JWT.Secret
	if secret == "" {
		secret = "default-secret-key"
	}

	// 简单的HMAC实现（实际项目中应使用crypto/hmac）
	data := baseToken + "_" + tenantInfo + "_" + secret
	return fmt.Sprintf("%x", []byte(data))[:32] // 取前32位作为token
}

// generateSecureRandomString 生成安全的随机字符串
func generateSecureRandomString(length int) (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)

	for i := range result {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		result[i] = charset[num.Int64()]
	}

	return string(result), nil
}

// sendVerificationToken 发送验证令牌
func (s *VerificationApplicationService) sendVerificationToken(ctx context.Context, token *entity.VerificationToken, config *entity.VerificationConfig, req *dto.SendVerificationRequest) error {
	switch token.TargetType {
	case entity.TargetTypePhone:
		return s.sendSMSVerification(ctx, token, config, req)
	case entity.TargetTypeEmail:
		return s.sendEmailVerification(ctx, token, config, req)
	case entity.TargetTypeMFA:
		return s.sendMFAVerification(ctx, token, config, req)
	default:
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("unsupported target type: %d", token.TargetType))
	}
}

// sendSMSVerification 发送短信验证
func (s *VerificationApplicationService) sendSMSVerification(ctx context.Context, token *entity.VerificationToken, config *entity.VerificationConfig, req *dto.SendVerificationRequest) error {
	// 获取短信模板
	template, err := s.templateRepo.GetEffectiveTemplate(ctx, token.TenantID, config.TemplateCode)
	if err != nil {
		return fmt.Errorf("failed to get SMS template: %w", err)
	}

	if template == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("SMS template not found: %s", config.TemplateCode))
	}

	// 准备模板变量
	variables := make(map[string]string)
	if req.Variables != nil {
		for k, v := range req.Variables {
			variables[k] = v
		}
	}
	variables["code"] = token.Token
	variables["expire_minutes"] = fmt.Sprintf("%d", config.ExpireMinutes)

	// 渲染短信内容
	content, err := template.RenderContent(map[string]interface{}{
		"code":           token.Token,
		"expire_minutes": config.ExpireMinutes,
	})
	if err != nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("SMS template render failed: %s, reason: %s", config.TemplateCode, err.Error()))
	}

	// 发送短信
	smsReq := &sms.SMSRequest{
		Phone:     token.Target,
		Content:   content,
		Variables: variables,
		RequestID: fmt.Sprintf("verify-%d", token.ID),
		TenantID:  token.TenantID,
	}

	response, err := s.smsService.SendSMS(ctx, smsReq)
	if err != nil {
		return user_errors.NewThirdPartyError("短信服务", fmt.Sprintf("target: %s, reason: %s", token.Target, err.Error()))
	}

	if !response.IsSuccessful() {
		return user_errors.NewThirdPartyError("短信服务", fmt.Sprintf("target: %s, reason: %s", token.Target, response.ErrorMsg))
	}

	return nil
}

// sendEmailVerification 发送邮件验证
func (s *VerificationApplicationService) sendEmailVerification(ctx context.Context, token *entity.VerificationToken, config *entity.VerificationConfig, req *dto.SendVerificationRequest) error {
	// 获取模板代码
	templateCode := config.TemplateCode

	// 准备模板变量
	variables := s.buildTemplateVariables(ctx, token, config, req)

	// 转换变量类型
	stringVariables := make(map[string]string)
	for k, v := range variables {
		stringVariables[k] = fmt.Sprintf("%v", v)
	}

	// 使用EmailServiceClient发送验证邮件
	err := s.emailServiceClient.SendVerificationEmail(ctx, templateCode, token.Target, stringVariables)
	if err != nil {
		s.logger.Error(ctx, "Failed to send verification email",
			logiface.Error(err),
			logiface.String("template_code", templateCode),
			logiface.String("target", dto.MaskTarget(token.Target, token.TargetType)))
		return user_errors.NewThirdPartyError("邮件服务", fmt.Sprintf("target: %s, reason: %s", token.Target, err.Error()))
	}

	s.logger.Info(ctx, "Email verification sent successfully via template",
		logiface.String("target", dto.MaskTarget(token.Target, token.TargetType)),
		logiface.String("template_code", templateCode),
		logiface.String("token_type", token.GetTokenTypeName()))

	return nil
}

// getTemplateCodeByPurpose 根据验证用途获取邮件模板代码
func (s *VerificationApplicationService) getTemplateCodeByPurpose(purpose entity.Purpose) string {
	switch purpose {
	case entity.PurposeRegistration:
		return "account_verification"
	case entity.PurposePasswordReset:
		return "password_reset"
	case entity.PurposeEmailChange:
		return "email_change_verification"
	case entity.PurposePhoneChange:
		return "phone_change_verification"
	case entity.PurposeLoginVerify:
		return "login_verification"
	case entity.PurposeMFAVerify:
		return "mfa_verification"
	default:
		return "general_verification"
	}
}

// FormatExpirationTime 智能格式化失效时间
// 将分钟数转换为用户友好的时间描述
func FormatExpirationTime(minutes int) string {
	if minutes <= 0 {
		return "立即失效"
	}

	if minutes < 60 {
		return fmt.Sprintf("%d分钟", minutes)
	}

	hours := minutes / 60
	remainingMinutes := minutes % 60

	if remainingMinutes == 0 {
		return fmt.Sprintf("%d小时", hours)
	}

	return fmt.Sprintf("%d小时%d分钟", hours, remainingMinutes)
}

// extractCodeFromToken 从链接令牌中提取验证码
func (s *VerificationApplicationService) extractCodeFromToken(token string, length int) string {
	if len(token) < length {
		// 如果令牌长度不够，使用整个令牌
		return token
	}
	// 取前length位作为验证码
	return token[:length]
}

// buildTemplateVariables 构建邮件模板变量
func (s *VerificationApplicationService) buildTemplateVariables(ctx context.Context, token *entity.VerificationToken, config *entity.VerificationConfig, req *dto.SendVerificationRequest) map[string]interface{} {
	// 智能转换失效时间
	expireTimeDescription := FormatExpirationTime(config.ExpireMinutes)

	variables := map[string]interface{}{
		"expire_time": expireTimeDescription, // 智能格式化的失效时间
		"purpose":     dto.GetPurposeName(token.Purpose),
	}

	// 根据token_type生成不同的变量
	switch token.TokenType {
	case entity.TokenTypeLink:
		// 链接类型：生成验证链接
		verifyLink := s.generateVerificationLink(token, req.URL)
		variables["link"] = verifyLink // 兼容性，保持原有变量名
	case entity.TokenTypeCode:
		// 验证码类型：直接使用token作为验证码
		variables["verify_code"] = token.Token
	case entity.TokenTypeHybrid:
		// 混合模式：同时提供链接和验证码
		verifyLink := s.generateVerificationLink(token, req.URL)
		variables["link"] = verifyLink
		variables["verify_code"] = token.Token
	}

	// 从上下文中获取用户信息（如果有）
	if token.UserID != nil {
		if userInfo := s.getUserInfoFromContext(ctx); userInfo != nil {
			variables["username"] = userInfo.Username
			variables["real_name"] = userInfo.RealName
		}
	}
	return variables
}

// generateVerificationLink 生成验证链接
func (s *VerificationApplicationService) generateVerificationLink(token *entity.VerificationToken, baseURL string) string {
	// 如果没有提供URL，使用应用配置的baseURL
	if baseURL == "" {
		baseURL = s.appConfig.BaseURL
	}
	// 直接使用传入的URL拼接token，不添加固定路径
	verifyLink := baseURL + "?token=" + token.Token
	return verifyLink
}

// sendMFAVerification 发送MFA验证
func (s *VerificationApplicationService) sendMFAVerification(ctx context.Context, token *entity.VerificationToken, config *entity.VerificationConfig, req *dto.SendVerificationRequest) error {
	// 检查MFA服务是否可用
	if s.mfaService == nil {
		s.logger.Warn(ctx, "MFA service not configured, skipping MFA verification",
			logiface.String("target", token.Target))
		return nil
	}

	// 检查用户ID是否存在（MFA需要用户ID）
	if token.UserID == nil {
		return user_errors.NewParameterValidationFailedError("user_id", "user_id is required for MFA verification")
	}

	// 构建MFA请求
	mfaReq := &mfa.MFARequest{
		UserID:     *token.UserID,
		Target:     token.Target,
		Purpose:    dto.GetPurposeName(token.Purpose),
		CodeLength: config.TokenLength,
		ExpiryTime: time.Duration(config.ExpireMinutes) * time.Minute,
		RequestID:  fmt.Sprintf("verify-mfa-%d", token.ID),
		TenantID:   token.TenantID,
	}

	// 生成MFA验证码
	response, err := s.mfaService.GenerateCode(ctx, mfaReq)
	if err != nil {
		return user_errors.NewThirdPartyError("MFA服务", fmt.Sprintf("target: %s, reason: %s", token.Target, err.Error()))
	}

	if !response.IsSuccessful() {
		return user_errors.NewThirdPartyError("MFA服务", fmt.Sprintf("target: %s, reason: %s", token.Target, response.ErrorMsg))
	}

	// 更新令牌的验证码（使用MFA生成的验证码）
	token.Token = response.Code
	if err := s.tokenRepo.Update(ctx, token); err != nil {
		s.logger.Error(ctx, "Failed to update token with MFA code",
			logiface.Error(err),
			logiface.Int64("token_id", token.ID))
		// 不阻断流程，继续执行
	}

	s.logger.Info(ctx, "MFA verification code generated successfully",
		logiface.String("target", dto.MaskTarget(token.Target, token.TargetType)),
		logiface.Int64("user_id", *token.UserID),
		logiface.Time("expires_at", response.ExpiresAt))

	return nil
}

// logVerificationAction 记录验证操作日志
func (s *VerificationApplicationService) logVerificationAction(ctx context.Context, tokenID int64, action entity.LogAction, success bool, ipAddress, userAgent, errorMessage string) {
	// 检查依赖是否已初始化
	if s.tokenRepo == nil || s.logRepo == nil {
		s.logger.Error(ctx, "Verification repositories not initialized")
		return
	}

	if tokenID == 0 {
		return // 无效的令牌ID，跳过日志记录
	}

	// 获取令牌信息以获取租户ID
	token, err := s.tokenRepo.FindByID(ctx, tokenID)
	if err != nil || token == nil {
		s.logger.Error(ctx, "Failed to find token for logging",
			logiface.Error(err),
			logiface.Int64("token_id", tokenID))
		return
	}

	log, err := entity.NewVerificationLogWithContext(ctx, token.TenantID, tokenID, action, success, ipAddress, userAgent, errorMessage)
	if err != nil {
		s.logger.Error(ctx, "Failed to create verification log",
			logiface.Error(err))
		return
	}

	if err := s.logRepo.Create(ctx, log); err != nil {
		s.logger.Error(ctx, "Failed to save verification log",
			logiface.Error(err))
	}
}

// UserInfo 用户信息结构体
type UserInfo struct {
	Username string
	RealName string
}

// TenantInfo 租户信息结构体
type TenantInfo struct {
	TenantName   string
	TenantCode   string
	CompanyName  string
	SupportEmail string
}

// getUserInfoFromContext 从上下文获取用户信息
func (s *VerificationApplicationService) getUserInfoFromContext(ctx context.Context) *UserInfo {
	// 检查依赖是否已初始化
	if s.userRepo == nil {
		s.logger.Error(ctx, "User repository not initialized")
		return nil
	}

	// 从上下文获取用户ID
	userID, ok := usercontext.GetUserID(ctx)
	if !ok || userID == 0 {
		s.logger.Debug(ctx, "No user ID found in context")
		return nil
	}

	// 查询用户信息
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get user info",
			logiface.Error(err),
			logiface.Int64("user_id", userID))
		return nil
	}

	if user == nil {
		s.logger.Debug(ctx, "User not found",
			logiface.Int64("user_id", userID))
		return nil
	}

	return &UserInfo{
		Username: user.Username,
		RealName: user.RealName,
	}
}

// getTenantInfoFromContext 从上下文获取租户信息
func (s *VerificationApplicationService) getTenantInfoFromContext(ctx context.Context) *TenantInfo {
	// 检查依赖是否已初始化
	if s.tenantRepo == nil {
		s.logger.Error(ctx, "Tenant repository not initialized")
		return nil
	}

	// 从上下文获取租户ID
	tenantID, ok := usercontext.GetTenantID(ctx)
	if !ok || tenantID == 0 {
		s.logger.Debug(ctx, "No tenant ID found in context")
		return nil
	}

	// 查询租户信息
	tenant, err := s.tenantRepo.FindByID(ctx, tenantID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tenant info",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil
	}

	if tenant == nil {
		s.logger.Debug(ctx, "Tenant not found",
			logiface.Int64("tenant_id", tenantID))
		return nil
	}

	return &TenantInfo{
		TenantName:   tenant.TenantName,
		TenantCode:   tenant.TenantCode,
		CompanyName:  tenant.TenantName,     // 暂时使用租户名称作为公司名称
		SupportEmail: "<EMAIL>", // 暂时使用默认值，后续可配置
	}
}

// GetTokenByTokenString 通过token字符串获取完整令牌信息
func (s *VerificationApplicationService) GetTokenByTokenString(ctx context.Context, tokenString string) (*entity.VerificationToken, error) {
	// 检查依赖是否已初始化
	if s.tokenRepo == nil {
		s.logger.Error(ctx, "Verification token repository not initialized")
		return nil, fmt.Errorf("verification service not properly initialized")
	}

	// 查找验证令牌
	token, err := s.tokenRepo.FindByToken(ctx, tokenString)
	if err != nil {
		s.logger.Error(ctx, "Failed to find verification token by token string",
			logiface.Error(err),
			logiface.String("token", tokenString))
		return nil, fmt.Errorf("failed to find verification token: %w", err)
	}

	if token == nil {
		s.logger.Debug(ctx, "Verification token not found",
			logiface.String("token", tokenString))
		return nil, nil
	}

	return token, nil
}

// SaveToken 保存验证令牌
func (s *VerificationApplicationService) SaveToken(ctx context.Context, token *entity.VerificationToken) error {
	// 检查依赖是否已初始化
	if s.tokenRepo == nil {
		s.logger.Error(ctx, "Verification token repository not initialized")
		return fmt.Errorf("verification service not properly initialized")
	}

	// 保存令牌
	if err := s.tokenRepo.Create(ctx, token); err != nil {
		s.logger.Error(ctx, "Failed to save verification token",
			logiface.Error(err),
			logiface.Int64("token_id", token.ID))
		return fmt.Errorf("failed to save verification token: %w", err)
	}

	s.logger.Info(ctx, "Verification token saved successfully",
		logiface.Int64("token_id", token.ID),
		logiface.String("target", dto.MaskTarget(token.Target, token.TargetType)),
		logiface.Int("purpose", int(token.Purpose)))

	return nil
}

// CreateStaticConfig 创建静态验证配置
func (s *VerificationApplicationService) CreateStaticConfig(ctx context.Context, tenantID int64, req *dto.CreateStaticConfigRequest) (*dto.ConfigResponse, error) {
	// 检查依赖是否已初始化
	if s.configRepo == nil {
		s.logger.Error(ctx, "Verification config repository not initialized")
		return nil, fmt.Errorf("verification service not properly initialized")
	}

	// 检查配置是否已存在
	exists, err := s.configRepo.ExistsByPurposeAndTarget(ctx, tenantID, req.Purpose, req.TargetType)
	if err != nil {
		s.logger.Error(ctx, "Failed to check config existence",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to check config existence: %w", err)
	}

	if exists {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config already exists for purpose %d and target type %d", req.Purpose, req.TargetType))
	}

	// 验证模板是否存在
	if err := s.validateTemplate(ctx, tenantID, req.TemplateCode, req.TargetType); err != nil {
		return nil, err
	}

	// 创建配置实体
	config, err := entity.NewStaticConfigWithContext(ctx, tenantID, req.Purpose, req.TargetType, req.TokenType, req.TemplateCode)
	if err != nil {
		s.logger.Error(ctx, "Failed to create config entity",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	// 设置配置属性
	config.TokenLength = req.TokenLength
	config.ExpireMinutes = req.ExpireMinutes
	config.MaxAttempts = req.MaxAttempts
	config.RateLimitPerMinute = req.RateLimitPerMinute
	config.RateLimitPerHour = req.RateLimitPerHour
	config.RateLimitPerDay = req.RateLimitPerDay
	config.Priority = req.Priority
	if req.Description != nil {
		config.Description = req.Description
	}

	// 保存配置
	if err := s.configRepo.Create(ctx, config); err != nil {
		s.logger.Error(ctx, "Failed to save config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("config_id", config.ID),
			logiface.String("config_mode", string(config.ConfigMode)))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "Verification config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", config.ID),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.Int("target_type", int(req.TargetType)))

	return dto.NewConfigResponse(config), nil
}

// CreateDynamicConfig 创建动态验证配置
func (s *VerificationApplicationService) CreateDynamicConfig(ctx context.Context, tenantID int64, req *dto.CreateDynamicConfigRequest) (*dto.ConfigResponse, error) {
	// 检查依赖是否已初始化
	if s.configRepo == nil {
		s.logger.Error(ctx, "Verification config repository not initialized")
		return nil, fmt.Errorf("verification service not properly initialized")
	}

	// 验证模板是否存在
	if err := s.validateTemplate(ctx, tenantID, req.TemplateCode, req.TargetType); err != nil {
		return nil, err
	}

	// 创建配置实体
	config, err := entity.NewDynamicConfigWithContext(ctx, tenantID, req.BusinessScene, req.JudgmentDimension, req.ConditionExpr, req.TargetType, req.TokenType, req.TemplateCode)
	if err != nil {
		s.logger.Error(ctx, "Failed to create config entity",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	// 设置配置属性
	config.TokenLength = req.TokenLength
	config.ExpireMinutes = req.ExpireMinutes
	config.MaxAttempts = req.MaxAttempts
	config.RateLimitPerMinute = req.RateLimitPerMinute
	config.RateLimitPerHour = req.RateLimitPerHour
	config.RateLimitPerDay = req.RateLimitPerDay
	config.VerificationLevel = req.VerificationLevel
	config.RequireVerification = req.RequireVerification
	config.Priority = req.Priority
	if req.Description != nil {
		config.Description = req.Description
	}

	// 保存配置
	if err := s.configRepo.Create(ctx, config); err != nil {
		s.logger.Error(ctx, "Failed to save config",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "Dynamic verification config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", config.ID),
		logiface.String("business_scene", req.BusinessScene),
		logiface.Int("target_type", int(req.TargetType)))

	return dto.NewConfigResponse(config), nil
}

// validateTemplate 验证模板是否存在
func (s *VerificationApplicationService) validateTemplate(ctx context.Context, tenantID int64, templateCode string, targetType entity.TargetType) error {
	switch targetType {
	case entity.TargetTypeEmail:
		// 邮件模板验证 - 调用邮件服务的gRPC接口
		exists, err := s.emailServiceClient.CheckTemplateExists(ctx, tenantID, templateCode)
		if err != nil {
			s.logger.Error(ctx, "Failed to check email template existence",
				logiface.Error(err),
				logiface.Int64("tenant_id", tenantID),
				logiface.String("template_code", templateCode))
			return err
		}

		if !exists {
			s.logger.Warn(ctx, "Email template not found",
				logiface.Int64("tenant_id", tenantID),
				logiface.String("template_code", templateCode))
			return fmt.Errorf("email template not found: %s", templateCode)
		}

		s.logger.Info(ctx, "Email template validation passed",
			logiface.Int64("tenant_id", tenantID),
			logiface.String("template_code", templateCode))

	case entity.TargetTypePhone:
		// 短信模板验证 - 调用短信服务的gRPC接口
		// TODO: 实现短信模板验证
		s.logger.Info(ctx, "SMS template validation not implemented yet",
			logiface.Int64("tenant_id", tenantID),
			logiface.String("template_code", templateCode))

	default:
		return fmt.Errorf("unsupported target type: %d", targetType)
	}

	return nil
}

// checkRateLimit 检查频率限制
func (s *VerificationApplicationService) checkRateLimit(ctx context.Context, tenantID int64, req *dto.SendVerificationRequest, config *entity.VerificationConfig) error {
	// 基于verification_configs的频控参数进行检查
	now := time.Now()

	// 检查每分钟限制
	if config.RateLimitPerMinute > 0 {
		since := now.Add(-1 * time.Minute)
		count, err := s.tokenRepo.CountByTarget(ctx, tenantID, req.Target, req.TargetType, *config.Purpose, since)
		if err != nil {
			s.logger.Error(ctx, "Failed to check rate limit per minute",
				logiface.Error(err),
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))
			return user_errors.NewSystemError("rate_limit_check", "频率限制检查失败")
		}

		if count >= int64(config.RateLimitPerMinute) {
			s.logger.Warn(ctx, "Rate limit exceeded per minute",
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
				logiface.Int64("count", count),
				logiface.Int("limit", config.RateLimitPerMinute))
			return user_errors.NewUserError(40018, "发送频率过高，请稍后再试")
		}
	}

	// 检查每小时限制
	if config.RateLimitPerHour > 0 {
		since := now.Add(-1 * time.Hour)
		count, err := s.tokenRepo.CountByTarget(ctx, tenantID, req.Target, req.TargetType, *config.Purpose, since)
		if err != nil {
			s.logger.Error(ctx, "Failed to check rate limit per hour",
				logiface.Error(err),
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))
			return user_errors.NewSystemError("rate_limit_check", "频率限制检查失败")
		}

		if count >= int64(config.RateLimitPerHour) {
			s.logger.Warn(ctx, "Rate limit exceeded per hour",
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
				logiface.Int64("count", count),
				logiface.Int("limit", config.RateLimitPerHour))
			return user_errors.NewUserError(40018, "发送频率过高，请稍后再试")
		}
	}

	// 检查每天限制
	if config.RateLimitPerDay > 0 {
		since := now.Add(-24 * time.Hour)
		count, err := s.tokenRepo.CountByTarget(ctx, tenantID, req.Target, req.TargetType, *config.Purpose, since)
		if err != nil {
			s.logger.Error(ctx, "Failed to check rate limit per day",
				logiface.Error(err),
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))
			return user_errors.NewSystemError("rate_limit_check", "频率限制检查失败")
		}

		if count >= int64(config.RateLimitPerDay) {
			s.logger.Warn(ctx, "Rate limit exceeded per day",
				logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
				logiface.Int64("count", count),
				logiface.Int("limit", config.RateLimitPerDay))
			return user_errors.NewUserError(40018, "发送频率过高，请稍后再试")
		}
	}

	s.logger.Debug(ctx, "Rate limit check passed",
		logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)),
		logiface.Int("per_minute_limit", config.RateLimitPerMinute),
		logiface.Int("per_hour_limit", config.RateLimitPerHour),
		logiface.Int("per_day_limit", config.RateLimitPerDay))

	return nil
}

// ListConfigs 获取验证配置列表
func (s *VerificationApplicationService) ListConfigs(ctx context.Context, tenantID int64, req *dto.ConfigListRequest) ([]*dto.ConfigResponse, int64, error) {

	// 构建过滤器
	filter := &verificationRepo.VerificationConfigFilter{
		TenantID:   &tenantID,
		Purpose:    req.Purpose,
		TargetType: req.TargetType,
		TokenType:  req.TokenType,
		IsActive:   req.IsActive,
		Page:       req.Page,
		Size:       req.Size,
		OrderBy:    req.OrderBy,
		OrderDesc:  req.OrderDesc,
	}

	// 转换 ConfigMode 类型
	if req.ConfigMode != nil {
		var configMode entity.ConfigMode
		switch *req.ConfigMode {
		case "static":
			configMode = entity.ConfigModeStatic
		case "dynamic":
			configMode = entity.ConfigModeDynamic
		default:
			s.logger.Warn(ctx, "Invalid config mode", logiface.String("config_mode", *req.ConfigMode))
			return nil, 0, fmt.Errorf("invalid config mode: %s", *req.ConfigMode)
		}
		filter.ConfigMode = &configMode
	}

	// 处理关键词搜索
	if req.Keyword != "" {
		// 关键词搜索通过模板代码字段实现
		filter.TemplateCode = req.Keyword
	}

	s.logger.Info(ctx, "Listing verification configs",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size),
		logiface.String("keyword", req.Keyword))

	// 查询配置列表
	configs, total, err := s.configRepo.FindWithPagination(ctx, filter)
	if err != nil {
		s.logger.Error(ctx, "Failed to list verification configs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, fmt.Errorf("failed to list verification configs: %w", err)
	}

	// 转换为响应DTO
	responseConfigs := make([]*dto.ConfigResponse, len(configs))
	for i, config := range configs {
		responseConfigs[i] = dto.NewConfigResponse(config)
	}

	s.logger.Info(ctx, "Verification configs listed successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("count", len(responseConfigs)),
		logiface.Int64("total", total))

	return responseConfigs, total, nil
}

// SendEmailVerification 发送邮件验证（优化版本）
// 使用EmailVerificationRequest对象传递参数，提高代码可读性和维护性
func (s *VerificationApplicationService) SendEmailVerification(ctx context.Context, tenantID int64, req *dto.EmailVerificationRequest) (*dto.SendVerificationResponse, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		s.logger.Error(ctx, "Email verification request validation failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("target", dto.MaskTarget(req.Target, req.TargetType)))
		return nil, err
	}

	// 转换为通用请求格式
	sendReq := req.ToSendVerificationRequest()

	// 调用通用的发送验证方法
	return s.SendVerification(ctx, tenantID, sendReq)
}
