package service

import (
	"context"
	"fmt"
	"platforms-user/internal/application/verification/dto"
	user_errors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"

	"platforms-pkg/logiface"
)

// ConfigApplicationService 配置管理应用服务
type ConfigApplicationService struct {
	configRepo   repository.VerificationConfigRepository
	templateRepo repository.SMSTemplateRepository
	logger       logiface.Logger
}

// NewConfigApplicationService 创建配置管理应用服务
func NewConfigApplicationService(
	configRepo repository.VerificationConfigRepository,
	templateRepo repository.SMSTemplateRepository,
	logger logiface.Logger,
) *ConfigApplicationService {
	return &ConfigApplicationService{
		configRepo:   configRepo,
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// CreateConfig 创建验证配置
func (s *ConfigApplicationService) CreateConfig(ctx context.Context, tenantID int64, req *dto.CreateConfigRequest) (*dto.ConfigResponse, error) {

	// 检查配置是否已存在
	exists, err := s.configRepo.ExistsByPurposeAndTarget(ctx, tenantID, req.Purpose, req.TargetType)
	if err != nil {
		s.logger.Error(ctx, "Failed to check config existence",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to check config existence: %w", err)
	}

	if exists {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config already exists for purpose %d and target type %d", req.Purpose, req.TargetType))
	}

	// 验证模板是否存在
	if err := s.validateTemplate(ctx, tenantID, req.TemplateCode); err != nil {
		return nil, err
	}

	// 创建配置实体
	config, err := entity.NewVerificationConfigWithContext(ctx, tenantID, req.Purpose, req.TargetType, req.TokenType, req.TemplateCode)
	if err != nil {
		s.logger.Error(ctx, "Failed to create config entity",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	// 设置配置属性
	config.TokenLength = req.TokenLength
	config.ExpireMinutes = req.ExpireMinutes
	config.MaxAttempts = req.MaxAttempts
	config.RateLimitPerMinute = req.RateLimitPerMinute
	config.RateLimitPerHour = req.RateLimitPerHour
	config.RateLimitPerDay = req.RateLimitPerDay

	// 保存配置
	if err := s.configRepo.Create(ctx, config); err != nil {
		s.logger.Error(ctx, "Failed to save config",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "Verification config created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", config.ID),
		logiface.Int("purpose", int(req.Purpose)),
		logiface.Int("target_type", int(req.TargetType)))

	return dto.NewConfigResponse(config), nil
}

// UpdateConfig 更新验证配置
func (s *ConfigApplicationService) UpdateConfig(ctx context.Context, tenantID int64, req *dto.UpdateConfigRequest) (*dto.ConfigResponse, error) {

	// 查找配置
	config, err := s.configRepo.FindByID(ctx, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find config",
			logiface.Error(err),
			logiface.Int64("config_id", req.ID))
		return nil, fmt.Errorf("failed to find config: %w", err)
	}

	if config == nil {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", req.ID))
	}

	// 检查租户权限
	if config.TenantID != tenantID {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", req.ID))
	}

	// 验证模板是否存在
	if err := s.validateTemplate(ctx, tenantID, req.TemplateCode); err != nil {
		return nil, err
	}

	// 更新配置属性
	config.TokenType = req.TokenType
	config.TokenLength = req.TokenLength
	config.ExpireMinutes = req.ExpireMinutes
	config.MaxAttempts = req.MaxAttempts
	config.RateLimitPerMinute = req.RateLimitPerMinute
	config.RateLimitPerHour = req.RateLimitPerHour
	config.RateLimitPerDay = req.RateLimitPerDay
	config.TemplateCode = req.TemplateCode
	config.IsActive = req.IsActive

	// 保存更新
	if err := s.configRepo.Update(ctx, config); err != nil {
		s.logger.Error(ctx, "Failed to update config",
			logiface.Error(err),
			logiface.Int64("config_id", req.ID))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "Verification config updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", config.ID))

	return dto.NewConfigResponse(config), nil
}

// GetConfig 获取验证配置
func (s *ConfigApplicationService) GetConfig(ctx context.Context, tenantID int64, configID int64) (*dto.ConfigResponse, error) {
	config, err := s.configRepo.FindByID(ctx, configID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find config",
			logiface.Error(err),
			logiface.Int64("config_id", configID))
		return nil, fmt.Errorf("failed to find config: %w", err)
	}

	if config == nil {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	// 检查租户权限
	if config.TenantID != tenantID {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	return dto.NewConfigResponse(config), nil
}

// ListConfigs 获取配置列表
func (s *ConfigApplicationService) ListConfigs(ctx context.Context, tenantID int64, req *dto.ConfigListRequest) ([]*dto.ConfigResponse, int64, error) {

	// 构建过滤器
	filter := &repository.VerificationConfigFilter{
		TenantID:   &tenantID,
		Purpose:    req.Purpose,
		TargetType: req.TargetType,
		TokenType:  req.TokenType,
		IsActive:   req.IsActive,
		Page:       req.Page,
		Size:       req.Size,
		OrderBy:    req.OrderBy,
		OrderDesc:  req.OrderDesc,
	}

	// 查询配置列表
	configs, total, err := s.configRepo.FindWithPagination(ctx, filter)
	if err != nil {
		s.logger.Error(ctx, "Failed to list configs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, fmt.Errorf("failed to list configs: %w", err)
	}

	responseConfigs := make([]*dto.ConfigResponse, len(configs))
	for i, config := range configs {
		responseConfigs[i] = dto.NewConfigResponse(config)
	}

	return responseConfigs, total, nil
}

// DeleteConfig 删除验证配置
func (s *ConfigApplicationService) DeleteConfig(ctx context.Context, tenantID int64, configID int64) error {
	// 查找配置
	config, err := s.configRepo.FindByID(ctx, configID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find config",
			logiface.Error(err),
			logiface.Int64("config_id", configID))
		return fmt.Errorf("failed to find config: %w", err)
	}

	if config == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	// 检查租户权限
	if config.TenantID != tenantID {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	// 删除配置
	if err := s.configRepo.Delete(ctx, configID); err != nil {
		s.logger.Error(ctx, "Failed to delete config",
			logiface.Error(err),
			logiface.Int64("config_id", configID))
		return fmt.Errorf("failed to delete config: %w", err)
	}

	s.logger.Info(ctx, "Verification config deleted successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	return nil
}

// EnableConfig 启用配置
func (s *ConfigApplicationService) EnableConfig(ctx context.Context, tenantID int64, configID int64) error {
	return s.updateConfigStatus(ctx, tenantID, configID, true)
}

// DisableConfig 禁用配置
func (s *ConfigApplicationService) DisableConfig(ctx context.Context, tenantID int64, configID int64) error {
	return s.updateConfigStatus(ctx, tenantID, configID, false)
}

// updateConfigStatus 更新配置状态
func (s *ConfigApplicationService) updateConfigStatus(ctx context.Context, tenantID int64, configID int64, isActive bool) error {
	// 查找配置
	config, err := s.configRepo.FindByID(ctx, configID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find config",
			logiface.Error(err),
			logiface.Int64("config_id", configID))
		return fmt.Errorf("failed to find config: %w", err)
	}

	if config == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	// 检查租户权限
	if config.TenantID != tenantID {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("config not found: %d", configID))
	}

	// 更新状态
	if isActive {
		if err := s.configRepo.EnableConfig(ctx, configID); err != nil {
			return fmt.Errorf("failed to enable config: %w", err)
		}
	} else {
		if err := s.configRepo.DisableConfig(ctx, configID); err != nil {
			return fmt.Errorf("failed to disable config: %w", err)
		}
	}

	action := "disabled"
	if isActive {
		action = "enabled"
	}

	s.logger.Info(ctx, fmt.Sprintf("Verification config %s successfully", action),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("config_id", configID))

	return nil
}

// validateTemplate 验证模板是否存在
func (s *ConfigApplicationService) validateTemplate(ctx context.Context, tenantID int64, templateCode string) error {
	template, err := s.templateRepo.GetEffectiveTemplate(ctx, tenantID, templateCode)
	if err != nil {
		return fmt.Errorf("failed to get template: %w", err)
	}

	if template == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %s", templateCode))
	}

	return nil
}

// BatchUpdateConfigs 批量更新配置
func (s *ConfigApplicationService) BatchUpdateConfigs(ctx context.Context, tenantID int64, req *dto.BatchConfigRequest) (*dto.BatchConfigResponse, error) {

	response := &dto.BatchConfigResponse{
		SuccessCount: 0,
		FailureCount: 0,
		Errors:       []string{},
	}

	// 验证所有配置都属于当前租户
	for _, id := range req.IDs {
		config, err := s.configRepo.FindByID(ctx, id)
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("config %d: failed to find", id))
			continue
		}

		if config == nil || config.TenantID != tenantID {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("config %d: not found", id))
			continue
		}
	}

	// 如果有验证失败的，直接返回
	if response.FailureCount > 0 {
		return response, nil
	}

	// 执行批量操作
	switch req.Operation {
	case "enable":
		err := s.configRepo.BatchUpdateStatus(ctx, req.IDs, true)
		if err != nil {
			response.FailureCount = len(req.IDs)
			response.Errors = append(response.Errors, fmt.Sprintf("batch enable failed: %v", err))
		} else {
			response.SuccessCount = len(req.IDs)
		}

	case "disable":
		err := s.configRepo.BatchUpdateStatus(ctx, req.IDs, false)
		if err != nil {
			response.FailureCount = len(req.IDs)
			response.Errors = append(response.Errors, fmt.Sprintf("batch disable failed: %v", err))
		} else {
			response.SuccessCount = len(req.IDs)
		}

	case "delete":
		for _, id := range req.IDs {
			if err := s.configRepo.Delete(ctx, id); err != nil {
				response.FailureCount++
				response.Errors = append(response.Errors, fmt.Sprintf("config %d: delete failed: %v", id, err))
			} else {
				response.SuccessCount++
			}
		}
	}

	s.logger.Info(ctx, "Batch config operation completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("operation", req.Operation),
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("failure_count", response.FailureCount))

	return response, nil
}

// CopySystemConfigs 复制系统配置到租户
func (s *ConfigApplicationService) CopySystemConfigs(ctx context.Context, tenantID int64) error {
	if err := s.configRepo.CopySystemConfigsToTenant(ctx, tenantID); err != nil {
		s.logger.Error(ctx, "Failed to copy system configs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return fmt.Errorf("failed to copy system configs: %w", err)
	}

	s.logger.Info(ctx, "System configs copied successfully",
		logiface.Int64("tenant_id", tenantID))

	return nil
}
