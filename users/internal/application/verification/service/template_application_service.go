package service

import (
	"context"
	"encoding/json"
	"fmt"

	"platforms-pkg/logiface"

	"platforms-user/internal/application/verification/dto"
	user_errors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/repository"
)

// TemplateApplicationService 模板管理应用服务
type TemplateApplicationService struct {
	templateRepo repository.SMSTemplateRepository
	logger       logiface.Logger
}

// NewTemplateApplicationService 创建模板管理应用服务
func NewTemplateApplicationService(
	templateRepo repository.SMSTemplateRepository,
	logger logiface.Logger,
) *TemplateApplicationService {
	return &TemplateApplicationService{
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// CreateTemplate 创建短信模板
func (s *TemplateApplicationService) CreateTemplate(ctx context.Context, tenantID int64, req *dto.CreateTemplateRequest) (*dto.TemplateResponse, error) {

	// 检查模板代码是否已存在
	exists, err := s.templateRepo.ExistsByCode(ctx, tenantID, req.Code)
	if err != nil {
		s.logger.Error(ctx, "Failed to check template existence",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("code", req.Code))
		return nil, fmt.Errorf("failed to check template existence: %w", err)
	}

	if exists {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template code already exists: %s", req.Code))
	}

	// 创建模板实体
	template, err := entity.NewSMSTemplateWithContext(ctx, tenantID, req.Name, req.Code, req.Description, req.Content, req.Variables)
	if err != nil {
		s.logger.Error(ctx, "Failed to create template entity",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	// 设置模板属性
	template.IsActive = true

	// 保存模板
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.Error(ctx, "Failed to save template",
			logiface.Error(err))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "SMS template created successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", template.ID),
		logiface.String("template_code", req.Code))

	return dto.NewTemplateResponse(template), nil
}

// UpdateTemplate 更新短信模板
func (s *TemplateApplicationService) UpdateTemplate(ctx context.Context, tenantID int64, req *dto.UpdateTemplateRequest) (*dto.TemplateResponse, error) {

	// 查找模板
	template, err := s.templateRepo.FindByID(ctx, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template",
			logiface.Error(err),
			logiface.Int64("template_id", req.ID))
		return nil, fmt.Errorf("failed to find template: %w", err)
	}

	if template == nil {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", req.ID))
	}

	// 检查租户权限（系统模板所有租户都可以修改）
	if template.TenantID != tenantID && !template.IsSystem {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", req.ID))
	}

	// 更新模板属性
	template.Name = req.Name
	template.Content = req.Content
	template.Description = req.Description
	template.IsActive = req.IsActive

	// 转换Variables类型
	variablesJSON, _ := json.Marshal(req.Variables)
	template.Variables = variablesJSON

	// 保存更新
	if err := s.templateRepo.Update(ctx, template); err != nil {
		s.logger.Error(ctx, "Failed to update template",
			logiface.Error(err),
			logiface.Int64("template_id", req.ID))
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, err.Error())
	}

	s.logger.Info(ctx, "SMS template updated successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", template.ID))

	return dto.NewTemplateResponse(template), nil
}

// GetTemplate 获取短信模板
func (s *TemplateApplicationService) GetTemplate(ctx context.Context, tenantID int64, templateID int64) (*dto.TemplateResponse, error) {
	template, err := s.templateRepo.FindByID(ctx, templateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return nil, fmt.Errorf("failed to find template: %w", err)
	}

	if template == nil {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	// 检查租户权限（系统模板所有租户都可以查看）
	if template.TenantID != tenantID && !template.IsSystem {
		return nil, user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	return dto.NewTemplateResponse(template), nil
}

// ListTemplates 获取模板列表
func (s *TemplateApplicationService) ListTemplates(ctx context.Context, tenantID int64, req *dto.TemplateListRequest) (*dto.TemplateListResponse, error) {

	// 构建过滤器
	filter := &entity.SMSTemplateFilter{
		TenantID: &tenantID,
		Keyword:  req.Keyword,
		IsActive: req.IsActive,
		IsSystem: req.IsSystem,
		Page:     req.Page,
		Size:     req.Size,
	}

	// 查询模板列表
	templates, total, err := s.templateRepo.FindWithPagination(ctx, filter)
	if err != nil {
		s.logger.Error(ctx, "Failed to list templates",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	return dto.NewTemplateListResponse(templates, total, req.Page, req.Size), nil
}

// DeleteTemplate 删除短信模板
func (s *TemplateApplicationService) DeleteTemplate(ctx context.Context, tenantID int64, templateID int64) error {
	// 查找模板
	template, err := s.templateRepo.FindByID(ctx, templateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return fmt.Errorf("failed to find template: %w", err)
	}

	if template == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	// 检查租户权限（系统模板不能被删除）
	if template.TenantID != tenantID || template.IsSystem {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	// 删除模板
	if err := s.templateRepo.Delete(ctx, templateID); err != nil {
		s.logger.Error(ctx, "Failed to delete template",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return fmt.Errorf("failed to delete template: %w", err)
	}

	s.logger.Info(ctx, "SMS template deleted successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	return nil
}

// EnableTemplate 启用模板
func (s *TemplateApplicationService) EnableTemplate(ctx context.Context, tenantID int64, templateID int64) error {
	return s.updateTemplateStatus(ctx, tenantID, templateID, true)
}

// DisableTemplate 禁用模板
func (s *TemplateApplicationService) DisableTemplate(ctx context.Context, tenantID int64, templateID int64) error {
	return s.updateTemplateStatus(ctx, tenantID, templateID, false)
}

// updateTemplateStatus 更新模板状态
func (s *TemplateApplicationService) updateTemplateStatus(ctx context.Context, tenantID int64, templateID int64, isActive bool) error {
	// 查找模板
	template, err := s.templateRepo.FindByID(ctx, templateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return fmt.Errorf("failed to find template: %w", err)
	}

	if template == nil {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	// 检查租户权限（系统模板所有租户都可以修改状态）
	if template.TenantID != tenantID && !template.IsSystem {
		return user_errors.NewUserError(user_errors.CodeSystemError, fmt.Sprintf("template not found: %d", templateID))
	}

	// 更新状态
	if isActive {
		if err := s.templateRepo.EnableTemplate(ctx, templateID); err != nil {
			return fmt.Errorf("failed to enable template: %w", err)
		}
	} else {
		if err := s.templateRepo.DisableTemplate(ctx, templateID); err != nil {
			return fmt.Errorf("failed to disable template: %w", err)
		}
	}

	action := "disabled"
	if isActive {
		action = "enabled"
	}

	s.logger.Info(ctx, fmt.Sprintf("SMS template %s successfully", action),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID))

	return nil
}

// BatchUpdateTemplates 批量更新模板
func (s *TemplateApplicationService) BatchUpdateTemplates(ctx context.Context, tenantID int64, req *dto.BatchTemplateRequest) (*dto.BatchTemplateResponse, error) {

	response := &dto.BatchTemplateResponse{
		SuccessCount: 0,
		FailureCount: 0,
		Errors:       []string{},
	}

	// 验证所有模板都属于当前租户且非系统模板
	for _, id := range req.IDs {
		template, err := s.templateRepo.FindByID(ctx, id)
		if err != nil {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("template %d: failed to find", id))
			continue
		}

		if template == nil || template.TenantID != tenantID {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("template %d: not found", id))
			continue
		}

		if template.IsSystem && req.Operation == "delete" {
			response.FailureCount++
			response.Errors = append(response.Errors, fmt.Sprintf("template %d: cannot delete system template", id))
			continue
		}
	}

	// 如果有验证失败的，直接返回
	if response.FailureCount > 0 {
		return response, nil
	}

	// 执行批量操作
	switch req.Operation {
	case "enable":
		err := s.templateRepo.BatchUpdateStatus(ctx, req.IDs, true)
		if err != nil {
			response.FailureCount = len(req.IDs)
			response.Errors = append(response.Errors, fmt.Sprintf("batch enable failed: %v", err))
		} else {
			response.SuccessCount = len(req.IDs)
		}

	case "disable":
		err := s.templateRepo.BatchUpdateStatus(ctx, req.IDs, false)
		if err != nil {
			response.FailureCount = len(req.IDs)
			response.Errors = append(response.Errors, fmt.Sprintf("batch disable failed: %v", err))
		} else {
			response.SuccessCount = len(req.IDs)
		}

	case "delete":
		for _, id := range req.IDs {
			if err := s.templateRepo.Delete(ctx, id); err != nil {
				response.FailureCount++
				response.Errors = append(response.Errors, fmt.Sprintf("template %d: delete failed: %v", id, err))
			} else {
				response.SuccessCount++
			}
		}
	}

	s.logger.Info(ctx, "Batch template operation completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("operation", req.Operation),
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("failure_count", response.FailureCount))

	return response, nil
}

// CopySystemTemplates 复制系统模板到租户
func (s *TemplateApplicationService) CopySystemTemplates(ctx context.Context, tenantID int64) error {
	if err := s.templateRepo.CopySystemTemplatesToTenant(ctx, tenantID); err != nil {
		s.logger.Error(ctx, "Failed to copy system templates",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return fmt.Errorf("failed to copy system templates: %w", err)
	}

	s.logger.Info(ctx, "System templates copied successfully",
		logiface.Int64("tenant_id", tenantID))

	return nil
}
