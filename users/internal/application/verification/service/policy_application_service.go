package service

import (
	"context"
	"fmt"
	"time"

	"platforms-pkg/logiface"

	verificationDto "platforms-user/internal/application/verification/dto"
	"platforms-user/internal/domain/verification/entity"
	"platforms-user/internal/domain/verification/errors"
	"platforms-user/internal/domain/verification/repository"
	"platforms-user/internal/domain/verification/service"
)

// PolicyApplicationService 策略应用服务
type PolicyApplicationService struct {
	policyRepo    repository.VerificationPolicyRepository
	expressionSvc *service.ExpressionService
	logger        logiface.Logger
}

// NewPolicyApplicationService 创建策略应用服务
func NewPolicyApplicationService(
	policyRepo repository.VerificationPolicyRepository,
	expressionSvc *service.ExpressionService,
	logger logiface.Logger,
) *PolicyApplicationService {
	return &PolicyApplicationService{
		policyRepo:    policyRepo,
		expressionSvc: expressionSvc,
		logger:        logger,
	}
}

// CreatePolicy 创建策略
func (s *PolicyApplicationService) CreatePolicy(ctx context.Context, req *verificationDto.CreatePolicyRequest) (*verificationDto.CreatePolicyResponse, error) {
	s.logger.Info(ctx, "创建验证策略", logiface.String("scene", req.Scene), logiface.String("dimension", req.Dimension))
	// 验证表达式语法
	if req.ConditionExpr != "" {
		if err := s.expressionSvc.ValidateExpression(req.ConditionExpr); err != nil {
			s.logger.Error(ctx, "表达式验证失败", logiface.Error(err), logiface.String("expression", req.ConditionExpr))
			return nil, errors.NewVerificationError(errors.CodeVerificationExprInvalid, err.Error())
		}
	}

	// 转换为实体
	policy := req.ToEntity()
	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()

	// 创建策略
	createdPolicy, err := s.policyRepo.Create(ctx, policy)
	if err != nil {
		s.logger.Error(ctx, "创建策略失败", logiface.Error(err))
		return nil, fmt.Errorf("创建策略失败: %w", err)
	}

	s.logger.Info(ctx, "策略创建成功", logiface.Int64("policy_id", createdPolicy.ID))
	return &verificationDto.CreatePolicyResponse{
		ID: createdPolicy.ID,
	}, nil
}

// UpdatePolicy 更新策略
func (s *PolicyApplicationService) UpdatePolicy(ctx context.Context, req *verificationDto.UpdatePolicyRequest) error {
	s.logger.Info(ctx, "更新验证策略", logiface.Int64("policy_id", req.PolicyID))

	// 获取现有策略
	existingPolicy, err := s.policyRepo.GetByID(ctx, req.PolicyID, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "获取策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("获取策略失败: %w", err)
	}
	if existingPolicy == nil {
		return errors.NewVerificationError(errors.CodeVerificationPolicyNotFound, "策略不存在")
	}

	// 验证表达式语法
	if req.ConditionExpr != "" {
		if err := s.expressionSvc.ValidateExpression(req.ConditionExpr); err != nil {
			s.logger.Error(ctx, "表达式验证失败", logiface.Error(err), logiface.String("expression", req.ConditionExpr))
			return errors.NewVerificationError(errors.CodeVerificationExprInvalid, err.Error())
		}
	}
	// 应用更新
	req.UpdateEntity(existingPolicy)
	existingPolicy.UpdatedAt = time.Now()
	// 更新策略
	if err := s.policyRepo.Update(ctx, existingPolicy); err != nil {
		s.logger.Error(ctx, "更新策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("更新策略失败: %w", err)
	}
	s.logger.Info(ctx, "策略更新成功", logiface.Int64("policy_id", req.PolicyID))
	return nil
}

// DeletePolicy 删除策略
func (s *PolicyApplicationService) DeletePolicy(ctx context.Context, req *verificationDto.DeletePolicyRequest) error {
	s.logger.Info(ctx, "删除验证策略", logiface.Int64("policy_id", req.PolicyID))

	// 检查策略是否存在
	policy, err := s.policyRepo.GetByID(ctx, req.PolicyID, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "获取策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("获取策略失败: %w", err)
	}
	if policy == nil {
		return errors.NewVerificationError(errors.CodeVerificationPolicyNotFound, "策略不存在")
	}

	// 删除策略
	if err := s.policyRepo.Delete(ctx, req.PolicyID, req.TenantID); err != nil {
		s.logger.Error(ctx, "删除策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("删除策略失败: %w", err)
	}

	s.logger.Info(ctx, "策略删除成功", logiface.Int64("policy_id", req.PolicyID))
	return nil
}

// SetPolicyStatus 设置策略状态
func (s *PolicyApplicationService) SetPolicyStatus(ctx context.Context, req *verificationDto.SetPolicyStatusRequest) error {
	s.logger.Info(ctx, "设置策略状态", logiface.Int64("policy_id", req.PolicyID), logiface.Bool("enabled", req.Enabled))

	// 检查策略是否存在
	policy, err := s.policyRepo.GetByID(ctx, req.PolicyID, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "获取策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("获取策略失败: %w", err)
	}
	if policy == nil {
		return errors.NewVerificationError(errors.CodeVerificationPolicyNotFound, "策略不存在")
	}

	// 设置状态
	if err := s.policyRepo.SetStatus(ctx, req.PolicyID, req.TenantID, req.Enabled); err != nil {
		s.logger.Error(ctx, "设置策略状态失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return fmt.Errorf("设置策略状态失败: %w", err)
	}

	s.logger.Info(ctx, "策略状态设置成功", logiface.Int64("policy_id", req.PolicyID), logiface.Bool("enabled", req.Enabled))
	return nil
}

// GetPolicyList 获取策略列表
func (s *PolicyApplicationService) GetPolicyList(ctx context.Context, req *verificationDto.PolicyListRequest) (*verificationDto.PolicyListResponse, error) {
	s.logger.Info(ctx, "获取策略列表", logiface.Int64("tenant_id", req.TenantID), logiface.Int("page", req.Page), logiface.Int("page_size", req.PageSize))

	// 获取策略列表
	params := &repository.ListPolicyParams{
		TenantID: req.TenantID,
		Scene:    req.Scene,
		IsActive: req.IsActive,
		Keyword:  req.Keyword,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	policies, total, err := s.policyRepo.List(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "获取策略列表失败", logiface.Error(err))
		return nil, fmt.Errorf("获取策略列表失败: %w", err)
	}

	// 转换为响应
	response := &verificationDto.PolicyListResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     verificationDto.FromEntities(policies),
	}

	s.logger.Info(ctx, "策略列表获取成功", logiface.Int("count", len(policies)), logiface.Int64("total", total))
	return response, nil
}

// GetPolicyDetail 获取策略详情
func (s *PolicyApplicationService) GetPolicyDetail(ctx context.Context, req *verificationDto.PolicyDetailRequest) (*verificationDto.PolicyResponse, error) {
	s.logger.Info(ctx, "获取策略详情", logiface.Int64("policy_id", req.PolicyID))

	// 获取策略
	policy, err := s.policyRepo.GetByID(ctx, req.PolicyID, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "获取策略失败", logiface.Error(err), logiface.Int64("policy_id", req.PolicyID))
		return nil, fmt.Errorf("获取策略失败: %w", err)
	}
	if policy == nil {
		return nil, errors.NewVerificationError(errors.CodeVerificationPolicyNotFound, "策略不存在")
	}

	s.logger.Info(ctx, "策略详情获取成功", logiface.Int64("policy_id", req.PolicyID))
	return verificationDto.FromEntity(policy), nil
}

// ValidateExpression 验证表达式
func (s *PolicyApplicationService) ValidateExpression(ctx context.Context, req *verificationDto.ExprValidateRequest) (*verificationDto.ExprValidateResponse, error) {
	s.logger.Info(ctx, "验证表达式", logiface.String("expression", req.ConditionExpr))

	// 验证表达式
	err := s.expressionSvc.ValidateExpression(req.ConditionExpr)
	if err != nil {
		s.logger.Error(ctx, "表达式验证失败", logiface.Error(err), logiface.String("expression", req.ConditionExpr))
		return &verificationDto.ExprValidateResponse{
			Valid: false,
			Error: err.Error(),
		}, nil
	}

	s.logger.Info(ctx, "表达式验证成功", logiface.String("expression", req.ConditionExpr))
	return &verificationDto.ExprValidateResponse{
		Valid: true,
	}, nil
}

// TestExpression 测试表达式
func (s *PolicyApplicationService) TestExpression(ctx context.Context, req *verificationDto.ExprTestRequest) (*verificationDto.ExprTestResponse, error) {
	s.logger.Info(ctx, "测试表达式", logiface.String("expression", req.ConditionExpr))

	// 测试表达式
	result, err := s.expressionSvc.TestExpression(req.ConditionExpr, req.Sample)
	if err != nil {
		s.logger.Error(ctx, "表达式测试失败", logiface.Error(err), logiface.String("expression", req.ConditionExpr))
		return &verificationDto.ExprTestResponse{
			Result: false,
		}, nil
	}

	s.logger.Info(ctx, "表达式测试成功", logiface.String("expression", req.ConditionExpr), logiface.Any("result", result))
	return &verificationDto.ExprTestResponse{
		Result: result,
	}, nil
}

// GetActivePoliciesByScene 根据业务场景获取激活的策略
func (s *PolicyApplicationService) GetActivePoliciesByScene(ctx context.Context, tenantID int64, businessScene string) ([]*entity.VerificationPolicy, error) {
	s.logger.Info(ctx, "获取场景激活策略", logiface.Int64("tenant_id", tenantID), logiface.String("business_scene", businessScene))

	policies, err := s.policyRepo.GetActivePoliciesByScene(ctx, tenantID, businessScene)
	if err != nil {
		s.logger.Error(ctx, "获取场景激活策略失败", logiface.Error(err))
		return nil, fmt.Errorf("获取场景激活策略失败: %w", err)
	}

	s.logger.Info(ctx, "场景激活策略获取成功", logiface.Int("count", len(policies)))
	return policies, nil
}
