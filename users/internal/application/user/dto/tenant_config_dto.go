package dto

import (
	"platforms-user/internal/domain/user/entity"
)

// GetTenantConfigRequest 获取租户配置请求
type GetTenantConfigRequest struct {
	TenantID int64 `json:"tenant_id" binding:"required"`
}

// UpdateTenantConfigRequest 更新租户配置请求
type UpdateTenantConfigRequest struct {
	TenantID    int64  `json:"tenant_id" binding:"required"`
	ConfigKey   string `json:"config_key" binding:"required"`
	ConfigValue string `json:"config_value" binding:"required"`
}

// GetPasswordPolicyRequest 获取密码策略请求
type GetPasswordPolicyRequest struct {
	TenantID int64 `json:"tenant_id" binding:"required"`
}

// UpdatePasswordPolicyRequest 更新密码策略请求
type UpdatePasswordPolicyRequest struct {
	TenantID int64                  `json:"tenant_id" binding:"required"`
	Policy   *entity.PasswordPolicy `json:"policy" binding:"required"`
}

// GetRegistrationMethodsRequest 获取注册方式请求
type GetRegistrationMethodsRequest struct {
	TenantID int64 `json:"tenant_id" binding:"required"`
}

// UpdateRegistrationMethodsRequest 更新注册方式请求
type UpdateRegistrationMethodsRequest struct {
	TenantID int64                       `json:"tenant_id" binding:"required"`
	Methods  *entity.RegistrationMethods `json:"methods" binding:"required"`
}

// TenantConfigResponse 租户配置响应
type TenantConfigResponse struct {
	TenantID    int64  `json:"tenant_id"`
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	ConfigType  string `json:"config_type"`
}

// TenantPasswordPolicyResponse 租户密码策略响应
type TenantPasswordPolicyResponse struct {
	TenantID int64                  `json:"tenant_id"`
	Policy   *entity.PasswordPolicy `json:"policy"`
}

// RegistrationMethodsResponse 注册方式响应
type RegistrationMethodsResponse struct {
	TenantID int64                       `json:"tenant_id"`
	Methods  *entity.RegistrationMethods `json:"methods"`
}

// TenantConfigListResponse 租户配置列表响应
type TenantConfigListResponse struct {
	Configs []*TenantConfigResponse `json:"configs"`
	Total   int64                   `json:"total"`
}
