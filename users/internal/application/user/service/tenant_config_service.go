package service

import (
	"context"
	"platforms-pkg/logiface"
	"platforms-user/internal/application/user/dto"
	userErrors "platforms-user/internal/domain/errors"
	"platforms-user/internal/domain/user/entity"
	"platforms-user/internal/domain/user/repository"
)

// 密码策略常量
const (
	MinPasswordLength = 6
	MaxPasswordLength = 128
)

// TenantConfigService 租户配置应用服务
type TenantConfigService struct {
	tenantConfigRepo repository.TenantConfigRepository
	logger           logiface.Logger
}

// Ensure TenantConfigService implements TenantConfigServiceInterface
var _ TenantConfigServiceInterface = (*TenantConfigService)(nil)

// NewTenantConfigService 创建租户配置应用服务
func NewTenantConfigService(tenantConfigRepo repository.TenantConfigRepository, logger logiface.Logger) *TenantConfigService {
	return &TenantConfigService{
		tenantConfigRepo: tenantConfigRepo,
		logger:           logger,
	}
}

// GetPasswordPolicy 获取密码策略
func (s *TenantConfigService) GetPasswordPolicy(ctx context.Context, req *dto.GetPasswordPolicyRequest) (*dto.TenantPasswordPolicyResponse, error) {
	s.logger.Info(ctx, "Getting password policy", logiface.Int64("tenant_id", req.TenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, req.TenantID, entity.ConfigKeyPasswordPolicy)
	if err != nil {
		return nil, err
	}

	var policy *entity.PasswordPolicy
	if config != nil {
		// 解析租户配置
		policy = &entity.PasswordPolicy{}
		if err := policy.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse password policy config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
			return nil, userErrors.NewSystemError("解析密码策略配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		policy = entity.NewDefaultPasswordPolicy()
	}

	return &dto.TenantPasswordPolicyResponse{
		TenantID: req.TenantID,
		Policy:   policy,
	}, nil
}

// UpdatePasswordPolicy 更新密码策略
func (s *TenantConfigService) UpdatePasswordPolicy(ctx context.Context, req *dto.UpdatePasswordPolicyRequest) error {
	s.logger.Info(ctx, "Updating password policy", logiface.Int64("tenant_id", req.TenantID))

	// 验证密码策略
	if err := s.validatePasswordPolicy(req.Policy); err != nil {
		s.logger.Warn(ctx, "Password policy validation failed", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码策略验证失败: "+err.Error())
	}

	// 转换为JSON
	configValue, err := req.Policy.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize password policy", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return userErrors.NewSystemError("序列化密码策略失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    req.TenantID,
		ConfigKey:   entity.ConfigKeyPasswordPolicy,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// GetRegistrationMethods 获取注册方式配置
func (s *TenantConfigService) GetRegistrationMethods(ctx context.Context, req *dto.GetRegistrationMethodsRequest) (*dto.RegistrationMethodsResponse, error) {
	s.logger.Info(ctx, "Getting registration methods", logiface.Int64("tenant_id", req.TenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, req.TenantID, entity.ConfigKeyRegistrationMethods)
	if err != nil {
		return nil, err
	}

	var methods *entity.RegistrationMethods
	if config != nil {
		// 解析租户配置
		methods = &entity.RegistrationMethods{}
		if err := methods.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse registration methods config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
			return nil, userErrors.NewSystemError("解析注册方式配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		methods = entity.NewDefaultRegistrationMethods()
	}

	return &dto.RegistrationMethodsResponse{
		TenantID: req.TenantID,
		Methods:  methods,
	}, nil
}

// UpdateRegistrationMethods 更新注册方式配置
func (s *TenantConfigService) UpdateRegistrationMethods(ctx context.Context, req *dto.UpdateRegistrationMethodsRequest) error {
	s.logger.Info(ctx, "Updating registration methods", logiface.Int64("tenant_id", req.TenantID))

	// 转换为JSON
	configValue, err := req.Methods.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize registration methods", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return userErrors.NewSystemError("序列化注册方式配置失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    req.TenantID,
		ConfigKey:   entity.ConfigKeyRegistrationMethods,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// GetTenantConfigs 获取租户所有配置
func (s *TenantConfigService) GetTenantConfigs(ctx context.Context, tenantID int64) (*dto.TenantConfigListResponse, error) {
	s.logger.Info(ctx, "Getting tenant configs", logiface.Int64("tenant_id", tenantID))

	configs, err := s.tenantConfigRepo.FindByTenantID(ctx, tenantID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tenant configs", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return nil, userErrors.NewDatabaseError("获取租户配置失败", err.Error())
	}

	// 转换为响应格式
	configResponses := make([]*dto.TenantConfigResponse, len(configs))
	for i, config := range configs {
		configResponses[i] = &dto.TenantConfigResponse{
			TenantID:    config.TenantID,
			ConfigKey:   config.ConfigKey,
			ConfigValue: config.ConfigValue,
			ConfigType:  config.ConfigType,
		}
	}

	return &dto.TenantConfigListResponse{
		Configs: configResponses,
		Total:   int64(len(configResponses)),
	}, nil
}

// GetConfig 获取租户的单个配置项
func (s *TenantConfigService) GetConfig(ctx context.Context, tenantID int64, configKey string) (*entity.TenantConfig, error) {
	s.logger.Debug(ctx, "Getting tenant config",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("config_key", configKey))

	config, err := s.tenantConfigRepo.GetEffectiveConfig(ctx, tenantID, configKey)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tenant config",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("config_key", configKey))
		return nil, userErrors.NewDatabaseError("获取租户配置失败", err.Error())
	}

	return config, nil
}

// UpdateTenantConfig 更新租户配置
func (s *TenantConfigService) UpdateTenantConfig(ctx context.Context, req *dto.UpdateTenantConfigRequest) error {
	s.logger.Info(ctx, "Updating tenant config", logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))

	// 创建UpsertConfigRequest对象
	upsertReq := &dto.UpsertConfigRequest{
		TenantID:    req.TenantID,
		ConfigKey:   req.ConfigKey,
		ConfigValue: req.ConfigValue,
		ConfigType:  "json",
	}

	// 保存配置
	if err := s.tenantConfigRepo.UpsertConfig(ctx, upsertReq); err != nil {
		s.logger.Error(ctx, "Failed to update tenant config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
		return userErrors.NewDatabaseError("更新租户配置失败", err.Error())
	}

	s.logger.Info(ctx, "Tenant config updated successfully", logiface.Int64("tenant_id", req.TenantID), logiface.String("config_key", req.ConfigKey))
	return nil
}

// CopySystemConfigsToTenant 复制系统配置到租户
func (s *TenantConfigService) CopySystemConfigsToTenant(ctx context.Context, tenantID int64) error {
	s.logger.Info(ctx, "Copying system configs to tenant", logiface.Int64("tenant_id", tenantID))

	if err := s.tenantConfigRepo.CopySystemConfigsToTenant(ctx, tenantID); err != nil {
		s.logger.Error(ctx, "Failed to copy system configs to tenant", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return userErrors.NewDatabaseError("复制系统配置到租户失败", err.Error())
	}

	s.logger.Info(ctx, "System configs copied to tenant successfully", logiface.Int64("tenant_id", tenantID))
	return nil
}

// validatePasswordPolicy 验证密码策略
func (s *TenantConfigService) validatePasswordPolicy(policy *entity.PasswordPolicy) error {
	if policy.MinLength < MinPasswordLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最小长度不能小于6位")
	}
	if policy.MaxLength > MaxPasswordLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最大长度不能超过128位")
	}
	if policy.MinLength > policy.MaxLength {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码最小长度不能大于最大长度")
	}
	if policy.PasswordHistoryCount < 0 {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码历史记录数不能为负数")
	}
	if policy.ExpireDays < 0 {
		return userErrors.NewBusinessError(userErrors.CodePasswordPolicyViolated, "密码过期天数不能为负数")
	}
	return nil
}

// GetTenantInfo 获取租户信息配置
func (s *TenantConfigService) GetTenantInfo(ctx context.Context, req *dto.GetTenantInfoRequest) (*dto.TenantInfoResponse, error) {
	s.logger.Info(ctx, "Getting tenant info", logiface.Int64("tenant_id", req.TenantID))

	// 使用公共方法获取配置
	config, err := s.GetConfig(ctx, req.TenantID, entity.ConfigKeyTenantInfo)
	if err != nil {
		return nil, err
	}

	var info *entity.TenantInfo
	if config != nil {
		// 解析租户配置
		info = &entity.TenantInfo{}
		if err := info.FromJSON(config.ConfigValue); err != nil {
			s.logger.Error(ctx, "Failed to parse tenant info config", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
			return nil, userErrors.NewSystemError("解析租户信息配置失败", err.Error())
		}
	} else {
		// 使用默认配置
		info = entity.NewDefaultTenantInfo()
	}

	return &dto.TenantInfoResponse{
		TenantID: req.TenantID,
		Info:     info,
	}, nil
}

// UpdateTenantInfo 更新租户信息配置
func (s *TenantConfigService) UpdateTenantInfo(ctx context.Context, req *dto.UpdateTenantInfoRequest) error {
	s.logger.Info(ctx, "Updating tenant info", logiface.Int64("tenant_id", req.TenantID))

	// 验证租户信息
	if err := s.validateTenantInfo(req.Info); err != nil {
		s.logger.Warn(ctx, "Tenant info validation failed", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return err // 直接返回验证错误，因为validateTenantInfo已经使用了NewParameterValidationFailedError
	}

	// 转换为JSON
	configValue, err := req.Info.ToJSON()
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize tenant info", logiface.Error(err), logiface.Int64("tenant_id", req.TenantID))
		return userErrors.NewSystemError("序列化租户信息失败", err.Error())
	}

	// 使用公共方法更新配置
	updateReq := &dto.UpdateTenantConfigRequest{
		TenantID:    req.TenantID,
		ConfigKey:   entity.ConfigKeyTenantInfo,
		ConfigValue: configValue,
	}

	return s.UpdateTenantConfig(ctx, updateReq)
}

// validateTenantInfo 验证租户信息
func (s *TenantConfigService) validateTenantInfo(info *entity.TenantInfo) error {
	if info.Type != "enterprise" && info.Type != "personal" {
		return userErrors.NewParameterValidationFailedError("type", "租户类型必须是 enterprise 或 personal")
	}
	if info.SystemName == "" {
		return userErrors.NewParameterValidationFailedError("system_name", "系统名称不能为空")
	}
	if len(info.SystemName) > 100 {
		return userErrors.NewParameterValidationFailedError("system_name", "系统名称长度不能超过100个字符")
	}
	if info.ServiceEmail != "" && len(info.ServiceEmail) > 100 {
		return userErrors.NewParameterValidationFailedError("service_email", "客服邮箱长度不能超过100个字符")
	}
	if len(info.Address) > 500 {
		return userErrors.NewParameterValidationFailedError("address", "地址长度不能超过500个字符")
	}
	if len(info.ContactPerson) > 50 {
		return userErrors.NewParameterValidationFailedError("contact_person", "联系人长度不能超过50个字符")
	}
	if len(info.ContactPhone) > 20 {
		return userErrors.NewParameterValidationFailedError("contact_phone", "联系电话长度不能超过20个字符")
	}
	if len(info.Website) > 200 {
		return userErrors.NewParameterValidationFailedError("website", "网站地址长度不能超过200个字符")
	}
	if len(info.Description) > 1000 {
		return userErrors.NewParameterValidationFailedError("description", "描述长度不能超过1000个字符")
	}
	return nil
}
