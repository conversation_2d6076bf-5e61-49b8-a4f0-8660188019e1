/*
 Navicat Premium Data Transfer

 Source Server         : nas-mysql-remote
 Source Server Type    : MySQL
 Source Server Version : 90100 (9.1.0)
 Source Host           : **************:3308
 Source Schema         : platforms-user

 Target Server Type    : MySQL
 Target Server Version : 90100 (9.1.0)
 File Encoding         : 65001

 Date: 28/07/2025 19:22:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for audit_log
-- ----------------------------
DROP TABLE IF EXISTS `audit_log`;
CREATE TABLE `audit_log` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：login, logout, create, update, delete等',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型：user, role, permission等',
  `resource_id` bigint DEFAULT NULL COMMENT '资源ID',
  `details` json DEFAULT NULL COMMENT '操作详情（JSON格式）',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_audit_tenant_id` (`tenant_id`),
  KEY `idx_audit_user_id` (`user_id`),
  KEY `idx_audit_action` (`action`),
  KEY `idx_audit_resource` (`resource_type`,`resource_id`),
  KEY `idx_audit_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- ----------------------------
-- Records of audit_log
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for auth_sessions
-- ----------------------------
DROP TABLE IF EXISTS `auth_sessions`;
CREATE TABLE `auth_sessions` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，UUID格式',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `jti` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'JWT ID，用于令牌撤销和验证',
  `access_token_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问令牌哈希，用于快速验证',
  `refresh_token_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '刷新令牌哈希，用于快速验证',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '会话状态：active-活跃，expired-过期，revoked-撤销，logged_out-登出',
  `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型：desktop-桌面，mobile-移动，tablet-平板',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `os_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统版本',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `browser_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器版本',
  `device_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备型号',
  `screen_size` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '屏幕尺寸',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '时区',
  `fingerprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备指纹',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `last_used_at` datetime NOT NULL COMMENT '最后使用时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_auth_sessions_jti` (`jti`),
  KEY `idx_auth_sessions_user_id` (`user_id`),
  KEY `idx_auth_sessions_tenant_id` (`tenant_id`),
  KEY `idx_auth_sessions_status` (`status`),
  KEY `idx_auth_sessions_expires_at` (`expires_at`),
  KEY `idx_auth_sessions_jti` (`jti`),
  KEY `idx_auth_sessions_access_token_hash` (`access_token_hash`),
  KEY `idx_auth_sessions_refresh_token_hash` (`refresh_token_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='认证会话表（JTI + 令牌哈希方案）';

-- ----------------------------
-- Records of auth_sessions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for blocked_ips
-- ----------------------------
DROP TABLE IF EXISTS `blocked_ips`;
CREATE TABLE `blocked_ips` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '阻止原因',
  `blocked_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '阻止时间',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_blocked_ips_tenant_id` (`tenant_id`),
  KEY `idx_blocked_ips_ip_address` (`ip_address`),
  KEY `idx_blocked_ips_expires_at` (`expires_at`),
  KEY `idx_blocked_ips_tenant_ip` (`tenant_id`,`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP阻止表';

-- ----------------------------
-- Records of blocked_ips
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `parent_id` bigint DEFAULT NULL COMMENT '父部门ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门编码',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '部门描述',
  `level` int NOT NULL DEFAULT '1' COMMENT '部门层级',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '部门状态：active-活跃，disabled-禁用',
  `manager_id` bigint DEFAULT NULL COMMENT '部门负责人ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_department_tenant_code` (`tenant_id`,`code`),
  KEY `idx_department_tenant_id` (`tenant_id`),
  KEY `idx_department_parent_id` (`parent_id`),
  KEY `idx_department_manager_id` (`manager_id`),
  KEY `idx_department_status` (`status`),
  KEY `idx_department_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- ----------------------------
-- Records of departments
-- ----------------------------
BEGIN;
INSERT INTO `departments` (`id`, `tenant_id`, `parent_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `manager_id`, `created_at`, `updated_at`, `deleted_at`) VALUES (727867074570883072, 1, NULL, '测试', 'LNS:001', '测试描述', 1, 1, 'active', NULL, '2025-07-01 12:43:54', '2025-07-01 12:43:54', NULL);
COMMIT;

-- ----------------------------
-- Table structure for file_records
-- ----------------------------
DROP TABLE IF EXISTS `file_records`;
CREATE TABLE `file_records` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `user_id` bigint NOT NULL COMMENT '上传用户ID',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `file_hash` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件MD5哈希',
  `storage_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储路径',
  `storage_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储类型：oss, local',
  `access_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '访问URL',
  `is_temporary` tinyint(1) DEFAULT '1' COMMENT '是否为临时文件',
  `is_permanent` tinyint(1) DEFAULT '0' COMMENT '是否已标记为永久',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，deleted-已删除',
  `meta` text COLLATE utf8mb4_unicode_ci COMMENT '元数据信息，JSON格式',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_file_tenant_id` (`tenant_id`),
  KEY `idx_file_user_id` (`user_id`),
  KEY `idx_file_scene_code` (`scene_code`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_file_temporary` (`is_temporary`),
  KEY `idx_file_permanent` (`is_permanent`),
  KEY `idx_file_expire_at` (`expire_at`),
  KEY `idx_file_status` (`status`),
  KEY `idx_file_created_at` (`created_at`),
  KEY `idx_file_deleted_at` (`deleted_at`),
  KEY `idx_file_storage_type` (`storage_type`),
  KEY `idx_file_file_type` (`file_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件记录表';

-- ----------------------------
-- Records of file_records
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for file_upload_configs
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_configs`;
CREATE TABLE `file_upload_configs` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `scene_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景名称',
  `allowed_types` text COLLATE utf8mb4_unicode_ci COMMENT '允许的文件类型，JSON格式',
  `max_file_size` bigint NOT NULL COMMENT '最大文件大小（字节）',
  `is_temporary` tinyint(1) DEFAULT '0' COMMENT '是否为临时文件',
  `enable_dedup` tinyint(1) DEFAULT '0' COMMENT '是否开启文件去重',
  `temp_expire_time` int DEFAULT '30' COMMENT '临时文件过期时间（分钟）',
  `is_system_scene` tinyint(1) DEFAULT '0' COMMENT '是否为系统场景',
  `extra_config` text COLLATE utf8mb4_unicode_ci COMMENT '额外配置信息，JSON格式',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，disabled-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_tenant_scene` (`tenant_id`,`scene_code`),
  KEY `idx_config_tenant_id` (`tenant_id`),
  KEY `idx_config_scene_code` (`scene_code`),
  KEY `idx_config_status` (`status`),
  KEY `idx_config_system_scene` (`is_system_scene`),
  KEY `idx_config_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传配置表';

-- ----------------------------
-- Records of file_upload_configs
-- ----------------------------
BEGIN;
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1001, 1, 'general', '通用文件上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\"]', 104857600, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-07-10 17:56:21');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1002, 1, 'image', '图片上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"bmp\",\"webp\"]', 20971520, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-07-10 17:56:21');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1003, 1, 'document', '文档上传', '[\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\"]', 52428800, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-07-10 17:56:21');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1004, 1, 'temporary', '临时文件上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\",\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"txt\"]', 10485760, 1, 0, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-07-10 17:56:21');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1005, 1, 'avatar', '头像上传', '[\"jpg\",\"jpeg\",\"png\",\"gif\"]', 5242880, 1, 1, 0, 0, '', 'active', '2025-07-10 17:56:21', '2025-07-21 13:27:52');
INSERT INTO `file_upload_configs` (`id`, `tenant_id`, `scene_code`, `scene_name`, `allowed_types`, `max_file_size`, `is_temporary`, `enable_dedup`, `temp_expire_time`, `is_system_scene`, `extra_config`, `status`, `created_at`, `updated_at`) VALUES (1006, 1, 'attachment', '附件上传', '[\"pdf\",\"doc\",\"docx\",\"xls\",\"xlsx\",\"ppt\",\"pptx\",\"txt\",\"zip\",\"rar\",\"7z\"]', 104857600, 0, 1, 30, 1, NULL, 'active', '2025-07-10 17:56:21', '2025-07-10 17:56:21');
COMMIT;

-- ----------------------------
-- Table structure for id_allocation
-- ----------------------------
DROP TABLE IF EXISTS `id_allocation`;
CREATE TABLE `id_allocation` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sequence_id` bigint NOT NULL,
  `tenant_id` bigint NOT NULL,
  `business_type` varchar(50) NOT NULL DEFAULT '' COMMENT '业务类型',
  `start_value` bigint NOT NULL,
  `end_value` bigint NOT NULL,
  `segment_size` int NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'AVAILABLE',
  `allocated_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sequence_tenant_range` (`sequence_id`,`tenant_id`,`start_value`,`end_value`),
  KEY `idx_sequence_tenant_status` (`sequence_id`,`tenant_id`,`status`),
  KEY `idx_business_tenant_status` (`business_type`,`tenant_id`,`status`),
  KEY `idx_business_type` (`business_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1865 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='ID生成预分配表';

-- ----------------------------
-- Records of id_allocation
-- ----------------------------
BEGIN;
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1785, 4, 1, 'user', 48023, 48023, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1786, 4, 1, 'user', 48024, 48024, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1787, 4, 1, 'user', 48025, 48025, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1788, 4, 1, 'user', 48026, 48026, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1789, 4, 1, 'user', 48027, 48027, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1790, 5, 1, 'tenant', 48026, 48026, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1791, 5, 1, 'tenant', 48027, 48027, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1792, 5, 1, 'tenant', 48028, 48028, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1793, 5, 1, 'tenant', 48029, 48029, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1794, 5, 1, 'tenant', 48030, 48030, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:19', '2025-07-28 11:12:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1795, 6, 1, 'role', 48063, 48063, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1796, 6, 1, 'role', 48064, 48064, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1797, 6, 1, 'role', 48065, 48065, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1798, 6, 1, 'role', 48066, 48066, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1799, 6, 1, 'role', 48067, 48067, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1800, 7, 1, 'permission', 48059, 48059, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1801, 7, 1, 'permission', 48060, 48060, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1802, 7, 1, 'permission', 48061, 48061, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1803, 7, 1, 'permission', 48062, 48062, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1804, 7, 1, 'permission', 48063, 48063, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1805, 8, 1, 'department', 48059, 48059, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1806, 8, 1, 'department', 48060, 48060, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1807, 8, 1, 'department', 48061, 48061, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1808, 8, 1, 'department', 48062, 48062, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1809, 8, 1, 'department', 48063, 48063, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1810, 9, 1, 'position', 48059, 48059, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1811, 9, 1, 'position', 48060, 48060, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1812, 9, 1, 'position', 48061, 48061, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1813, 9, 1, 'position', 48062, 48062, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1814, 9, 1, 'position', 48063, 48063, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1815, 14, 1, 'resource', 3098, 3098, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1816, 14, 1, 'resource', 3099, 3099, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1817, 14, 1, 'resource', 3100, 3100, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1818, 14, 1, 'resource', 3101, 3101, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1819, 14, 1, 'resource', 3102, 3102, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:20', '2025-07-28 11:12:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1820, 28, 0, 'email_account', 34, 34, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:21', '2025-07-28 11:12:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1821, 28, 0, 'email_account', 35, 35, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:21', '2025-07-28 11:12:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1822, 28, 0, 'email_account', 36, 36, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:21', '2025-07-28 11:12:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1823, 28, 0, 'email_account', 37, 37, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:21', '2025-07-28 11:12:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1824, 28, 0, 'email_account', 38, 38, 1, 'AVAILABLE', NULL, '2025-07-28 11:12:21', '2025-07-28 11:12:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1825, 4, 1, 'user', 48028, 48028, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1826, 4, 1, 'user', 48029, 48029, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1827, 4, 1, 'user', 48030, 48030, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1828, 4, 1, 'user', 48031, 48031, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1829, 4, 1, 'user', 48032, 48032, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1830, 5, 1, 'tenant', 48031, 48031, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1831, 5, 1, 'tenant', 48032, 48032, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1832, 5, 1, 'tenant', 48033, 48033, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1833, 5, 1, 'tenant', 48034, 48034, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1834, 5, 1, 'tenant', 48035, 48035, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:19', '2025-07-28 11:17:19');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1835, 6, 1, 'role', 48068, 48068, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1836, 6, 1, 'role', 48069, 48069, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1837, 6, 1, 'role', 48070, 48070, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1838, 6, 1, 'role', 48071, 48071, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1839, 6, 1, 'role', 48072, 48072, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1840, 7, 1, 'permission', 48064, 48064, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1841, 7, 1, 'permission', 48065, 48065, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1842, 7, 1, 'permission', 48066, 48066, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1843, 7, 1, 'permission', 48067, 48067, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1844, 7, 1, 'permission', 48068, 48068, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1845, 8, 1, 'department', 48064, 48064, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1846, 8, 1, 'department', 48065, 48065, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1847, 8, 1, 'department', 48066, 48066, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1848, 8, 1, 'department', 48067, 48067, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1849, 8, 1, 'department', 48068, 48068, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1850, 9, 1, 'position', 48064, 48064, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1851, 9, 1, 'position', 48065, 48065, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1852, 9, 1, 'position', 48066, 48066, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1853, 9, 1, 'position', 48067, 48067, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1854, 9, 1, 'position', 48068, 48068, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:20', '2025-07-28 11:17:20');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1855, 14, 1, 'resource', 3103, 3103, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1856, 14, 1, 'resource', 3104, 3104, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1857, 14, 1, 'resource', 3105, 3105, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1858, 14, 1, 'resource', 3106, 3106, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1859, 14, 1, 'resource', 3107, 3107, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1860, 28, 0, 'email_account', 39, 39, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1861, 28, 0, 'email_account', 40, 40, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1862, 28, 0, 'email_account', 41, 41, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1863, 28, 0, 'email_account', 42, 42, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
INSERT INTO `id_allocation` (`id`, `sequence_id`, `tenant_id`, `business_type`, `start_value`, `end_value`, `segment_size`, `status`, `allocated_at`, `created_at`, `updated_at`) VALUES (1864, 28, 0, 'email_account', 43, 43, 1, 'AVAILABLE', NULL, '2025-07-28 11:17:21', '2025-07-28 11:17:21');
COMMIT;

-- ----------------------------
-- Table structure for id_sequence
-- ----------------------------
DROP TABLE IF EXISTS `id_sequence`;
CREATE TABLE `id_sequence` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `business_type` varchar(50) NOT NULL,
  `sequence_name` varchar(100) NOT NULL,
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '序列描述',
  `tenant_id` bigint NOT NULL,
  `current_value` bigint NOT NULL DEFAULT '0',
  `increment_step` int NOT NULL DEFAULT '1' COMMENT '步长',
  `cache_size` int NOT NULL DEFAULT '100' COMMENT '缓存大小',
  `max_value` bigint NOT NULL DEFAULT '100' COMMENT '最大值',
  `min_value` bigint NOT NULL DEFAULT '1',
  `threshold` int NOT NULL DEFAULT '20' COMMENT '预分配阈值百分比',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `remarks` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sequence_business_tenant` (`business_type`,`tenant_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of id_sequence
-- ----------------------------
BEGIN;
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (4, 'user', 'user_id', '', 1, 48032, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:19');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (5, 'tenant', 'tenant_id', '', 1, 48035, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:19');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (6, 'role', 'role_id', '', 1, 48072, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:19');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (7, 'permission', 'permission_id', '', 1, 48068, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:20');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (8, 'department', 'department_id', '', 1, 48068, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:20');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (9, 'position', 'position_id', '', 1, 48068, 1, 10, 0, 1, 20, 1, NULL, '2025-07-06 05:50:19', '2025-07-28 11:17:20');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (14, 'resource', '系统资源', '', 1, 3107, 1, 10, 0, 1, 20, 1, '', '2025-07-07 02:57:54', '2025-07-28 11:17:21');
INSERT INTO `id_sequence` (`id`, `business_type`, `sequence_name`, `description`, `tenant_id`, `current_value`, `increment_step`, `cache_size`, `max_value`, `min_value`, `threshold`, `is_active`, `remarks`, `created_at`, `updated_at`) VALUES (28, 'email_account', '邮件账户', '', 0, 43, 1, 10, 0, 1, 20, 1, '', '2025-07-28 10:10:48', '2025-07-28 11:17:21');
COMMIT;

-- ----------------------------
-- Table structure for login_attempts
-- ----------------------------
DROP TABLE IF EXISTS `login_attempts`;
CREATE TABLE `login_attempts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID（可能为空，因为用户名可能不存在）',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户代理',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'failed' COMMENT '尝试状态：success-成功，failed-失败，locked-锁定',
  `attempted_at` datetime NOT NULL COMMENT '尝试时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_login_attempts_user_id` (`user_id`),
  KEY `idx_login_attempts_tenant_id` (`tenant_id`),
  KEY `idx_login_attempts_username` (`username`),
  KEY `idx_login_attempts_ip_address` (`ip_address`),
  KEY `idx_login_attempts_status` (`status`),
  KEY `idx_login_attempts_attempted_at` (`attempted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录尝试表';

-- ----------------------------
-- Records of login_attempts
-- ----------------------------
BEGIN;
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (51, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-19 13:32:11', '2025-07-19 13:32:11');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (52, NULL, 2, 'kevin', '*************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-19 13:40:34', '2025-07-19 13:40:34');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (53, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-20 03:29:49', '2025-07-20 03:29:49');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (54, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-20 14:43:34', '2025-07-20 14:43:34');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (55, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-21 08:12:37', '2025-07-21 08:12:37');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (56, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-21 08:12:42', '2025-07-21 08:12:42');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (57, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-21 08:13:34', '2025-07-21 08:13:34');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (58, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-21 13:35:41', '2025-07-21 13:35:41');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (59, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-21 13:35:45', '2025-07-21 13:35:45');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (60, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-21 13:36:25', '2025-07-21 13:36:25');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (61, NULL, 0, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:23:20', '2025-07-22 10:23:21');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (62, NULL, 0, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:23:28', '2025-07-22 10:23:28');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (63, NULL, 4, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:27:50', '2025-07-22 10:27:50');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (64, NULL, 4, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:28:03', '2025-07-22 10:28:03');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (65, NULL, 4, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:28:14', '2025-07-22 10:28:14');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (66, NULL, 4, '<EMAIL>', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'failed', '2025-07-22 10:28:36', '2025-07-22 10:28:36');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (67, NULL, 0, '<EMAIL>', '::1', 'curl/8.7.1', 'failed', '2025-07-22 10:29:38', '2025-07-22 10:29:39');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (68, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-23 09:43:12', '2025-07-23 09:43:12');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (69, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-23 13:21:44', '2025-07-23 13:21:44');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (70, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-24 04:35:49', '2025-07-24 04:35:49');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (71, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-24 04:41:25', '2025-07-24 04:41:25');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (72, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'success', '2025-07-24 12:22:24', '2025-07-24 12:22:24');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (73, NULL, 0, 'admin', '::1', 'curl/8.7.1', 'failed', '2025-07-24 15:37:15', '2025-07-24 15:37:15');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (74, NULL, 0, 'admin', '::1', 'curl/8.7.1', 'failed', '2025-07-24 15:38:35', '2025-07-24 15:38:35');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (75, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-25 12:42:27', '2025-07-25 12:42:27');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (76, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-26 14:38:44', '2025-07-26 14:38:44');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (77, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-26 15:29:27', '2025-07-26 15:29:27');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (78, NULL, 2, 'kevin', '**************', 'Dart/3.8 (dart:io)', 'failed', '2025-07-26 15:42:31', '2025-07-26 15:42:31');
INSERT INTO `login_attempts` (`id`, `user_id`, `tenant_id`, `username`, `ip_address`, `user_agent`, `status`, `attempted_at`, `created_at`) VALUES (79, 1001, 1, 'admin', '::1', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'success', '2025-07-27 15:43:54', '2025-07-27 15:43:54');
COMMIT;

-- ----------------------------
-- Table structure for mfa_configs
-- ----------------------------
DROP TABLE IF EXISTS `mfa_configs`;
CREATE TABLE `mfa_configs` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MFA类型：totp-基于时间的一次性密码，sms-短信验证码，email-邮件验证码',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'TOTP密钥',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用',
  `backup_codes` json DEFAULT NULL COMMENT '备用码（JSON数组）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `idx_mfa_configs_user_id` (`user_id`),
  KEY `idx_mfa_configs_type` (`type`),
  KEY `idx_mfa_configs_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='MFA配置表';

-- ----------------------------
-- Records of mfa_configs
-- ----------------------------
BEGIN;
INSERT INTO `mfa_configs` (`user_id`, `type`, `secret`, `phone`, `email`, `enabled`, `backup_codes`, `created_at`, `updated_at`) VALUES (1001, 'totp', 'mock_secret_key', NULL, NULL, 1, NULL, '2025-06-27 18:24:27', '2025-06-27 18:24:27');
COMMIT;

-- ----------------------------
-- Table structure for oauth_channel_config
-- ----------------------------
DROP TABLE IF EXISTS `oauth_channel_config`;
CREATE TABLE `oauth_channel_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel_code` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `channel_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道名称',
  `icon_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `client_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `client_secret` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `auth_url` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token_url` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_info_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `redirect_uri` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scope` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `grant_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'authorization_code',
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `jwks_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `response_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'code',
  `issuer` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `config` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_code` (`channel_code`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_order_num` (`order_num`),
  KEY `idx_channel_code` (`channel_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OAuth渠道配置表';

-- ----------------------------
-- Records of oauth_channel_config
-- ----------------------------
BEGIN;
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (1, 'github', 'GitHub', 'https://github.com/favicon.ico', 'your_github_client_id', 'your_github_client_secret', 'https://github.com/login/oauth/authorize', 'https://github.com/login/oauth/access_token', 'https://api.github.com/user', 'http://localhost:8084/api/user/oauth-login/callback', 'user:email', 'authorization_code', 1, 1, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (2, 'google', 'Google', 'https://www.google.com/favicon.ico', 'your_google_client_id', 'your_google_client_secret', 'https://accounts.google.com/o/oauth2/auth', 'https://oauth2.googleapis.com/token', 'https://www.googleapis.com/oauth2/v2/userinfo', 'http://localhost:8084/api/user/oauth-login/callback', 'openid email profile', 'authorization_code', 1, 2, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
INSERT INTO `oauth_channel_config` (`id`, `channel_code`, `channel_name`, `icon_url`, `client_id`, `client_secret`, `auth_url`, `token_url`, `user_info_url`, `redirect_uri`, `scope`, `grant_type`, `enabled`, `order_num`, `created_at`, `updated_at`, `name`, `description`, `jwks_url`, `response_type`, `issuer`, `config`) VALUES (3, 'wechat', '微信', 'https://res.wx.qq.com/a/wx_fed/assets/res/OTE0YTAw.png', 'your_wechat_appid', 'your_wechat_secret', 'https://open.weixin.qq.com/connect/oauth2/authorize', 'https://api.weixin.qq.com/sns/oauth2/access_token', 'https://api.weixin.qq.com/sns/userinfo', 'http://localhost:8084/api/user/oauth-login/callback', 'snsapi_userinfo', 'authorization_code', 1, 3, '2025-07-12 15:13:44.000', '2025-07-12 15:13:44.000', '', NULL, NULL, 'code', NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限描述',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源类型：user, role, permission, tenant等',
  `resource_id` bigint DEFAULT NULL COMMENT '资源ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作类型：create, read, update, delete等',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '权限状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统权限',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限编码',
  `scope` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'self' COMMENT '权限范围：all-全部权限，self-普通权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_permission_tenant_name` (`tenant_id`,`name`),
  UNIQUE KEY `uq_permission_tenant_code` (`tenant_id`,`code`),
  KEY `idx_permission_tenant_id` (`tenant_id`),
  KEY `idx_permission_resource` (`resource_type`),
  KEY `idx_permission_action` (`action`),
  KEY `idx_permission_system` (`is_system`),
  KEY `idx_permission_status` (`status`),
  KEY `idx_permission_deleted` (`deleted_at`),
  KEY `idx_permission_code` (`code`),
  KEY `idx_permission_scope` (`scope`),
  CONSTRAINT `chk_permission_scope` CHECK ((`scope` in (_utf8mb4'all',_utf8mb4'self')))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- ----------------------------
-- Records of permissions
-- ----------------------------
BEGIN;
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1001, 1, 'user:create', '创建用户', '创建新用户', 'user', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'USER_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1002, 1, 'user:read', '查看用户', '查看用户信息', 'user', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'USER_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1003, 1, 'user:update', '更新用户', '更新用户信息', 'user', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'USER_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1004, 1, 'user:delete', '删除用户', '删除用户', 'user', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'USER_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1005, 1, 'role:create', '创建角色', '创建新角色', 'role', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'ROLE_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1006, 1, 'role:read', '查看角色', '查看角色信息', 'role', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'ROLE_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1007, 1, 'role:update', '更新角色', '更新角色信息', 'role', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'ROLE_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1008, 1, 'role:delete', '删除角色', '删除角色', 'role', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'ROLE_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1009, 1, 'permission:read', '查看权限', '查看权限信息', 'permission', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'PERMISSION_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1010, 1, 'tenant:read', '查看租户', '查看租户信息', 'tenant', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'TENANT_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1011, 1, 'tenant:update', '更新租户', '更新租户信息', 'tenant', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'TENANT_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1012, 1, 'department:create', '创建部门', '创建新部门', 'department', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'DEPARTMENT_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1013, 1, 'department:read', '查看部门', '查看部门信息', 'department', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'DEPARTMENT_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1014, 1, 'department:update', '更新部门', '更新部门信息', 'department', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'DEPARTMENT_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1015, 1, 'department:delete', '删除部门', '删除部门', 'department', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'DEPARTMENT_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1016, 1, 'position:create', '创建职位', '创建新职位', 'position', NULL, 'create', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'POSITION_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1017, 1, 'position:read', '查看职位', '查看职位信息', 'position', NULL, 'read', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'POSITION_READ', 'self');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1018, 1, 'position:update', '更新职位', '更新职位信息', 'position', NULL, 'update', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'POSITION_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (1019, 1, 'position:delete', '删除职位', '删除职位', 'position', NULL, 'delete', 'active', 1, '2025-06-27 17:06:36', '2025-07-07 13:03:52', NULL, 'POSITION_DELETE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3009, 1, 'permission:create', '创建权限', '创建新权限', NULL, 10003, 'create', 'active', 1, '2025-07-07 09:47:02', '2025-07-07 13:03:52', NULL, 'PERMISSION_CREATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3011, 1, 'permission:update', '更新权限', '更新权限信息', NULL, 10003, 'update', 'active', 1, '2025-07-07 09:47:02', '2025-07-07 13:03:52', NULL, 'PERMISSION_UPDATE', 'all');
INSERT INTO `permissions` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `resource_id`, `action`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`, `code`, `scope`) VALUES (3012, 1, 'permission:delete', '删除权限', '删除权限', NULL, 10003, 'delete', 'active', 1, '2025-07-07 09:47:02', '2025-07-07 13:03:52', NULL, 'PERMISSION_DELETE', 'all');
COMMIT;

-- ----------------------------
-- Table structure for plugin
-- ----------------------------
DROP TABLE IF EXISTS `plugin`;
CREATE TABLE `plugin` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '插件名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件描述',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '插件版本',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '插件作者',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'disabled' COMMENT '插件状态：enabled-启用，disabled-禁用，error-错误',
  `config` json DEFAULT NULL COMMENT '插件配置（JSON格式）',
  `installed_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '安装时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_plugin_tenant_name` (`tenant_id`,`name`),
  KEY `idx_plugin_tenant_id` (`tenant_id`),
  KEY `idx_plugin_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='插件表';

-- ----------------------------
-- Records of plugin
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for positions
-- ----------------------------
DROP TABLE IF EXISTS `positions`;
CREATE TABLE `positions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '职位名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '职位编码',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职位描述',
  `level` int NOT NULL DEFAULT '1' COMMENT '职位级别',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '职位状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否系统职位',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_position_tenant_code` (`tenant_id`,`code`),
  KEY `idx_position_tenant_id` (`tenant_id`),
  KEY `idx_position_department_id` (`department_id`),
  KEY `idx_position_status` (`status`),
  KEY `idx_position_system` (`is_system`),
  KEY `idx_position_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='职位表';

-- ----------------------------
-- Records of positions
-- ----------------------------
BEGIN;
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728487499189456896, 1, NULL, '业务员', 'LNS', '', 1, 1, 'active', 0, '2025-07-03 05:49:14', '2025-07-03 05:49:14', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527070199549952, 1, NULL, '开发1751531188', 'dev1751531188', '', 1, 0, 'active', 0, '2025-07-03 08:26:29', '2025-07-03 08:26:29', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527072024072192, 1, NULL, '开发', 'dev004', '', 1, 0, 'active', 0, '2025-07-03 08:26:29', '2025-07-03 08:26:29', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527072938430464, 1, NULL, '开发\'; DROP TABLE user; --', 'dev007', '', 1, 0, 'active', 0, '2025-07-03 08:26:30', '2025-07-03 08:26:30', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527074070892544, 1, NULL, '并发职位', 'dev009', '', 1, 0, 'active', 0, '2025-07-03 08:26:30', '2025-07-03 08:26:30', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728527572031246336, 1, NULL, '开发1751531308', 'dev1751531308', '', 1, 0, 'active', 0, '2025-07-03 08:28:29', '2025-07-03 08:28:29', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728532089850302464, 1, NULL, '开发1751532385', 'dev1751532385', '', 1, 0, 'active', 0, '2025-07-03 08:46:26', '2025-07-03 08:46:26', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728532319723327488, 1, NULL, '开发1751532440', 'dev1751532440', '', 1, 0, 'active', 0, '2025-07-03 08:47:21', '2025-07-03 08:47:21', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728537714411966464, 1, NULL, '开发1751533726', 'dev1751533726', '', 1, 0, 'active', 0, '2025-07-03 09:08:47', '2025-07-03 09:08:47', NULL);
INSERT INTO `positions` (`id`, `tenant_id`, `department_id`, `name`, `code`, `description`, `level`, `sort`, `status`, `is_system`, `created_at`, `updated_at`, `deleted_at`) VALUES (728537910449541120, 1, NULL, '开发1751533773', 'dev1751533773', '', 1, 0, 'active', 0, '2025-07-03 09:09:33', '2025-07-03 09:09:33', NULL);
COMMIT;

-- ----------------------------
-- Table structure for rate_limit_config
-- ----------------------------
DROP TABLE IF EXISTS `rate_limit_config`;
CREATE TABLE `rate_limit_config` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID（NULL表示全局配置）',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '限流配置名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '限流配置描述',
  `target_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标类型：global, tenant, user, api',
  `target_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标值（如API路径、用户ID等）',
  `limit_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '限流类型：requests_per_second, requests_per_minute, requests_per_hour',
  `limit_value` int NOT NULL COMMENT '限流值',
  `burst_size` int DEFAULT '0' COMMENT '突发大小',
  `window_size` int DEFAULT '60' COMMENT '时间窗口大小（秒）',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_rate_limit_tenant_name` (`tenant_id`,`name`),
  KEY `idx_rate_limit_tenant_id` (`tenant_id`),
  KEY `idx_rate_limit_target` (`target_type`,`target_value`),
  KEY `idx_rate_limit_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='限流配置表';

-- ----------------------------
-- Records of rate_limit_config
-- ----------------------------
BEGIN;
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4001, NULL, 'global_rate_limit', '全局限流', 'global', NULL, 'requests_per_second', 10000, 2000, 1, 1, '2025-06-27 17:06:36', '2025-06-27 17:06:36');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4002, NULL, 'tenant_rate_limit', '租户限流', 'tenant', NULL, 'requests_per_second', 1000, 200, 1, 1, '2025-06-27 17:06:36', '2025-06-27 17:06:36');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4003, NULL, 'user_rate_limit', '用户限流', 'user', NULL, 'requests_per_second', 100, 20, 1, 1, '2025-06-27 17:06:36', '2025-06-27 17:06:36');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4004, NULL, 'login_api_rate_limit', '登录接口限流', 'api', '/api/v1/auth/login', 'requests_per_minute', 5, 1, 60, 1, '2025-06-27 17:06:36', '2025-06-27 17:06:36');
INSERT INTO `rate_limit_config` (`id`, `tenant_id`, `name`, `description`, `target_type`, `target_value`, `limit_type`, `limit_value`, `burst_size`, `window_size`, `enabled`, `created_at`, `updated_at`) VALUES (4005, NULL, 'user_api_rate_limit', '用户接口限流', 'api', '/api/v1/users', 'requests_per_minute', 60, 10, 60, 1, '2025-06-27 17:06:36', '2025-06-27 17:06:36');
COMMIT;

-- ----------------------------
-- Table structure for resource
-- ----------------------------
DROP TABLE IF EXISTS `resource`;
CREATE TABLE `resource` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源描述',
  `resource_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源类型：menu-菜单，button-按钮，api-接口，page-页面\n',
  `service_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用服务名称',
  `request_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '请求数据类型: json, form, file, text, stream, xml, binary',
  `response_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '响应数据类型: json, html, xml, stream, file, text, binary',
  `api_method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'HTTP方法: GET, POST, PUT, DELETE, PATCH',
  `content_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Content-Type: application/json, multipart/form-data等',
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父资源ID',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源路径',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统资源',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除实践',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开访问',
  `public_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'none' COMMENT '公开级别：none-非公开，anonymous-匿名，authenticated-已认证，conditional-条件式',
  `assignable` tinyint(1) DEFAULT '1' COMMENT '是否可分配给用户，TRUE表示可以分配，FALSE表示不可分配',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_resource_tenant_name` (`tenant_id`,`name`),
  KEY `idx_resource_tenant_id` (`tenant_id`),
  KEY `idx_resource_parent_id` (`parent_id`),
  KEY `idx_resource_system` (`is_system`),
  KEY `idx_resource_public` (`tenant_id`,`is_public`,`public_level`),
  KEY `idx_resource_public_path` (`tenant_id`,`path`,`is_public`),
  KEY `idx_resource_assignable` (`tenant_id`,`assignable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源表';

-- ----------------------------
-- Records of resource
-- ----------------------------
BEGIN;
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (1, 1, 'user_list', '用户列表查询', '管理后台查询用户列表', 'api', 'user-service', 'json', 'json', 'POST', '', 0, '/api/user/list', '', 1, 0, NULL, '2025-07-24 10:33:11', '2025-07-24 15:46:52', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (2, 1, 'admin_user_create', '管理员创建用户', '管理后台手动创建用户', 'api', 'user-service', 'json', 'json', 'POST', '', 0, '/api/user/create', '', 2, 0, NULL, '2025-07-24 10:40:36', '2025-07-24 15:45:03', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (100, 1, 'dashboard', '仪表盘', '系统仪表盘页面', 'page', NULL, NULL, NULL, NULL, NULL, 0, '/dashboard', 'DashboardOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:37:28', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (101, 1, 'user-management', '用户管理', '用户管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'TeamOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (102, 1, 'system-management', '系统管理', '系统管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'SettingOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (103, 1, 'file-system', '文件系统', '文件系统相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'FolderOutlined', 4, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (104, 1, 'communication', '通讯管理', '通讯管理相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'MailOutlined', 5, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (105, 1, 'user-system', '用户系统', '用户系统相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'UserSwitchOutlined', 6, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (106, 1, 'tools', '工具箱', '工具箱相关功能', 'page', NULL, NULL, NULL, NULL, NULL, 0, NULL, 'ThunderboltOutlined', 7, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (107, 1, 'tenant', '租户管理', '租户管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 0, '/tenant', 'BankOutlined', 8, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (108, 1, 'user', '用户列表', '用户列表管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/user', 'UserOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (109, 1, 'department', '组织架构', '组织架构管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/department', 'ApartmentOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (110, 1, 'position', '职位管理', '职位管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 101, '/position', 'SolutionOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (111, 1, 'role', '角色管理', '角色管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/role', 'CrownOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (112, 1, 'permission', '权限管理', '权限管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/permission', 'SafetyCertificateOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (113, 1, 'permission-group', '权限组管理', '权限组管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/permission-group', 'TeamOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (114, 1, 'resource', '资源管理', '资源管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/resource', 'DatabaseOutlined', 4, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (115, 1, 'api-management', 'API管理', 'API管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 102, '/api-management', 'ApiOutlined', 5, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (116, 1, 'file-system/files', '文件管理', '文件管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/files', 'FileTextOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (117, 1, 'file-system/scenes', '场景配置', '场景配置页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/scenes', 'SettingOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (118, 1, 'file-system/upload', '文件上传', '文件上传页面', 'page', NULL, NULL, NULL, NULL, NULL, 103, '/file-system/upload', 'UploadOutlined', 3, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (119, 1, 'email/accounts', '邮件账户', '邮件账户管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 104, '/email/accounts', 'UserOutlined', 1, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (120, 1, 'email/templates', '邮件模板', '邮件模板管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 104, '/email/templates', 'FileTextOutlined', 2, 1, NULL, '2025-07-25 01:35:35', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (121, 1, 'users/policies', '验证策略', '验证策略管理页面', 'page', NULL, NULL, NULL, NULL, NULL, 105, '/users/policies', 'SecurityScanOutlined', 1, 1, NULL, '2025-07-25 01:35:36', '2025-07-25 01:41:18', 0, 'none', 1);
INSERT INTO `resource` (`id`, `tenant_id`, `name`, `display_name`, `description`, `resource_type`, `service_name`, `request_type`, `response_type`, `api_method`, `content_type`, `parent_id`, `path`, `icon`, `sort_order`, `is_system`, `deleted_at`, `created_at`, `updated_at`, `is_public`, `public_level`, `assignable`) VALUES (122, 1, 'idgenerator', 'ID生成器', 'ID生成器页面', 'page', NULL, NULL, NULL, NULL, NULL, 106, '/idgenerator/sequences', 'KeyOutlined', 1, 1, NULL, '2025-07-25 01:35:36', '2025-07-25 01:41:18', 0, 'none', 1);
COMMIT;

-- ----------------------------
-- Table structure for resource_relations
-- ----------------------------
DROP TABLE IF EXISTS `resource_relations`;
CREATE TABLE `resource_relations` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `source_resource_id` bigint NOT NULL COMMENT '源资源ID',
  `target_resource_id` bigint NOT NULL COMMENT '目标资源ID',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_resource_relation` (`tenant_id`,`source_resource_id`,`target_resource_id`),
  KEY `idx_resource_relations_tenant_id` (`tenant_id`),
  KEY `idx_resource_relations_source_id` (`source_resource_id`),
  KEY `idx_resource_relations_target_id` (`target_resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源关联关系表';

-- ----------------------------
-- Records of resource_relations
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `granted_by` bigint NOT NULL COMMENT '授权人ID',
  `granted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_role_permission` (`role_id`,`permission_id`),
  KEY `idx_role_permissions_role_id` (`role_id`),
  KEY `idx_role_permissions_permission_id` (`permission_id`),
  KEY `idx_role_permissions_granted_by` (`granted_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ----------------------------
-- Records of role_permissions
-- ----------------------------
BEGIN;
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5001, 2001, 3001, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5002, 2001, 3002, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5003, 2001, 3003, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5004, 2001, 3004, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5005, 2001, 3005, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5006, 2001, 3006, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5007, 2001, 3007, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5008, 2001, 3008, 1001, '2025-07-07 09:47:02', '2025-06-27 17:07:12');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5009, 2001, 3009, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5010, 2001, 3010, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5011, 2001, 3011, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5012, 2001, 3012, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5013, 2001, 3013, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5014, 2001, 3014, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5015, 2001, 3015, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5016, 2001, 3016, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5017, 2001, 3017, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5018, 2001, 3018, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5019, 2001, 3019, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`, `granted_by`, `granted_at`, `created_at`) VALUES (5020, 2001, 3020, 1001, '2025-07-07 09:47:02', '2025-07-07 09:47:02');
COMMIT;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '角色编码',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色显示名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '角色状态：active-活跃，disabled-禁用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统角色',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序字段',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_role_tenant_name` (`tenant_id`,`name`),
  UNIQUE KEY `uq_role_code` (`tenant_id`,`code`),
  KEY `idx_role_tenant_id` (`tenant_id`),
  KEY `idx_role_system` (`is_system`),
  KEY `idx_role_status` (`status`),
  KEY `idx_role_deleted` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- ----------------------------
-- Records of roles
-- ----------------------------
BEGIN;
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2001, 1, 'super_admin', 'super_admin', '系统管理员', '系统管理员，拥有所有权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-06-28 20:21:57', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2002, 1, 'admin', 'admin', '普通用户', '普通用户，基础权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-06-28 20:22:00', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2003, 1, 'user', 'user', '普通用户', '普通用户，基础权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-06-28 20:22:03', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (2004, 1, 'manager', 'manager', '部门经理', '部门经理，部门管理权限', 'active', 1, 0, '2025-06-27 17:06:36', '2025-06-28 20:22:06', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601011895996416, 1, '业务员', 'lns:worker', '业务员', '', 'active', 0, 0, '2025-06-28 00:53:01', '2025-06-28 00:53:01', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601602584023040, 1, '测试角色', 'test_role', '测试角色', '这是一个测试角色', 'active', 0, 0, '2025-06-28 00:55:22', '2025-06-28 00:55:22', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726601784021225472, 1, '测试角色3', 'test_role3', '测试角色3', '这是一个测试角色', 'active', 0, 0, '2025-06-28 00:56:05', '2025-06-28 00:56:05', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726666439049613312, 1, 'test', 'test', 'test', '', 'active', 0, 0, '2025-06-28 05:13:00', '2025-06-28 05:13:00', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726687864112287744, 1, 'ddd', 'lns:user', 'ddd', '', 'active', 0, 0, '2025-06-28 06:38:08', '2025-06-28 06:38:08', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775673351311360, 1, '调试角色', 'DEBUG_ROLE', '调试角色', '用于调试的角色', 'active', 0, 0, '2025-06-28 12:27:03', '2025-06-28 12:27:03', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775694297665536, 1, '测试角色1', 'TEST_ROLE_1', '测试角色1', '测试角色1描述', 'active', 0, 0, '2025-06-28 12:27:08', '2025-06-28 12:27:08', NULL);
INSERT INTO `roles` (`id`, `tenant_id`, `name`, `code`, `display_name`, `description`, `status`, `is_system`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES (726775695082000384, 1, '测试角色2', 'TEST_ROLE_2', '测试角色2', '测试角色2描述', 'active', 0, 0, '2025-06-28 12:27:09', '2025-06-28 12:27:09', NULL);
COMMIT;

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint DEFAULT NULL COMMENT '租户ID（NULL表示全局配置）',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值',
  `config_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置描述',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_config_tenant_key` (`tenant_id`,`config_key`),
  KEY `idx_config_tenant_id` (`tenant_id`),
  KEY `idx_config_key` (`config_key`),
  KEY `idx_config_system` (`is_system`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- ----------------------------
-- Records of system_config
-- ----------------------------
BEGIN;
INSERT INTO `system_config` (`id`, `tenant_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (1, 2, 'password_policy', '{\"min_length\":8,\"max_length\":32,\"require_uppercase\":false,\"require_lowercase\":false,\"require_digits\":false,\"require_special_chars\":false,\"forbidden_patterns\":[\"123456\",\"password\",\"admin\"],\"password_history_count\":5,\"expire_days\":90}', 'json', NULL, 0, '2025-07-20 02:13:34', '2025-07-20 14:44:01');
INSERT INTO `system_config` (`id`, `tenant_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (3, 4, 'registration_methods', '{\"email\":{\"enabled\":true,\"require_verification\":true,\"manual_activation\":false,\"verification_template_code\":\"account_activation\"},\"phone\":{\"enabled\":true,\"require_verification\":false,\"manual_activation\":false},\"oauth\":{\"enabled\":true,\"manual_activation\":false},\"admin_creation\":{\"enabled\":true,\"require_approval\":false}}', 'json', NULL, 0, '2025-07-27 03:18:48', '2025-07-27 03:18:48');
INSERT INTO `system_config` (`id`, `tenant_id`, `config_key`, `config_value`, `config_type`, `description`, `is_system`, `created_at`, `updated_at`) VALUES (4, 2, 'registration_methods', '{\"email\":{\"enabled\":true,\"require_verification\":true,\"manual_activation\":false,\"verification_template_code\":\"account_activation\"},\"phone\":{\"enabled\":true,\"require_verification\":false,\"manual_activation\":false},\"oauth\":{\"enabled\":true,\"manual_activation\":false},\"admin_creation\":{\"enabled\":true,\"require_approval\":false}}', 'json', NULL, 0, '2025-07-27 03:18:59', '2025-07-27 03:18:59');
COMMIT;

-- ----------------------------
-- Table structure for tenant_oauth_channel
-- ----------------------------
DROP TABLE IF EXISTS `tenant_oauth_channel`;
CREATE TABLE `tenant_oauth_channel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `channel_config_id` bigint NOT NULL COMMENT '渠道配置ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义渠道名称',
  `icon_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '自定义图标URL',
  `enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `order_num` int DEFAULT '0' COMMENT '排序号',
  `config` json DEFAULT NULL COMMENT '自定义配置',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_channel` (`tenant_id`,`channel_config_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_channel_config_id` (`channel_config_id`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_order_num` (`order_num`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户OAuth渠道关联表';

-- ----------------------------
-- Records of tenant_oauth_channel
-- ----------------------------
BEGIN;
INSERT INTO `tenant_oauth_channel` (`id`, `tenant_id`, `channel_config_id`, `name`, `icon_url`, `enabled`, `order_num`, `config`, `created_at`, `updated_at`) VALUES (1, 1, 1, 'GitHub登录', NULL, 1, 1, NULL, '2025-07-12 15:13:44', '2025-07-12 15:13:44');
INSERT INTO `tenant_oauth_channel` (`id`, `tenant_id`, `channel_config_id`, `name`, `icon_url`, `enabled`, `order_num`, `config`, `created_at`, `updated_at`) VALUES (2, 1, 2, 'Google登录', NULL, 1, 2, NULL, '2025-07-12 15:13:44', '2025-07-12 15:13:44');
COMMIT;

-- ----------------------------
-- Table structure for tenants
-- ----------------------------
DROP TABLE IF EXISTS `tenants`;
CREATE TABLE `tenants` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户编码，业务唯一标识',
  `tenant_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户名称',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '租户状态：active-活跃，disabled-禁用，expired-过期',
  `max_users` int DEFAULT '1000' COMMENT '最大用户数',
  `max_storage` bigint DEFAULT '***********' COMMENT '最大存储空间（字节）',
  `subscription_plan` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'basic' COMMENT '订阅计划：basic-基础版，pro-专业版，enterprise-企业版',
  `expires_at` datetime DEFAULT NULL COMMENT '租户过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenant_code` (`tenant_code`),
  KEY `idx_tenant_code` (`tenant_code`),
  KEY `idx_tenant_status` (`status`),
  KEY `idx_tenant_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户表';

-- ----------------------------
-- Records of tenants
-- ----------------------------
BEGIN;
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (1, 'system', '系统租户', 'active', 1000, ***********, 'enterprise', NULL, '2025-07-21 14:14:12', '2025-07-21 14:14:22');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (2, 'ilike', 'ilike', 'active', 10, 10, 'enterprise', NULL, '2025-07-09 10:37:02', '2025-07-09 10:37:02');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (3, 'demo', '演示租户', 'active', 1000, ***********, 'basic', NULL, '2025-06-27 17:07:12', '2025-07-21 14:13:38');
INSERT INTO `tenants` (`id`, `tenant_code`, `tenant_name`, `status`, `max_users`, `max_storage`, `subscription_plan`, `expires_at`, `created_at`, `updated_at`) VALUES (4, 'prompts', '提示词项目', 'active', 1000, ***********, 'basic', NULL, '2025-07-21 15:25:39', '2025-07-21 15:25:39');
COMMIT;

-- ----------------------------
-- Table structure for third_party_account
-- ----------------------------
DROP TABLE IF EXISTS `third_party_account`;
CREATE TABLE `third_party_account` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提供商：wechat, dingtalk, feishu, google, microsoft等',
  `external_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '外部用户ID',
  `external_username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部用户名',
  `external_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部邮箱',
  `external_avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外部头像',
  `access_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '访问令牌',
  `refresh_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '刷新令牌',
  `token_expires_at` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，disabled-禁用',
  `bind_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_third_party_user_provider` (`user_id`,`provider`),
  UNIQUE KEY `uq_third_party_provider_external` (`provider`,`external_user_id`),
  KEY `idx_third_party_user_id` (`user_id`),
  KEY `idx_third_party_provider` (`provider`),
  KEY `idx_third_party_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='三方账号表';

-- ----------------------------
-- Records of third_party_account
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for upload_tokens
-- ----------------------------
DROP TABLE IF EXISTS `upload_tokens`;
CREATE TABLE `upload_tokens` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `scene_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '上传令牌',
  `oss_config` text COLLATE utf8mb4_unicode_ci COMMENT 'OSS配置信息，JSON格式',
  `expire_at` datetime NOT NULL COMMENT '过期时间（通常30-60分钟）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_token_tenant_id` (`tenant_id`),
  KEY `idx_token_user_id` (`user_id`),
  KEY `idx_token_scene_code` (`scene_code`),
  KEY `idx_token_expire_at` (`expire_at`),
  KEY `idx_token_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传令牌表';

-- ----------------------------
-- Records of upload_tokens
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_departments
-- ----------------------------
DROP TABLE IF EXISTS `user_departments`;
CREATE TABLE `user_departments` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_department` (`user_id`,`department_id`),
  KEY `idx_user_departments_user_id` (`user_id`),
  KEY `idx_user_departments_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户部门关联表';

-- ----------------------------
-- Records of user_departments
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_ext
-- ----------------------------
DROP TABLE IF EXISTS `user_ext`;
CREATE TABLE `user_ext` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '扩展属性键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '扩展属性值',
  `ext_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '扩展属性类型：string, number, boolean, json',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_ext_key` (`user_id`,`ext_key`),
  KEY `idx_user_ext_user_id` (`user_id`),
  KEY `idx_user_ext_key` (`ext_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户扩展属性表';

-- ----------------------------
-- Records of user_ext
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_password_history
-- ----------------------------
DROP TABLE IF EXISTS `user_password_history`;
CREATE TABLE `user_password_history` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希（BCrypt加密）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_password_history_user_id` (`user_id`),
  KEY `idx_user_password_history_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户密码历史表';

-- ----------------------------
-- Records of user_password_history
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_positions
-- ----------------------------
DROP TABLE IF EXISTS `user_positions`;
CREATE TABLE `user_positions` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `position_id` bigint NOT NULL COMMENT '职位ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_position` (`user_id`,`position_id`),
  KEY `idx_user_positions_user_id` (`user_id`),
  KEY `idx_user_positions_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户职位关联表';

-- ----------------------------
-- Records of user_positions
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for user_roles
-- ----------------------------
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `granted_by` bigint NOT NULL COMMENT '授权人ID',
  `granted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `expires_at` datetime DEFAULT NULL COMMENT '授权过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_role` (`user_id`,`role_id`),
  KEY `idx_user_roles_user_id` (`user_id`),
  KEY `idx_user_roles_role_id` (`role_id`),
  KEY `idx_user_roles_granted_by` (`granted_by`),
  KEY `idx_user_roles_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- ----------------------------
-- Records of user_roles
-- ----------------------------
BEGIN;
INSERT INTO `user_roles` (`id`, `user_id`, `role_id`, `granted_by`, `granted_at`, `expires_at`, `created_at`) VALUES (4001, 1001, 2001, 1001, '2025-07-07 09:47:02', NULL, '2025-06-27 17:07:12');
COMMIT;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL COMMENT '分布式ID，雪花算法生成',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '用户状态：active-活跃，disabled-禁用，locked-锁定',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希（BCrypt加密）',
  `password_changed_at` datetime DEFAULT NULL COMMENT '密码最后修改时间',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `failed_count` int DEFAULT '0' COMMENT '失败次数',
  `locked_at` datetime DEFAULT NULL COMMENT '锁定时间',
  `locked_until` datetime DEFAULT NULL COMMENT '锁定到期时间',
  `lock_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '锁定原因',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `position_id` bigint DEFAULT NULL COMMENT '职位ID',
  `employee_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '员工编号',
  `hire_date` datetime DEFAULT NULL COMMENT '入职日期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_user_tenant_username` (`tenant_id`,`username`),
  UNIQUE KEY `uq_user_tenant_email` (`tenant_id`,`email`),
  KEY `idx_user_tenant_id` (`tenant_id`),
  KEY `idx_user_status` (`status`),
  KEY `idx_user_username` (`username`),
  KEY `idx_user_email` (`email`),
  KEY `idx_user_deleted` (`deleted_at`),
  KEY `idx_user_department_id` (`department_id`),
  KEY `idx_user_position_id` (`position_id`),
  KEY `idx_user_employee_id` (`employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Records of users
-- ----------------------------
BEGIN;
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1, 1, 'testuser5', '<EMAIL>', '', '测试用户5', '', 'active', 0, '$2a$10$UBtBpjUUrrzvMfEvnJMzROgPu5jWfkqGiu4AySEKRSAs6HEC2ho8.', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:15:34', '2025-07-06 08:15:34', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2, 1, 'testuser6', '<EMAIL>', '', '测试用户6', '', 'active', 0, '$2a$10$VBMz8HgwZtKgBy0kqqERJ.emegBex62TGWHrQZTpPZbYi9BQS8z92', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:15:53', '2025-07-06 08:15:53', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (3, 1, 'testuser7', '<EMAIL>', '', '测试用户7', '', 'active', 0, '$2a$10$zJBipyzgrYHvbS/LTLuoluZLCG8BdIgt3rlKvBN4m7RrB01HfljYW', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:16:01', '2025-07-06 08:16:01', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1001, 1, 'admin', '<EMAIL>', '', '系统管理员', '', 'active', 0, '$2a$10$eKcxwPBZUAJQds4mUmQqVeTTNDE73j6I/HoZIh7VLZM8CPLrWhCaq', NULL, '2025-06-27 12:13:11', '::1', 1, 4, NULL, NULL, '', NULL, NULL, '', NULL, '2025-06-27 17:07:12', '2025-06-27 12:27:12', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (1002, 1, 'testuser9', '<EMAIL>', '', '测试用户9', '', 'active', 0, '$2a$10$vx.MJE7rZz6A/RpfV0jugO5x8gE8YgXzsM6kzoGkG3/uFsw1U6PPG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:43:18', '2025-07-06 08:43:18', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2001, 2, 'kevin', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$xlxi.9Xq/yTfGg2KdPeL9.CoYl5fenPKX6HDhwhnW8iHrIzSJnljK', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-20 17:43:02', '2025-07-20 17:43:02', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (2002, 2, 'testuser', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$DbRpE24B7I7m6t45mpqDiu/22D/JnARZYeS2dLs6c9w/btXDndEi2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-20 17:43:08', '2025-07-20 17:43:08', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (17003, 4, 'kevin_prompts', '<EMAIL>', '', '', '', 'active', 0, '$2a$10$4Pi1.4adKgMcCVjUFnaCzOa4Uof0/8FiptcaeX83VeRpv1txAEvx2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-27 03:40:56', '2025-07-27 03:40:56', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (727751058184474624, 1, 'testuser', '<EMAIL>', '', 'Test User', '', 'active', 0, '$2a$10$RPOboNjYsPxHI.RNtLcRo.Eu0HbrS44ZW7Pcyp.TXMdT1zqHHe7u6', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-01 05:02:53', '2025-07-01 05:02:53', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (727876535444312064, 1, 'testuser1', '<EMAIL>', '13900000000', '新名字', '', 'inactive', 0, '$2a$10$2yH3QSf.9hKfjbrNRQEaV.sjpHVnj3/URaQv7oMR2kSzd7HX0Rdv2', NULL, NULL, '', 0, 0, NULL, NULL, '', 1, 1, '', NULL, '2025-07-01 13:21:29', '2025-07-01 14:56:35', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575207857033216, 1, 'testuser001', '<EMAIL>', '13800138001', '测试用户001', '', 'active', 0, '$2a$10$6YqT2dPaaPtyjDmJsUQn3.ZcNAzh/eLUvIaI/fmJ82SNwDpVTHbVK', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:24', '2025-07-06 05:51:24', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575244007739392, 1, 'testuser002', '<EMAIL>', '13800138002', '测试用户002', '', 'active', 0, '$2a$10$6nLSu/cORsiW/x3/uG65/./6af800taQVOMrXY7mXrW83JnrvIj6K', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:33', '2025-07-06 05:51:33', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729575276794613760, 1, 'testuser003', '<EMAIL>', '13800138003', '测试用户003', '', 'active', 0, '$2a$10$Wxx/SxZRH5PLPCsvfBX7KOL1mFhC7u4XRFwOX90dBXr9IaOlecCO2', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:51:41', '2025-07-06 05:51:41', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729576201215021056, 1, 'distributed001', '<EMAIL>', '13800138101', '分布式ID测试用户001', '', 'active', 0, '$2a$10$dzQvwuNYpf/iCIYR3WQztu3tFMfA9s3sI5JW39yrqAYeQATQiTeeG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:55:21', '2025-07-06 05:55:21', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729576239341244416, 1, 'distributed002', '<EMAIL>', '13800138102', '分布式ID测试用户002', '', 'active', 0, '$2a$10$ryFqih3UdW.eNYYpmx/iGey1SxJQOxz0Karo5ZgPbZHS63PWVS0HG', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:55:30', '2025-07-06 05:55:30', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577054139322368, 1, 'distributed_new_01', '<EMAIL>', '13800138201', '分布式ID新测试用户01', '', 'active', 0, '$2a$10$ZUl.mN6AhGfpqOuK0zbI7eBJfb0Ptx2yEue0dIU6Au27yIzTT9W1O', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:58:45', '2025-07-06 05:58:45', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577091502182400, 1, 'distributed_new_02', '<EMAIL>', '***********', '分布式ID新测试用户02', '', 'active', 0, '$2a$10$zv1El8l/w7T0kNmjoYb5leoSYElYABN8qMkOujtPThA3520spbun.', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 05:58:54', '2025-07-06 05:58:54', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577427663065088, 1, 'distributed_test01', '<EMAIL>', '***********', '分布式ID测试用户01', '', 'active', 0, '$2a$10$GrJrf.fGVbsNUtI08vm77OxmGu6Sw6VfHI5lXvFceWcfRp.3RY12C', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 06:00:14', '2025-07-06 06:00:14', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729577464078012416, 1, 'distributed_test02', '<EMAIL>', '13800138302', '分布式ID测试用户02', '', 'active', 0, '$2a$10$kBs52C7TzJUu23S5YSZpyOUfLQ4ODvxYkxrqug08vqlG2sLERrPLC', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 06:00:22', '2025-07-06 06:00:22', NULL);
INSERT INTO `users` (`id`, `tenant_id`, `username`, `email`, `phone`, `real_name`, `avatar`, `status`, `is_system`, `password`, `password_changed_at`, `last_login_at`, `last_login_ip`, `login_count`, `failed_count`, `locked_at`, `locked_until`, `lock_reason`, `department_id`, `position_id`, `employee_id`, `hire_date`, `created_at`, `updated_at`, `deleted_at`) VALUES (729611211011788800, 1, 'testuser4', '<EMAIL>', '', '测试用户4', '', 'active', 0, '$2a$10$.tyKf3.LSJF59tyUKdcfvOyVUWlg9DmYMmQa2RTs5aysgf9pN.H2a', NULL, NULL, '', 0, 0, NULL, NULL, '', NULL, NULL, '', NULL, '2025-07-06 08:14:28', '2025-07-06 08:14:28', NULL);
COMMIT;

-- ----------------------------
-- Table structure for verification_configs
-- ----------------------------
DROP TABLE IF EXISTS `verification_configs`;
CREATE TABLE `verification_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `config_mode` enum('static','dynamic') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'static' COMMENT '配置模式：static=静态配置，dynamic=动态策略',
  `purpose` tinyint unsigned DEFAULT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
  `target_type` tinyint unsigned NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
  `business_scene` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务场景：user_register, password_reset, email_change等',
  `judgment_dimension` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '判定维度：ip_location, user_behavior, device_info等',
  `condition_expr` text COLLATE utf8mb4_unicode_ci COMMENT '条件表达式，支持复杂逻辑判断',
  `token_type` tinyint unsigned NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
  `token_length` int NOT NULL DEFAULT '6' COMMENT '验证码长度',
  `expire_minutes` int NOT NULL DEFAULT '30' COMMENT '过期时间（分钟）',
  `max_attempts` int NOT NULL DEFAULT '5' COMMENT '最大尝试次数',
  `rate_limit_per_minute` int NOT NULL DEFAULT '3' COMMENT '每分钟限制',
  `rate_limit_per_hour` int NOT NULL DEFAULT '10' COMMENT '每小时限制',
  `rate_limit_per_day` int NOT NULL DEFAULT '50' COMMENT '每日限制',
  `template_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板代码',
  `require_verification` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要验证',
  `verification_level` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '验证级别：1=低,2=中,3=高',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_static_config` (`tenant_id`,`purpose`,`target_type`,`config_mode`),
  UNIQUE KEY `uk_dynamic_config` (`tenant_id`,`business_scene`,`judgment_dimension`,`config_mode`),
  KEY `idx_tenant_mode` (`tenant_id`,`config_mode`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_priority` (`priority`),
  KEY `idx_business_scene` (`business_scene`),
  KEY `idx_purpose` (`purpose`),
  KEY `idx_target_type` (`target_type`),
  KEY `idx_token_type` (`token_type`),
  KEY `idx_template_code` (`template_code`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=737303290340970497 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='统一验证配置表';

-- ----------------------------
-- Records of verification_configs
-- ----------------------------
BEGIN;
INSERT INTO `verification_configs` (`id`, `tenant_id`, `config_mode`, `purpose`, `target_type`, `business_scene`, `judgment_dimension`, `condition_expr`, `token_type`, `token_length`, `expire_minutes`, `max_attempts`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `template_code`, `require_verification`, `verification_level`, `priority`, `is_active`, `description`, `created_at`, `updated_at`, `deleted_at`) VALUES (737271804510343168, 1, 'static', 1, 1, NULL, NULL, NULL, 1, 6, 30, 3, 1, 10, 50, 'account_activation', 1, 1, 10, 1, NULL, '2025-07-27 11:34:56', '2025-07-27 11:34:56', NULL);
INSERT INTO `verification_configs` (`id`, `tenant_id`, `config_mode`, `purpose`, `target_type`, `business_scene`, `judgment_dimension`, `condition_expr`, `token_type`, `token_length`, `expire_minutes`, `max_attempts`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `template_code`, `require_verification`, `verification_level`, `priority`, `is_active`, `description`, `created_at`, `updated_at`, `deleted_at`) VALUES (737303290340970496, 1, 'static', 2, 1, NULL, NULL, NULL, 1, 6, 1440, 2, 3, 5, 5, 'password_reset_code', 1, 1, 10, 1, NULL, '2025-07-27 13:40:03', '2025-07-27 13:40:03', NULL);
COMMIT;

-- ----------------------------
-- Table structure for verification_tokens
-- ----------------------------
DROP TABLE IF EXISTS `verification_tokens`;
CREATE TABLE `verification_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint unsigned NOT NULL COMMENT '租户ID',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '用户ID，可为空（如注册时）',
  `token` varchar(255) NOT NULL COMMENT '验证令牌/验证码',
  `token_type` tinyint unsigned NOT NULL COMMENT '令牌类型：1=链接,2=验证码',
  `target` varchar(255) NOT NULL COMMENT '目标地址（邮箱/手机号）',
  `target_type` tinyint unsigned NOT NULL COMMENT '目标类型：1=邮箱,2=手机号,3=MFA',
  `purpose` tinyint unsigned NOT NULL COMMENT '用途：1=注册激活,2=密码重置,3=邮箱变更,4=手机变更,5=登录验证,6=MFA验证',
  `template_code` varchar(100) NOT NULL COMMENT '关联的模板代码',
  `status` tinyint unsigned DEFAULT '1' COMMENT '状态：1=未使用,2=已使用,3=已过期,4=已撤销',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `revoked_at` timestamp NULL DEFAULT NULL COMMENT '撤销时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '请求IP地址',
  `user_agent` text COMMENT '用户代理',
  `attempt_count` int DEFAULT '0' COMMENT '尝试次数',
  `max_attempts` int DEFAULT '5' COMMENT '最大尝试次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_target` (`target`),
  KEY `idx_purpose` (`purpose`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=737549125557424129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证令牌表';

-- ----------------------------
-- Records of verification_tokens
-- ----------------------------
BEGIN;
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737152517799940096, 4, 17003, 'd9d9b1ccbe36917ededc28b989c6977b', 1, '<EMAIL>', 1, 1, 'account_activation', 1, '2025-07-28 03:40:56', NULL, NULL, '', '', 0, 5, '2025-07-27 03:40:56', '2025-07-27 03:40:56');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737303665823453184, 2, 2001, 'ebbd3e7d06b5fc906caa56dbc39b693b', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 13:41:32', NULL, '2025-07-27 13:46:24', '', '', 0, 2, '2025-07-27 13:41:32', '2025-07-27 13:46:24');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737304890333728768, 2, 2001, 'b1eb29e214ecdbd99355962f0820eabf', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 13:46:24', NULL, '2025-07-27 13:48:17', '', '', 0, 2, '2025-07-27 13:46:24', '2025-07-27 13:48:17');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737305364474630144, 2, 2001, '956e7b7e6061641534e7eb3f2b747fef', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 13:48:17', NULL, '2025-07-27 14:14:37', '', '', 0, 2, '2025-07-27 13:48:17', '2025-07-27 14:14:37');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737311990065664000, 2, 2001, '09f25c3b6440c7493fef21dae2ab4c7d', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 14:14:37', NULL, '2025-07-27 14:26:42', '', '', 0, 2, '2025-07-27 14:14:37', '2025-07-27 14:26:42');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737315029103153152, 2, 2001, '91d041c15d5657a1ec3c40522506fb0e', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 14:26:42', NULL, '2025-07-27 14:48:29', '', '', 0, 2, '2025-07-27 14:26:42', '2025-07-27 14:48:29');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737320514040631296, 2, 2001, '572604381f27b999601ad1f73be36ab5', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 14:48:29', NULL, '2025-07-27 14:54:05', '', '', 0, 2, '2025-07-27 14:48:29', '2025-07-27 14:54:05');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737321920843747328, 2, 2001, 'd175991185335087eea3ada817cd3bf4', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 14:54:05', NULL, '2025-07-27 15:05:38', '', '', 0, 2, '2025-07-27 14:54:05', '2025-07-27 15:05:38');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737324830562455552, 2, 2001, '62e934d8b6ff2c18f409569492c9f707', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 15:05:38', NULL, '2025-07-27 15:09:35', '', '', 0, 2, '2025-07-27 15:05:38', '2025-07-27 15:09:35');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737325823006085120, 2, 2001, 'b85a0d0b63d547eaa098a0cf71c1e223', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 15:09:35', NULL, '2025-07-27 15:32:50', '', '', 0, 2, '2025-07-27 15:09:35', '2025-07-27 15:32:50');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737331672583770112, 2, 2001, '2fe281bee48b02a21c591e4ff1ac1f59', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 15:32:50', NULL, '2025-07-27 15:49:57', '', '', 0, 2, '2025-07-27 15:32:50', '2025-07-27 15:49:57');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737335983443283968, 2, 2001, '8df632ce1542741e3d1fcffab0de326b', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-28 15:49:57', NULL, '2025-07-28 02:46:18', '', '', 0, 2, '2025-07-27 15:49:57', '2025-07-28 02:46:18');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737501156229517312, 2, 2001, 'f59046fcbecaa5f185ee8b6f8cd06e03', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-29 02:46:18', NULL, '2025-07-28 03:46:36', '', '', 0, 2, '2025-07-28 02:46:18', '2025-07-28 03:46:36');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737516333083660288, 2, 2001, '12203d60446bf6769d614c636b5f1310', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-29 03:46:36', NULL, '2025-07-28 05:38:07', '', '', 0, 2, '2025-07-28 03:46:36', '2025-07-28 05:38:07');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737544397620514816, 2, 2001, 'fcf09e75fed93a8f775095b9d31b5ab4', 1, '<EMAIL>', 1, 2, 'password_reset_code', 4, '2025-07-29 05:38:07', NULL, '2025-07-28 05:56:54', '', '', 0, 2, '2025-07-28 05:38:07', '2025-07-28 05:56:54');
INSERT INTO `verification_tokens` (`id`, `tenant_id`, `user_id`, `token`, `token_type`, `target`, `target_type`, `purpose`, `template_code`, `status`, `expires_at`, `used_at`, `revoked_at`, `ip_address`, `user_agent`, `attempt_count`, `max_attempts`, `created_at`, `updated_at`) VALUES (737549125557424128, 2, 2001, '2bb15ef89035e59f231c3aeedc2b4f95', 1, '<EMAIL>', 1, 2, 'password_reset_code', 1, '2025-07-29 05:56:55', NULL, NULL, '', '', 0, 2, '2025-07-28 05:56:55', '2025-07-28 05:56:55');
COMMIT;

-- ----------------------------
-- Table structure for verification_trigger_logs
-- ----------------------------
DROP TABLE IF EXISTS `verification_trigger_logs`;
CREATE TABLE `verification_trigger_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `device_id` varchar(128) DEFAULT NULL COMMENT '设备ID',
  `policy_id` bigint DEFAULT NULL COMMENT '命中的策略ID',
  `verification_needed` tinyint(1) NOT NULL COMMENT '是否需要验证',
  `verification_level` enum('none','low','medium','high') NOT NULL COMMENT '验证级别',
  `trigger_reason` varchar(255) DEFAULT NULL COMMENT '触发原因/命中条件',
  `scene` varchar(32) NOT NULL COMMENT '业务场景，如login、register',
  `request_id` varchar(64) DEFAULT NULL COMMENT '请求唯一ID，便于链路追踪',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `extra` json DEFAULT NULL COMMENT '其他扩展信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='验证触发判定记录表';

-- ----------------------------
-- Records of verification_trigger_logs
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
