-- 修复 verification_tokens 表结构
-- 迁移时间: 2025-01-27
-- 问题描述: verification_tokens表缺少attempt_count和max_attempts字段，导致插入数据时出现错误

-- 1. 检查当前表结构
DESCRIBE verification_tokens;

-- 2. 检查字段是否已存在
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'platforms-user' 
  AND TABLE_NAME = 'verification_tokens'
  AND COLUMN_NAME IN ('attempt_count', 'max_attempts');

-- 3. 添加缺失字段（如果不存在）
-- 注意：MySQL不支持在单个ALTER语句中同时添加多个字段，需要分别执行

-- 添加 attempt_count 字段
ALTER TABLE verification_tokens 
ADD COLUMN attempt_count INT DEFAULT 0 COMMENT '尝试次数' AFTER user_agent;

-- 添加 max_attempts 字段
ALTER TABLE verification_tokens 
ADD COLUMN max_attempts INT DEFAULT 5 COMMENT '最大尝试次数' AFTER attempt_count;

-- 4. 添加相关索引
ALTER TABLE verification_tokens 
ADD INDEX idx_attempt_count (attempt_count),
ADD INDEX idx_max_attempts (max_attempts);

-- 5. 验证修复结果
DESCRIBE verification_tokens;

-- 6. 验证字段添加成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'platforms-user' 
  AND TABLE_NAME = 'verification_tokens'
  AND COLUMN_NAME IN ('attempt_count', 'max_attempts');

-- 7. 验证索引创建成功
SHOW INDEX FROM verification_tokens WHERE Key_name IN ('idx_attempt_count', 'idx_max_attempts');

-- 8. 测试插入操作（可选）
-- INSERT INTO verification_tokens (
--     tenant_id, user_id, token, token_type, target, target_type, 
--     purpose, template_code, status, expires_at, ip_address, 
--     user_agent, attempt_count, max_attempts
-- ) VALUES (
--     1, 1001, 'test_token_123', 1, '<EMAIL>', 1, 
--     1, 'test_template', 1, DATE_ADD(NOW(), INTERVAL 30 MINUTE), 
--     '127.0.0.1', 'test_agent', 0, 5
-- );

-- 9. 清理测试数据（如果执行了测试插入）
-- DELETE FROM verification_tokens WHERE token = 'test_token_123';

-- 修复完成提示
SELECT 'verification_tokens table structure fixed successfully' as status; 