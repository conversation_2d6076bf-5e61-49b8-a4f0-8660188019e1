#!/bin/bash

# 停止 Jaeger 的脚本

echo "Stopping <PERSON><PERSON><PERSON>..."

# 检查 Jaeger 是否在运行
if ! docker ps | grep -q jaeger; then
    echo "<PERSON><PERSON><PERSON> is not running."
    exit 0
fi

# 停止 J<PERSON>ger 容器
echo "Stopping Jaeger container..."
docker stop jaeger

if [ $? -eq 0 ]; then
    echo "<PERSON><PERSON><PERSON> stopped successfully!"
    echo ""
    echo "To remove the container, run: docker rm jaeger"
    echo "To start <PERSON><PERSON><PERSON> again, run: ./scripts/start-jaeger.sh"
else
    echo "Failed to stop <PERSON><PERSON><PERSON>."
    exit 1
fi 