#!/bin/bash

# 启动 J<PERSON>ger 的脚本
# 用于开发环境

echo "Starting Jaeger for development environment..."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker first."
    exit 1
fi

# 检查 Jaeger 是否已经在运行
if docker ps | grep -q jaeger; then
    echo "<PERSON><PERSON><PERSON> is already running."
    echo "Jaeger UI: http://localhost:16686"
    echo "Jaeger Collector: http://localhost:14268"
    exit 0
fi

# 启动 Jaeger
echo "Starting Jaeger container..."
docker run -d \
    --name jaeger \
    -p 16686:16686 \
    -p 14268:14268 \
    -p 14250:14250 \
    -p 6831:6831/udp \
    -p 6832:6832/udp \
    jaegertracing/all-in-one:latest

if [ $? -eq 0 ]; then
    echo "Jae<PERSON> started successfully!"
    echo "Jaeger UI: http://localhost:16686"
    echo "Jaeger Collector: http://localhost:14268"
    echo ""
    echo "To stop <PERSON><PERSON><PERSON>, run: docker stop jaeger"
    echo "To remove <PERSON><PERSON><PERSON> container, run: docker rm jaeger"
else
    echo "Failed to start <PERSON><PERSON><PERSON>."
    exit 1
fi 