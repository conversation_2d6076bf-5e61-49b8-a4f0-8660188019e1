package logiface

import (
	"context"
	"net/http"

	"go.opentelemetry.io/otel/trace"
)

// TraceIDFromContext 优先级：
// 1. OpenTelemetry traceID（最可靠）
// 2. HTTP/gRPC request context/header: X-Trace-ID
// 3. gin.Context (X-Trace-ID)
// 4. 通用 context value
func TraceIDFromContext(ctx context.Context) string {
	// 1. OpenTelemetry - 优先使用（最可靠的trace ID）
	if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
		if traceID := span.SpanContext().TraceID().String(); traceID != "" && traceID != "00000000000000000000000000000000" {
			return traceID
		}
	}
	// 2. HTTP request
	if req, ok := ctx.Value("httpRequest").(*http.Request); ok && req != nil {
		if tid := req.Header.Get("X-Trace-ID"); tid != "" {
			return tid
		}
	}
	// 3. gRPC metadata (可扩展)
	if md, ok := ctx.Value("grpcMetadata").(map[string][]string); ok {
		if vals, ok := md["x-trace-id"]; ok && len(vals) > 0 && vals[0] != "" {
			return vals[0]
		}
	}
	// 4. gin.Context
	if ginCtx, ok := ctx.Value("GinContextKey").(interface{ GetString(string) string }); ok {
		if tid := ginCtx.GetString("X-Trace-ID"); tid != "" {
			return tid
		}
	}
	// 5. 通用 context value
	if tid, ok := ctx.Value("X-Trace-ID").(string); ok && tid != "" {
		return tid
	}
	return ""
}

// SpanIDFromContext 获取SpanID
func SpanIDFromContext(ctx context.Context) string {
	// OpenTelemetry SpanID
	if span := trace.SpanFromContext(ctx); span.SpanContext().IsValid() {
		if spanID := span.SpanContext().SpanID().String(); spanID != "" && spanID != "0000000000000000" {
			return spanID
		}
	}
	return ""
}

// RequestIDFromContext 优先级：
// 1. HTTP/gRPC request context/header: X-Request-ID
// 2. gin.Context (X-Request-ID)
// 3. 通用 context value
func RequestIDFromContext(ctx context.Context) string {
	// 1. HTTP request
	if req, ok := ctx.Value("httpRequest").(*http.Request); ok && req != nil {
		if rid := req.Header.Get("X-Request-ID"); rid != "" {
			return rid
		}
	}
	// 2. gRPC metadata (可扩展)
	if md, ok := ctx.Value("grpcMetadata").(map[string][]string); ok {
		if vals, ok := md["x-request-id"]; ok && len(vals) > 0 && vals[0] != "" {
			return vals[0]
		}
	}
	// 3. gin.Context
	if ginCtx, ok := ctx.Value("GinContextKey").(interface{ GetString(string) string }); ok {
		if rid := ginCtx.GetString("X-Request-ID"); rid != "" {
			return rid
		}
	}
	// 4. 通用 context value
	if rid, ok := ctx.Value("X-Request-ID").(string); ok && rid != "" {
		return rid
	}
	return ""
}

// AddTraceFieldsToLogger 添加trace相关字段到日志
func AddTraceFieldsToLogger(ctx context.Context, fields []Field) []Field {
	if traceID := TraceIDFromContext(ctx); traceID != "" {
		fields = append(fields, String("traceID", traceID))
	}
	if spanID := SpanIDFromContext(ctx); spanID != "" {
		fields = append(fields, String("spanID", spanID))
	}
	if requestID := RequestIDFromContext(ctx); requestID != "" {
		fields = append(fields, String("requestID", requestID))
	}
	return fields
}

// CreateTraceAwareContext 创建包含trace信息的context
func CreateTraceAwareContext(ctx context.Context, traceID, spanID, requestID string) context.Context {
	if traceID != "" {
		ctx = context.WithValue(ctx, "X-Trace-ID", traceID)
	}
	if requestID != "" {
		ctx = context.WithValue(ctx, "X-Request-ID", requestID)
	}
	return ctx
}
