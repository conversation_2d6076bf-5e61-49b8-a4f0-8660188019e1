package grpcregistry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"platforms-pkg/grpcmiddleware"
	"platforms-pkg/logiface"

	"github.com/nacos-group/nacos-sdk-go/v2/model"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// ClientManager gRPC客户端管理器
type ClientManager struct {
	serviceDiscovery *ServiceDiscovery
	connectionPool   map[string]*grpc.ClientConn
	mutex            sync.RWMutex
	logger           logiface.Logger
	config           *ClientManagerConfig
}

// ClientManagerConfig 客户端管理器配置
type ClientManagerConfig struct {
	ServiceName         string        `yaml:"serviceName" json:"serviceName" toml:"serviceName"`
	Group               string        `yaml:"group" json:"group" toml:"group"`
	Namespace           string        `yaml:"namespace" json:"namespace" toml:"namespace"`
	Strategy            string        `yaml:"strategy" json:"strategy" toml:"strategy"`
	Subscribe           bool          `yaml:"subscribe" json:"subscribe" toml:"subscribe"`
	ConnTimeout         string        `yaml:"connTimeout" json:"connTimeout" toml:"connTimeout"`
	MaxConnections      int           `yaml:"maxConnections" json:"maxConnections" toml:"maxConnections"`
	ConnTimeoutDuration time.Duration `yaml:"-" json:"-" toml:"-"` // 运行时用，从ConnTimeout解析而来
}

// NewClientManager 创建gRPC客户端管理器
func NewClientManager(config *ClientManagerConfig, logger logiface.Logger) (*ClientManager, error) {
	// 设置默认值
	if config.ConnTimeout == "" {
		config.ConnTimeout = "1s"
	}
	if config.MaxConnections == 0 {
		config.MaxConnections = 10
	}
	if config.Strategy == "" {
		config.Strategy = LoadBalanceRandom.String()
	}
	// 解析超时时间
	timeout, err := time.ParseDuration(config.ConnTimeout)
	if err != nil {
		logger.Warn(context.Background(), "Invalid ConnTimeout, fallback to 1s", logiface.String("value", config.ConnTimeout), logiface.Error(err))
		timeout = 1 * time.Second
	}
	config.ConnTimeoutDuration = timeout

	// 创建服务发现
	discovery, err := NewServiceDiscovery(config.ServiceName, config.Group, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create service discovery: %w", err)
	}

	manager := &ClientManager{
		serviceDiscovery: discovery,
		connectionPool:   make(map[string]*grpc.ClientConn),
		logger:           logger,
		config:           config,
	}

	// 启动服务订阅（如果启用）
	if config.Subscribe {
		err = manager.startServiceSubscription()
		if err != nil {
			logger.Warn(context.Background(), "Failed to start service subscription",
				logiface.Error(err))
		}
	}

	return manager, nil
}

// GetConnection 获取gRPC连接
func (m *ClientManager) GetConnection(ctx context.Context) (*grpc.ClientConn, error) {
	// 获取服务地址（使用配置的负载均衡策略）
	address, err := m.serviceDiscovery.GetServiceAddressWithStrategy(ctx, ParseLoadBalanceStrategy(m.config.Strategy))
	if err != nil {
		return nil, fmt.Errorf("failed to get service address: %w", err)
	}

	// 获取或创建连接
	conn, err := m.getOrCreateConnection(address)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection: %w", err)
	}

	m.logger.Debug(ctx, "Got gRPC connection",
		logiface.String("service", m.config.ServiceName),
		logiface.String("address", address),
		logiface.String("strategy", m.config.Strategy),
	)

	return conn, nil
}

// GetClient 获取泛型客户端
func (m *ClientManager) GetClient(ctx context.Context, clientFactory func(*grpc.ClientConn) interface{}) (interface{}, error) {
	conn, err := m.GetConnection(ctx)
	if err != nil {
		return nil, err
	}

	return clientFactory(conn), nil
}

// getOrCreateConnection 获取或创建连接
func (m *ClientManager) getOrCreateConnection(address string) (*grpc.ClientConn, error) {
	m.mutex.RLock()
	if conn, exists := m.connectionPool[address]; exists {
		// 检查连接状态
		if conn.GetState().String() != "SHUTDOWN" {
			m.mutex.RUnlock()
			return conn, nil
		}
	}
	m.mutex.RUnlock()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 双重检查
	if conn, exists := m.connectionPool[address]; exists {
		if conn.GetState().String() != "SHUTDOWN" {
			return conn, nil
		}
		// 删除已关闭的连接
		delete(m.connectionPool, address)
	}

	// 检查连接数限制
	if len(m.connectionPool) >= m.config.MaxConnections {
		return nil, fmt.Errorf("max connections (%d) reached", m.config.MaxConnections)
	}

	// 创建新连接（使用推荐的 grpc.NewClient）
	conn, err := grpc.NewClient(address,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),                                                            // 添加 OpenTelemetry 支持
		grpc.WithUnaryInterceptor(grpcmiddleware.ClientAccessLogInterceptor(m.logger, []string{"password", "token"})), // 添加客户端访问日志
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create grpc connection: %w", err)
	}

	m.connectionPool[address] = conn
	m.logger.Info(context.Background(), "Created new gRPC connection",
		logiface.String("address", address),
		logiface.String("service", m.config.ServiceName))

	return conn, nil
}

// startServiceSubscription 启动服务订阅
func (m *ClientManager) startServiceSubscription() error {
	return m.serviceDiscovery.SubscribeService(context.Background(), func(instances []model.Instance, err error) {
		if err != nil {
			m.logger.Error(context.Background(), "Service subscription error",
				logiface.Error(err),
				logiface.String("service", m.config.ServiceName))
			return
		}

		m.logger.Info(context.Background(), "Service instances updated",
			logiface.String("service", m.config.ServiceName),
			logiface.Int("count", len(instances)))

		// 清理无效连接
		m.cleanupInvalidConnections(instances)
	})
}

// cleanupInvalidConnections 清理无效连接
func (m *ClientManager) cleanupInvalidConnections(instances []model.Instance) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 构建有效地址集合
	validAddresses := make(map[string]bool)
	for _, instance := range instances {
		address := fmt.Sprintf("%s:%d", instance.Ip, instance.Port)
		validAddresses[address] = true
	}

	// 关闭无效连接
	for address, conn := range m.connectionPool {
		if !validAddresses[address] {
			conn.Close()
			delete(m.connectionPool, address)
			m.logger.Info(context.Background(), "Closed invalid connection",
				logiface.String("address", address),
				logiface.String("service", m.config.ServiceName))
		}
	}
}

// ClearConnectionPool 清理连接池，强制重新创建所有连接
func (m *ClientManager) ClearConnectionPool() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有现有连接
	for address, conn := range m.connectionPool {
		conn.Close()
		m.logger.Info(context.Background(), "Closed connection for pool refresh",
			logiface.String("address", address),
			logiface.String("service", m.config.ServiceName))
	}

	// 清空连接池
	m.connectionPool = make(map[string]*grpc.ClientConn)

	m.logger.Info(context.Background(), "Connection pool cleared",
		logiface.String("service", m.config.ServiceName))
}

// Close 关闭管理器
func (m *ClientManager) Close() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 关闭所有连接
	for address, conn := range m.connectionPool {
		conn.Close()
		m.logger.Info(context.Background(), "Closed gRPC connection",
			logiface.String("address", address),
			logiface.String("service", m.config.ServiceName))
	}

	// 清空连接池
	m.connectionPool = make(map[string]*grpc.ClientConn)

	// 关闭服务发现
	if m.serviceDiscovery != nil {
		m.serviceDiscovery.Close()
	}

	return nil
}

// GetConnectionStats 获取连接统计信息
func (m *ClientManager) GetConnectionStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_connections"] = len(m.connectionPool)
	stats["max_connections"] = m.config.MaxConnections
	stats["service_name"] = m.config.ServiceName
	stats["strategy"] = m.config.Strategy

	connections := make([]map[string]interface{}, 0, len(m.connectionPool))
	for address, conn := range m.connectionPool {
		connInfo := map[string]interface{}{
			"address": address,
			"state":   conn.GetState().String(),
		}
		connections = append(connections, connInfo)
	}
	stats["connections"] = connections

	return stats
}

// Health 检查管理器健康状态
func (m *ClientManager) Health(ctx context.Context) error {
	instances, err := m.serviceDiscovery.GetHealthyInstances(ctx)
	if err != nil {
		return fmt.Errorf("service discovery health check failed: %w", err)
	}

	if len(instances) == 0 {
		return fmt.Errorf("no healthy instances available for service: %s", m.config.ServiceName)
	}

	return nil
}

// GetServiceInfo 获取服务信息
func (m *ClientManager) GetServiceInfo() *ClientManagerConfig {
	return m.config
}

// DefaultClientManagerConfig 返回默认客户端管理器配置
func DefaultClientManagerConfig() *ClientManagerConfig {
	return &ClientManagerConfig{
		Group:          "DEFAULT_GROUP",
		Strategy:       LoadBalanceRandom.String(),
		Subscribe:      true,
		ConnTimeout:    "10s",
		MaxConnections: 10,
	}
}
