package grpcregistry

import (
	"context"
	"fmt"
	"sync"

	"platforms-pkg/logiface"

	"google.golang.org/grpc"
)

// GlobalManager 全局gRPC客户端管理器
type GlobalManager struct {
	managers map[string]*ClientManager
	mutex    sync.RWMutex
	logger   logiface.Logger
}

// globalManager 全局实例
var (
	globalManager *GlobalManager
	globalOnce    sync.Once
)

// InitGlobalManager 初始化全局管理器
func InitGlobalManager(logger logiface.Logger) {
	globalOnce.Do(func() {
		globalManager = &GlobalManager{
			managers: make(map[string]*ClientManager),
			logger:   logger,
		}
	})
}

// GetGlobalManager 获取全局管理器
func GetGlobalManager() *GlobalManager {
	if globalManager == nil {
		panic("GlobalManager not initialized, call InitGlobalManager first")
	}
	return globalManager
}

// RegisterService 订阅服务客户端管理器
func (gm *GlobalManager) RegisterService(serviceName string, config *ClientManagerConfig) error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()

	if _, exists := gm.managers[serviceName]; exists {
		return fmt.Errorf("service %s already subscribed", serviceName)
	}

	// 确保配置中的服务名称一致
	config.ServiceName = serviceName

	manager, err := NewClientManager(config, gm.logger)
	if err != nil {
		return fmt.Errorf("failed to create client manager for %s: %w", serviceName, err)
	}

	gm.managers[serviceName] = manager
	gm.logger.Info(context.Background(), "Subscribed to gRPC service",
		logiface.String("service", serviceName),
		logiface.String("strategy", config.Strategy),
	)
	return nil
}

// GetServiceManager 获取指定服务的客户端管理器
func (gm *GlobalManager) GetServiceManager(serviceName string) (*ClientManager, error) {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()

	manager, exists := gm.managers[serviceName]
	if !exists {
		return nil, fmt.Errorf("service %s not subscribed", serviceName)
	}

	return manager, nil
}

// GetConnection 获取指定服务的gRPC连接
func (gm *GlobalManager) GetConnection(ctx context.Context, serviceName string) (*grpc.ClientConn, error) {
	manager, err := gm.GetServiceManager(serviceName)
	if err != nil {
		return nil, err
	}

	return manager.GetConnection(ctx)
}

// GetClient 获取指定服务的客户端
func (gm *GlobalManager) GetClient(ctx context.Context, serviceName string, clientFactory func(*grpc.ClientConn) interface{}) (interface{}, error) {
	manager, err := gm.GetServiceManager(serviceName)
	if err != nil {
		return nil, err
	}

	return manager.GetClient(ctx, clientFactory)
}

// UnregisterService 取消订阅服务客户端管理器
func (gm *GlobalManager) UnregisterService(serviceName string) error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()

	manager, exists := gm.managers[serviceName]
	if !exists {
		return fmt.Errorf("service %s not subscribed", serviceName)
	}

	// 关闭管理器
	if err := manager.Close(); err != nil {
		gm.logger.Warn(context.Background(), "Failed to close client manager",
			logiface.String("service", serviceName),
			logiface.Error(err))
	}

	delete(gm.managers, serviceName)
	gm.logger.Info(context.Background(), "Unsubscribed from gRPC service",
		logiface.String("service", serviceName))

	return nil
}

// GetAllStats 获取所有服务的连接统计信息
func (gm *GlobalManager) GetAllStats() map[string]interface{} {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_services"] = len(gm.managers)

	services := make(map[string]interface{})
	for serviceName, manager := range gm.managers {
		services[serviceName] = manager.GetConnectionStats()
	}
	stats["services"] = services

	return stats
}

// HealthCheck 检查所有服务的健康状态
func (gm *GlobalManager) HealthCheck(ctx context.Context) map[string]error {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()

	results := make(map[string]error)
	for serviceName, manager := range gm.managers {
		results[serviceName] = manager.Health(ctx)
	}

	return results
}

// Close 关闭全局管理器
func (gm *GlobalManager) Close() error {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()

	for serviceName, manager := range gm.managers {
		if err := manager.Close(); err != nil {
			gm.logger.Warn(context.Background(), "Failed to close client manager",
				logiface.String("service", serviceName),
				logiface.Error(err))
		}
	}

	gm.managers = make(map[string]*ClientManager)
	gm.logger.Info(context.Background(), "Closed global gRPC client manager")

	return nil
}

// GetRegisteredServices 获取已订阅的服务列表
func (gm *GlobalManager) GetRegisteredServices() []string {
	gm.mutex.RLock()
	defer gm.mutex.RUnlock()

	services := make([]string, 0, len(gm.managers))
	for serviceName := range gm.managers {
		services = append(services, serviceName)
	}

	return services
}

// ClearAllConnectionPools 清理所有服务的连接池
func (gm *GlobalManager) ClearAllConnectionPools() {
	gm.mutex.Lock()
	defer gm.mutex.Unlock()

	for serviceName, manager := range gm.managers {
		manager.ClearConnectionPool()
		gm.logger.Info(context.Background(), "Cleared connection pool for service",
			logiface.String("service", serviceName))
	}
}

// 便捷函数

// SubscribeServiceGlobal 全局订阅服务
func SubscribeServiceGlobal(serviceName string, config *ClientManagerConfig) error {
	return GetGlobalManager().RegisterService(serviceName, config)
}

// GetConnectionGlobal 全局获取连接
func GetConnectionGlobal(ctx context.Context, serviceName string) (*grpc.ClientConn, error) {
	return GetGlobalManager().GetConnection(ctx, serviceName)
}

// GetClientGlobal 全局获取客户端
func GetClientGlobal(ctx context.Context, serviceName string, clientFactory func(*grpc.ClientConn) interface{}) (interface{}, error) {
	return GetGlobalManager().GetClient(ctx, serviceName, clientFactory)
}

// UnsubscribeServiceGlobal 全局取消订阅服务
func UnsubscribeServiceGlobal(serviceName string) error {
	return GetGlobalManager().UnregisterService(serviceName)
}

// ClearAllConnectionPoolsGlobal 全局清理所有连接池
func ClearAllConnectionPoolsGlobal() {
	GetGlobalManager().ClearAllConnectionPools()
}

// ParseStrategyFromString 从字符串解析负载均衡策略
func ParseStrategyFromString(strategy string) LoadBalanceStrategy {
	switch strategy {
	case "random":
		return LoadBalanceRandom
	case "round_robin":
		return LoadBalanceRoundRobin
	case "weighted":
		return LoadBalanceWeighted
	case "consistent_hash":
		return LoadBalanceConsistentHash
	case "least_connections":
		return LoadBalanceLeastConnections
	default:
		return LoadBalanceRandom
	}
}

// BatchSubscribeServices 批量订阅多个gRPC服务的变化
func BatchSubscribeServices(configs []*ClientManagerConfig, logger logiface.Logger) error {
	if len(configs) == 0 {
		logger.Info(context.Background(), "no grpc services to subscribe")
		return nil
	}
	for _, cfg := range configs {
		if err := SubscribeServiceGlobal(cfg.ServiceName, cfg); err != nil {
			logger.Error(context.Background(), "failed to subscribe grpc service",
				logiface.String("service", cfg.ServiceName), logiface.Error(err))
			return err // 如需全部订阅可改为 continue
		}
	}
	return nil
}
