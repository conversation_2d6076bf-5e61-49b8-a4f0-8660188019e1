package common_database

import (
	"context"
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
	"log"
	"platforms-pkg/logiface"
	"time"
)

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL MySQLConfig `json:"mysql" yaml:"mysql" toml:"mysql"`
}

// MySQLConfig MySQL配置
type MySQLConfig struct {
	Host            string            `json:"host" yaml:"host" toml:"host"`
	Port            int               `json:"port" yaml:"port" toml:"port"`
	Database        string            `json:"database" yaml:"database" toml:"database"`
	Username        string            `json:"username" yaml:"username" toml:"username"`
	Password        string            `json:"password" yaml:"password" toml:"password"`
	Charset         string            `json:"charset" yaml:"charset" toml:"charset"`
	ParseTime       bool              `json:"parse_time" yaml:"parse_time" toml:"parse_time"`
	Loc             string            `json:"loc" yaml:"loc" toml:"loc"`
	MaxOpenConns    int               `json:"max_open_conns" yaml:"max_open_conns" toml:"max_open_conns"`
	MaxIdleConns    int               `json:"max_idle_conns" yaml:"max_idle_conns" toml:"max_idle_conns"`
	ConnMaxLifetime string            `json:"conn_max_lifetime" yaml:"conn_max_lifetime" toml:"conn_max_lifetime"`
	Params          map[string]string `json:"params" yaml:"params" toml:"params"`
	// 新增连接池相关配置
	ConnMaxIdleTime string `json:"conn_max_idle_time" yaml:"conn_max_idle_time" toml:"conn_max_idle_time"`
}

// GormLoggerAdapter 适配gorm logger.Interface，转发到统一logger
// 支持trace_id、慢SQL、结构化日志、动态级别调整
type GormLoggerAdapter struct {
	Logger        logiface.Logger
	SlowThreshold time.Duration
	LogLevel      gormlogger.LogLevel
}

// SetLogLevel 动态设置日志级别
func (g *GormLoggerAdapter) SetLogLevel(level gormlogger.LogLevel) {
	g.LogLevel = level
}

// GetLogLevel 获取当前日志级别
func (g *GormLoggerAdapter) GetLogLevel() gormlogger.LogLevel {
	return g.LogLevel
}

func (g *GormLoggerAdapter) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	// 创建新实例避免修改原实例状态，确保线程安全
	newAdapter := *g
	newAdapter.LogLevel = level
	return &newAdapter
}

func (g *GormLoggerAdapter) Info(ctx context.Context, msg string, data ...interface{}) {
	if g.LogLevel >= gormlogger.Info {
		g.Logger.Info(ctx, msg, logiface.Any("data", data))
	}
}

func (g *GormLoggerAdapter) Warn(ctx context.Context, msg string, data ...interface{}) {
	if g.LogLevel >= gormlogger.Warn {
		g.Logger.Warn(ctx, msg, logiface.Any("data", data))
	}
}

func (g *GormLoggerAdapter) Error(ctx context.Context, msg string, data ...interface{}) {
	if g.LogLevel >= gormlogger.Error {
		g.Logger.Error(ctx, msg, logiface.Any("data", data))
	}
}

func (g *GormLoggerAdapter) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	fields := []logiface.Field{
		logiface.String("sql", sql),
		logiface.Int64("rows", rows),
		logiface.Duration("elapsed", elapsed),
	}
	if err != nil && g.LogLevel >= gormlogger.Error {
		// 忽略未查询到记录的错误（如 First 查询不到数据）
		if err == gorm.ErrRecordNotFound {
			// 降级为 Info 级别记录，不认为是错误
			if g.LogLevel >= gormlogger.Info {
				g.Logger.Info(ctx, "gorm record not found", append(fields, logiface.Error(err))...)
			}
		} else {
			g.Logger.Error(ctx, "gorm error", append(fields, logiface.Error(err))...)
		}
	} else if g.SlowThreshold > 0 && elapsed > g.SlowThreshold && g.LogLevel >= gormlogger.Warn {
		g.Logger.Warn(ctx, "gorm slow query", fields...)
	} else if g.LogLevel >= gormlogger.Info {
		g.Logger.Info(ctx, "gorm query", fields...)
	}
}

// NewGormLoggerAdapter 创建增强的GORM日志适配器，支持动态级别调整
func NewGormLoggerAdapter(logger logiface.Logger, level gormlogger.LogLevel) *GormLoggerAdapter {
	return &GormLoggerAdapter{
		Logger:        logger,
		SlowThreshold: 200 * time.Millisecond, // 默认慢查询阈值
		LogLevel:      level,
	}
}

// NewDB 根据配置初始化数据库连接，支持注入自定义GORM Logger
func NewDB(mysqlCfg *MySQLConfig, gormLogger gormlogger.Interface) *gorm.DB {
	// 构建DSN
	params := ""
	for key, value := range mysqlCfg.Params {
		if params != "" {
			params += "&"
		}
		params += fmt.Sprintf("%s=%s", key, value)
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
		mysqlCfg.Username,
		mysqlCfg.Password,
		mysqlCfg.Host,
		mysqlCfg.Port,
		mysqlCfg.Database,
		params)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		panic(fmt.Sprintf("failed to connect to MySQL database: %v", err))
	}

	sqlDB, err := db.DB()
	if err != nil {
		panic(fmt.Sprintf("failed to get underlying sql.DB: %v", err))
	}

	sqlDB.SetMaxOpenConns(mysqlCfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(mysqlCfg.MaxIdleConns)
	// 连接最大生命周期
	if mysqlCfg.ConnMaxLifetime != "" {
		dur, err := time.ParseDuration(mysqlCfg.ConnMaxLifetime)
		if err == nil {
			sqlDB.SetConnMaxLifetime(dur)
		}
	}
	// 连接最大生命周期
	if mysqlCfg.ConnMaxIdleTime != "" {
		dur, err := time.ParseDuration(mysqlCfg.ConnMaxIdleTime)
		if err == nil {
			sqlDB.SetConnMaxIdleTime(dur)
		}
	}
	log.Println("Database connected successfully via NewDB. Please ensure tables are created using database_schema.sql")
	return db
}
